import type { CollectionConfig } from 'payload'

export const StoryTemplates: CollectionConfig = {
  slug: 'story-templates',
  dbName: 'story_templates', // Shorter database name
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'genre', 'ageGroup', 'status', 'publishedAt'],
    group: 'Content',
  },
  access: {
    read: () => true,
    create: ({ req: { user } }) => {
      return (user as unknown as { role: string })?.role === 'content-creator' || (user as unknown as { role: string })?.role === 'admin'
    },
    update: ({ req: { user } }) => {
      return (user as unknown as { role: string })?.role === 'content-creator' || (user as unknown as { role: string })?.role === 'admin'
    },
    delete: ({ req: { user } }) => {
      return (user as unknown as { role: string })?.role === 'admin'
    },
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      admin: {
        description: 'Title of the story template',
      },
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
      hooks: {
        beforeValidate: [
          ({ value, data }) => {
            if (!value && data?.title) {
              return data.title
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/(^-|-$)/g, '')
            }
            return value
          },
        ],
      },
    },
    {
      name: 'description',
      type: 'textarea',
      required: true,
      admin: {
        description: 'Brief description of the story template',
      },
    },
    {
      name: 'genre',
      type: 'select',
      required: true,
      hasMany: true,
      options: [
        { label: 'Adventure', value: 'adventure' },
        { label: 'Fantasy', value: 'fantasy' },
        { label: 'Science Fiction', value: 'sci-fi' },
        { label: 'Mystery', value: 'mystery' },
        { label: 'Friendship', value: 'friendship' },
        { label: 'Family', value: 'family' },
        { label: 'Animals', value: 'animals' },
        { label: 'Magic', value: 'magic' },
        { label: 'Space', value: 'space' },
        { label: 'Underwater', value: 'underwater' },
      ],
    },
    {
      name: 'ageGroup',
      type: 'select',
      required: true,
      hasMany: true,
      options: [
        { label: '6-8 years', value: '6-8' },
        { label: '9-11 years', value: '9-11' },
        { label: '12-14 years', value: '12-14' },
      ],
    },
    {
      name: 'storyPrompt',
      type: 'textarea',
      required: true,
      admin: {
        description: 'The opening prompt that starts the story',
        rows: 4,
      },
    },
    {
      name: 'characterOptions',
      type: 'array',
      required: true,
      minRows: 2,
      maxRows: 8,
      fields: [
        {
          name: 'name',
          type: 'text',
          required: true,
        },
        {
          name: 'description',
          type: 'text',
          required: true,
        },
        {
          name: 'image',
          type: 'upload',
          relationTo: 'media',
        },
      ],
      admin: {
        description: 'Character options children can choose from',
      },
    },
    {
      name: 'settingOptions',
      type: 'array',
      required: true,
      minRows: 2,
      maxRows: 6,
      fields: [
        {
          name: 'name',
          type: 'text',
          required: true,
        },
        {
          name: 'description',
          type: 'text',
          required: true,
        },
        {
          name: 'image',
          type: 'upload',
          relationTo: 'media',
        },
      ],
      admin: {
        description: 'Setting options for the story',
      },
    },
    {
      name: 'plotPoints',
      type: 'array',
      required: true,
      minRows: 3,
      maxRows: 10,
      fields: [
        {
          name: 'title',
          type: 'text',
          required: true,
        },
        {
          name: 'description',
          type: 'textarea',
          required: true,
        },
        {
          name: 'order',
          type: 'number',
          required: true,
          defaultValue: 1,
        },
        {
          name: 'optional',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Whether this plot point is optional',
          },
        },
      ],
      admin: {
        description: 'Key plot points to guide the story structure',
      },
    },
    {
      name: 'writingPrompts',
      type: 'array',
      fields: [
        {
          name: 'prompt',
          type: 'text',
          required: true,
        },
        {
          name: 'category',
          type: 'select',
          options: [
            { label: 'Character Development', value: 'character' },
            { label: 'Setting Description', value: 'setting' },
            { label: 'Action Scene', value: 'action' },
            { label: 'Dialogue', value: 'dialogue' },
            { label: 'Emotion', value: 'emotion' },
          ],
        },
      ],
      admin: {
        description: 'Additional writing prompts to help children',
      },
    },
    {
      name: 'estimatedLength',
      type: 'group',
      fields: [
        {
          name: 'minWords',
          type: 'number',
          defaultValue: 100,
        },
        {
          name: 'maxWords',
          type: 'number',
          defaultValue: 500,
        },
      ],
      admin: {
        description: 'Suggested story length',
      },
    },
    {
      name: 'learningObjectives',
      type: 'array',
      required: true,
      fields: [
        {
          name: 'objective',
          type: 'text',
          required: true,
        },
      ],
      admin: {
        description: 'What children will learn from this story template',
      },
    },
    {
      name: 'subscriptionTier',
      type: 'select',
      required: true,
      options: [
        { label: 'Free', value: 'free' },
        { label: 'Premium', value: 'premium' },
      ],
      defaultValue: 'premium',
    },
    {
      name: 'featured',
      type: 'checkbox',
      defaultValue: false,
    },
    {
      name: 'seasonal',
      type: 'group',
      fields: [
        {
          name: 'isSeasonalContent',
          type: 'checkbox',
          defaultValue: false,
        },
        {
          name: 'season',
          type: 'select',
          options: [
            { label: 'Spring', value: 'spring' },
            { label: 'Summer', value: 'summer' },
            { label: 'Fall/Autumn', value: 'fall' },
            { label: 'Winter', value: 'winter' },
            { label: 'Halloween', value: 'halloween' },
            { label: 'Christmas', value: 'christmas' },
            { label: 'New Year', value: 'newyear' },
          ],
          admin: {
            condition: (data, siblingData) => siblingData?.isSeasonalContent,
          },
        },
      ],
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Under Review', value: 'review' },
        { label: 'Published', value: 'published' },
        { label: 'Archived', value: 'archived' },
      ],
      defaultValue: 'draft',
    },
    {
      name: 'publishedAt',
      type: 'date',
      admin: {
        condition: (data) => data.status === 'published',
      },
    },
    {
      name: 'createdBy',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        readOnly: true,
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ data, operation, req }) => {
        if (data.status === 'published' && !data.publishedAt) {
          data.publishedAt = new Date()
        }
        
        if (operation === 'create' && req.user) {
          data.createdBy = req.user.id
        }
        
        return data
      },
    ],
  },
  versions: {
    drafts: true,
    maxPerDoc: 5,
  },
}
