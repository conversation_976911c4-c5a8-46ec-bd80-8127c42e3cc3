"use strict";exports.id=3505,exports.ids=[3505],exports.modules={1039:(a,b,c)=>{c.d(b,{k:()=>d});function d(a){return (b={})=>{let c=b.width?String(b.width):a.defaultWidth;return a.formats[c]||a.formats[a.defaultWidth]}}},2871:(a,b,c)=>{c.d(b,{k:()=>f});var d=c(8570),e=c(57241);function f(a,b){let c=(0,d.q)(),f=b?.weekStartsOn??b?.locale?.options?.weekStartsOn??c.weekStartsOn??c.locale?.options?.weekStartsOn??0,g=(0,e.a)(a,b?.in),h=g.getDay();return g.setDate(g.getDate()-(7*(h<f)+h-f)),g.setHours(0,0,0,0),g}},3843:(a,b,c)=>{c.d(b,{w:()=>e});var d=c(39819);function e(a,b){return"function"==typeof a?a(b):a&&"object"==typeof a&&d._P in a?a[d._P](b):a instanceof Date?new a.constructor(b):new Date(b)}},8570:(a,b,c)=>{c.d(b,{q:()=>e});let d={};function e(){return d}},13505:(a,b,c)=>{function d(a){return(b,c)=>{if(1===b)if(c?.addSuffix)return a.one[0].replace("{{time}}",a.one[2]);else return a.one[0].replace("{{time}}",a.one[1]);{let d=b%10==1&&b%100!=11;return c?.addSuffix?a.other[0].replace("{{time}}",d?a.other[3]:a.other[4]).replace("{{count}}",String(b)):a.other[0].replace("{{time}}",d?a.other[1]:a.other[2]).replace("{{count}}",String(b))}}}c.r(b),c.d(b,{default:()=>o,lv:()=>n});let e={lessThanXSeconds:d({one:["mazāk par {{time}}","sekundi","sekundi"],other:["mazāk nekā {{count}} {{time}}","sekunde","sekundes","sekundes","sekundēm"]}),xSeconds:d({one:["1 {{time}}","sekunde","sekundes"],other:["{{count}} {{time}}","sekunde","sekundes","sekundes","sekundēm"]}),halfAMinute:(a,b)=>b?.addSuffix?"pusminūtes":"pusminūte",lessThanXMinutes:d({one:["mazāk par {{time}}","minūti","minūti"],other:["mazāk nekā {{count}} {{time}}","minūte","minūtes","minūtes","minūtēm"]}),xMinutes:d({one:["1 {{time}}","minūte","minūtes"],other:["{{count}} {{time}}","minūte","minūtes","minūtes","minūtēm"]}),aboutXHours:d({one:["apmēram 1 {{time}}","stunda","stundas"],other:["apmēram {{count}} {{time}}","stunda","stundas","stundas","stundām"]}),xHours:d({one:["1 {{time}}","stunda","stundas"],other:["{{count}} {{time}}","stunda","stundas","stundas","stundām"]}),xDays:d({one:["1 {{time}}","diena","dienas"],other:["{{count}} {{time}}","diena","dienas","dienas","dienām"]}),aboutXWeeks:d({one:["apmēram 1 {{time}}","nedēļa","nedēļas"],other:["apmēram {{count}} {{time}}","nedēļa","nedēļu","nedēļas","nedēļām"]}),xWeeks:d({one:["1 {{time}}","nedēļa","nedēļas"],other:["{{count}} {{time}}","nedēļa","nedēļu","nedēļas","nedēļām"]}),aboutXMonths:d({one:["apmēram 1 {{time}}","mēnesis","mēneša"],other:["apmēram {{count}} {{time}}","mēnesis","mēneši","mēneša","mēnešiem"]}),xMonths:d({one:["1 {{time}}","mēnesis","mēneša"],other:["{{count}} {{time}}","mēnesis","mēneši","mēneša","mēnešiem"]}),aboutXYears:d({one:["apmēram 1 {{time}}","gads","gada"],other:["apmēram {{count}} {{time}}","gads","gadi","gada","gadiem"]}),xYears:d({one:["1 {{time}}","gads","gada"],other:["{{count}} {{time}}","gads","gadi","gada","gadiem"]}),overXYears:d({one:["ilgāk par 1 {{time}}","gadu","gadu"],other:["vairāk nekā {{count}} {{time}}","gads","gadi","gada","gadiem"]}),almostXYears:d({one:["gandrīz 1 {{time}}","gads","gada"],other:["vairāk nekā {{count}} {{time}}","gads","gadi","gada","gadiem"]})};var f=c(1039);let g={date:(0,f.k)({formats:{full:"EEEE, y. 'gada' d. MMMM",long:"y. 'gada' d. MMMM",medium:"dd.MM.y.",short:"dd.MM.y."},defaultWidth:"full"}),time:(0,f.k)({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},defaultWidth:"full"}),dateTime:(0,f.k)({formats:{full:"{{date}} 'plkst.' {{time}}",long:"{{date}} 'plkst.' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})};var h=c(84362);let i=["svētdienā","pirmdienā","otrdienā","trešdienā","ceturtdienā","piektdienā","sestdienā"],j={lastWeek:(a,b,c)=>(0,h.R)(a,b,c)?"eeee 'plkst.' p":"'Pagājušā "+i[a.getDay()]+" plkst.' p",yesterday:"'Vakar plkst.' p",today:"'Šodien plkst.' p",tomorrow:"'Rīt plkst.' p",nextWeek:(a,b,c)=>(0,h.R)(a,b,c)?"eeee 'plkst.' p":"'Nākamajā "+i[a.getDay()]+" plkst.' p",other:"P"};var k=c(80429);let l={ordinalNumber:(a,b)=>Number(a)+".",era:(0,k.o)({values:{narrow:["p.m.ē","m.ē"],abbreviated:["p. m. ē.","m. ē."],wide:["pirms mūsu ēras","mūsu ērā"]},defaultWidth:"wide"}),quarter:(0,k.o)({values:{narrow:["1","2","3","4"],abbreviated:["1. cet.","2. cet.","3. cet.","4. cet."],wide:["pirmais ceturksnis","otrais ceturksnis","trešais ceturksnis","ceturtais ceturksnis"]},defaultWidth:"wide",formattingValues:{narrow:["1","2","3","4"],abbreviated:["1. cet.","2. cet.","3. cet.","4. cet."],wide:["pirmajā ceturksnī","otrajā ceturksnī","trešajā ceturksnī","ceturtajā ceturksnī"]},defaultFormattingWidth:"wide",argumentCallback:a=>a-1}),month:(0,k.o)({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["janv.","febr.","marts","apr.","maijs","jūn.","jūl.","aug.","sept.","okt.","nov.","dec."],wide:["janvāris","februāris","marts","aprīlis","maijs","jūnijs","jūlijs","augusts","septembris","oktobris","novembris","decembris"]},defaultWidth:"wide",formattingValues:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["janv.","febr.","martā","apr.","maijs","jūn.","jūl.","aug.","sept.","okt.","nov.","dec."],wide:["janvārī","februārī","martā","aprīlī","maijā","jūnijā","jūlijā","augustā","septembrī","oktobrī","novembrī","decembrī"]},defaultFormattingWidth:"wide"}),day:(0,k.o)({values:{narrow:["S","P","O","T","C","P","S"],short:["Sv","P","O","T","C","Pk","S"],abbreviated:["svētd.","pirmd.","otrd.","trešd.","ceturtd.","piektd.","sestd."],wide:["svētdiena","pirmdiena","otrdiena","trešdiena","ceturtdiena","piektdiena","sestdiena"]},defaultWidth:"wide",formattingValues:{narrow:["S","P","O","T","C","P","S"],short:["Sv","P","O","T","C","Pk","S"],abbreviated:["svētd.","pirmd.","otrd.","trešd.","ceturtd.","piektd.","sestd."],wide:["svētdienā","pirmdienā","otrdienā","trešdienā","ceturtdienā","piektdienā","sestdienā"]},defaultFormattingWidth:"wide"}),dayPeriod:(0,k.o)({values:{narrow:{am:"am",pm:"pm",midnight:"pusn.",noon:"pusd.",morning:"rīts",afternoon:"diena",evening:"vakars",night:"nakts"},abbreviated:{am:"am",pm:"pm",midnight:"pusn.",noon:"pusd.",morning:"rīts",afternoon:"pēcpusd.",evening:"vakars",night:"nakts"},wide:{am:"am",pm:"pm",midnight:"pusnakts",noon:"pusdienlaiks",morning:"rīts",afternoon:"pēcpusdiena",evening:"vakars",night:"nakts"}},defaultWidth:"wide",formattingValues:{narrow:{am:"am",pm:"pm",midnight:"pusn.",noon:"pusd.",morning:"rītā",afternoon:"dienā",evening:"vakarā",night:"naktī"},abbreviated:{am:"am",pm:"pm",midnight:"pusn.",noon:"pusd.",morning:"rītā",afternoon:"pēcpusd.",evening:"vakarā",night:"naktī"},wide:{am:"am",pm:"pm",midnight:"pusnaktī",noon:"pusdienlaikā",morning:"rītā",afternoon:"pēcpusdienā",evening:"vakarā",night:"naktī"}},defaultFormattingWidth:"wide"})};var m=c(28153);let n={code:"lv",formatDistance:(a,b,c)=>{let d=e[a](b,c);if(c?.addSuffix)if(c.comparison&&c.comparison>0)return"pēc "+d;else return"pirms "+d;return d},formatLong:g,formatRelative:(a,b,c,d)=>{let e=j[a];return"function"==typeof e?e(b,c,d):e},localize:l,match:{ordinalNumber:(0,c(55991).K)({matchPattern:/^(\d+)\./i,parsePattern:/\d+/i,valueCallback:a=>parseInt(a,10)}),era:(0,m.A)({matchPatterns:{narrow:/^(p\.m\.ē|m\.ē)/i,abbreviated:/^(p\. m\. ē\.|m\. ē\.)/i,wide:/^(pirms mūsu ēras|mūsu ērā)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^p/i,/^m/i]},defaultParseWidth:"any"}),quarter:(0,m.A)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^[1234](\. cet\.)/i,wide:/^(pirma(is|jā)|otra(is|jā)|treša(is|jā)|ceturta(is|jā)) ceturksn(is|ī)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^1/i,/^2/i,/^3/i,/^4/i],abbreviated:[/^1/i,/^2/i,/^3/i,/^4/i],wide:[/^p/i,/^o/i,/^t/i,/^c/i]},defaultParseWidth:"wide",valueCallback:a=>a+1}),month:(0,m.A)({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(janv\.|febr\.|marts|apr\.|maijs|jūn\.|jūl\.|aug\.|sept\.|okt\.|nov\.|dec\.)/i,wide:/^(janvār(is|ī)|februār(is|ī)|mart[sā]|aprīl(is|ī)|maij[sā]|jūnij[sā]|jūlij[sā]|august[sā]|septembr(is|ī)|oktobr(is|ī)|novembr(is|ī)|decembr(is|ī))/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^mai/i,/^jūn/i,/^jūl/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:(0,m.A)({matchPatterns:{narrow:/^[spotc]/i,short:/^(sv|pi|o|t|c|pk|s)/i,abbreviated:/^(svētd\.|pirmd\.|otrd.\|trešd\.|ceturtd\.|piektd\.|sestd\.)/i,wide:/^(svētdien(a|ā)|pirmdien(a|ā)|otrdien(a|ā)|trešdien(a|ā)|ceturtdien(a|ā)|piektdien(a|ā)|sestdien(a|ā))/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^p/i,/^o/i,/^t/i,/^c/i,/^p/i,/^s/i],any:[/^sv/i,/^pi/i,/^o/i,/^t/i,/^c/i,/^p/i,/^se/i]},defaultParseWidth:"any"}),dayPeriod:(0,m.A)({matchPatterns:{narrow:/^(am|pm|pusn\.|pusd\.|rīt(s|ā)|dien(a|ā)|vakar(s|ā)|nakt(s|ī))/,abbreviated:/^(am|pm|pusn\.|pusd\.|rīt(s|ā)|pēcpusd\.|vakar(s|ā)|nakt(s|ī))/,wide:/^(am|pm|pusnakt(s|ī)|pusdienlaik(s|ā)|rīt(s|ā)|pēcpusdien(a|ā)|vakar(s|ā)|nakt(s|ī))/i},defaultMatchWidth:"wide",parsePatterns:{any:{am:/^am/i,pm:/^pm/i,midnight:/^pusn/i,noon:/^pusd/i,morning:/^r/i,afternoon:/^(d|pēc)/i,evening:/^v/i,night:/^n/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:4}},o=n},23815:(a,b,c)=>{c.d(b,{x:()=>e});var d=c(3843);function e(a,...b){let c=d.w.bind(null,a||b.find(a=>"object"==typeof a));return b.map(c)}},28153:(a,b,c)=>{function d(a){return(b,c={})=>{let d,e=c.width,f=e&&a.matchPatterns[e]||a.matchPatterns[a.defaultMatchWidth],g=b.match(f);if(!g)return null;let h=g[0],i=e&&a.parsePatterns[e]||a.parsePatterns[a.defaultParseWidth],j=Array.isArray(i)?function(a,b){for(let c=0;c<a.length;c++)if(b(a[c]))return c}(i,a=>a.test(h)):function(a,b){for(let c in a)if(Object.prototype.hasOwnProperty.call(a,c)&&b(a[c]))return c}(i,a=>a.test(h));return d=a.valueCallback?a.valueCallback(j):j,{value:d=c.valueCallback?c.valueCallback(d):d,rest:b.slice(h.length)}}}c.d(b,{A:()=>d})},39819:(a,b,c)=>{c.d(b,{_P:()=>f,my:()=>d,w4:()=>e});let d=6048e5,e=864e5,f=Symbol.for("constructDateFrom")},55991:(a,b,c)=>{c.d(b,{K:()=>d});function d(a){return(b,c={})=>{let d=b.match(a.matchPattern);if(!d)return null;let e=d[0],f=b.match(a.parsePattern);if(!f)return null;let g=a.valueCallback?a.valueCallback(f[0]):f[0];return{value:g=c.valueCallback?c.valueCallback(g):g,rest:b.slice(e.length)}}}},57241:(a,b,c)=>{c.d(b,{a:()=>e});var d=c(3843);function e(a,b){return(0,d.w)(b||a,a)}},80429:(a,b,c)=>{c.d(b,{o:()=>d});function d(a){return(b,c)=>{let d;if("formatting"===(c?.context?String(c.context):"standalone")&&a.formattingValues){let b=a.defaultFormattingWidth||a.defaultWidth,e=c?.width?String(c.width):b;d=a.formattingValues[e]||a.formattingValues[b]}else{let b=a.defaultWidth,e=c?.width?String(c.width):a.defaultWidth;d=a.values[e]||a.values[b]}return d[a.argumentCallback?a.argumentCallback(b):b]}}},84362:(a,b,c)=>{c.d(b,{R:()=>f});var d=c(23815),e=c(2871);function f(a,b,c){let[f,g]=(0,d.x)(c?.in,a,b);return+(0,e.k)(f,c)==+(0,e.k)(g,c)}}};