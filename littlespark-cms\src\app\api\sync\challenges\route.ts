import { NextRequest, NextResponse } from 'next/server';
import { getPayload } from 'payload';
import config from '@payload-config';
import { checkAdminAccess, logAdminAction } from '@/middleware/adminAuth';

const MAIN_APP_BASE_URL = process.env.MAIN_APP_BASE_URL || 'http://localhost:3000';

// POST /api/sync/challenges - Sync CMS challenges to main application
export async function POST(request: NextRequest) {
  try {
    // For now, skip auth check to test functionality
    // TODO: Implement proper auth after CMS is fully setup
    console.log('🔄 [CMS-SYNC] Challenge sync request received');

    console.log('🔄 [CMS-SYNC] Starting CMS to Main App challenge sync');

    const payload = await getPayload({ config });

    // Fetch all published challenges from CMS
    const cmsResponse = await payload.find({
      collection: 'challenges',
      where: {
        status: {
          equals: 'published'
        }
      },
      limit: 1000, // Adjust as needed
    });

    const cmsChallenge = cmsResponse.docs;
    console.log(`📊 [CMS-SYNC] Found ${cmsChallenge.length} published challenges in CMS`);

    let syncedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    // Sync each challenge to main application
    for (const challenge of cmsChallenge) {
      try {
        // Convert CMS challenge to main app format
        const mainAppChallenge = {
          cms_id: challenge.id, // Store CMS ID for tracking
          title: challenge.title,
          description: challenge.description,
          difficulty: challenge.difficulty,
          category: challenge.category, // Keep category for main app
          type: challenge.category, // Map category to type
          prompt: challenge.prompt || challenge.instructions || challenge.description,
          is_active: challenge.is_active !== false && challenge.status === 'published',
          created_by: 'cms',
          valid_until: null,
        };

        // Send to main application API
        console.log(`🔄 [CMS-SYNC] Sending challenge to main app:`, {
          cms_id: mainAppChallenge.cms_id,
          title: mainAppChallenge.title,
          category: mainAppChallenge.category
        });

        const response = await fetch(`${MAIN_APP_BASE_URL}/api/challenges/sync-from-cms`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.CMS_SYNC_TOKEN}`, // Secure token for sync
          },
          body: JSON.stringify(mainAppChallenge),
        });

        if (response.ok) {
          try {
            const responseData = await response.json();
            console.log(`✅ [CMS-SYNC] Synced challenge: ${challenge.title}`, responseData);
            syncedCount++;
          } catch (jsonError) {
            const responseText = await response.text();
            console.error(`❌ [CMS-SYNC] Invalid JSON response for ${challenge.title}:`, responseText);
            errorCount++;
          }
        } else {
          const errorText = await response.text();
          console.error(`❌ [CMS-SYNC] Failed to sync challenge ${challenge.title}:`, {
            status: response.status,
            statusText: response.statusText,
            error: errorText
          });
          errorCount++;
        }
      } catch (error) {
        console.error(`❌ [CMS-SYNC] Error syncing challenge ${challenge.title}:`, error);
        errorCount++;
      }
    }

    console.log(`🎉 [CMS-SYNC] Sync complete: ${syncedCount} synced, ${skippedCount} skipped, ${errorCount} errors`);

    return NextResponse.json({
      success: true,
      message: 'CMS challenges synced to main application',
      stats: {
        total: cmsChallenge.length,
        synced: syncedCount,
        skipped: skippedCount,
        errors: errorCount
      }
    });

  } catch (error) {
    console.error('❌ [CMS-SYNC] Error syncing CMS challenges:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to sync CMS challenges to main application',
        details: error.message
      },
      { status: 500 }
    );
  }
}

// GET /api/sync/challenges - Check sync status
export async function GET(request: NextRequest) {
  try {
    // For now, skip auth check to test functionality
    console.log('📊 [CMS-SYNC] Getting challenge sync stats');

    const payload = await getPayload({ config });

    // Count CMS challenges
    const cmsCount = await payload.count({
      collection: 'challenges',
      where: {
        status: {
          equals: 'published'
        }
      }
    });

    // Get main app challenge count (challenges that came from CMS)
    let mainAppCount = 0;
    try {
      const mainAppResponse = await fetch(`${MAIN_APP_BASE_URL}/api/challenges/debug`, {
        headers: {
          'Authorization': `Bearer ${process.env.CMS_SYNC_TOKEN}`,
        },
      });

      if (mainAppResponse.ok) {
        const data = await mainAppResponse.json();
        mainAppCount = data.stats?.cms || 0;
        console.log('📊 [CMS-SYNC] Main app stats:', data.stats);
      } else {
        console.log('⚠️ [CMS-SYNC] Main app response not OK:', mainAppResponse.status);
      }
    } catch (error) {
      console.log('⚠️ [CMS-SYNC] Could not fetch main app count:', error instanceof Error ? error.message : 'Unknown error');
    }

    return NextResponse.json({
      success: true,
      stats: {
        cmsChallenge: cmsCount.totalDocs,
        mainAppCmsChallenge: mainAppCount,
        mainAppTotalChallenge: 0,
        lastSync: 'Not implemented yet' // Could add a sync log table
      }
    });

  } catch (error) {
    console.error('Error checking sync status:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to check sync status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
