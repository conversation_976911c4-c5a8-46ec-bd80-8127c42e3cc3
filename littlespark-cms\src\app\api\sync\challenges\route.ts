import { NextRequest, NextResponse } from 'next/server';
import { getPayload } from 'payload';
import config from '@payload-config';
import { checkAdminAccess, logAdminAction } from '@/middleware/adminAuth';

const MAIN_APP_BASE_URL = process.env.MAIN_APP_BASE_URL || 'http://localhost:3000';

// POST /api/sync/challenges - Sync CMS challenges to main application
export async function POST(request: NextRequest) {
  try {
    // Check admin access
    const authCheck = await checkAdminAccess(request);
    if (!authCheck.isAdmin) {
      return NextResponse.json(
        { success: false, error: authCheck.error },
        { status: 403 }
      );
    }

    console.log('🔄 [CMS-SYNC] Starting CMS to Main App challenge sync');

    // Log admin action for audit trail
    logAdminAction(authCheck.user, 'SYNC_CHALLENGES_TO_MAIN_APP', {
      timestamp: new Date().toISOString()
    });

    const payload = await getPayload({ config });

    // Fetch all published challenges from CMS
    const cmsResponse = await payload.find({
      collection: 'challenges',
      where: {
        status: {
          equals: 'published'
        }
      },
      limit: 1000, // Adjust as needed
    });

    const cmsChallenge = cmsResponse.docs;
    console.log(`📊 [CMS-SYNC] Found ${cmsChallenge.length} published challenges in CMS`);

    let syncedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    // Sync each challenge to main application
    for (const challenge of cmsChallenge) {
      try {
        // Convert CMS challenge to main app format
        const mainAppChallenge = {
          id: `cms-${challenge.id}`, // Prefix to avoid conflicts
          title: challenge.title,
          description: challenge.description,
          difficulty: challenge.difficulty,
          type: challenge.category, // Map category to type
          prompt: challenge.prompt || challenge.instructions || challenge.description,
          is_active: challenge.is_active !== false && challenge.status === 'published',
          created_by: 'cms',
          valid_until: null,
        };

        // Send to main application API
        const response = await fetch(`${MAIN_APP_BASE_URL}/api/challenges/sync-from-cms`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.CMS_SYNC_TOKEN}`, // Secure token for sync
          },
          body: JSON.stringify(mainAppChallenge),
        });

        if (response.ok) {
          console.log(`✅ [CMS-SYNC] Synced challenge: ${challenge.title}`);
          syncedCount++;
        } else {
          console.error(`❌ [CMS-SYNC] Failed to sync challenge ${challenge.title}:`, await response.text());
          errorCount++;
        }
      } catch (error) {
        console.error(`❌ [CMS-SYNC] Error syncing challenge ${challenge.title}:`, error);
        errorCount++;
      }
    }

    console.log(`🎉 [CMS-SYNC] Sync complete: ${syncedCount} synced, ${skippedCount} skipped, ${errorCount} errors`);

    return NextResponse.json({
      success: true,
      message: 'CMS challenges synced to main application',
      stats: {
        total: cmsChallenge.length,
        synced: syncedCount,
        skipped: skippedCount,
        errors: errorCount
      }
    });

  } catch (error) {
    console.error('❌ [CMS-SYNC] Error syncing CMS challenges:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to sync CMS challenges to main application',
        details: error.message
      },
      { status: 500 }
    );
  }
}

// GET /api/sync/challenges - Check sync status
export async function GET(request: NextRequest) {
  try {
    // Check admin access
    const authCheck = await checkAdminAccess(request);
    if (!authCheck.isAdmin) {
      return NextResponse.json(
        { success: false, error: authCheck.error },
        { status: 403 }
      );
    }

    const payload = await getPayload({ config });

    // Count CMS challenges
    const cmsCount = await payload.count({
      collection: 'challenges',
      where: {
        status: {
          equals: 'published'
        }
      }
    });

    // Get sync status from main app
    let mainAppStats = { cmsChallenge: 0, totalChallenge: 0 };
    try {
      const response = await fetch(`${MAIN_APP_BASE_URL}/api/challenges/sync-status`, {
        headers: {
          'Authorization': `Bearer ${process.env.CMS_SYNC_TOKEN}`,
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        mainAppStats = data.stats || mainAppStats;
      }
    } catch (error) {
      console.warn('Could not fetch main app sync status:', error);
    }

    return NextResponse.json({
      success: true,
      stats: {
        cmsChallenge: cmsCount.totalDocs,
        mainAppCmsChallenge: mainAppStats.cmsChallenge,
        mainAppTotalChallenge: mainAppStats.totalChallenge,
        lastSync: 'Not implemented yet' // Could add a sync log table
      }
    });

  } catch (error) {
    console.error('Error checking sync status:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to check sync status',
        details: error.message
      },
      { status: 500 }
    );
  }
}
