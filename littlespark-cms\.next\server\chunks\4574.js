"use strict";exports.id=4574,exports.ids=[4574],exports.modules={64574:(e,n,r)=>{r.d(n,{fromIni:()=>S});var t=r(77648),i=r(53438),o=r(6884);let s=(e,n,t)=>{let o={EcsContainer:async e=>{let{fromHttp:n}=await r.e(7674).then(r.bind(r,87674)),{fromContainerMetadata:o}=await r.e(3352).then(r.bind(r,73352));return t?.debug("@aws-sdk/credential-provider-ini - credential_source is EcsContainer"),async()=>(0,i.cy)(n(e??{}),o(e))().then(a)},Ec2InstanceMetadata:async e=>{t?.debug("@aws-sdk/credential-provider-ini - credential_source is Ec2InstanceMetadata");let{fromInstanceMetadata:n}=await r.e(3352).then(r.bind(r,73352));return async()=>n(e)().then(a)},Environment:async e=>{t?.debug("@aws-sdk/credential-provider-ini - credential_source is Environment");let{fromEnv:n}=await Promise.resolve().then(r.bind(r,6401));return async()=>n(e)().then(a)}};if(e in o)return o[e];throw new i.C1(`Unsupported credential source in profile ${n}. Got ${e}, expected EcsContainer or Ec2InstanceMetadata or Environment.`,{logger:t})},a=e=>(0,o.g)(e,"CREDENTIALS_PROFILE_NAMED_PROVIDER","p"),l=(e,{profile:n="default",logger:r}={})=>!!e&&"object"==typeof e&&"string"==typeof e.role_arn&&["undefined","string"].indexOf(typeof e.role_session_name)>-1&&["undefined","string"].indexOf(typeof e.external_id)>-1&&["undefined","string"].indexOf(typeof e.mfa_serial)>-1&&(d(e,{profile:n,logger:r})||c(e,{profile:n,logger:r})),d=(e,{profile:n,logger:r})=>{let t="string"==typeof e.source_profile&&void 0===e.credential_source;return t&&r?.debug?.(`    ${n} isAssumeRoleWithSourceProfile source_profile=${e.source_profile}`),t},c=(e,{profile:n,logger:r})=>{let t="string"==typeof e.credential_source&&void 0===e.source_profile;return t&&r?.debug?.(`    ${n} isCredentialSourceProfile credential_source=${e.credential_source}`),t},g=async(e,n,a,l={})=>{a.logger?.debug("@aws-sdk/credential-provider-ini - resolveAssumeRoleCredentials (STS)");let d=n[e],{source_profile:c,region:g}=d;if(!a.roleAssumer){let{getDefaultRoleAssumer:e}=await r.e(5452).then(r.bind(r,45452));a.roleAssumer=e({...a.clientConfig,credentialProviderLogger:a.logger,parentClientConfig:{...a?.parentClientConfig,region:g??a?.parentClientConfig?.region}},a.clientPlugins)}if(c&&c in l)throw new i.C1(`Detected a cycle attempting to resolve credentials for profile ${(0,t.Bz)(a)}. Profiles visited: `+Object.keys(l).join(", "),{logger:a.logger});a.logger?.debug(`@aws-sdk/credential-provider-ini - finding credential resolver using ${c?`source_profile=[${c}]`:`profile=[${e}]`}`);let f=c?b(c,n,a,{...l,[c]:!0},_(n[c]??{})):(await s(d.credential_source,e,a.logger)(a))();if(_(d))return f.then(e=>(0,o.g)(e,"CREDENTIALS_PROFILE_SOURCE_PROFILE","o"));{let n={RoleArn:d.role_arn,RoleSessionName:d.role_session_name||`aws-sdk-js-${Date.now()}`,ExternalId:d.external_id,DurationSeconds:parseInt(d.duration_seconds||"3600",10)},{mfa_serial:r}=d;if(r){if(!a.mfaCodeProvider)throw new i.C1(`Profile ${e} requires multi-factor authentication, but no MFA code callback was provided.`,{logger:a.logger,tryNextLink:!1});n.SerialNumber=r,n.TokenCode=await a.mfaCodeProvider(r)}let t=await f;return a.roleAssumer(t,n).then(e=>(0,o.g)(e,"CREDENTIALS_PROFILE_SOURCE_PROFILE","o"))}},_=e=>!e.role_arn&&!!e.credential_source,f=e=>!!e&&"object"==typeof e&&"string"==typeof e.credential_process,u=async(e,n)=>r.e(416).then(r.bind(r,30416)).then(({fromProcess:r})=>r({...e,profile:n})().then(e=>(0,o.g)(e,"CREDENTIALS_PROFILE_PROCESS","v"))),p=async(e,n,t={})=>{let{fromSSO:i}=await r.e(6449).then(r.bind(r,86449));return i({profile:e,logger:t.logger,parentClientConfig:t.parentClientConfig,clientConfig:t.clientConfig})().then(e=>n.sso_session?(0,o.g)(e,"CREDENTIALS_PROFILE_SSO","r"):(0,o.g)(e,"CREDENTIALS_PROFILE_SSO_LEGACY","t"))},y=e=>e&&("string"==typeof e.sso_start_url||"string"==typeof e.sso_account_id||"string"==typeof e.sso_session||"string"==typeof e.sso_region||"string"==typeof e.sso_role_name),C=e=>!!e&&"object"==typeof e&&"string"==typeof e.aws_access_key_id&&"string"==typeof e.aws_secret_access_key&&["undefined","string"].indexOf(typeof e.aws_session_token)>-1&&["undefined","string"].indexOf(typeof e.aws_account_id)>-1,E=async(e,n)=>{n?.logger?.debug("@aws-sdk/credential-provider-ini - resolveStaticCredentials");let r={accessKeyId:e.aws_access_key_id,secretAccessKey:e.aws_secret_access_key,sessionToken:e.aws_session_token,...e.aws_credential_scope&&{credentialScope:e.aws_credential_scope},...e.aws_account_id&&{accountId:e.aws_account_id}};return(0,o.g)(r,"CREDENTIALS_PROFILE","n")},w=e=>!!e&&"object"==typeof e&&"string"==typeof e.web_identity_token_file&&"string"==typeof e.role_arn&&["undefined","string"].indexOf(typeof e.role_session_name)>-1,I=async(e,n)=>r.e(6199).then(r.bind(r,16199)).then(({fromTokenFile:r})=>r({webIdentityTokenFile:e.web_identity_token_file,roleArn:e.role_arn,roleSessionName:e.role_session_name,roleAssumerWithWebIdentity:n.roleAssumerWithWebIdentity,logger:n.logger,parentClientConfig:n.parentClientConfig})().then(e=>(0,o.g)(e,"CREDENTIALS_PROFILE_STS_WEB_ID_TOKEN","q"))),b=async(e,n,r,t={},o=!1)=>{let s=n[e];if(Object.keys(t).length>0&&C(s))return E(s,r);if(o||l(s,{profile:e,logger:r.logger}))return g(e,n,r,t);if(C(s))return E(s,r);if(w(s))return I(s,r);if(f(s))return u(r,e);if(y(s))return await p(e,s,r);throw new i.C1(`Could not resolve credentials using profile: [${e}] in configuration/credentials file(s).`,{logger:r.logger})},S=(e={})=>async({callerClientConfig:n}={})=>{let r={...e,parentClientConfig:{...n,...e.parentClientConfig}};r.logger?.debug("@aws-sdk/credential-provider-ini - fromIni");let i=await (0,t.YU)(r);return b((0,t.Bz)({profile:e.profile??n?.profile}),i,r)}}};