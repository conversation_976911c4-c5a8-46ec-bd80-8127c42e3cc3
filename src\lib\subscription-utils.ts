import { prisma } from '@/lib/prisma';

export interface SubscriptionStatus {
  subscription_status: string | null;
  plan_name: string | null;
  subscription_start: Date | null;
  subscription_end: Date | null;
  billing_cycle: string | null;
  trial_start: Date | null;
  trial_end: Date | null;
  plan_id: string | null;
  trial_used: boolean;
}

export interface AccessControlResult {
  hasAccess: boolean;
  reason?: string;
  subscriptionStatus?: SubscriptionStatus;
  isTrialExpired?: boolean;
  daysUntilExpiry?: number;
  isNewUserTrial?: boolean;
}

/**
 * Check if a trial period has expired
 */
export function isTrialExpired(trialEnd: Date | null): boolean {
  if (!trialEnd) return false;
  return new Date() > trialEnd;
}

/**
 * Check if a subscription has expired
 */
export function isSubscriptionExpired(subscriptionEnd: Date | null): boolean {
  if (!subscriptionEnd) return false;
  return new Date() > subscriptionEnd;
}

/**
 * Calculate days until expiry (trial or subscription)
 */
export function getDaysUntilExpiry(endDate: Date | null): number | null {
  if (!endDate) return null;
  const now = new Date();
  const diffTime = endDate.getTime() - now.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
}

/**
 * Comprehensive access control check for a user
 */
export async function checkUserAccess(userId: string): Promise<AccessControlResult> {
  try {
    const profile = await prisma.profile.findUnique({
      where: { id: userId },
      select: {
        subscription_status: true,
        plan_name: true,
        plan_id: true,
        subscription_start: true,
        subscription_end: true,
        billing_cycle: true,
        trial_start: true,
        trial_end: true,
        trial_used: true,
      },
    });

    if (!profile) {
      return {
        hasAccess: false,
        reason: 'User profile not found'
      };
    }

    const subscriptionStatus = profile as SubscriptionStatus;

    // NEW USER TRIAL LOGIC: If user has no subscription status and hasn't used trial, grant trial access
    if (!profile.subscription_status && !profile.trial_used) {
      console.log(`Granting automatic trial access to new user: ${userId}`);
      return {
        hasAccess: true,
        reason: 'New user trial access',
        subscriptionStatus,
        isNewUserTrial: true
      };
    }

    // Check if user has an active subscription
    const activeStatuses = ['active', 'trialing', 'cancel_at_period_end', 'incomplete'];
    const hasActiveSubscription = activeStatuses.includes(profile.subscription_status ?? '');

    if (!hasActiveSubscription) {
      // If user has used trial but no active subscription, deny access
      if (profile.trial_used) {
        return {
          hasAccess: false,
          reason: 'Trial expired and no active subscription',
          subscriptionStatus,
          isTrialExpired: true
        };
      }

      return {
        hasAccess: false,
        reason: 'No active subscription',
        subscriptionStatus
      };
    }

    // Special handling for trialing status
    if (profile.subscription_status === 'trialing') {
      const trialExpired = isTrialExpired(profile.trial_end);
      
      if (trialExpired) {
        // Trial has expired - check if there are successful payments
        const hasSuccessfulPayments = await prisma.payment.findFirst({
          where: {
            profile_id: userId,
            status: 'succeeded',
            created_at: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Within last 30 days
            }
          }
        });

        if (!hasSuccessfulPayments) {
          return {
            hasAccess: false,
            reason: 'Trial expired and no payment on file',
            subscriptionStatus,
            isTrialExpired: true
          };
        }
        
        // Has payments but status is still trialing - this indicates a sync issue
        console.warn(`User ${userId} has trialing status but trial expired and payments exist - status sync needed`);
      }

      const daysUntilExpiry = getDaysUntilExpiry(profile.trial_end);
      return {
        hasAccess: true,
        subscriptionStatus,
        isTrialExpired: trialExpired,
        daysUntilExpiry: daysUntilExpiry ?? undefined
      };
    }

    // For active subscriptions, check if subscription has expired
    if (profile.subscription_status === 'active') {
      const subscriptionExpired = isSubscriptionExpired(profile.subscription_end);
      
      if (subscriptionExpired) {
        return {
          hasAccess: false,
          reason: 'Subscription expired',
          subscriptionStatus
        };
      }

      const daysUntilExpiry = getDaysUntilExpiry(profile.subscription_end);
      return {
        hasAccess: true,
        subscriptionStatus,
        daysUntilExpiry: daysUntilExpiry ?? undefined
      };
    }

    // For cancel_at_period_end, allow access until period ends
    if (profile.subscription_status === 'cancel_at_period_end') {
      const periodEnd = profile.trial_end || profile.subscription_end;
      const periodExpired = isSubscriptionExpired(periodEnd);
      
      if (periodExpired) {
        return {
          hasAccess: false,
          reason: 'Cancelled subscription period ended',
          subscriptionStatus
        };
      }

      const daysUntilExpiry = getDaysUntilExpiry(periodEnd);
      return {
        hasAccess: true,
        subscriptionStatus,
        daysUntilExpiry: daysUntilExpiry ?? undefined
      };
    }

    // For incomplete status, check if there are successful payments
    if (profile.subscription_status === 'incomplete') {
      const hasSuccessfulPayments = await prisma.payment.findFirst({
        where: {
          profile_id: userId,
          status: 'succeeded',
          created_at: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Within last 30 days
          }
        }
      });

      if (hasSuccessfulPayments) {
        // Has payments - allow access
        return {
          hasAccess: true,
          subscriptionStatus
        };
      } else {
        // No payments - deny access
        return {
          hasAccess: false,
          reason: 'Payment required to complete subscription',
          subscriptionStatus
        };
      }
    }

    // Default to allowing access for other statuses
    return {
      hasAccess: true,
      subscriptionStatus
    };

  } catch (error) {
    console.error('Error checking user access:', error);
    return {
      hasAccess: false,
      reason: 'Error checking subscription status'
    };
  }
}

/**
 * Simple check if user has active subscription (for backwards compatibility)
 */
export async function hasActiveSubscription(userId: string): Promise<boolean> {
  const accessResult = await checkUserAccess(userId);
  return accessResult.hasAccess;
}
