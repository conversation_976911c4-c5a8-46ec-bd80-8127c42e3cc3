// Sync Management JavaScript
(function() {
  'use strict';

  // State management
  let isLoading = false;
  let stats = null;

  // DOM elements
  const elements = {
    cmsChallengersCount: document.getElementById('cms-challenges-count'),
    syncedChallengesCount: document.getElementById('synced-challenges-count'),
    cmsUsersCount: document.getElementById('cms-users-count'),
    syncedUsersCount: document.getElementById('synced-users-count'),
    availableUserChallengesCount: document.getElementById('available-user-challenges-count'),
    importedChallengesCount: document.getElementById('imported-challenges-count'),
    syncChallengesBtn: document.getElementById('sync-challenges-btn'),
    syncUsersBtn: document.getElementById('sync-users-btn'),
    importChallengesBtn: document.getElementById('import-challenges-btn'),
    refreshStatsBtn: document.getElementById('refresh-stats-btn'),
    addTestDataBtn: document.getElementById('add-test-data-btn'),
    challengeSyncResult: document.getElementById('challenge-sync-result'),
    userSyncResult: document.getElementById('user-sync-result'),
    importSyncResult: document.getElementById('import-sync-result'),
    errorDisplay: document.getElementById('error-display'),
    errorMessage: document.getElementById('error-message')
  };

  // Utility functions
  function showError(message) {
    if (elements.errorMessage && elements.errorDisplay) {
      elements.errorMessage.textContent = message;
      elements.errorDisplay.style.display = 'block';
      setTimeout(() => {
        elements.errorDisplay.style.display = 'none';
      }, 5000);
    } else {
      console.error('Error:', message);
      alert('Error: ' + message);
    }
  }

  function showSuccess(message) {
    console.log('Success:', message);
    alert('Success: ' + message);
  }

  function setLoading(loading) {
    isLoading = loading;
    const buttons = [elements.syncChallengesBtn, elements.syncUsersBtn, elements.importChallengesBtn, elements.refreshStatsBtn, elements.addTestDataBtn];

    buttons.forEach(btn => {
      if (btn) {
        btn.disabled = loading;
        btn.style.opacity = loading ? '0.6' : '1';
        btn.style.cursor = loading ? 'not-allowed' : 'pointer';
      }
    });

    if (loading) {
      if (elements.syncChallengesBtn) {
        elements.syncChallengesBtn.textContent = '🔄 Syncing Challenges...';
      }
      if (elements.addTestDataBtn) {
        elements.addTestDataBtn.textContent = '🧪 Adding Sample Data...';
      }
    } else {
      if (elements.syncChallengesBtn) {
        elements.syncChallengesBtn.textContent = '🔄 Sync Challenges to Main App';
      }
      if (elements.addTestDataBtn) {
        elements.addTestDataBtn.textContent = '🧪 Add Sample Challenges';
      }
    }
  }

  // Fetch sync statistics
  async function fetchStats() {
    try {
      console.log('Fetching sync stats...');
      
      const response = await fetch('/api/sync/challenges', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch sync statistics: ${response.status}`);
      }

      const data = await response.json();
      console.log('Fetched stats:', data);

      if (data.success) {
        stats = {
          challenges: data.stats,
          users: { cmsUsers: 0, mainAppCmsUsers: 0 }, // Placeholder
          import: { availableForSync: 0, alreadySynced: 0 } // Placeholder
        };
        updateStatsDisplay();
      } else {
        throw new Error('Invalid response from sync API');
      }
    } catch (error) {
      console.error('Error fetching sync stats:', error);
      showError(error.message);
    }
  }

  // Update stats display
  function updateStatsDisplay() {
    if (!stats) return;

    console.log('Updating stats display with:', stats);

    if (elements.cmsChallengersCount) {
      elements.cmsChallengersCount.textContent = stats.challenges.cmsChallenge || '0';
    }
    if (elements.syncedChallengesCount) {
      elements.syncedChallengesCount.textContent = stats.challenges.mainAppCmsChallenge || '0';
    }
    if (elements.cmsUsersCount) {
      elements.cmsUsersCount.textContent = stats.users.cmsUsers || '0';
    }
    if (elements.syncedUsersCount) {
      elements.syncedUsersCount.textContent = stats.users.mainAppCmsUsers || '0';
    }
    if (elements.availableUserChallengesCount) {
      elements.availableUserChallengesCount.textContent = stats.import.availableForSync || '0';
    }
    if (elements.importedChallengesCount) {
      elements.importedChallengesCount.textContent = stats.import.alreadySynced || '0';
    }
  }

  // Sync challenges
  async function syncChallenges() {
    if (isLoading) return;
    
    setLoading(true);
    
    try {
      console.log('Starting challenge sync...');
      
      const response = await fetch('/api/sync/challenges', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      console.log('Sync response:', data);

      if (data.success) {
        showSuccess(`Challenge sync completed! ${data.stats?.synced || 0} challenges synced successfully.`);
        
        if (elements.challengeSyncResult) {
          elements.challengeSyncResult.innerHTML = `
            <strong>Last Sync:</strong> 
            Total: ${data.stats?.total || 0}, 
            Synced: <span style="color: #16a34a;">${data.stats?.synced || 0}</span>
            ${data.stats?.errors > 0 ? `, Errors: <span style="color: #dc2626;">${data.stats.errors}</span>` : ''}
          `;
        }
        
        // Refresh stats
        await fetchStats();
      } else {
        throw new Error(data.error || 'Challenge sync failed');
      }
    } catch (error) {
      console.error('Error syncing challenges:', error);
      showError(error.message);
    } finally {
      setLoading(false);
    }
  }

  // Add test data
  async function addTestData() {
    if (isLoading) return;

    setLoading(true);

    try {
      console.log('Adding sample challenges...');

      const response = await fetch('/api/test-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      console.log('Test data response:', data);

      if (data.success) {
        showSuccess(`Sample challenges added! ${data.stats?.created || 0} challenges created, ${data.stats?.skipped || 0} skipped.`);

        // Refresh stats
        await fetchStats();
      } else {
        throw new Error(data.error || 'Failed to add sample challenges');
      }
    } catch (error) {
      console.error('Error adding test data:', error);
      showError(error.message);
    } finally {
      setLoading(false);
    }
  }

  // Event listeners
  function setupEventListeners() {
    console.log('Setting up event listeners...');
    
    if (elements.syncChallengesBtn) {
      elements.syncChallengesBtn.addEventListener('click', syncChallenges);
      console.log('Added click listener to sync challenges button');
    } else {
      console.warn('Sync challenges button not found');
    }

    if (elements.refreshStatsBtn) {
      elements.refreshStatsBtn.addEventListener('click', fetchStats);
      console.log('Added click listener to refresh stats button');
    } else {
      console.warn('Refresh stats button not found');
    }

    if (elements.addTestDataBtn) {
      elements.addTestDataBtn.addEventListener('click', addTestData);
      console.log('Added click listener to add test data button');
    } else {
      console.warn('Add test data button not found');
    }
  }

  // Initialize
  function init() {
    console.log('Initializing Sync Management...');
    console.log('Available elements:', Object.keys(elements).filter(key => elements[key] !== null));
    setupEventListeners();
    fetchStats();
  }

  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }

})();
