import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// Helper function to verify sync token
function verifySyncToken(req: NextRequest) {
  const authHeader = req.headers.get('authorization');
  const expectedToken = process.env.CMS_SYNC_TOKEN;
  
  if (!authHeader || !authHeader.startsWith('Bearer ') || !expectedToken) {
    return false;
  }
  
  const token = authHeader.substring(7);
  return token === expectedToken;
}

// GET /api/challenges/sync-status - Get sync status for CMS
export async function GET(request: NextRequest) {
  try {
    // Verify sync token
    if (!verifySyncToken(request)) {
      return NextResponse.json(
        { error: 'Unauthorized sync request' },
        { status: 401 }
      );
    }

    // Count CMS challenges in database
    const cmsCount = await prisma.challenge.count({
      where: {
        created_by: 'cms'
      }
    });

    // Count total challenges
    const totalCount = await prisma.challenge.count();

    // Count user-created challenges that could be synced back to CMS
    const userCreatedCount = await prisma.challenge.count({
      where: {
        created_by: {
          not: 'cms'
        }
      }
    });

    return NextResponse.json({
      success: true,
      stats: {
        cmsChallenge: cmsCount,
        totalChallenge: totalCount,
        userCreatedChallenge: userCreatedCount,
        lastSync: 'Not implemented yet' // Could add a sync log table
      }
    });

  } catch (error) {
    console.error('Error checking sync status:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to check sync status',
        details: error.message
      },
      { status: 500 }
    );
  }
}
