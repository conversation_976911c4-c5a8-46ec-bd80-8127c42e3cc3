"use strict";exports.id=9714,exports.ids=[9714],exports.modules={78526:(e,t,n)=>{n.d(t,{k:()=>a});function a(e){return (t={})=>{let n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}},94940:(e,t,n)=>{n.d(t,{o:()=>a});function a(e){return(t,n)=>{let a;if("formatting"===(n?.context?String(n.context):"standalone")&&e.formattingValues){let t=e.defaultFormattingWidth||e.defaultWidth,r=n?.width?String(n.width):t;a=e.formattingValues[r]||e.formattingValues[t]}else{let t=e.defaultWidth,r=n?.width?String(n.width):e.defaultWidth;a=e.values[r]||e.values[t]}return a[e.argumentCallback?e.argumentCallback(t):t]}}},44754:(e,t,n)=>{function a(e){return(t,n={})=>{let a;let r=n.width,o=r&&e.matchPatterns[r]||e.matchPatterns[e.defaultMatchWidth],u=t.match(o);if(!u)return null;let i=u[0],d=r&&e.parsePatterns[r]||e.parsePatterns[e.defaultParseWidth],l=Array.isArray(d)?function(e,t){for(let n=0;n<e.length;n++)if(t(e[n]))return n}(d,e=>e.test(i)):function(e,t){for(let n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t(e[n]))return n}(d,e=>e.test(i));return a=e.valueCallback?e.valueCallback(l):l,{value:a=n.valueCallback?n.valueCallback(a):a,rest:t.slice(i.length)}}}n.d(t,{A:()=>a})},71886:(e,t,n)=>{n.d(t,{K:()=>a});function a(e){return(t,n={})=>{let a=t.match(e.matchPattern);if(!a)return null;let r=a[0],o=t.match(e.parsePattern);if(!o)return null;let u=e.valueCallback?e.valueCallback(o[0]):o[0];return{value:u=n.valueCallback?n.valueCallback(u):u,rest:t.slice(r.length)}}}},39714:(e,t,n)=>{n.r(t),n.d(t,{cs:()=>s,default:()=>c});let a={lessThanXSeconds:{one:{regular:"m\xe9ně než 1 sekunda",past:"před m\xe9ně než 1 sekundou",future:"za m\xe9ně než 1 sekundu"},few:{regular:"m\xe9ně než {{count}} sekundy",past:"před m\xe9ně než {{count}} sekundami",future:"za m\xe9ně než {{count}} sekundy"},many:{regular:"m\xe9ně než {{count}} sekund",past:"před m\xe9ně než {{count}} sekundami",future:"za m\xe9ně než {{count}} sekund"}},xSeconds:{one:{regular:"1 sekunda",past:"před 1 sekundou",future:"za 1 sekundu"},few:{regular:"{{count}} sekundy",past:"před {{count}} sekundami",future:"za {{count}} sekundy"},many:{regular:"{{count}} sekund",past:"před {{count}} sekundami",future:"za {{count}} sekund"}},halfAMinute:{type:"other",other:{regular:"půl minuty",past:"před půl minutou",future:"za půl minuty"}},lessThanXMinutes:{one:{regular:"m\xe9ně než 1 minuta",past:"před m\xe9ně než 1 minutou",future:"za m\xe9ně než 1 minutu"},few:{regular:"m\xe9ně než {{count}} minuty",past:"před m\xe9ně než {{count}} minutami",future:"za m\xe9ně než {{count}} minuty"},many:{regular:"m\xe9ně než {{count}} minut",past:"před m\xe9ně než {{count}} minutami",future:"za m\xe9ně než {{count}} minut"}},xMinutes:{one:{regular:"1 minuta",past:"před 1 minutou",future:"za 1 minutu"},few:{regular:"{{count}} minuty",past:"před {{count}} minutami",future:"za {{count}} minuty"},many:{regular:"{{count}} minut",past:"před {{count}} minutami",future:"za {{count}} minut"}},aboutXHours:{one:{regular:"přibližně 1 hodina",past:"přibližně před 1 hodinou",future:"přibližně za 1 hodinu"},few:{regular:"přibližně {{count}} hodiny",past:"přibližně před {{count}} hodinami",future:"přibližně za {{count}} hodiny"},many:{regular:"přibližně {{count}} hodin",past:"přibližně před {{count}} hodinami",future:"přibližně za {{count}} hodin"}},xHours:{one:{regular:"1 hodina",past:"před 1 hodinou",future:"za 1 hodinu"},few:{regular:"{{count}} hodiny",past:"před {{count}} hodinami",future:"za {{count}} hodiny"},many:{regular:"{{count}} hodin",past:"před {{count}} hodinami",future:"za {{count}} hodin"}},xDays:{one:{regular:"1 den",past:"před 1 dnem",future:"za 1 den"},few:{regular:"{{count}} dny",past:"před {{count}} dny",future:"za {{count}} dny"},many:{regular:"{{count}} dn\xed",past:"před {{count}} dny",future:"za {{count}} dn\xed"}},aboutXWeeks:{one:{regular:"přibližně 1 t\xfdden",past:"přibližně před 1 t\xfddnem",future:"přibližně za 1 t\xfdden"},few:{regular:"přibližně {{count}} t\xfddny",past:"přibližně před {{count}} t\xfddny",future:"přibližně za {{count}} t\xfddny"},many:{regular:"přibližně {{count}} t\xfddnů",past:"přibližně před {{count}} t\xfddny",future:"přibližně za {{count}} t\xfddnů"}},xWeeks:{one:{regular:"1 t\xfdden",past:"před 1 t\xfddnem",future:"za 1 t\xfdden"},few:{regular:"{{count}} t\xfddny",past:"před {{count}} t\xfddny",future:"za {{count}} t\xfddny"},many:{regular:"{{count}} t\xfddnů",past:"před {{count}} t\xfddny",future:"za {{count}} t\xfddnů"}},aboutXMonths:{one:{regular:"přibližně 1 měs\xedc",past:"přibližně před 1 měs\xedcem",future:"přibližně za 1 měs\xedc"},few:{regular:"přibližně {{count}} měs\xedce",past:"přibližně před {{count}} měs\xedci",future:"přibližně za {{count}} měs\xedce"},many:{regular:"přibližně {{count}} měs\xedců",past:"přibližně před {{count}} měs\xedci",future:"přibližně za {{count}} měs\xedců"}},xMonths:{one:{regular:"1 měs\xedc",past:"před 1 měs\xedcem",future:"za 1 měs\xedc"},few:{regular:"{{count}} měs\xedce",past:"před {{count}} měs\xedci",future:"za {{count}} měs\xedce"},many:{regular:"{{count}} měs\xedců",past:"před {{count}} měs\xedci",future:"za {{count}} měs\xedců"}},aboutXYears:{one:{regular:"přibližně 1 rok",past:"přibližně před 1 rokem",future:"přibližně za 1 rok"},few:{regular:"přibližně {{count}} roky",past:"přibližně před {{count}} roky",future:"přibližně za {{count}} roky"},many:{regular:"přibližně {{count}} roků",past:"přibližně před {{count}} roky",future:"přibližně za {{count}} roků"}},xYears:{one:{regular:"1 rok",past:"před 1 rokem",future:"za 1 rok"},few:{regular:"{{count}} roky",past:"před {{count}} roky",future:"za {{count}} roky"},many:{regular:"{{count}} roků",past:"před {{count}} roky",future:"za {{count}} roků"}},overXYears:{one:{regular:"v\xedce než 1 rok",past:"před v\xedce než 1 rokem",future:"za v\xedce než 1 rok"},few:{regular:"v\xedce než {{count}} roky",past:"před v\xedce než {{count}} roky",future:"za v\xedce než {{count}} roky"},many:{regular:"v\xedce než {{count}} roků",past:"před v\xedce než {{count}} roky",future:"za v\xedce než {{count}} roků"}},almostXYears:{one:{regular:"skoro 1 rok",past:"skoro před 1 rokem",future:"skoro za 1 rok"},few:{regular:"skoro {{count}} roky",past:"skoro před {{count}} roky",future:"skoro za {{count}} roky"},many:{regular:"skoro {{count}} roků",past:"skoro před {{count}} roky",future:"skoro za {{count}} roků"}}};var r=n(78526);let o={date:(0,r.k)({formats:{full:"EEEE, d. MMMM yyyy",long:"d. MMMM yyyy",medium:"d. M. yyyy",short:"dd.MM.yyyy"},defaultWidth:"full"}),time:(0,r.k)({formats:{full:"H:mm:ss zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},defaultWidth:"full"}),dateTime:(0,r.k)({formats:{full:"{{date}} 'v' {{time}}",long:"{{date}} 'v' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},u=["neděli","ponděl\xed","\xfater\xfd","středu","čtvrtek","p\xe1tek","sobotu"],i={lastWeek:"'posledn\xed' eeee 've' p",yesterday:"'včera v' p",today:"'dnes v' p",tomorrow:"'z\xedtra v' p",nextWeek:e=>"'v "+u[e.getDay()]+" o' p",other:"P"};var d=n(94940);let l={ordinalNumber:(e,t)=>Number(e)+".",era:(0,d.o)({values:{narrow:["př. n. l.","n. l."],abbreviated:["př. n. l.","n. l."],wide:["před naš\xedm letopočtem","našeho letopočtu"]},defaultWidth:"wide"}),quarter:(0,d.o)({values:{narrow:["1","2","3","4"],abbreviated:["1. čtvrtlet\xed","2. čtvrtlet\xed","3. čtvrtlet\xed","4. čtvrtlet\xed"],wide:["1. čtvrtlet\xed","2. čtvrtlet\xed","3. čtvrtlet\xed","4. čtvrtlet\xed"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:(0,d.o)({values:{narrow:["L","\xda","B","D","K","Č","Č","S","Z","Ř","L","P"],abbreviated:["led","\xfano","bře","dub","kvě","čvn","čvc","srp","z\xe1ř","ř\xedj","lis","pro"],wide:["leden","\xfanor","březen","duben","květen","červen","červenec","srpen","z\xe1ř\xed","ř\xedjen","listopad","prosinec"]},defaultWidth:"wide",formattingValues:{narrow:["L","\xda","B","D","K","Č","Č","S","Z","Ř","L","P"],abbreviated:["led","\xfano","bře","dub","kvě","čvn","čvc","srp","z\xe1ř","ř\xedj","lis","pro"],wide:["ledna","\xfanora","března","dubna","května","června","července","srpna","z\xe1ř\xed","ř\xedjna","listopadu","prosince"]},defaultFormattingWidth:"wide"}),day:(0,d.o)({values:{narrow:["ne","po","\xfat","st","čt","p\xe1","so"],short:["ne","po","\xfat","st","čt","p\xe1","so"],abbreviated:["ned","pon","\xfate","stř","čtv","p\xe1t","sob"],wide:["neděle","ponděl\xed","\xfater\xfd","středa","čtvrtek","p\xe1tek","sobota"]},defaultWidth:"wide"}),dayPeriod:(0,d.o)({values:{narrow:{am:"dop.",pm:"odp.",midnight:"půlnoc",noon:"poledne",morning:"r\xe1no",afternoon:"odpoledne",evening:"večer",night:"noc"},abbreviated:{am:"dop.",pm:"odp.",midnight:"půlnoc",noon:"poledne",morning:"r\xe1no",afternoon:"odpoledne",evening:"večer",night:"noc"},wide:{am:"dopoledne",pm:"odpoledne",midnight:"půlnoc",noon:"poledne",morning:"r\xe1no",afternoon:"odpoledne",evening:"večer",night:"noc"}},defaultWidth:"wide",formattingValues:{narrow:{am:"dop.",pm:"odp.",midnight:"půlnoc",noon:"poledne",morning:"r\xe1no",afternoon:"odpoledne",evening:"večer",night:"noc"},abbreviated:{am:"dop.",pm:"odp.",midnight:"půlnoc",noon:"poledne",morning:"r\xe1no",afternoon:"odpoledne",evening:"večer",night:"noc"},wide:{am:"dopoledne",pm:"odpoledne",midnight:"půlnoc",noon:"poledne",morning:"r\xe1no",afternoon:"odpoledne",evening:"večer",night:"noc"}},defaultFormattingWidth:"wide"})};var p=n(44754);let s={code:"cs",formatDistance:(e,t,n)=>{let r;let o=a[e];r="other"===o.type?o.other:1===t?o.one:t>1&&t<5?o.few:o.many;let u=n?.addSuffix===!0,i=n?.comparison;return(u&&-1===i?r.past:u&&1===i?r.future:r.regular).replace("{{count}}",String(t))},formatLong:o,formatRelative:(e,t)=>{let n=i[e];return"function"==typeof n?n(t):n},localize:l,match:{ordinalNumber:(0,n(71886).K)({matchPattern:/^(\d+)\.?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:(0,p.A)({matchPatterns:{narrow:/^(p[řr](\.|ed) Kr\.|p[řr](\.|ed) n\. l\.|po Kr\.|n\. l\.)/i,abbreviated:/^(p[řr](\.|ed) Kr\.|p[řr](\.|ed) n\. l\.|po Kr\.|n\. l\.)/i,wide:/^(p[řr](\.|ed) Kristem|p[řr](\.|ed) na[šs][íi]m letopo[čc]tem|po Kristu|na[šs]eho letopo[čc]tu)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^p[řr]/i,/^(po|n)/i]},defaultParseWidth:"any"}),quarter:(0,p.A)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^[1234]\. [čc]tvrtlet[íi]/i,wide:/^[1234]\. [čc]tvrtlet[íi]/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:(0,p.A)({matchPatterns:{narrow:/^[lúubdkčcszřrlp]/i,abbreviated:/^(led|[úu]no|b[řr]e|dub|kv[ěe]|[čc]vn|[čc]vc|srp|z[áa][řr]|[řr][íi]j|lis|pro)/i,wide:/^(leden|ledna|[úu]nora?|b[řr]ezen|b[řr]ezna|duben|dubna|kv[ěe]ten|kv[ěe]tna|[čc]erven(ec|ce)?|[čc]ervna|srpen|srpna|z[áa][řr][íi]|[řr][íi]jen|[řr][íi]jna|listopad(a|u)?|prosinec|prosince)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^l/i,/^[úu]/i,/^b/i,/^d/i,/^k/i,/^[čc]/i,/^[čc]/i,/^s/i,/^z/i,/^[řr]/i,/^l/i,/^p/i],any:[/^led/i,/^[úu]n/i,/^b[řr]e/i,/^dub/i,/^kv[ěe]/i,/^[čc]vn|[čc]erven(?!\w)|[čc]ervna/i,/^[čc]vc|[čc]erven(ec|ce)/i,/^srp/i,/^z[áa][řr]/i,/^[řr][íi]j/i,/^lis/i,/^pro/i]},defaultParseWidth:"any"}),day:(0,p.A)({matchPatterns:{narrow:/^[npuúsčps]/i,short:/^(ne|po|[úu]t|st|[čc]t|p[áa]|so)/i,abbreviated:/^(ned|pon|[úu]te|st[rř]|[čc]tv|p[áa]t|sob)/i,wide:/^(ned[ěe]le|pond[ěe]l[íi]|[úu]ter[ýy]|st[řr]eda|[čc]tvrtek|p[áa]tek|sobota)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^n/i,/^p/i,/^[úu]/i,/^s/i,/^[čc]/i,/^p/i,/^s/i],any:[/^ne/i,/^po/i,/^[úu]t/i,/^st/i,/^[čc]t/i,/^p[áa]/i,/^so/i]},defaultParseWidth:"any"}),dayPeriod:(0,p.A)({matchPatterns:{any:/^dopoledne|dop\.?|odpoledne|odp\.?|p[ůu]lnoc|poledne|r[áa]no|odpoledne|ve[čc]er|(v )?noci?/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^dop/i,pm:/^odp/i,midnight:/^p[ůu]lnoc/i,noon:/^poledne/i,morning:/r[áa]no/i,afternoon:/odpoledne/i,evening:/ve[čc]er/i,night:/noc/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:4}},c=s}};