"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_date-fns_locale_ca_js"],{

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/ca.js":
/*!********************************************!*\
  !*** ./node_modules/date-fns/locale/ca.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ca: () => (/* binding */ ca),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ca_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ca/_lib/formatDistance.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/ca/_lib/formatDistance.js\");\n/* harmony import */ var _ca_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ca/_lib/formatLong.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/ca/_lib/formatLong.js\");\n/* harmony import */ var _ca_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ca/_lib/formatRelative.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/ca/_lib/formatRelative.js\");\n/* harmony import */ var _ca_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ca/_lib/localize.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/ca/_lib/localize.js\");\n/* harmony import */ var _ca_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ca/_lib/match.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/ca/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary Catalan locale.\n * @language Catalan\n * @iso-639-2 cat\n * <AUTHOR> Grau [@guigrpa](https://github.com/guigrpa)\n * <AUTHOR> Vizcaino [@avizcaino](https://github.com/avizcaino)\n */ const ca = {\n    code: \"ca\",\n    formatDistance: _ca_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _ca_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _ca_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _ca_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _ca_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 1 /* Monday */ ,\n        firstWeekContainsDate: 4\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ca);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/ca.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/ca/_lib/formatDistance.js":
/*!****************************************************************!*\
  !*** ./node_modules/date-fns/locale/ca/_lib/formatDistance.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\n/**\n * Davant de les xifres que es diuen amb vocal inicial, 1 i 11, s'apostrofen els articles el i la i la preposició de igual que si estiguessin escrits amb lletres.\n *    l'1 de juliol ('l'u')\n *    l'11 de novembre ('l'onze')\n *    l'11a clàusula del contracte ('l'onzena')\n *    la contractació d'11 jugadors ('d'onze')\n *    l'aval d'11.000 socis ('d'onze mil')\n *\n * Reference: https://aplicacions.llengua.gencat.cat/llc/AppJava/index.html?input_cercar=apostrofaci%25F3+davant+xifres&action=Principal&method=detall_completa&numPagina=1&idHit=11236&database=FITXES_PUB&tipusFont=Fitxes%20de%20l%27Optimot&idFont=11236&titol=apostrofaci%F3%20davant%20de%20xifres%20%2F%20apostrofaci%F3%20davant%20de%201%20i%2011&numeroResultat=1&clickLink=detall&tipusCerca=cerca.normes\n */ const formatDistanceLocale = {\n    lessThanXSeconds: {\n        one: \"menys d'un segon\",\n        eleven: \"menys d'onze segons\",\n        other: \"menys de {{count}} segons\"\n    },\n    xSeconds: {\n        one: \"1 segon\",\n        other: \"{{count}} segons\"\n    },\n    halfAMinute: \"mig minut\",\n    lessThanXMinutes: {\n        one: \"menys d'un minut\",\n        eleven: \"menys d'onze minuts\",\n        other: \"menys de {{count}} minuts\"\n    },\n    xMinutes: {\n        one: \"1 minut\",\n        other: \"{{count}} minuts\"\n    },\n    aboutXHours: {\n        one: \"aproximadament una hora\",\n        other: \"aproximadament {{count}} hores\"\n    },\n    xHours: {\n        one: \"1 hora\",\n        other: \"{{count}} hores\"\n    },\n    xDays: {\n        one: \"1 dia\",\n        other: \"{{count}} dies\"\n    },\n    aboutXWeeks: {\n        one: \"aproximadament una setmana\",\n        other: \"aproximadament {{count}} setmanes\"\n    },\n    xWeeks: {\n        one: \"1 setmana\",\n        other: \"{{count}} setmanes\"\n    },\n    aboutXMonths: {\n        one: \"aproximadament un mes\",\n        other: \"aproximadament {{count}} mesos\"\n    },\n    xMonths: {\n        one: \"1 mes\",\n        other: \"{{count}} mesos\"\n    },\n    aboutXYears: {\n        one: \"aproximadament un any\",\n        other: \"aproximadament {{count}} anys\"\n    },\n    xYears: {\n        one: \"1 any\",\n        other: \"{{count}} anys\"\n    },\n    overXYears: {\n        one: \"més d'un any\",\n        eleven: \"més d'onze anys\",\n        other: \"més de {{count}} anys\"\n    },\n    almostXYears: {\n        one: \"gairebé un any\",\n        other: \"gairebé {{count}} anys\"\n    }\n};\nconst formatDistance = (token, count, options)=>{\n    let result;\n    const tokenValue = formatDistanceLocale[token];\n    if (typeof tokenValue === \"string\") {\n        result = tokenValue;\n    } else if (count === 1) {\n        result = tokenValue.one;\n    } else if (count === 11 && tokenValue.eleven) {\n        result = tokenValue.eleven;\n    } else {\n        result = tokenValue.other.replace(\"{{count}}\", String(count));\n    }\n    if (options === null || options === void 0 ? void 0 : options.addSuffix) {\n        if (options.comparison && options.comparison > 0) {\n            return \"en \" + result;\n        } else {\n            return \"fa \" + result;\n        }\n    }\n    return result;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/ca/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/ca/_lib/formatLong.js":
/*!************************************************************!*\
  !*** ./node_modules/date-fns/locale/ca/_lib/formatLong.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\nconst dateFormats = {\n    full: \"EEEE, d 'de' MMMM y\",\n    long: \"d 'de' MMMM y\",\n    medium: \"d MMM y\",\n    short: \"dd/MM/y\"\n};\nconst timeFormats = {\n    full: \"HH:mm:ss zzzz\",\n    long: \"HH:mm:ss z\",\n    medium: \"HH:mm:ss\",\n    short: \"HH:mm\"\n};\nconst dateTimeFormats = {\n    full: \"{{date}} 'a les' {{time}}\",\n    long: \"{{date}} 'a les' {{time}}\",\n    medium: \"{{date}}, {{time}}\",\n    short: \"{{date}}, {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"full\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"full\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/ca/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/ca/_lib/formatRelative.js":
/*!****************************************************************!*\
  !*** ./node_modules/date-fns/locale/ca/_lib/formatRelative.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\nconst formatRelativeLocale = {\n    lastWeek: \"'el' eeee 'passat a la' LT\",\n    yesterday: \"'ahir a la' p\",\n    today: \"'avui a la' p\",\n    tomorrow: \"'demà a la' p\",\n    nextWeek: \"eeee 'a la' p\",\n    other: \"P\"\n};\nconst formatRelativeLocalePlural = {\n    lastWeek: \"'el' eeee 'passat a les' p\",\n    yesterday: \"'ahir a les' p\",\n    today: \"'avui a les' p\",\n    tomorrow: \"'demà a les' p\",\n    nextWeek: \"eeee 'a les' p\",\n    other: \"P\"\n};\nconst formatRelative = (token, date, _baseDate, _options)=>{\n    if (date.getHours() !== 1) {\n        return formatRelativeLocalePlural[token];\n    }\n    return formatRelativeLocale[token];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9kYXRlLWZucy9sb2NhbGUvY2EvX2xpYi9mb3JtYXRSZWxhdGl2ZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsTUFBTUEsdUJBQXVCO0lBQzNCQyxVQUFVO0lBQ1ZDLFdBQVc7SUFDWEMsT0FBTztJQUNQQyxVQUFVO0lBQ1ZDLFVBQVU7SUFDVkMsT0FBTztBQUNUO0FBRUEsTUFBTUMsNkJBQTZCO0lBQ2pDTixVQUFVO0lBQ1ZDLFdBQVc7SUFDWEMsT0FBTztJQUNQQyxVQUFVO0lBQ1ZDLFVBQVU7SUFDVkMsT0FBTztBQUNUO0FBRU8sTUFBTUUsaUJBQWlCLENBQUNDLE9BQU9DLE1BQU1DLFdBQVdDO0lBQ3JELElBQUlGLEtBQUtHLFFBQVEsT0FBTyxHQUFHO1FBQ3pCLE9BQU9OLDBCQUEwQixDQUFDRSxNQUFNO0lBQzFDO0lBQ0EsT0FBT1Qsb0JBQW9CLENBQUNTLE1BQU07QUFDcEMsRUFBRSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxyYXZpMVxca2F2eWEtZ2l0XFxzcGFyay1uZXdcXGxpdHRsZXNwYXJrLWNtc1xcbm9kZV9tb2R1bGVzXFxkYXRlLWZuc1xcbG9jYWxlXFxjYVxcX2xpYlxcZm9ybWF0UmVsYXRpdmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgZm9ybWF0UmVsYXRpdmVMb2NhbGUgPSB7XG4gIGxhc3RXZWVrOiBcIidlbCcgZWVlZSAncGFzc2F0IGEgbGEnIExUXCIsXG4gIHllc3RlcmRheTogXCInYWhpciBhIGxhJyBwXCIsXG4gIHRvZGF5OiBcIidhdnVpIGEgbGEnIHBcIixcbiAgdG9tb3Jyb3c6IFwiJ2RlbcOgIGEgbGEnIHBcIixcbiAgbmV4dFdlZWs6IFwiZWVlZSAnYSBsYScgcFwiLFxuICBvdGhlcjogXCJQXCIsXG59O1xuXG5jb25zdCBmb3JtYXRSZWxhdGl2ZUxvY2FsZVBsdXJhbCA9IHtcbiAgbGFzdFdlZWs6IFwiJ2VsJyBlZWVlICdwYXNzYXQgYSBsZXMnIHBcIixcbiAgeWVzdGVyZGF5OiBcIidhaGlyIGEgbGVzJyBwXCIsXG4gIHRvZGF5OiBcIidhdnVpIGEgbGVzJyBwXCIsXG4gIHRvbW9ycm93OiBcIidkZW3DoCBhIGxlcycgcFwiLFxuICBuZXh0V2VlazogXCJlZWVlICdhIGxlcycgcFwiLFxuICBvdGhlcjogXCJQXCIsXG59O1xuXG5leHBvcnQgY29uc3QgZm9ybWF0UmVsYXRpdmUgPSAodG9rZW4sIGRhdGUsIF9iYXNlRGF0ZSwgX29wdGlvbnMpID0+IHtcbiAgaWYgKGRhdGUuZ2V0SG91cnMoKSAhPT0gMSkge1xuICAgIHJldHVybiBmb3JtYXRSZWxhdGl2ZUxvY2FsZVBsdXJhbFt0b2tlbl07XG4gIH1cbiAgcmV0dXJuIGZvcm1hdFJlbGF0aXZlTG9jYWxlW3Rva2VuXTtcbn07XG4iXSwibmFtZXMiOlsiZm9ybWF0UmVsYXRpdmVMb2NhbGUiLCJsYXN0V2VlayIsInllc3RlcmRheSIsInRvZGF5IiwidG9tb3Jyb3ciLCJuZXh0V2VlayIsIm90aGVyIiwiZm9ybWF0UmVsYXRpdmVMb2NhbGVQbHVyYWwiLCJmb3JtYXRSZWxhdGl2ZSIsInRva2VuIiwiZGF0ZSIsIl9iYXNlRGF0ZSIsIl9vcHRpb25zIiwiZ2V0SG91cnMiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/ca/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/ca/_lib/localize.js":
/*!**********************************************************!*\
  !*** ./node_modules/date-fns/locale/ca/_lib/localize.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\n/**\n * General information\n * Reference: https://aplicacions.llengua.gencat.cat\n * Reference: https://www.uoc.edu/portal/ca/servei-linguistic/convencions/abreviacions/simbols/simbols-habituals.html\n */ /**\n * Abans de Crist: https://aplicacions.llengua.gencat.cat/llc/AppJava/index.html?input_cercar=abans+de+crist&action=Principal&method=detall_completa&numPagina=1&idHit=6876&database=FITXES_PUB&tipusFont=Fitxes%20de%20l%27Optimot&idFont=6876&titol=abans%20de%20Crist%20(abreviatura)%20/%20abans%20de%20Crist%20(sigla)&numeroResultat=1&clickLink=detall&tipusCerca=cerca.fitxes\n * Desprest de Crist: https://aplicacions.llengua.gencat.cat/llc/AppJava/index.html?input_cercar=despr%E9s+de+crist&action=Principal&method=detall_completa&numPagina=1&idHit=6879&database=FITXES_PUB&tipusFont=Fitxes%20de%20l%27Optimot&idFont=6879&titol=despr%E9s%20de%20Crist%20(sigla)%20/%20despr%E9s%20de%20Crist%20(abreviatura)&numeroResultat=1&clickLink=detall&tipusCerca=cerca.fitxes\n */ const eraValues = {\n    narrow: [\n        \"aC\",\n        \"dC\"\n    ],\n    abbreviated: [\n        \"a. de C.\",\n        \"d. de C.\"\n    ],\n    wide: [\n        \"abans de Crist\",\n        \"després de Crist\"\n    ]\n};\nconst quarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"T1\",\n        \"T2\",\n        \"T3\",\n        \"T4\"\n    ],\n    wide: [\n        \"1r trimestre\",\n        \"2n trimestre\",\n        \"3r trimestre\",\n        \"4t trimestre\"\n    ]\n};\n/**\n * Dins d'un text convé fer servir la forma sencera dels mesos, ja que sempre és més clar el mot sencer que l'abreviatura, encara que aquesta sigui força coneguda.\n * Cal reservar, doncs, les abreviatures per a les llistes o classificacions, els gràfics, les taules o quadres estadístics, els textos publicitaris, etc.\n *\n * Reference: https://aplicacions.llengua.gencat.cat/llc/AppJava/index.html?input_cercar=abreviacions+mesos&action=Principal&method=detall_completa&numPagina=1&idHit=8402&database=FITXES_PUB&tipusFont=Fitxes%20de%20l%27Optimot&idFont=8402&titol=abreviatures%20dels%20mesos%20de%20l%27any&numeroResultat=5&clickLink=detall&tipusCerca=cerca.fitxes\n */ const monthValues = {\n    narrow: [\n        \"GN\",\n        \"FB\",\n        \"MÇ\",\n        \"AB\",\n        \"MG\",\n        \"JN\",\n        \"JL\",\n        \"AG\",\n        \"ST\",\n        \"OC\",\n        \"NV\",\n        \"DS\"\n    ],\n    /**\n   * Les abreviatures dels mesos de l'any es formen seguint una de les normes generals de formació d'abreviatures.\n   * S'escriu la primera síl·laba i les consonants de la síl·laba següent anteriors a la primera vocal.\n   * Els mesos de març, maig i juny no s'abreugen perquè són paraules d'una sola síl·laba.\n   */ abbreviated: [\n        \"gen.\",\n        \"febr.\",\n        \"març\",\n        \"abr.\",\n        \"maig\",\n        \"juny\",\n        \"jul.\",\n        \"ag.\",\n        \"set.\",\n        \"oct.\",\n        \"nov.\",\n        \"des.\"\n    ],\n    wide: [\n        \"gener\",\n        \"febrer\",\n        \"març\",\n        \"abril\",\n        \"maig\",\n        \"juny\",\n        \"juliol\",\n        \"agost\",\n        \"setembre\",\n        \"octubre\",\n        \"novembre\",\n        \"desembre\"\n    ]\n};\n/**\n * Les abreviatures dels dies de la setmana comencen totes amb la lletra d.\n * Tot seguit porten la consonant següent a la i, excepte en el cas de dimarts, dimecres i diumenge, en què aquesta consonant és la m i, per tant, hi podria haver confusió.\n * Per evitar-ho, s'ha substituït la m per una t (en el cas de dimarts), una c (en el cas de dimecres) i una g (en el cas de diumenge), respectivament.\n *\n * Seguint la norma general d'ús de les abreviatures, les dels dies de la setmana sempre porten punt final.\n * Igualment, van amb la primera lletra en majúscula quan la paraula sencera també hi aniria.\n * En canvi, van amb la primera lletra en minúscula quan la inicial de la paraula sencera també hi aniria.\n *\n * Reference: https://aplicacions.llengua.gencat.cat/llc/AppJava/index.html?input_cercar=abreviatures+dies&action=Principal&method=detall_completa&numPagina=1&idHit=8387&database=FITXES_PUB&tipusFont=Fitxes%20de%20l%27Optimot&idFont=8387&titol=abreviatures%20dels%20dies%20de%20la%20setmana&numeroResultat=1&clickLink=detall&tipusCerca=cerca.tot\n */ const dayValues = {\n    narrow: [\n        \"dg.\",\n        \"dl.\",\n        \"dt.\",\n        \"dm.\",\n        \"dj.\",\n        \"dv.\",\n        \"ds.\"\n    ],\n    short: [\n        \"dg.\",\n        \"dl.\",\n        \"dt.\",\n        \"dm.\",\n        \"dj.\",\n        \"dv.\",\n        \"ds.\"\n    ],\n    abbreviated: [\n        \"dg.\",\n        \"dl.\",\n        \"dt.\",\n        \"dm.\",\n        \"dj.\",\n        \"dv.\",\n        \"ds.\"\n    ],\n    wide: [\n        \"diumenge\",\n        \"dilluns\",\n        \"dimarts\",\n        \"dimecres\",\n        \"dijous\",\n        \"divendres\",\n        \"dissabte\"\n    ]\n};\n/**\n * Reference: https://aplicacions.llengua.gencat.cat/llc/AppJava/index.html?action=Principal&method=detall&input_cercar=parts+del+dia&numPagina=1&database=FITXES_PUB&idFont=12801&idHit=12801&tipusFont=Fitxes+de+l%27Optimot&numeroResultat=1&databases_avansada=&categories_avansada=&clickLink=detall&titol=Nom+de+les+parts+del+dia&tematica=&tipusCerca=cerca.fitxes\n */ const dayPeriodValues = {\n    narrow: {\n        am: \"am\",\n        pm: \"pm\",\n        midnight: \"mitjanit\",\n        noon: \"migdia\",\n        morning: \"matí\",\n        afternoon: \"tarda\",\n        evening: \"vespre\",\n        night: \"nit\"\n    },\n    abbreviated: {\n        am: \"a.m.\",\n        pm: \"p.m.\",\n        midnight: \"mitjanit\",\n        noon: \"migdia\",\n        morning: \"matí\",\n        afternoon: \"tarda\",\n        evening: \"vespre\",\n        night: \"nit\"\n    },\n    wide: {\n        am: \"ante meridiem\",\n        pm: \"post meridiem\",\n        midnight: \"mitjanit\",\n        noon: \"migdia\",\n        morning: \"matí\",\n        afternoon: \"tarda\",\n        evening: \"vespre\",\n        night: \"nit\"\n    }\n};\nconst formattingDayPeriodValues = {\n    narrow: {\n        am: \"am\",\n        pm: \"pm\",\n        midnight: \"de la mitjanit\",\n        noon: \"del migdia\",\n        morning: \"del matí\",\n        afternoon: \"de la tarda\",\n        evening: \"del vespre\",\n        night: \"de la nit\"\n    },\n    abbreviated: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"de la mitjanit\",\n        noon: \"del migdia\",\n        morning: \"del matí\",\n        afternoon: \"de la tarda\",\n        evening: \"del vespre\",\n        night: \"de la nit\"\n    },\n    wide: {\n        am: \"ante meridiem\",\n        pm: \"post meridiem\",\n        midnight: \"de la mitjanit\",\n        noon: \"del migdia\",\n        morning: \"del matí\",\n        afternoon: \"de la tarda\",\n        evening: \"del vespre\",\n        night: \"de la nit\"\n    }\n};\n/**\n * Quan van en singular, els nombres ordinals es representen, en forma d’abreviatura, amb la xifra seguida de l’última lletra del mot desplegat.\n * És optatiu posar punt després de la lletra.\n *\n * Reference: https://aplicacions.llengua.gencat.cat/llc/AppJava/pdf/abrevia.pdf#page=18\n */ const ordinalNumber = (dirtyNumber, _options)=>{\n    const number = Number(dirtyNumber);\n    const rem100 = number % 100;\n    if (rem100 > 20 || rem100 < 10) {\n        switch(rem100 % 10){\n            case 1:\n                return number + \"r\";\n            case 2:\n                return number + \"n\";\n            case 3:\n                return number + \"r\";\n            case 4:\n                return number + \"t\";\n        }\n    }\n    return number + \"è\";\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        defaultWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"wide\",\n        formattingValues: formattingDayPeriodValues,\n        defaultFormattingWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/ca/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/ca/_lib/match.js":
/*!*******************************************************!*\
  !*** ./node_modules/date-fns/locale/ca/_lib/match.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^(\\d+)(è|r|n|r|t)?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^(aC|dC)/i,\n    abbreviated: /^(a. de C.|d. de C.)/i,\n    wide: /^(abans de Crist|despr[eé]s de Crist)/i\n};\nconst parseEraPatterns = {\n    narrow: [\n        /^aC/i,\n        /^dC/i\n    ],\n    abbreviated: [\n        /^(a. de C.)/i,\n        /^(d. de C.)/i\n    ],\n    wide: [\n        /^(abans de Crist)/i,\n        /^(despr[eé]s de Crist)/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]/i,\n    abbreviated: /^T[1234]/i,\n    wide: /^[1234](è|r|n|r|t)? trimestre/i\n};\nconst parseQuarterPatterns = {\n    any: [\n        /1/i,\n        /2/i,\n        /3/i,\n        /4/i\n    ]\n};\nconst matchMonthPatterns = {\n    narrow: /^(GN|FB|MÇ|AB|MG|JN|JL|AG|ST|OC|NV|DS)/i,\n    abbreviated: /^(gen.|febr.|març|abr.|maig|juny|jul.|ag.|set.|oct.|nov.|des.)/i,\n    wide: /^(gener|febrer|març|abril|maig|juny|juliol|agost|setembre|octubre|novembre|desembre)/i\n};\nconst parseMonthPatterns = {\n    narrow: [\n        /^GN/i,\n        /^FB/i,\n        /^MÇ/i,\n        /^AB/i,\n        /^MG/i,\n        /^JN/i,\n        /^JL/i,\n        /^AG/i,\n        /^ST/i,\n        /^OC/i,\n        /^NV/i,\n        /^DS/i\n    ],\n    abbreviated: [\n        /^gen./i,\n        /^febr./i,\n        /^març/i,\n        /^abr./i,\n        /^maig/i,\n        /^juny/i,\n        /^jul./i,\n        /^ag./i,\n        /^set./i,\n        /^oct./i,\n        /^nov./i,\n        /^des./i\n    ],\n    wide: [\n        /^gener/i,\n        /^febrer/i,\n        /^març/i,\n        /^abril/i,\n        /^maig/i,\n        /^juny/i,\n        /^juliol/i,\n        /^agost/i,\n        /^setembre/i,\n        /^octubre/i,\n        /^novembre/i,\n        /^desembre/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^(dg\\.|dl\\.|dt\\.|dm\\.|dj\\.|dv\\.|ds\\.)/i,\n    short: /^(dg\\.|dl\\.|dt\\.|dm\\.|dj\\.|dv\\.|ds\\.)/i,\n    abbreviated: /^(dg\\.|dl\\.|dt\\.|dm\\.|dj\\.|dv\\.|ds\\.)/i,\n    wide: /^(diumenge|dilluns|dimarts|dimecres|dijous|divendres|dissabte)/i\n};\nconst parseDayPatterns = {\n    narrow: [\n        /^dg./i,\n        /^dl./i,\n        /^dt./i,\n        /^dm./i,\n        /^dj./i,\n        /^dv./i,\n        /^ds./i\n    ],\n    abbreviated: [\n        /^dg./i,\n        /^dl./i,\n        /^dt./i,\n        /^dm./i,\n        /^dj./i,\n        /^dv./i,\n        /^ds./i\n    ],\n    wide: [\n        /^diumenge/i,\n        /^dilluns/i,\n        /^dimarts/i,\n        /^dimecres/i,\n        /^dijous/i,\n        /^divendres/i,\n        /^disssabte/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    narrow: /^(a|p|mn|md|(del|de la) (matí|tarda|vespre|nit))/i,\n    abbreviated: /^([ap]\\.?\\s?m\\.?|mitjanit|migdia|(del|de la) (matí|tarda|vespre|nit))/i,\n    wide: /^(ante meridiem|post meridiem|mitjanit|migdia|(del|de la) (matí|tarda|vespre|nit))/i\n};\nconst parseDayPeriodPatterns = {\n    any: {\n        am: /^a/i,\n        pm: /^p/i,\n        midnight: /^mitjanit/i,\n        noon: /^migdia/i,\n        morning: /matí/i,\n        afternoon: /tarda/i,\n        evening: /vespre/i,\n        night: /nit/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value, 10)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"wide\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/ca/_lib/match.js\n"));

/***/ })

}]);