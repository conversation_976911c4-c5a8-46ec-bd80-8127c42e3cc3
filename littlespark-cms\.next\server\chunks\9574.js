"use strict";exports.id=9574,exports.ids=[9574],exports.modules={9574:(e,t,r)=>{r.d(t,{o:()=>w});var o=r(9206),l=r(60676);let n=Object.prototype.hasOwnProperty,a=Array.isArray,i={allowDots:!1,allowEmptyArrays:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decodeDotInKeys:!1,decoder:l.D4,delimiter:"&",depth:5,duplicates:"combine",ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},c=function(e,t){return e&&"string"==typeof e&&t.comma&&e.indexOf(",")>-1?e.split(","):e},s=function(e,t){let r;let o={__proto__:null},s=t.ignoreQueryPrefix?e.replace(/^\?/,""):e,p=t.parameterLimit===1/0?void 0:t.parameterLimit,u=s.split(t.delimiter,p),f=-1,d=t.charset;if(t.charsetSentinel)for(r=0;r<u.length;++r)0===u[r].indexOf("utf8=")&&("utf8=%E2%9C%93"===u[r]?d="utf-8":"utf8=%26%2310003%3B"===u[r]&&(d="iso-8859-1"),f=r,r=u.length);for(r=0;r<u.length;++r){let e,s;if(r===f)continue;let p=u[r],y=p.indexOf("]="),h=-1===y?p.indexOf("="):y+1;-1===h?(e=t.decoder(p,i.decoder,d,"key"),s=t.strictNullHandling?null:""):(e=t.decoder(p.slice(0,h),i.decoder,d,"key"),s=l.F7(c(p.slice(h+1),t),function(e){return t.decoder(e,i.decoder,d,"value")})),s&&t.interpretNumericEntities&&"iso-8859-1"===d&&(s=s.replace(/&#(\d+);/g,function(e,t){return String.fromCharCode(parseInt(t,10))})),p.indexOf("[]=")>-1&&(s=a(s)?[s]:s);let g=n.call(o,e);g&&"combine"===t.duplicates?o[e]=l.kg(o[e],s):g&&"last"!==t.duplicates||(o[e]=s)}return o},p=function(e,t,r,o){let l=o?t:c(t,r);for(let t=e.length-1;t>=0;--t){let o;let n=e[t];if("[]"===n&&r.parseArrays)o=r.allowEmptyArrays&&""===l?[]:[].concat(l);else{o=r.plainObjects?Object.create(null):{};let e="["===n.charAt(0)&&"]"===n.charAt(n.length-1)?n.slice(1,-1):n,t=r.decodeDotInKeys?e.replace(/%2E/g,"."):e,a=parseInt(t,10);r.parseArrays||""!==t?!isNaN(a)&&n!==t&&String(a)===t&&a>=0&&r.parseArrays&&a<=r.arrayLimit?(o=[])[a]=l:"__proto__"!==t&&(o[t]=l):o={0:l}}l=o}return l},u=function(e,t,r,o){if(!e)return;let l=r.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,a=/(\[[^[\]]*])/g,i=r.depth>0&&/(\[[^[\]]*])/.exec(l),c=i?l.slice(0,i.index):l,s=[];if(c){if(!r.plainObjects&&n.call(Object.prototype,c)&&!r.allowPrototypes)return;s.push(c)}let u=0;for(;r.depth>0&&null!==(i=a.exec(l))&&u<r.depth;){if(u+=1,!r.plainObjects&&n.call(Object.prototype,i[1].slice(1,-1))&&!r.allowPrototypes)return;s.push(i[1])}return i&&s.push("["+l.slice(i.index)+"]"),p(s,t,r,o)},f=function(e){if(!e)return i;if(void 0!==e.allowEmptyArrays&&"boolean"!=typeof e.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==e.decodeDotInKeys&&"boolean"!=typeof e.decodeDotInKeys)throw TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==e.decoder&&void 0!==e.decoder&&"function"!=typeof e.decoder)throw TypeError("Decoder has to be a function.");if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let t=void 0===e.charset?i.charset:e.charset,r=void 0===e.duplicates?i.duplicates:e.duplicates;if("combine"!==r&&"first"!==r&&"last"!==r)throw TypeError("The duplicates option must be either combine, first, or last");return{allowDots:void 0===e.allowDots?!0===e.decodeDotInKeys||i.allowDots:!!e.allowDots,allowEmptyArrays:"boolean"==typeof e.allowEmptyArrays?!!e.allowEmptyArrays:i.allowEmptyArrays,allowPrototypes:"boolean"==typeof e.allowPrototypes?e.allowPrototypes:i.allowPrototypes,allowSparse:"boolean"==typeof e.allowSparse?e.allowSparse:i.allowSparse,arrayLimit:"number"==typeof e.arrayLimit?e.arrayLimit:i.arrayLimit,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:i.charsetSentinel,comma:"boolean"==typeof e.comma?e.comma:i.comma,decodeDotInKeys:"boolean"==typeof e.decodeDotInKeys?e.decodeDotInKeys:i.decodeDotInKeys,decoder:"function"==typeof e.decoder?e.decoder:i.decoder,delimiter:"string"==typeof e.delimiter||l.gd(e.delimiter)?e.delimiter:i.delimiter,depth:"number"==typeof e.depth||!1===e.depth?+e.depth:i.depth,duplicates:r,ignoreQueryPrefix:!0===e.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof e.interpretNumericEntities?e.interpretNumericEntities:i.interpretNumericEntities,parameterLimit:"number"==typeof e.parameterLimit?e.parameterLimit:i.parameterLimit,parseArrays:!1!==e.parseArrays,plainObjects:"boolean"==typeof e.plainObjects?e.plainObjects:i.plainObjects,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:i.strictNullHandling}};var d=r(77659),y=r(83887),h=r(66280),g=r(38879);let b=["ar","az","bg","bn-BD","bn-IN","ca","cs","bn-BD","bn-IN","da","de","en","es","et","fa","fr","he","hr","hu","hy","it","ja","ko","lt","lv","my","nb","nl","pl","pt","ro","rs","rs-latin","ru","sk","sl","sv","th","tr","uk","vi","zh","zh-TW"],m=({config:e,cookies:t,headers:r})=>{let o=Object.keys(e.i18n.supportedLanguages),l=t.get(`${e.cookiePrefix||"payload"}-lng`),n="string"==typeof l?l:l?.value;if(n&&o.includes(n))return n;let a=r.get("Accept-Language")?function(e){let t;for(let{language:r}of e.split(",").map(e=>{let[t,r]=e.trim().split(";q=");return{language:t,quality:r?parseFloat(r):1}}).sort((e,t)=>t.quality-e.quality))!t&&b.includes(r)&&(t=r);return t}(r.get("Accept-Language")):void 0;return a&&o.includes(a)?a:e.i18n.fallbackLanguage};var j=r(90045);let w=async({canSetHeaders:e,config:t,params:r,request:n})=>{let a=(0,j.J)(n.headers),i=await (0,h.nm0)({config:t,cron:!0}),{config:c}=i,p=c.localization,b=new URL(n.url),{pathname:w,searchParams:O}=b,L=!c.graphQL.disable&&w===`${c.routes.api}${c.routes.graphQL}`,A=m({config:c,cookies:a,headers:n.headers}),E=await (0,o.L)({config:c.i18n,context:"api",language:A}),v=O.get("fallback-locale")||O.get("fallbackLocale"),D=O.get("locale"),S=v,{search:x}=b,P=x?function(e,t){let r=f(t);if(""===e||null==e)return r.plainObjects?Object.create(null):{};let o="string"==typeof e?s(e,r):e,n=r.plainObjects?Object.create(null):{},a=Object.keys(o);for(let t=0;t<a.length;++t){let i=a[t],c=u(i,o[i],r,"string"==typeof e);n=l.h1(n,c,r)}return!0===r.allowSparse?n:l.oE(n)}(x,{arrayLimit:1e3,depth:10,ignoreQueryPrefix:!0}):{};if(p){let e=(0,g.T)({fallbackLocale:S,locale:D,localization:p});S=e.fallbackLocale,D=e.locale}let k=Object.assign(n,{context:{},fallbackLocale:S,hash:b.hash,host:b.host,href:b.href,i18n:E,locale:D,origin:b.origin,pathname:b.pathname,payload:i,payloadAPI:L?"GraphQL":"REST",payloadDataLoader:void 0,payloadUploadSizes:{},port:b.port,protocol:b.protocol,query:P,routeParams:r||{},search:b.search,searchParams:b.searchParams,t:E.t,transactionID:void 0,user:null});k.payloadDataLoader=(0,y.Y)(k);let{responseHeaders:I,user:C}=await (0,d.F)({canSetHeaders:e,headers:k.headers,isGraphQL:L,payload:i});return k.user=C,I&&(k.responseHeaders=I),k}},961:(e,t,r)=>{r.d(t,{Ay:()=>c,_J:()=>a,j1:()=>i});let o=String.prototype.replace,l=/%20/g,n={RFC1738:"RFC1738",RFC3986:"RFC3986"},a={RFC1738:function(e){return o.call(e,l,"+")},RFC3986:function(e){return String(e)}},i=n.RFC1738;n.RFC3986;let c=n.RFC3986},60676:(e,t,r)=>{r.d(t,{D4:()=>p,F7:()=>g,Pe:()=>y,gd:()=>d,h1:()=>s,kg:()=>h,lF:()=>u,oE:()=>f});var o=r(961);let l=Object.prototype.hasOwnProperty,n=Array.isArray,a=function(){let e=[];for(let t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),i=function(e){for(;e.length>1;){let t=e.pop(),r=t.obj[t.prop];if(n(r)){let e=[];for(let t=0;t<r.length;++t)void 0!==r[t]&&e.push(r[t]);t.obj[t.prop]=e}}},c=function(e,t){let r=t&&t.plainObjects?Object.create(null):{};for(let t=0;t<e.length;++t)void 0!==e[t]&&(r[t]=e[t]);return r},s=function e(t,r,o){if(!r)return t;if("object"!=typeof r){if(n(t))t.push(r);else{if(!t||"object"!=typeof t)return[t,r];(o&&(o.plainObjects||o.allowPrototypes)||!l.call(Object.prototype,r))&&(t[r]=!0)}return t}if(!t||"object"!=typeof t)return[t].concat(r);let a=t;return(n(t)&&!n(r)&&(a=c(t,o)),n(t)&&n(r))?(r.forEach(function(r,n){if(l.call(t,n)){let l=t[n];l&&"object"==typeof l&&r&&"object"==typeof r?t[n]=e(l,r,o):t.push(r)}else t[n]=r}),t):Object.keys(r).reduce(function(t,n){let a=r[n];return l.call(t,n)?t[n]=e(t[n],a,o):t[n]=a,t},a)},p=function(e,t,r){let o=e.replace(/\+/g," ");if("iso-8859-1"===r)return o.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(o)}catch(e){return o}},u=function(e,t,r,l,n){if(0===e.length)return e;let i=e;if("symbol"==typeof e?i=Symbol.prototype.toString.call(e):"string"!=typeof e&&(i=String(e)),"iso-8859-1"===r)return escape(i).replace(/%u[0-9a-f]{4}/gi,function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"});let c="";for(let e=0;e<i.length;e+=1024){let t=i.length>=1024?i.slice(e,e+1024):i,r=[];for(let e=0;e<t.length;++e){let l=t.charCodeAt(e);if(45===l||46===l||95===l||126===l||l>=48&&l<=57||l>=65&&l<=90||l>=97&&l<=122||n===o.j1&&(40===l||41===l)){r[r.length]=t.charAt(e);continue}if(l<128){r[r.length]=a[l];continue}if(l<2048){r[r.length]=a[192|l>>6]+a[128|63&l];continue}if(l<55296||l>=57344){r[r.length]=a[224|l>>12]+a[128|l>>6&63]+a[128|63&l];continue}e+=1,l=65536+((1023&l)<<10|1023&t.charCodeAt(e)),r[r.length]=a[240|l>>18]+a[128|l>>12&63]+a[128|l>>6&63]+a[128|63&l]}c+=r.join("")}return c},f=function(e){let t=[{obj:{o:e},prop:"o"}],r=[];for(let e=0;e<t.length;++e){let o=t[e],l=o.obj[o.prop],n=Object.keys(l);for(let e=0;e<n.length;++e){let o=n[e],a=l[o];"object"==typeof a&&null!==a&&-1===r.indexOf(a)&&(t.push({obj:l,prop:o}),r.push(a))}}return i(t),e},d=function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},y=function(e){return!!e&&"object"==typeof e&&!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))},h=function(e,t){return[].concat(e,t)},g=function(e,t){if(n(e)){let r=[];for(let o=0;o<e.length;o+=1)r.push(t(e[o]));return r}return t(e)}}};