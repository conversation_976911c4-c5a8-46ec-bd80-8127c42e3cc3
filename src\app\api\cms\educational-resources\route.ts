import { NextRequest, NextResponse } from 'next/server';

const CMS_BASE_URL = process.env.CMS_BASE_URL || 'http://localhost:3001';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const cmsParams = new URLSearchParams();
    cmsParams.append('where[status][equals]', 'published');
    
    // Forward query parameters
    searchParams.forEach((value, key) => {
      if (key !== 'status') {
        if (key === 'subject' || key === 'ageGroup' || key === 'tags') {
          cmsParams.append(`where[${key}][contains]`, value);
        } else {
          cmsParams.append(`where[${key}][equals]`, value);
        }
      }
    });
    
    const cmsUrl = `${CMS_BASE_URL}/api/educational-resources?${cmsParams}`;
    console.log('Fetching educational resources from CMS:', cmsUrl);
    
    const response = await fetch(cmsUrl, {
      headers: {
        'Content-Type': 'application/json',
      },
      signal: AbortSignal.timeout(10000),
    });
    
    if (!response.ok) {
      throw new Error(`CMS API error: ${response.status}`);
    }
    
    const data = await response.json();
    
    const transformedResources = data.docs?.map((resource: {
      id: string;
      title: string;
      slug: string;
      description: string;
      type: string;
      subject: string;
      ageGroup: string[];
      content: string;
      media?: Array<{file: {url: string; alt?: string}}>;
    }) => ({
      id: resource.id,
      title: resource.title,
      slug: resource.slug,
      description: resource.description,
      type: resource.type,
      subject: resource.subject,
      ageGroup: resource.ageGroup,
      content: resource.content,
      media: resource.media?.map((m: {file: {url: string; alt?: string}}) => ({
        ...m,
        file: {
          ...m.file,
          url: m.file.url.startsWith('http') ? m.file.url : `${CMS_BASE_URL}${m.file.url}`
        }
      })),
      downloadableFiles: (resource as any).downloadableFiles?.map((file: {file: {url: string}}) => ({
        ...file,
        file: {
          ...file.file,
          url: file.file.url.startsWith('http') ? file.file.url : `${CMS_BASE_URL}${file.file.url}`
        }
      })),
        }
      })),
      relatedChallenges: resource.relatedChallenges,
      prerequisites: resource.prerequisites,
      learningOutcomes: resource.learningOutcomes,
      estimatedReadTime: resource.estimatedReadTime,
      difficulty: resource.difficulty,
      subscriptionTier: resource.subscriptionTier,
      featured: resource.featured,
      tags: resource.tags,
      publishedAt: resource.publishedAt,
    })) || [];
    
    return NextResponse.json({
      success: true,
      educationalResources: transformedResources,
      total: data.totalDocs || 0,
      page: data.page || 1,
      totalPages: data.totalPages || 1,
    });
    
  } catch (error) {
    console.error('Error fetching educational resources from CMS:', error);
    
    // Fallback educational resources
    const fallbackResources = [
      {
        id: 'fallback-edu-1',
        title: 'Color Theory Basics',
        slug: 'color-theory-basics',
        description: 'Learn the fundamentals of color theory for young artists',
        type: 'guide',
        subject: ['art-techniques', 'color-theory'],
        ageGroup: ['6-8', '9-11'],
        content: 'Colors are everywhere! Let\'s learn how they work together...',
        learningOutcomes: [{ outcome: 'Understand primary and secondary colors' }],
        difficulty: 'beginner',
        subscriptionTier: 'free',
        featured: true,
        publishedAt: new Date().toISOString(),
      }
    ];
    
    return NextResponse.json({
      success: false,
      educationalResources: fallbackResources,
      error: 'CMS unavailable, showing fallback content',
      total: fallbackResources.length,
    });
  }
}
