import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

const CMS_BASE_URL = process.env.CMS_BASE_URL || 'http://localhost:3001';

// POST /api/challenges/sync-cms - Sync CMS challenges to Supabase database
export async function POST(request: NextRequest) {
  try {
    console.log('🔄 [SYNC] Starting CMS to Supabase challenge sync');

    // Fetch all published challenges from CMS
    const cmsUrl = `${CMS_BASE_URL}/my-route`;
    const response = await fetch(cmsUrl, {
      headers: {
        'Content-Type': 'application/json',
      },
      signal: AbortSignal.timeout(10000),
    });

    if (!response.ok) {
      throw new Error(`CMS API error: ${response.status}`);
    }

    const data = await response.json();
    const cmsChallenge = data.challenges || [];

    console.log(`📦 [SYNC] Found ${cmsChallenge.length} CMS challenges`);

    let syncedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    for (const challenge of cmsChallenge) {
      try {
        // Validate required fields
        if (!challenge.title || !challenge.description || !challenge.difficulty || !challenge.category) {
          console.warn(`⚠️ [SYNC] Skipping challenge ${challenge.id}: missing required fields`);
          skippedCount++;
          continue;
        }

        // Convert CMS challenge to Supabase format
        const supabaseChallenge = {
          id: `cms-${challenge.id}`, // Prefix to avoid conflicts
          title: challenge.title,
          description: challenge.description,
          difficulty: challenge.difficulty,
          type: challenge.category, // Map category to type
          prompt: challenge.prompt || challenge.instructions || challenge.description, // Use prompt or fallback
          is_active: challenge.is_active !== false && challenge.status === 'published', // Active if published and not explicitly disabled
          created_by: 'cms',
          valid_until: null,
        };

        // Check if challenge already exists
        const existingChallenge = await prisma.challenge.findUnique({
          where: { id: supabaseChallenge.id }
        });

        if (existingChallenge) {
          // Update existing challenge
          await prisma.challenge.update({
            where: { id: supabaseChallenge.id },
            data: {
              title: supabaseChallenge.title,
              description: supabaseChallenge.description,
              difficulty: supabaseChallenge.difficulty,
              type: supabaseChallenge.type,
              prompt: supabaseChallenge.prompt,
              is_active: supabaseChallenge.is_active,
              updated_at: new Date(),
            }
          });
          console.log(`✅ [SYNC] Updated challenge: ${challenge.title}`);
        } else {
          // Create new challenge
          await prisma.challenge.create({
            data: supabaseChallenge
          });
          console.log(`🆕 [SYNC] Created challenge: ${challenge.title}`);
        }

        syncedCount++;
      } catch (error) {
        console.error(`❌ [SYNC] Error syncing challenge ${challenge.title}:`, error);
        errorCount++;
      }
    }

    console.log(`🎉 [SYNC] Sync complete: ${syncedCount} synced, ${skippedCount} skipped, ${errorCount} errors`);

    return NextResponse.json({
      success: true,
      message: 'CMS challenges synced to Supabase',
      stats: {
        total: cmsChallenge.length,
        synced: syncedCount,
        skipped: skippedCount,
        errors: errorCount
      }
    });

  } catch (error) {
    console.error('❌ [SYNC] Error syncing CMS challenges:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to sync CMS challenges to Supabase' 
      },
      { status: 500 }
    );
  }
}

// GET /api/challenges/sync-cms - Check sync status
export async function GET() {
  try {
    // Count CMS challenges in database
    const cmsCount = await prisma.challenge.count({
      where: {
        created_by: 'cms'
      }
    });

    // Count total challenges
    const totalCount = await prisma.challenge.count();

    return NextResponse.json({
      success: true,
      stats: {
        cmsChallenge: cmsCount,
        totalChallenge: totalCount,
        lastSync: 'Not implemented yet' // Could add a sync log table
      }
    });

  } catch (error) {
    console.error('Error checking sync status:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to check sync status' 
      },
      { status: 500 }
    );
  }
}
