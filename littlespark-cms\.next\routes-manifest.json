{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [{"source": "/:path*", "headers": [{"key": "Accept-CH", "value": "Sec-CH-Prefers-Color-Scheme"}, {"key": "Vary", "value": "Sec-CH-Prefers-Color-Scheme"}, {"key": "Critical-CH", "value": "Sec-CH-Prefers-Color-Scheme"}, {"key": "X-Powered-By", "value": "Next.js, Payload"}], "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}], "dynamicRoutes": [{"page": "/api/[...slug]", "regex": "^/api/(.+?)(?:/)?$", "routeKeys": {"nxtPslug": "nxtPslug"}, "namedRegex": "^/api/(?<nxtPslug>.+?)(?:/)?$"}], "staticRoutes": [{"page": "/my-route", "regex": "^/my\\-route(?:/)?$", "routeKeys": {}, "namedRegex": "^/my\\-route(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}