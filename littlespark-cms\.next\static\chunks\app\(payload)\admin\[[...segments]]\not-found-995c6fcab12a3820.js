(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1687,7250],{2144:(e,n,o)=>{Promise.resolve().then(o.bind(o,58405)),Promise.resolve().then(o.bind(o,42625)),Promise.resolve().then(o.bind(o,566)),Promise.resolve().then(o.bind(o,72823)),Promise.resolve().then(o.bind(o,1777)),Promise.resolve().then(o.bind(o,6005)),Promise.resolve().then(o.bind(o,47075)),Promise.resolve().then(o.bind(o,98552)),Promise.resolve().then(o.bind(o,24404)),Promise.resolve().then(o.bind(o,11981)),Promise.resolve().then(o.bind(o,87790)),Promise.resolve().then(o.bind(o,19557)),Promise.resolve().then(o.bind(o,88991)),Promise.resolve().then(o.bind(o,65582)),Promise.resolve().then(o.bind(o,27270)),Promise.resolve().then(o.bind(o,78229)),Promise.resolve().then(o.bind(o,70685)),Promise.resolve().then(o.bind(o,74289)),Promise.resolve().then(o.bind(o,42202)),Promise.resolve().then(o.bind(o,58029)),Promise.resolve().then(o.bind(o,97260)),Promise.resolve().then(o.bind(o,68539)),Promise.resolve().then(o.bind(o,56717)),Promise.resolve().then(o.bind(o,51593)),Promise.resolve().then(o.bind(o,66858)),Promise.resolve().then(o.bind(o,1611)),Promise.resolve().then(o.bind(o,85513)),Promise.resolve().then(o.bind(o,27181)),Promise.resolve().then(o.bind(o,2921)),Promise.resolve().then(o.bind(o,98574)),Promise.resolve().then(o.bind(o,79615)),Promise.resolve().then(o.bind(o,85849)),Promise.resolve().then(o.bind(o,18940)),Promise.resolve().then(o.bind(o,93974)),Promise.resolve().then(o.bind(o,55559)),Promise.resolve().then(o.bind(o,93253)),Promise.resolve().then(o.bind(o,77912)),Promise.resolve().then(o.bind(o,40402)),Promise.resolve().then(o.bind(o,6434)),Promise.resolve().then(o.bind(o,19958)),Promise.resolve().then(o.bind(o,81581)),Promise.resolve().then(o.bind(o,37305)),Promise.resolve().then(o.bind(o,77283)),Promise.resolve().then(o.bind(o,92825)),Promise.resolve().then(o.bind(o,90533))},90533:(e,n,o)=>{"use strict";o.d(n,{default:()=>s});var r=o(95155);o(12115);let s=()=>(0,r.jsx)("li",{style:{listStyle:"none"},children:(0,r.jsxs)("button",{onClick:()=>{window.location.href="/admin/sync"},style:{display:"flex",alignItems:"center",gap:"8px",width:"100%",padding:"8px 16px",backgroundColor:"#0070f3",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"14px",fontWeight:"500",textDecoration:"none",transition:"background-color 0.2s ease"},onMouseOver:e=>e.currentTarget.style.backgroundColor="#0056b3",onMouseOut:e=>e.currentTarget.style.backgroundColor="#0070f3",children:[(0,r.jsx)("span",{children:"\uD83D\uDD04"}),(0,r.jsx)("span",{children:"Sync to Main App"})]})})}},e=>{e.O(0,[8179,2434,6313,2984,6539,8441,5964,7358],()=>e(e.s=2144)),_N_E=e.O()}]);