import { NextRequest, NextResponse } from 'next/server';
import { getPayload } from 'payload';
import config from '@payload-config';

export interface AdminAuthResult {
  isAdmin: boolean;
  user?: unknown;
  error?: string;
}

/**
 * Middleware to check if the current user is a CMS admin
 * This ensures only admins can access sync functionality
 */
export async function checkAdminAccess(req: NextRequest): Promise<AdminAuthResult> {
  try {
    const payload = await getPayload({ config });
    
    // Get the authorization header
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return { 
        isAdmin: false, 
        error: 'No valid authorization token provided' 
      };
    }
    
    // Verify the token and get user
    const headers = new Headers();
    headers.set('authorization', authHeader);
    const { user } = await payload.auth({ headers });
    
    if (!user) {
      return { 
        isAdmin: false, 
        error: 'Invalid or expired token' 
      };
    }

    // Check if user has admin role
    if (user.role !== 'admin') {
      return { 
        isAdmin: false, 
        error: 'Admin access required. Only CMS administrators can perform sync operations.' 
      };
    }

    // Additional security checks
    if (!user.isActive) {
      return { 
        isAdmin: false, 
        error: 'Account is not active' 
      };
    }

    return { 
      isAdmin: true, 
      user 
    };

  } catch (error) {
    console.error('Admin auth check error:', error);
    return { 
      isAdmin: false, 
      error: 'Authentication failed' 
    };
  }
}

/**
 * Middleware to protect admin-only routes
 * Returns a 403 response if user is not an admin
 */
export async function requireAdminAccess(req: NextRequest): Promise<NextResponse | null> {
  const authResult = await checkAdminAccess(req);
  
  if (!authResult.isAdmin) {
    return NextResponse.json(
      { 
        success: false, 
        error: authResult.error,
        code: 'ADMIN_ACCESS_REQUIRED'
      },
      { status: 403 }
    );
  }

  return null; // No error, proceed with request
}

/**
 * Check if a user has specific permissions for sync operations
 */
export function hasPermission(user: unknown, permission: string): boolean {
  if (!user || (user as { role: string })?.role !== 'admin') {
    return false;
  }

  // Define permission mappings
  const permissions: Record<string, boolean> = {
    'sync:challenges': (user as { role: string })?.role === 'admin',
    'sync:users': (user as { role: string })?.role === 'admin',
    'sync:import': (user as { role: string })?.role === 'admin',
    'sync:view': (user as { role: string })?.role === 'admin',
  };

  return permissions[permission] || false;
}

/**
 * Log admin actions for audit trail
 */
  export function logAdminAction(user: unknown, action: string, details?: unknown) {
  const logEntry = {
    timestamp: new Date().toISOString(),
    userId: (user as { id: string }).id,
    userEmail: (user as { email: string }).email,
    action,
    details,
    ip: 'unknown', // Could be extracted from request
  };

  console.log('🔐 [ADMIN-ACTION]', JSON.stringify(logEntry));
  
  // In a production environment, you might want to store this in a database
  // or send to a logging service
}
