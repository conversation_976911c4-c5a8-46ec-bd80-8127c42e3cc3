import { NextRequest, NextResponse } from 'next/server';
import { getPayload } from 'payload';
import config from '@payload-config';

export interface AdminAuthResult {
  isAdmin: boolean;
  user?: any;
  error?: string;
}

/**
 * Middleware to check if the current user is a CMS admin
 * This ensures only admins can access sync functionality
 */
export async function checkAdminAccess(req: NextRequest): Promise<AdminAuthResult> {
  try {
    const payload = await getPayload({ config });
    
    // Get the authorization header
    const authHeader = req.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return { 
        isAdmin: false, 
        error: 'No valid authorization token provided' 
      };
    }

    const token = authHeader.substring(7);
    
    // Verify the token and get user
    const user = await payload.auth.verifyToken(token);
    
    if (!user) {
      return { 
        isAdmin: false, 
        error: 'Invalid or expired token' 
      };
    }

    // Check if user has admin role
    if (user.role !== 'admin') {
      return { 
        isAdmin: false, 
        error: 'Admin access required. Only CMS administrators can perform sync operations.' 
      };
    }

    // Additional security checks
    if (!user.isActive) {
      return { 
        isAdmin: false, 
        error: 'Account is not active' 
      };
    }

    return { 
      isAdmin: true, 
      user 
    };

  } catch (error) {
    console.error('Admin auth check error:', error);
    return { 
      isAdmin: false, 
      error: 'Authentication failed' 
    };
  }
}

/**
 * Middleware to protect admin-only routes
 * Returns a 403 response if user is not an admin
 */
export async function requireAdminAccess(req: NextRequest): Promise<NextResponse | null> {
  const authResult = await checkAdminAccess(req);
  
  if (!authResult.isAdmin) {
    return NextResponse.json(
      { 
        success: false, 
        error: authResult.error,
        code: 'ADMIN_ACCESS_REQUIRED'
      },
      { status: 403 }
    );
  }

  return null; // No error, proceed with request
}

/**
 * Check if a user has specific permissions for sync operations
 */
export function hasPermission(user: any, permission: string): boolean {
  if (!user || user.role !== 'admin') {
    return false;
  }

  // Define permission mappings
  const permissions = {
    'sync:challenges': user.role === 'admin',
    'sync:users': user.role === 'admin',
    'sync:import': user.role === 'admin',
    'sync:view': user.role === 'admin',
  };

  return permissions[permission] || false;
}

/**
 * Log admin actions for audit trail
 */
export function logAdminAction(user: any, action: string, details?: any) {
  const logEntry = {
    timestamp: new Date().toISOString(),
    userId: user.id,
    userEmail: user.email,
    action,
    details,
    ip: 'unknown', // Could be extracted from request
  };

  console.log('🔐 [ADMIN-ACTION]', JSON.stringify(logEntry));
  
  // In a production environment, you might want to store this in a database
  // or send to a logging service
}
