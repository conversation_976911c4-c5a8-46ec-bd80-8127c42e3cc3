'use client';

import { useState } from 'react';
import { CMSChallenge, getMediaUrl } from '@/lib/cms-api';
import { useSubscriptionStatus } from '@/hooks/useSubscriptionStatus';
import { ArrowLeft, Clock, Users, Star, CheckCircle } from 'lucide-react';
import { useRouter } from 'next/navigation';

interface ChallengeDetailClientProps {
  challenge: CMSChallenge;
}

export function ChallengeDetailClient({ challenge }: ChallengeDetailClientProps) {
  const [activeMediaIndex, setActiveMediaIndex] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const { hasActiveSubscription } = useSubscriptionStatus();
  const router = useRouter();

  const isLocked = challenge.subscriptionTier === 'premium' && !hasActiveSubscription;

  const handleStepComplete = (stepIndex: number) => {
    if (completedSteps.includes(stepIndex)) {
      setCompletedSteps(completedSteps.filter(i => i !== stepIndex));
    } else {
      setCompletedSteps([...completedSteps, stepIndex]);
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'art': return 'bg-pink-100 text-pink-800';
      case 'story': return 'bg-purple-100 text-purple-800';
      case 'music': return 'bg-green-100 text-green-800';
      case 'coding': return 'bg-blue-100 text-blue-800';
      case 'video': return 'bg-red-100 text-red-800';
      case 'game': return 'bg-indigo-100 text-indigo-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Parse instructions into steps
  const instructionSteps = challenge.instructions
    .split(/Step \d+:|^\d+\.|\n\d+\./)
    .filter(step => step.trim().length > 0)
    .map(step => step.trim());

  if (isLocked) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        {/* Header */}
        <div className="mb-6">
          <button
            onClick={() => router.back()}
            className="flex items-center gap-2 text-gray-600 hover:text-gray-800 mb-4"
          >
            <ArrowLeft className="w-4 h-4" />
            Back to Challenges
          </button>
        </div>

        {/* Locked Content */}
        <div className="bg-white rounded-xl shadow-lg overflow-hidden">
          <div className="relative">
            {challenge.media && challenge.media.length > 0 && (
              <img
                src={getMediaUrl(challenge.media[0].file.url)}
                alt={challenge.media[0].file.alt || challenge.title}
                className="w-full h-64 object-cover"
              />
            )}
            <div className="absolute inset-0 bg-black bg-opacity-60 flex items-center justify-center">
              <div className="text-center text-white">
                <div className="text-6xl mb-4">🔒</div>
                <h2 className="text-2xl font-bold mb-2">Premium Challenge</h2>
                <p className="text-lg mb-4">This challenge requires a premium subscription</p>
                <button
                  onClick={() => router.push('/checkout?plan=monthly-tier')}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                >
                  Upgrade to Premium
                </button>
              </div>
            </div>
          </div>
          
          <div className="p-6">
            <h1 className="text-3xl font-bold mb-4">{challenge.title}</h1>
            <div 
              className="text-gray-600 mb-4"
              dangerouslySetInnerHTML={{ __html: challenge.description }}
            />
            
            <div className="flex flex-wrap gap-2">
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getCategoryColor(challenge.category)}`}>
                {challenge.category}
              </span>
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(challenge.difficulty)}`}>
                {challenge.difficulty}
              </span>
              <span className="px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 flex items-center gap-1">
                <Clock className="w-3 h-3" />
                {challenge.estimatedTime} min
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      {/* Header */}
      <div className="mb-6">
        <button
          onClick={() => router.back()}
          className="flex items-center gap-2 text-gray-600 hover:text-gray-800 mb-4"
        >
          <ArrowLeft className="w-4 h-4" />
          Back to Challenges
        </button>
        
        <div className="flex flex-wrap gap-2 mb-4">
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getCategoryColor(challenge.category)}`}>
            {challenge.category}
          </span>
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getDifficultyColor(challenge.difficulty)}`}>
            {challenge.difficulty}
          </span>
          <span className="px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800 flex items-center gap-1">
            <Clock className="w-3 h-3" />
            {challenge.estimatedTime} min
          </span>
          {challenge.ageGroup && challenge.ageGroup.length > 0 && (
            <span className="px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 flex items-center gap-1">
              <Users className="w-3 h-3" />
              {challenge.ageGroup.join(', ')} years
            </span>
          )}
          {challenge.featured && (
            <span className="px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800 flex items-center gap-1">
              <Star className="w-3 h-3" />
              Featured
            </span>
          )}
        </div>
        
        <h1 className="text-4xl font-bold text-gray-800 mb-4">{challenge.title}</h1>
        <div 
          className="text-lg text-gray-600 mb-6"
          dangerouslySetInnerHTML={{ __html: challenge.description }}
        />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2">
          {/* Media Gallery */}
          {challenge.media && challenge.media.length > 0 && (
            <div className="mb-8">
              <h2 className="text-2xl font-bold mb-4">Visual Guide</h2>
              <div className="bg-white rounded-xl shadow-lg overflow-hidden">
                <div className="relative">
                  <img
                    src={getMediaUrl(challenge.media[activeMediaIndex].file.url)}
                    alt={challenge.media[activeMediaIndex].file.alt || challenge.title}
                    className="w-full h-64 object-cover"
                  />
                  {challenge.media[activeMediaIndex].type === 'tutorial' && (
                    <div className="absolute top-4 left-4 bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                      Tutorial
                    </div>
                  )}
                </div>
                
                {challenge.media[activeMediaIndex].caption && (
                  <div className="p-4 bg-gray-50">
                    <p className="text-sm text-gray-600">{challenge.media[activeMediaIndex].caption}</p>
                  </div>
                )}
                
                {challenge.media.length > 1 && (
                  <div className="p-4 flex gap-2 overflow-x-auto">
                    {challenge.media.map((media, index) => (
                      <button
                        key={index}
                        onClick={() => setActiveMediaIndex(index)}
                        className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 ${
                          index === activeMediaIndex ? 'border-blue-500' : 'border-gray-200'
                        }`}
                      >
                        <img
                          src={getMediaUrl(media.file.url)}
                          alt={media.file.alt || `Step ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Instructions */}
          <div className="mb-8">
            <h2 className="text-2xl font-bold mb-4">Step-by-Step Instructions</h2>
            <div className="bg-white rounded-xl shadow-lg p-6">
              {instructionSteps.length > 1 ? (
                <div className="space-y-4">
                  {instructionSteps.map((step, index) => (
                    <div key={index} className="flex items-start gap-4">
                      <button
                        onClick={() => handleStepComplete(index)}
                        className={`flex-shrink-0 w-8 h-8 rounded-full border-2 flex items-center justify-center transition-colors ${
                          completedSteps.includes(index)
                            ? 'bg-green-500 border-green-500 text-white'
                            : 'border-gray-300 hover:border-green-500'
                        }`}
                      >
                        {completedSteps.includes(index) ? (
                          <CheckCircle className="w-4 h-4" />
                        ) : (
                          <span className="text-sm font-medium">{index + 1}</span>
                        )}
                      </button>
                      <div className="flex-1">
                        <div 
                          className={`${
                            completedSteps.includes(index) ? 'line-through text-gray-500' : ''
                          }`}
                          dangerouslySetInnerHTML={{ __html: step }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div dangerouslySetInnerHTML={{ __html: challenge.instructions }} />
              )}
            </div>
          </div>
        </div>

        {/* Sidebar */}
        <div className="lg:col-span-1">
          {/* Learning Objectives */}
          {challenge.learningObjectives && challenge.learningObjectives.length > 0 && (
            <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
              <h3 className="text-xl font-bold mb-4">What You'll Learn</h3>
              <ul className="space-y-2">
                {challenge.learningObjectives.map((obj, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
                    <span className="text-gray-700">{obj.objective}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Materials */}
          {challenge.materials && challenge.materials.length > 0 && (
            <div className="bg-white rounded-xl shadow-lg p-6 mb-6">
              <h3 className="text-xl font-bold mb-4">Materials Needed</h3>
              <ul className="space-y-2">
                {challenge.materials.map((material, index) => (
                  <li key={index} className="flex items-center gap-2">
                    <span className={`w-2 h-2 rounded-full ${
                      material.optional ? 'bg-yellow-500' : 'bg-green-500'
                    }`}></span>
                    <span className={material.optional ? 'text-gray-600' : 'text-gray-800'}>
                      {material.material}
                      {material.optional && <span className="text-xs text-gray-500 ml-1">(optional)</span>}
                    </span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Progress */}
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h3 className="text-xl font-bold mb-4">Your Progress</h3>
            <div className="mb-4">
              <div className="flex justify-between text-sm text-gray-600 mb-2">
                <span>Steps Completed</span>
                <span>{completedSteps.length}/{instructionSteps.length}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-500 h-2 rounded-full transition-all duration-300"
                  style={{ 
                    width: `${(completedSteps.length / instructionSteps.length) * 100}%` 
                  }}
                ></div>
              </div>
            </div>
            
            {completedSteps.length === instructionSteps.length && (
              <div className="text-center">
                <div className="text-4xl mb-2">🎉</div>
                <p className="text-green-600 font-medium">Challenge Complete!</p>
                <p className="text-sm text-gray-600">Great job finishing this challenge!</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
