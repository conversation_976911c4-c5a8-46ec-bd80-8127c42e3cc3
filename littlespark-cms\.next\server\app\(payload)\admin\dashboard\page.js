(()=>{var e={};e.id=7331,e.ids=[7331],e.modules={91043:e=>{"use strict";e.exports=require("@aws-sdk/client-s3")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},82015:e=>{"use strict";e.exports=require("react")},22326:e=>{"use strict";e.exports=require("react-dom")},8732:e=>{"use strict";e.exports=require("react/jsx-runtime")},79428:e=>{"use strict";e.exports=require("buffer")},79646:e=>{"use strict";e.exports=require("child_process")},55511:e=>{"use strict";e.exports=require("crypto")},14985:e=>{"use strict";e.exports=require("dns")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},73496:e=>{"use strict";e.exports=require("http2")},55591:e=>{"use strict";e.exports=require("https")},8086:e=>{"use strict";e.exports=require("module")},91645:e=>{"use strict";e.exports=require("net")},21820:e=>{"use strict";e.exports=require("os")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},11997:e=>{"use strict";e.exports=require("punycode")},4984:e=>{"use strict";e.exports=require("readline")},27910:e=>{"use strict";e.exports=require("stream")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},28855:e=>{"use strict";e.exports=import("@libsql/client")},84739:e=>{"use strict";e.exports=import("@payloadcms/next/layouts")},34589:e=>{"use strict";e.exports=require("node:assert")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},4573:e=>{"use strict";e.exports=require("node:buffer")},37540:e=>{"use strict";e.exports=require("node:console")},77598:e=>{"use strict";e.exports=require("node:crypto")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},40610:e=>{"use strict";e.exports=require("node:dns")},78474:e=>{"use strict";e.exports=require("node:events")},73024:e=>{"use strict";e.exports=require("node:fs")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},37067:e=>{"use strict";e.exports=require("node:http")},32467:e=>{"use strict";e.exports=require("node:http2")},98995:e=>{"use strict";e.exports=require("node:module")},77030:e=>{"use strict";e.exports=require("node:net")},48161:e=>{"use strict";e.exports=require("node:os")},76760:e=>{"use strict";e.exports=require("node:path")},643:e=>{"use strict";e.exports=require("node:perf_hooks")},1708:e=>{"use strict";e.exports=require("node:process")},41792:e=>{"use strict";e.exports=require("node:querystring")},80099:e=>{"use strict";e.exports=require("node:sqlite")},57075:e=>{"use strict";e.exports=require("node:stream")},37830:e=>{"use strict";e.exports=require("node:stream/web")},41692:e=>{"use strict";e.exports=require("node:tls")},73136:e=>{"use strict";e.exports=require("node:url")},57975:e=>{"use strict";e.exports=require("node:util")},73429:e=>{"use strict";e.exports=require("node:util/types")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},38522:e=>{"use strict";e.exports=require("node:zlib")},23766:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>d,pages:()=>c,routeModule:()=>f,tree:()=>l});var n=r(70260),o=r(28203),i=r(25155),s=r.n(i),a=r(67292),u={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>a[e]);r.d(t,u);let l=["",{children:["(payload)",{children:["admin",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,22472)),"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\dashboard\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,19136)),"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"]}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"]}],c=["C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\dashboard\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},f=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/(payload)/admin/dashboard/page",pathname:"/admin/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},80312:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{"40fb2ba5fdcf9f0094269b16676dc4cb1768812611":()=>o.$$RSC_SERVER_ACTION_0});var o=r(19136),i=e([o]);o=(i.then?(await i)():i)[0],n()}catch(e){n(e)}})},36932:(e,t,r)=>{Promise.resolve().then(r.bind(r,81764))},90084:(e,t,r)=>{Promise.resolve().then(r.bind(r,94768))},85821:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,13219,23)),Promise.resolve().then(r.t.bind(r,34863,23)),Promise.resolve().then(r.t.bind(r,25155,23)),Promise.resolve().then(r.t.bind(r,9350,23)),Promise.resolve().then(r.t.bind(r,96313,23)),Promise.resolve().then(r.t.bind(r,48530,23)),Promise.resolve().then(r.t.bind(r,88921,23))},13437:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,66959,23)),Promise.resolve().then(r.t.bind(r,33875,23)),Promise.resolve().then(r.t.bind(r,88903,23)),Promise.resolve().then(r.t.bind(r,84178,23)),Promise.resolve().then(r.t.bind(r,86013,23)),Promise.resolve().then(r.t.bind(r,87190,23)),Promise.resolve().then(r.t.bind(r,61365,23))},39268:(e,t,r)=>{Promise.resolve().then(r.bind(r,22472))},69124:(e,t,r)=>{Promise.resolve().then(r.bind(r,19416))},54380:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return i}});let n=r(74147),o=r(54887);function i(e,t){return(0,o.normalizePathTrailingSlash)((0,n.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},68485:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return n}}),r(54887);let n=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},69793:(e,t,r)=>{"use strict";function n(e,t,r,n){return!1}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getDomainLocale",{enumerable:!0,get:function(){return n}}),r(54887),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25119:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return o}});let n=r(58610);function o(e){return(0,n.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},87801:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return x}});let n=r(25488),o=r(8732),i=n._(r(82015)),s=r(78919),a=r(25285),u=r(35024),l=r(43438),c=r(68485),d=r(84055),f=r(47829),p=r(59118),h=r(69793),g=r(54380),m=r(45267),y=r(73727);function _(e){return"string"==typeof e?e:(0,u.formatUrl)(e)}let x=i.default.forwardRef(function(e,t){let r,n;let{href:u,as:x,children:b,prefetch:v=null,passHref:E,replace:R,shallow:S,scroll:P,locale:A,onClick:O,onMouseEnter:C,onTouchStart:j,legacyBehavior:M=!1,...T}=e;r=b,M&&("string"==typeof r||"number"==typeof r)&&(r=(0,o.jsx)("a",{children:r}));let w=i.default.useContext(d.RouterContext),I=i.default.useContext(f.AppRouterContext),N=null!=w?w:I,D=!w,k=!1!==v,q=null===v?m.PrefetchKind.AUTO:m.PrefetchKind.FULL,{href:L,as:U}=i.default.useMemo(()=>{if(!w){let e=_(u);return{href:e,as:x?_(x):e}}let[e,t]=(0,s.resolveHref)(w,u,!0);return{href:e,as:x?(0,s.resolveHref)(w,x):t||e}},[w,u,x]),F=i.default.useRef(L),B=i.default.useRef(U);M&&(n=i.default.Children.only(r));let W=M?n&&"object"==typeof n&&n.ref:t,[X,G,z]=(0,p.useIntersection)({rootMargin:"200px"}),H=i.default.useCallback(e=>{(B.current!==U||F.current!==L)&&(z(),B.current=U,F.current=L),X(e)},[U,L,z,X]),Y=(0,y.useMergedRef)(H,W);i.default.useEffect(()=>{},[U,L,G,A,k,null==w?void 0:w.locale,N,D,q]);let K={ref:Y,onClick(e){M||"function"!=typeof O||O(e),M&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),N&&!e.defaultPrevented&&function(e,t,r,n,o,s,u,l,c){let{nodeName:d}=e.currentTarget;if("A"===d.toUpperCase()&&(function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||!c&&!(0,a.isLocalURL)(r)))return;e.preventDefault();let f=()=>{let e=null==u||u;"beforePopState"in t?t[o?"replace":"push"](r,n,{shallow:s,locale:l,scroll:e}):t[o?"replace":"push"](n||r,{scroll:e})};c?i.default.startTransition(f):f()}(e,N,L,U,R,S,P,A,D)},onMouseEnter(e){M||"function"!=typeof C||C(e),M&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e)},onTouchStart:function(e){M||"function"!=typeof j||j(e),M&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e)}};if((0,l.isAbsoluteUrl)(U))K.href=U;else if(!M||E||"a"===n.type&&!("href"in n.props)){let e=void 0!==A?A:null==w?void 0:w.locale,t=(null==w?void 0:w.isLocaleDomain)&&(0,h.getDomainLocale)(U,e,null==w?void 0:w.locales,null==w?void 0:w.domainLocales);K.href=t||(0,g.addBasePath)((0,c.addLocale)(U,e,null==w?void 0:w.defaultLocale))}return M?i.default.cloneElement(n,K):(0,o.jsx)("a",{...T,...K,children:r})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},54887:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return i}});let n=r(35612),o=r(31546),i=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:r,hash:i}=(0,o.parsePath)(e);return""+(0,n.removeTrailingSlash)(t)+r+i};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},28903:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return n},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},78919:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"resolveHref",{enumerable:!0,get:function(){return d}});let n=r(33866),o=r(35024),i=r(7258),s=r(43438),a=r(54887),u=r(25285),l=r(31335),c=r(75669);function d(e,t,r){let d;let f="string"==typeof t?t:(0,o.formatWithValidation)(t),p=f.match(/^[a-zA-Z]{1,}:\/\//),h=p?f.slice(p[0].length):f;if((h.split("?",1)[0]||"").match(/(\/\/|\\)/)){console.error("Invalid href '"+f+"' passed to next/router in page: '"+e.pathname+"'. Repeated forward-slashes (//) or backslashes \\ are not valid in the href.");let t=(0,s.normalizeRepeatedSlashes)(h);f=(p?p[0]:"")+t}if(!(0,u.isLocalURL)(f))return r?[f]:f;try{d=new URL(f.startsWith("#")?e.asPath:e.pathname,"http://n")}catch(e){d=new URL("/","http://n")}try{let e=new URL(f,d);e.pathname=(0,a.normalizePathTrailingSlash)(e.pathname);let t="";if((0,l.isDynamicRoute)(e.pathname)&&e.searchParams&&r){let r=(0,n.searchParamsToUrlQuery)(e.searchParams),{result:s,params:a}=(0,c.interpolateAs)(e.pathname,e.pathname,r);s&&(t=(0,o.formatWithValidation)({pathname:s,hash:e.hash,query:(0,i.omit)(r,a)}))}let s=e.origin===d.origin?e.href.slice(e.origin.length):e.href;return r?[s,t||s]:s}catch(e){return r?[f]:f}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59118:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useIntersection",{enumerable:!0,get:function(){return u}});let n=r(82015),o=r(28903),i="function"==typeof IntersectionObserver,s=new Map,a=[];function u(e){let{rootRef:t,rootMargin:r,disabled:u}=e,l=u||!i,[c,d]=(0,n.useState)(!1),f=(0,n.useRef)(null),p=(0,n.useCallback)(e=>{f.current=e},[]);return(0,n.useEffect)(()=>{if(i){if(l||c)return;let e=f.current;if(e&&e.tagName)return function(e,t,r){let{id:n,observer:o,elements:i}=function(e){let t;let r={root:e.root||null,margin:e.rootMargin||""},n=a.find(e=>e.root===r.root&&e.margin===r.margin);if(n&&(t=s.get(n)))return t;let o=new Map;return t={id:r,observer:new IntersectionObserver(e=>{e.forEach(e=>{let t=o.get(e.target),r=e.isIntersecting||e.intersectionRatio>0;t&&r&&t(r)})},e),elements:o},a.push(r),s.set(r,t),t}(r);return i.set(e,t),o.observe(e),function(){if(i.delete(e),o.unobserve(e),0===i.size){o.disconnect(),s.delete(n);let e=a.findIndex(e=>e.root===n.root&&e.margin===n.margin);e>-1&&a.splice(e,1)}}}(e,e=>e&&d(e),{root:null==t?void 0:t.current,rootMargin:r})}else if(!c){let e=(0,o.requestIdleCallback)(()=>d(!0));return()=>(0,o.cancelIdleCallback)(e)}},[l,r,t,c,f.current]),[p,c,(0,n.useCallback)(()=>{d(!1)},[])]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},73727:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let n=r(82015);function o(e,t){let r=(0,n.useRef)(()=>{}),o=(0,n.useRef)(()=>{});return(0,n.useMemo)(()=>e&&t?n=>{null===n?(r.current(),o.current()):(r.current=i(e,n),o.current=i(t,n))}:e||t,[e,t])}function i(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59204:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_SUFFIX:function(){return d},APP_DIR_ALIAS:function(){return w},CACHE_ONE_YEAR:function(){return S},DOT_NEXT_ALIAS:function(){return M},ESLINT_DEFAULT_DIRS:function(){return Q},GSP_NO_RETURNED_VALUE:function(){return z},GSSP_COMPONENT_MEMBER_ERROR:function(){return K},GSSP_NO_RETURNED_VALUE:function(){return H},INFINITE_CACHE:function(){return P},INSTRUMENTATION_HOOK_FILENAME:function(){return C},MATCHED_PATH_HEADER:function(){return o},MIDDLEWARE_FILENAME:function(){return A},MIDDLEWARE_LOCATION_REGEXP:function(){return O},NEXT_BODY_SUFFIX:function(){return h},NEXT_CACHE_IMPLICIT_TAG_ID:function(){return R},NEXT_CACHE_REVALIDATED_TAGS_HEADER:function(){return y},NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:function(){return _},NEXT_CACHE_SOFT_TAGS_HEADER:function(){return m},NEXT_CACHE_SOFT_TAG_MAX_LENGTH:function(){return E},NEXT_CACHE_TAGS_HEADER:function(){return g},NEXT_CACHE_TAG_MAX_ITEMS:function(){return b},NEXT_CACHE_TAG_MAX_LENGTH:function(){return v},NEXT_DATA_SUFFIX:function(){return f},NEXT_INTERCEPTION_MARKER_PREFIX:function(){return n},NEXT_META_SUFFIX:function(){return p},NEXT_QUERY_PARAM_PREFIX:function(){return r},NEXT_RESUME_HEADER:function(){return x},NON_STANDARD_NODE_ENV:function(){return V},PAGES_DIR_ALIAS:function(){return j},PRERENDER_REVALIDATE_HEADER:function(){return i},PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:function(){return s},PUBLIC_DIR_MIDDLEWARE_CONFLICT:function(){return U},ROOT_DIR_ALIAS:function(){return T},RSC_ACTION_CLIENT_WRAPPER_ALIAS:function(){return L},RSC_ACTION_ENCRYPTION_ALIAS:function(){return q},RSC_ACTION_PROXY_ALIAS:function(){return D},RSC_ACTION_VALIDATE_ALIAS:function(){return N},RSC_CACHE_WRAPPER_ALIAS:function(){return k},RSC_MOD_REF_PROXY_ALIAS:function(){return I},RSC_PREFETCH_SUFFIX:function(){return a},RSC_SEGMENTS_DIR_SUFFIX:function(){return u},RSC_SEGMENT_SUFFIX:function(){return l},RSC_SUFFIX:function(){return c},SERVER_PROPS_EXPORT_ERROR:function(){return G},SERVER_PROPS_GET_INIT_PROPS_CONFLICT:function(){return B},SERVER_PROPS_SSG_CONFLICT:function(){return W},SERVER_RUNTIME:function(){return Z},SSG_FALLBACK_EXPORT_ERROR:function(){return $},SSG_GET_INITIAL_PROPS_CONFLICT:function(){return F},STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:function(){return X},UNSTABLE_REVALIDATE_RENAME_ERROR:function(){return Y},WEBPACK_LAYERS:function(){return ee},WEBPACK_RESOURCE_QUERIES:function(){return et}});let r="nxtP",n="nxtI",o="x-matched-path",i="x-prerender-revalidate",s="x-prerender-revalidate-if-generated",a=".prefetch.rsc",u=".segments",l=".segment.rsc",c=".rsc",d=".action",f=".json",p=".meta",h=".body",g="x-next-cache-tags",m="x-next-cache-soft-tags",y="x-next-revalidated-tags",_="x-next-revalidate-tag-token",x="next-resume",b=64,v=256,E=1024,R="_N_T_",S=31536e3,P=0xfffffffe,A="middleware",O=`(?:src/)?${A}`,C="instrumentation",j="private-next-pages",M="private-dot-next",T="private-next-root-dir",w="private-next-app-dir",I="private-next-rsc-mod-ref-proxy",N="private-next-rsc-action-validate",D="private-next-rsc-server-reference",k="private-next-rsc-cache-wrapper",q="private-next-rsc-action-encryption",L="private-next-rsc-action-client-wrapper",U="You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",F="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",B="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",W="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",X="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",G="pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",z="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",H="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",Y="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",K="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",V='You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',$="Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",Q=["app","pages","components","lib","src"],Z={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"},J={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",api:"api",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser"},ee={...J,GROUP:{builtinReact:[J.reactServerComponents,J.actionBrowser],serverOnly:[J.reactServerComponents,J.actionBrowser,J.instrument,J.middleware],neutralTarget:[J.api],clientOnly:[J.serverSideRendering,J.appPagesBrowser],bundled:[J.reactServerComponents,J.actionBrowser,J.serverSideRendering,J.appPagesBrowser,J.shared,J.instrument],appPages:[J.reactServerComponents,J.serverSideRendering,J.appPagesBrowser,J.actionBrowser]}},et={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},84055:(e,t,r)=>{"use strict";e.exports=r(8104).vendored.contexts.RouterContext},880:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return o}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function o(e){return r.test(e)?e.replace(n,"\\$&"):e}},74147:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return o}});let n=r(31546);function o(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:o,hash:i}=(0,n.parsePath)(e);return""+t+r+o+i}},35024:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return i},formatWithValidation:function(){return a},urlObjectKeys:function(){return s}});let n=r(81063)._(r(33866)),o=/https?|ftp|gopher|file/;function i(e){let{auth:t,hostname:r}=e,i=e.protocol||"",s=e.pathname||"",a=e.hash||"",u=e.query||"",l=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?l=t+e.host:r&&(l=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(l+=":"+e.port)),u&&"object"==typeof u&&(u=String(n.urlQueryToSearchParams(u)));let c=e.search||u&&"?"+u||"";return i&&!i.endsWith(":")&&(i+=":"),e.slashes||(!i||o.test(i))&&!1!==l?(l="//"+(l||""),s&&"/"!==s[0]&&(s="/"+s)):l||(l=""),a&&"#"!==a[0]&&(a="#"+a),c&&"?"!==c[0]&&(c="?"+c),""+i+l+(s=s.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+a}let s=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function a(e){return i(e)}},31335:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getSortedRouteObjects:function(){return n.getSortedRouteObjects},getSortedRoutes:function(){return n.getSortedRoutes},isDynamicRoute:function(){return o.isDynamicRoute}});let n=r(92509),o=r(36431)},75669:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"interpolateAs",{enumerable:!0,get:function(){return i}});let n=r(16885),o=r(43004);function i(e,t,r){let i="",s=(0,o.getRouteRegex)(e),a=s.groups,u=(t!==e?(0,n.getRouteMatcher)(s)(t):"")||r;i=e;let l=Object.keys(a);return l.every(e=>{let t=u[e]||"",{repeat:r,optional:n}=a[e],o="["+(r?"...":"")+e+"]";return n&&(o=(t?"":"/")+"["+o+"]"),r&&!Array.isArray(t)&&(t=[t]),(n||e in u)&&(i=i.replace(o,r?t.map(e=>encodeURIComponent(e)).join("/"):encodeURIComponent(t))||"/")})||(i=""),{params:l,result:i}}},36431:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isDynamicRoute",{enumerable:!0,get:function(){return i}});let n=r(15640),o=/\/\[[^/]+?\](?=\/|$)/;function i(e){return(0,n.isInterceptionRouteAppPath)(e)&&(e=(0,n.extractInterceptionRouteInformation)(e).interceptedRoute),o.test(e)}},25285:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return i}});let n=r(43438),o=r(25119);function i(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,o.hasBasePath)(r.pathname)}catch(e){return!1}}},7258:(e,t)=>{"use strict";function r(e,t){let r={};return Object.keys(e).forEach(n=>{t.includes(n)||(r[n]=e[n])}),r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"omit",{enumerable:!0,get:function(){return r}})},31546:(e,t)=>{"use strict";function r(e){let t=e.indexOf("#"),r=e.indexOf("?"),n=r>-1&&(t<0||r<t);return n||t>-1?{pathname:e.substring(0,n?r:t),query:n?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return r}})},58610:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return o}});let n=r(31546);function o(e,t){if("string"!=typeof e)return!1;let{pathname:r}=(0,n.parsePath)(e);return r===t||r.startsWith(t+"/")}},33866:(e,t)=>{"use strict";function r(e){let t={};return e.forEach((e,r)=>{void 0===t[r]?t[r]=e:Array.isArray(t[r])?t[r].push(e):t[r]=[t[r],e]}),t}function n(e){return"string"!=typeof e&&("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[r,o]=e;Array.isArray(o)?o.forEach(e=>t.append(r,n(e))):t.set(r,n(o))}),t}function i(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return r.forEach(t=>{Array.from(t.keys()).forEach(t=>e.delete(t)),t.forEach((t,r)=>e.append(r,t))}),e}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return i},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return o}})},35612:(e,t)=>{"use strict";function r(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return r}})},16885:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return o}});let n=r(43438);function o(e){let{re:t,groups:r}=e;return e=>{let o=t.exec(e);if(!o)return!1;let i=e=>{try{return decodeURIComponent(e)}catch(e){throw new n.DecodeError("failed to decode param")}},s={};return Object.keys(r).forEach(e=>{let t=r[e],n=o[t.pos];void 0!==n&&(s[e]=~n.indexOf("/")?n.split("/").map(e=>i(e)):t.repeat?[i(n)]:i(n))}),s}}},43004:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return g},getNamedRouteRegex:function(){return h},getRouteRegex:function(){return d},parseParameter:function(){return u}});let n=r(59204),o=r(15640),i=r(880),s=r(35612),a=/\[((?:\[.*\])|.+)\]/;function u(e){let t=e.match(a);return t?l(t[1]):l(e)}function l(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function c(e){let t=(0,s.removeTrailingSlash)(e).slice(1).split("/"),r={},n=1;return{parameterizedRoute:t.map(e=>{let t=o.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t)),s=e.match(a);if(t&&s){let{key:e,optional:o,repeat:a}=l(s[1]);return r[e]={pos:n++,repeat:a,optional:o},"/"+(0,i.escapeStringRegexp)(t)+"([^/]+?)"}if(!s)return"/"+(0,i.escapeStringRegexp)(e);{let{key:e,repeat:t,optional:o}=l(s[1]);return r[e]={pos:n++,repeat:t,optional:o},t?o?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:r}}function d(e){let{parameterizedRoute:t,groups:r}=c(e);return{re:RegExp("^"+t+"(?:/)?$"),groups:r}}function f(e){let{interceptionMarker:t,getSafeRouteKey:r,segment:n,routeKeys:o,keyPrefix:s}=e,{key:a,optional:u,repeat:c}=l(n),d=a.replace(/\W/g,"");s&&(d=""+s+d);let f=!1;(0===d.length||d.length>30)&&(f=!0),isNaN(parseInt(d.slice(0,1)))||(f=!0),f&&(d=r()),s?o[d]=""+s+a:o[d]=a;let p=t?(0,i.escapeStringRegexp)(t):"";return c?u?"(?:/"+p+"(?<"+d+">.+?))?":"/"+p+"(?<"+d+">.+?)":"/"+p+"(?<"+d+">[^/]+?)"}function p(e,t){let r;let a=(0,s.removeTrailingSlash)(e).slice(1).split("/"),u=(r=0,()=>{let e="",t=++r;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),l={};return{namedParameterizedRoute:a.map(e=>{let r=o.INTERCEPTION_ROUTE_MARKERS.some(t=>e.startsWith(t)),s=e.match(/\[((?:\[.*\])|.+)\]/);if(r&&s){let[r]=e.split(s[0]);return f({getSafeRouteKey:u,interceptionMarker:r,segment:s[1],routeKeys:l,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0})}return s?f({getSafeRouteKey:u,segment:s[1],routeKeys:l,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0}):"/"+(0,i.escapeStringRegexp)(e)}).join(""),routeKeys:l}}function h(e,t){let r=p(e,t);return{...d(e),namedRegex:"^"+r.namedParameterizedRoute+"(?:/)?$",routeKeys:r.routeKeys}}function g(e,t){let{parameterizedRoute:r}=c(e),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:o}=p(e,!1);return{namedRegex:"^"+o+(n?"(?:(/.*)?)":"")+"$"}}},92509:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getSortedRouteObjects:function(){return o},getSortedRoutes:function(){return n}});class r{insert(e){this._insert(e.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){void 0===e&&(e="/");let t=[...this.children.keys()].sort();null!==this.slugName&&t.splice(t.indexOf("[]"),1),null!==this.restSlugName&&t.splice(t.indexOf("[...]"),1),null!==this.optionalRestSlugName&&t.splice(t.indexOf("[[...]]"),1);let r=t.map(t=>this.children.get(t)._smoosh(""+e+t+"/")).reduce((e,t)=>[...e,...t],[]);if(null!==this.slugName&&r.push(...this.children.get("[]")._smoosh(e+"["+this.slugName+"]/")),!this.placeholder){let t="/"===e?"/":e.slice(0,-1);if(null!=this.optionalRestSlugName)throw Error('You cannot define a route with the same specificity as a optional catch-all route ("'+t+'" and "'+t+"[[..."+this.optionalRestSlugName+']]").');r.unshift(t)}return null!==this.restSlugName&&r.push(...this.children.get("[...]")._smoosh(e+"[..."+this.restSlugName+"]/")),null!==this.optionalRestSlugName&&r.push(...this.children.get("[[...]]")._smoosh(e+"[[..."+this.optionalRestSlugName+"]]/")),r}_insert(e,t,n){if(0===e.length){this.placeholder=!1;return}if(n)throw Error("Catch-all must be the last part of the URL.");let o=e[0];if(o.startsWith("[")&&o.endsWith("]")){let r=o.slice(1,-1),s=!1;if(r.startsWith("[")&&r.endsWith("]")&&(r=r.slice(1,-1),s=!0),r.startsWith("…"))throw Error("Detected a three-dot character ('…') at ('"+r+"'). Did you mean ('...')?");if(r.startsWith("...")&&(r=r.substring(3),n=!0),r.startsWith("[")||r.endsWith("]"))throw Error("Segment names may not start or end with extra brackets ('"+r+"').");if(r.startsWith("."))throw Error("Segment names may not start with erroneous periods ('"+r+"').");function i(e,r){if(null!==e&&e!==r)throw Error("You cannot use different slug names for the same dynamic path ('"+e+"' !== '"+r+"').");t.forEach(e=>{if(e===r)throw Error('You cannot have the same slug name "'+r+'" repeat within a single dynamic path');if(e.replace(/\W/g,"")===o.replace(/\W/g,""))throw Error('You cannot have the slug names "'+e+'" and "'+r+'" differ only by non-word symbols within a single dynamic path')}),t.push(r)}if(n){if(s){if(null!=this.restSlugName)throw Error('You cannot use both an required and optional catch-all route at the same level ("[...'+this.restSlugName+']" and "'+e[0]+'" ).');i(this.optionalRestSlugName,r),this.optionalRestSlugName=r,o="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Error('You cannot use both an optional and required catch-all route at the same level ("[[...'+this.optionalRestSlugName+']]" and "'+e[0]+'").');i(this.restSlugName,r),this.restSlugName=r,o="[...]"}}else{if(s)throw Error('Optional route parameters are not yet supported ("'+e[0]+'").');i(this.slugName,r),this.slugName=r,o="[]"}}this.children.has(o)||this.children.set(o,new r),this.children.get(o)._insert(e.slice(1),t,n)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}}function n(e){let t=new r;return e.forEach(e=>t.insert(e)),t.smoosh()}function o(e,t){let r={},o=[];for(let n=0;n<e.length;n++){let i=t(e[n]);r[i]=n,o[n]=i}return n(o).map(t=>e[r[t]])}},43438:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return _},MissingStaticPage:function(){return y},NormalizeError:function(){return g},PageNotFoundError:function(){return m},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return u},getLocationOrigin:function(){return s},getURL:function(){return a},isAbsoluteUrl:function(){return i},isResSent:function(){return l},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return x}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return r||(r=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=e=>o.test(e);function s(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function a(){let{href:e}=window.location,t=s();return e.substring(t.length)}function u(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function l(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&l(r))return n;if(!n)throw Error('"'+u(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.');return n}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class g extends Error{}class m extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class _ extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function x(e){return JSON.stringify({message:e.message,stack:e.stack})}},19416:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(8732);r(82015);var o=r(58686),i=r(87801),s=r.n(i);function a(){let e=(0,o.useRouter)();return(0,n.jsxs)("div",{style:{padding:"20px",maxWidth:"1200px",margin:"0 auto"},children:[(0,n.jsxs)("div",{style:{marginBottom:"30px",textAlign:"center"},children:[(0,n.jsx)("h1",{style:{fontSize:"32px",fontWeight:"bold",marginBottom:"10px",color:"#333"},children:"\uD83C\uDFAF Little Spark CMS"}),(0,n.jsx)("p",{style:{color:"#666",fontSize:"18px",marginBottom:"20px"},children:"Content Management System Dashboard"}),(0,n.jsxs)("button",{type:"button",onClick:()=>{e.push("/admin/sync")},style:{backgroundColor:"#0070f3",color:"white",border:"none",padding:"15px 30px",borderRadius:"8px",fontSize:"16px",fontWeight:"600",cursor:"pointer",boxShadow:"0 4px 12px rgba(0,0,0,0.15)",display:"inline-flex",alignItems:"center",gap:"10px",transition:"all 0.3s ease",marginBottom:"30px"},onMouseOver:e=>{e.currentTarget.style.backgroundColor="#0056b3",e.currentTarget.style.transform="translateY(-2px)",e.currentTarget.style.boxShadow="0 6px 20px rgba(0,0,0,0.2)"},onMouseOut:e=>{e.currentTarget.style.backgroundColor="#0070f3",e.currentTarget.style.transform="translateY(0)",e.currentTarget.style.boxShadow="0 4px 12px rgba(0,0,0,0.15)"},children:[(0,n.jsx)("span",{style:{fontSize:"20px"},children:"\uD83D\uDD04"}),(0,n.jsx)("span",{children:"Open Sync Management Panel"})]})]}),(0,n.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(250px, 1fr))",gap:"20px",marginBottom:"30px"},children:[(0,n.jsxs)("div",{style:{backgroundColor:"#f8f9fa",padding:"20px",borderRadius:"12px",textAlign:"center",border:"1px solid #e9ecef"},children:[(0,n.jsx)("div",{style:{fontSize:"24px",marginBottom:"10px"},children:"\uD83D\uDCDD"}),(0,n.jsx)("h3",{style:{color:"#495057",marginBottom:"5px"},children:"Challenges"}),(0,n.jsx)("p",{style:{color:"#6c757d",fontSize:"14px"},children:"Manage creative challenges"})]}),(0,n.jsxs)("div",{style:{backgroundColor:"#f8f9fa",padding:"20px",borderRadius:"12px",textAlign:"center",border:"1px solid #e9ecef"},children:[(0,n.jsx)("div",{style:{fontSize:"24px",marginBottom:"10px"},children:"\uD83D\uDCDA"}),(0,n.jsx)("h3",{style:{color:"#495057",marginBottom:"5px"},children:"Story Templates"}),(0,n.jsx)("p",{style:{color:"#6c757d",fontSize:"14px"},children:"Create story templates"})]}),(0,n.jsxs)("div",{style:{backgroundColor:"#f8f9fa",padding:"20px",borderRadius:"12px",textAlign:"center",border:"1px solid #e9ecef"},children:[(0,n.jsx)("div",{style:{fontSize:"24px",marginBottom:"10px"},children:"\uD83C\uDF93"}),(0,n.jsx)("h3",{style:{color:"#495057",marginBottom:"5px"},children:"Educational Resources"}),(0,n.jsx)("p",{style:{color:"#6c757d",fontSize:"14px"},children:"Learning materials"})]}),(0,n.jsxs)("div",{style:{backgroundColor:"#f8f9fa",padding:"20px",borderRadius:"12px",textAlign:"center",border:"1px solid #e9ecef"},children:[(0,n.jsx)("div",{style:{fontSize:"24px",marginBottom:"10px"},children:"\uD83D\uDC65"}),(0,n.jsx)("h3",{style:{color:"#495057",marginBottom:"5px"},children:"Users"}),(0,n.jsx)("p",{style:{color:"#6c757d",fontSize:"14px"},children:"Manage CMS users"})]})]}),(0,n.jsxs)("div",{style:{backgroundColor:"#fff3cd",border:"1px solid #ffeaa7",borderRadius:"8px",padding:"20px",textAlign:"center"},children:[(0,n.jsx)("h3",{style:{color:"#856404",marginBottom:"10px"},children:"\uD83D\uDE80 Quick Actions"}),(0,n.jsx)("p",{style:{color:"#856404",fontSize:"14px",marginBottom:"15px"},children:"Use the navigation menu to access collections, or click the Sync button above to manage content synchronization."}),(0,n.jsxs)("div",{style:{display:"flex",justifyContent:"center",gap:"10px",flexWrap:"wrap"},children:[(0,n.jsx)(s(),{href:"/admin/collections/challenges",style:{backgroundColor:"#28a745",color:"white",padding:"8px 16px",borderRadius:"6px",textDecoration:"none",fontSize:"14px",fontWeight:"500"},children:"\uD83D\uDCDD Challenges"}),(0,n.jsx)(s(),{href:"/admin/collections/story-templates",style:{backgroundColor:"#007bff",color:"white",padding:"8px 16px",borderRadius:"6px",textDecoration:"none",fontSize:"14px",fontWeight:"500"},children:"\uD83D\uDCDA Story Templates"}),(0,n.jsx)(s(),{href:"/admin/collections/educational-resources",style:{backgroundColor:"#17a2b8",color:"white",padding:"8px 16px",borderRadius:"6px",textDecoration:"none",fontSize:"14px",fontWeight:"500"},children:"\uD83C\uDF93 Educational Resources"})]})]})]})}},94768:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var n=r(8732);r(82015);let o=()=>(0,n.jsx)("li",{style:{listStyle:"none"},children:(0,n.jsxs)("button",{onClick:()=>{window.location.href="/admin/sync"},style:{display:"flex",alignItems:"center",gap:"8px",width:"100%",padding:"8px 16px",backgroundColor:"#0070f3",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"14px",fontWeight:"500",textDecoration:"none",transition:"background-color 0.2s ease"},onMouseOver:e=>e.currentTarget.style.backgroundColor="#0056b3",onMouseOut:e=>e.currentTarget.style.backgroundColor="#0070f3",children:[(0,n.jsx)("span",{children:"\uD83D\uDD04"}),(0,n.jsx)("span",{children:"Sync to Main App"})]})})},88977:(e,t,r)=>{"use strict";Object.defineProperty(t,"A",{enumerable:!0,get:function(){return n.registerServerReference}});let n=r(46760)},54175:(e,t,r)=>{"use strict";let n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{arrayBufferToString:function(){return s},decrypt:function(){return l},encrypt:function(){return u},getActionEncryptionKey:function(){return h},getClientReferenceManifestForRsc:function(){return p},getServerModuleMap:function(){return f},setReferenceManifestsSingleton:function(){return d},stringToUint8Array:function(){return a}});let o=r(39212),i=r(29294);function s(e){let t=new Uint8Array(e),r=t.byteLength;if(r<65535)return String.fromCharCode.apply(null,t);let n="";for(let e=0;e<r;e++)n+=String.fromCharCode(t[e]);return n}function a(e){let t=e.length,r=new Uint8Array(t);for(let n=0;n<t;n++)r[n]=e.charCodeAt(n);return r}function u(e,t,r){return crypto.subtle.encrypt({name:"AES-GCM",iv:t},e,r)}function l(e,t,r){return crypto.subtle.decrypt({name:"AES-GCM",iv:t},e,r)}let c=Symbol.for("next.server.action-manifests");function d({page:e,clientReferenceManifest:t,serverActionsManifest:r,serverModuleMap:n}){var o;let i=null==(o=globalThis[c])?void 0:o.clientReferenceManifestsPerPage;globalThis[c]={clientReferenceManifestsPerPage:{...i,[g(e)]:t},serverActionsManifest:r,serverModuleMap:n}}function f(){let e=globalThis[c];if(!e)throw new o.InvariantError("Missing manifest for Server Actions.");return e.serverModuleMap}function p(){let e=globalThis[c];if(!e)throw new o.InvariantError("Missing manifest for Server Actions.");let{clientReferenceManifestsPerPage:t}=e,r=i.workAsyncStorage.getStore();if(!r)return function(e){let t=Object.values(e),r={clientModules:{},edgeRscModuleMapping:{},rscModuleMapping:{}};for(let e of t)r.clientModules={...r.clientModules,...e.clientModules},r.edgeRscModuleMapping={...r.edgeRscModuleMapping,...e.edgeRscModuleMapping},r.rscModuleMapping={...r.rscModuleMapping,...e.rscModuleMapping};return r}(t);let n=g(r.page),s=t[n];if(!s)throw new o.InvariantError(`Missing Client Reference Manifest for ${n}.`);return s}async function h(){if(n)return n;let e=globalThis[c];if(!e)throw new o.InvariantError("Missing manifest for Server Actions.");let t=process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY||e.serverActionsManifest.encryptionKey;if(void 0===t)throw new o.InvariantError("Missing encryption key for Server Actions");return n=await crypto.subtle.importKey("raw",a(atob(t)),"AES-GCM",!0,["encrypt","decrypt"])}function g(e){return e.replace(/\/(page|route)$/,"")}},98063:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{decryptActionBoundArgs:function(){return f},encryptActionBoundArgs:function(){return d}}),r(27315);let n=r(46760),o=r(8534),i=r(57212),s=r(54175),a=new TextEncoder,u=new TextDecoder;async function l(e,t){let r=await (0,s.getActionEncryptionKey)();if(void 0===r)throw Error("Missing encryption key for Server Action. This is a bug in Next.js");let n=atob(t),o=n.slice(0,16),i=n.slice(16),a=u.decode(await (0,s.decrypt)(r,(0,s.stringToUint8Array)(o),(0,s.stringToUint8Array)(i)));if(!a.startsWith(e))throw Error("Invalid Server Action payload: failed to decrypt.");return a.slice(e.length)}async function c(e,t){let r=await (0,s.getActionEncryptionKey)();if(void 0===r)throw Error("Missing encryption key for Server Action. This is a bug in Next.js");let n=new Uint8Array(16);crypto.getRandomValues(n);let o=(0,s.arrayBufferToString)(n.buffer),i=await (0,s.encrypt)(r,n,a.encode(e+t));return btoa(o+(0,s.arrayBufferToString)(i))}async function d(e,t){let{clientModules:r}=(0,s.getClientReferenceManifestForRsc)(),o=await (0,i.streamToString)((0,n.renderToReadableStream)(t,r));return await c(e,o)}async function f(e,t){let{edgeRscModuleMapping:r,rscModuleMapping:n}=(0,s.getClientReferenceManifestForRsc)(),i=await l(e,await t);return await (0,o.createFromReadableStream)(new ReadableStream({start(e){e.enqueue(a.encode(i)),e.close()}}),{serverConsumerManifest:{moduleLoading:null,moduleMap:n,serverModuleMap:(0,s.getServerModuleMap)()}})}},22472:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\dashboard\\page.tsx","default")},19136:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{$$RSC_SERVER_ACTION_0:()=>c,default:()=>f});var o=r(8732),i=r(88977);r(98063);var s=r(17750);r(63046);var a=r(84739);r(82015);var u=r(3401);r(44023);var l=e([s,a]);[s,a]=l.then?(await l)():l;let c=async function(e){return(0,a.handleServerFunctions)({...e,config:s.A,importMap:u.m})},d=(0,i.A)(c,"40fb2ba5fdcf9f0094269b16676dc4cb1768812611",null),f=({children:e})=>(0,o.jsx)(a.RootLayout,{config:s.A,importMap:u.m,serverFunction:d,children:e});n()}catch(e){n(e)}})},81764:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\components\\\\SyncButton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\components\\SyncButton.tsx","default")},63046:()=>{},44023:()=>{},3401:(e,t,r)=>{"use strict";r.d(t,{m:()=>n});let n={"/components/SyncButton#default":r(81764).default}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[5994,2259,1112,7750],()=>r(23766));module.exports=n})();