/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/sync/challenges/route";
exports.ids = ["app/api/sync/challenges/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsync%2Fchallenges%2Froute&page=%2Fapi%2Fsync%2Fchallenges%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsync%2Fchallenges%2Froute.ts&appDir=C%3A%5CUsers%5Cravi1%5Ckavya-git%5Cspark-new%5Clittlespark-cms%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cravi1%5Ckavya-git%5Cspark-new%5Clittlespark-cms&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsync%2Fchallenges%2Froute&page=%2Fapi%2Fsync%2Fchallenges%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsync%2Fchallenges%2Froute.ts&appDir=C%3A%5CUsers%5Cravi1%5Ckavya-git%5Cspark-new%5Clittlespark-cms%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cravi1%5Ckavya-git%5Cspark-new%5Clittlespark-cms&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var C_Users_ravi1_kavya_git_spark_new_littlespark_cms_src_app_api_sync_challenges_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/sync/challenges/route.ts */ \"(rsc)/./src/app/api/sync/challenges/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_ravi1_kavya_git_spark_new_littlespark_cms_src_app_api_sync_challenges_route_ts__WEBPACK_IMPORTED_MODULE_16__]);\nC_Users_ravi1_kavya_git_spark_new_littlespark_cms_src_app_api_sync_challenges_route_ts__WEBPACK_IMPORTED_MODULE_16__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/sync/challenges/route\",\n        pathname: \"/api/sync/challenges\",\n        filename: \"route\",\n        bundlePath: \"app/api/sync/challenges/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\api\\\\sync\\\\challenges\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_ravi1_kavya_git_spark_new_littlespark_cms_src_app_api_sync_challenges_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/sync/challenges/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsync%2Fchallenges%2Froute&page=%2Fapi%2Fsync%2Fchallenges%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsync%2Fchallenges%2Froute.ts&appDir=C%3A%5CUsers%5Cravi1%5Ckavya-git%5Cspark-new%5Clittlespark-cms%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cravi1%5Ckavya-git%5Cspark-new%5Clittlespark-cms&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/sync/challenges/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/sync/challenges/route.ts ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var payload__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! payload */ \"payload\");\n/* harmony import */ var _payload_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @payload-config */ \"(rsc)/./src/payload.config.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([payload__WEBPACK_IMPORTED_MODULE_1__, _payload_config__WEBPACK_IMPORTED_MODULE_2__]);\n([payload__WEBPACK_IMPORTED_MODULE_1__, _payload_config__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst MAIN_APP_BASE_URL = process.env.MAIN_APP_BASE_URL || 'http://localhost:3000';\n// POST /api/sync/challenges - Sync CMS challenges to main application\nasync function POST(request) {\n    try {\n        // For now, skip auth check to test functionality\n        // TODO: Implement proper auth after CMS is fully setup\n        console.log('🔄 [CMS-SYNC] Challenge sync request received');\n        console.log('🔄 [CMS-SYNC] Starting CMS to Main App challenge sync');\n        const payload = await (0,payload__WEBPACK_IMPORTED_MODULE_1__.getPayload)({\n            config: _payload_config__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        });\n        // Fetch all published challenges from CMS\n        const cmsResponse = await payload.find({\n            collection: 'challenges',\n            where: {\n                status: {\n                    equals: 'published'\n                }\n            },\n            limit: 1000\n        });\n        const cmsChallenge = cmsResponse.docs;\n        console.log(`📊 [CMS-SYNC] Found ${cmsChallenge.length} published challenges in CMS`);\n        let syncedCount = 0;\n        let skippedCount = 0;\n        let errorCount = 0;\n        // Sync each challenge to main application\n        for (const challenge of cmsChallenge){\n            try {\n                // Convert CMS challenge to main app format\n                const mainAppChallenge = {\n                    cms_id: challenge.id,\n                    title: challenge.title,\n                    description: challenge.description,\n                    difficulty: challenge.difficulty,\n                    category: challenge.category,\n                    type: challenge.category,\n                    prompt: challenge.prompt || challenge.instructions || challenge.description,\n                    is_active: challenge.is_active !== false && challenge.status === 'published',\n                    created_by: 'cms',\n                    valid_until: null\n                };\n                // Send to main application API\n                console.log(`🔄 [CMS-SYNC] Sending challenge to main app:`, {\n                    cms_id: mainAppChallenge.cms_id,\n                    title: mainAppChallenge.title,\n                    category: mainAppChallenge.category\n                });\n                const response = await fetch(`${MAIN_APP_BASE_URL}/api/challenges/sync-from-cms`, {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${process.env.CMS_SYNC_TOKEN}`\n                    },\n                    body: JSON.stringify(mainAppChallenge)\n                });\n                if (response.ok) {\n                    const responseData = await response.json();\n                    console.log(`✅ [CMS-SYNC] Synced challenge: ${challenge.title}`, responseData);\n                    syncedCount++;\n                } else {\n                    const errorText = await response.text();\n                    console.error(`❌ [CMS-SYNC] Failed to sync challenge ${challenge.title}:`, {\n                        status: response.status,\n                        statusText: response.statusText,\n                        error: errorText\n                    });\n                    errorCount++;\n                }\n            } catch (error) {\n                console.error(`❌ [CMS-SYNC] Error syncing challenge ${challenge.title}:`, error);\n                errorCount++;\n            }\n        }\n        console.log(`🎉 [CMS-SYNC] Sync complete: ${syncedCount} synced, ${skippedCount} skipped, ${errorCount} errors`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'CMS challenges synced to main application',\n            stats: {\n                total: cmsChallenge.length,\n                synced: syncedCount,\n                skipped: skippedCount,\n                errors: errorCount\n            }\n        });\n    } catch (error) {\n        console.error('❌ [CMS-SYNC] Error syncing CMS challenges:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to sync CMS challenges to main application',\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n// GET /api/sync/challenges - Check sync status\nasync function GET(request) {\n    try {\n        // For now, skip auth check to test functionality\n        console.log('📊 [CMS-SYNC] Getting challenge sync stats');\n        const payload = await (0,payload__WEBPACK_IMPORTED_MODULE_1__.getPayload)({\n            config: _payload_config__WEBPACK_IMPORTED_MODULE_2__[\"default\"]\n        });\n        // Count CMS challenges\n        const cmsCount = await payload.count({\n            collection: 'challenges',\n            where: {\n                status: {\n                    equals: 'published'\n                }\n            }\n        });\n        // Get main app challenge count (challenges that came from CMS)\n        let mainAppCount = 0;\n        try {\n            const mainAppResponse = await fetch(`${MAIN_APP_BASE_URL}/api/challenges/debug`, {\n                headers: {\n                    'Authorization': `Bearer ${process.env.CMS_SYNC_TOKEN}`\n                }\n            });\n            if (mainAppResponse.ok) {\n                const data = await mainAppResponse.json();\n                mainAppCount = data.stats?.cms || 0;\n                console.log('📊 [CMS-SYNC] Main app stats:', data.stats);\n            } else {\n                console.log('⚠️ [CMS-SYNC] Main app response not OK:', mainAppResponse.status);\n            }\n        } catch (error) {\n            console.log('⚠️ [CMS-SYNC] Could not fetch main app count:', error instanceof Error ? error.message : 'Unknown error');\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            stats: {\n                cmsChallenge: cmsCount.totalDocs,\n                mainAppCmsChallenge: mainAppCount,\n                mainAppTotalChallenge: 0,\n                lastSync: 'Not implemented yet' // Could add a sync log table\n            }\n        });\n    } catch (error) {\n        console.error('Error checking sync status:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: 'Failed to check sync status',\n            details: error instanceof Error ? error.message : 'Unknown error'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/sync/challenges/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/collections/Challenges.ts":
/*!***************************************!*\
  !*** ./src/collections/Challenges.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Challenges: () => (/* binding */ Challenges)\n/* harmony export */ });\nconst Challenges = {\n    slug: 'challenges',\n    admin: {\n        useAsTitle: 'title',\n        defaultColumns: [\n            'title',\n            'category',\n            'ageGroup',\n            'difficulty',\n            'status',\n            'publishedAt'\n        ],\n        group: 'Content'\n    },\n    access: {\n        // Content creators and admins can create challenges\n        create: ({ req: { user } })=>{\n            return user?.role === 'admin' || user?.role === 'content-creator' || user?.role === 'educator';\n        },\n        // For now, allow public read access - we'll filter in the API\n        read: ()=>true,\n        // Users can update their own challenges, admins can update all\n        update: ({ req: { user } })=>{\n            if (user?.role === 'admin') return true;\n            if (user?.role === 'content-creator' || user?.role === 'educator') {\n                return {\n                    createdBy: {\n                        equals: user.id\n                    }\n                };\n            }\n            return false;\n        },\n        // Only admins can delete challenges\n        delete: ({ req: { user } })=>{\n            return user?.role === 'admin';\n        }\n    },\n    fields: [\n        {\n            name: 'title',\n            type: 'text',\n            required: true,\n            admin: {\n                description: 'The main title of the creative challenge'\n            }\n        },\n        {\n            name: 'slug',\n            type: 'text',\n            required: true,\n            unique: true,\n            admin: {\n                description: 'URL-friendly version of the title'\n            },\n            hooks: {\n                beforeValidate: [\n                    ({ value, data })=>{\n                        if (!value && data?.title) {\n                            return data.title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');\n                        }\n                        return value;\n                    }\n                ]\n            }\n        },\n        {\n            name: 'description',\n            type: 'textarea',\n            required: true,\n            admin: {\n                description: 'Brief description of what children will create'\n            }\n        },\n        {\n            name: 'prompt',\n            type: 'textarea',\n            required: true,\n            admin: {\n                description: 'The main challenge prompt/question for users (required for Supabase compatibility)'\n            }\n        },\n        {\n            name: 'category',\n            type: 'select',\n            required: true,\n            options: [\n                {\n                    label: '🎨 Art & Drawing',\n                    value: 'art'\n                },\n                {\n                    label: '📚 Creative Writing',\n                    value: 'story'\n                },\n                {\n                    label: '🎵 Music & Sound',\n                    value: 'music'\n                },\n                {\n                    label: '🎮 Game Design',\n                    value: 'game'\n                },\n                {\n                    label: '💻 Coding & Logic',\n                    value: 'coding'\n                },\n                {\n                    label: '🎬 Video Creation',\n                    value: 'video'\n                }\n            ],\n            admin: {\n                description: 'Choose the primary category - this determines which tool opens when users click \"Start Challenge\"',\n                position: 'sidebar'\n            }\n        },\n        {\n            name: 'ageGroup',\n            type: 'select',\n            required: true,\n            hasMany: true,\n            options: [\n                {\n                    label: '6-8 years',\n                    value: '6-8'\n                },\n                {\n                    label: '9-11 years',\n                    value: '9-11'\n                },\n                {\n                    label: '12-14 years',\n                    value: '12-14'\n                }\n            ],\n            admin: {\n                description: 'Age groups this challenge is suitable for'\n            }\n        },\n        {\n            name: 'difficulty',\n            type: 'select',\n            required: true,\n            options: [\n                {\n                    label: 'Easy',\n                    value: 'easy'\n                },\n                {\n                    label: 'Medium',\n                    value: 'medium'\n                },\n                {\n                    label: 'Hard',\n                    value: 'hard'\n                }\n            ],\n            admin: {\n                description: 'Difficulty level for children'\n            }\n        },\n        {\n            name: 'estimatedTime',\n            type: 'number',\n            required: true,\n            min: 5,\n            max: 120,\n            admin: {\n                description: 'Estimated completion time in minutes',\n                step: 5\n            }\n        },\n        {\n            name: 'instructions',\n            type: 'textarea',\n            required: true,\n            admin: {\n                description: 'Step-by-step instructions for completing the challenge',\n                rows: 8\n            }\n        },\n        {\n            name: 'learningObjectives',\n            type: 'array',\n            required: true,\n            minRows: 1,\n            maxRows: 5,\n            fields: [\n                {\n                    name: 'objective',\n                    type: 'text',\n                    required: true\n                }\n            ],\n            admin: {\n                description: 'What children will learn from this challenge'\n            }\n        },\n        {\n            name: 'materials',\n            type: 'array',\n            fields: [\n                {\n                    name: 'material',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'optional',\n                    type: 'checkbox',\n                    defaultValue: false\n                }\n            ],\n            admin: {\n                description: 'Materials needed to complete the challenge'\n            }\n        },\n        {\n            name: 'media',\n            type: 'array',\n            fields: [\n                {\n                    name: 'file',\n                    type: 'upload',\n                    relationTo: 'media',\n                    required: true\n                },\n                {\n                    name: 'caption',\n                    type: 'text'\n                },\n                {\n                    name: 'type',\n                    type: 'select',\n                    options: [\n                        {\n                            label: 'Tutorial Video',\n                            value: 'tutorial'\n                        },\n                        {\n                            label: 'Example Image',\n                            value: 'example'\n                        },\n                        {\n                            label: 'Reference Material',\n                            value: 'reference'\n                        },\n                        {\n                            label: 'Step Image',\n                            value: 'step'\n                        }\n                    ],\n                    required: true\n                },\n                {\n                    name: 'order',\n                    type: 'number',\n                    defaultValue: 0,\n                    admin: {\n                        description: 'Display order (0 = first)'\n                    }\n                }\n            ],\n            admin: {\n                description: 'Images, videos, and other media for this challenge'\n            }\n        },\n        {\n            name: 'subscriptionTier',\n            type: 'select',\n            required: true,\n            options: [\n                {\n                    label: 'Free',\n                    value: 'free'\n                },\n                {\n                    label: 'Premium',\n                    value: 'premium'\n                }\n            ],\n            defaultValue: 'premium',\n            admin: {\n                description: 'Whether this challenge requires a subscription'\n            }\n        },\n        {\n            name: 'featured',\n            type: 'checkbox',\n            defaultValue: false,\n            admin: {\n                description: 'Show this challenge prominently on the homepage'\n            }\n        },\n        {\n            name: 'seasonal',\n            type: 'group',\n            fields: [\n                {\n                    name: 'isSeasonalContent',\n                    type: 'checkbox',\n                    defaultValue: false\n                },\n                {\n                    name: 'season',\n                    type: 'select',\n                    options: [\n                        {\n                            label: 'Spring',\n                            value: 'spring'\n                        },\n                        {\n                            label: 'Summer',\n                            value: 'summer'\n                        },\n                        {\n                            label: 'Fall/Autumn',\n                            value: 'fall'\n                        },\n                        {\n                            label: 'Winter',\n                            value: 'winter'\n                        },\n                        {\n                            label: 'Halloween',\n                            value: 'halloween'\n                        },\n                        {\n                            label: 'Christmas',\n                            value: 'christmas'\n                        },\n                        {\n                            label: 'New Year',\n                            value: 'newyear'\n                        }\n                    ],\n                    admin: {\n                        condition: (_, siblingData)=>siblingData?.isSeasonalContent\n                    }\n                }\n            ]\n        },\n        {\n            name: 'is_active',\n            type: 'checkbox',\n            defaultValue: true,\n            admin: {\n                description: 'Whether this challenge is active and available to users (Supabase compatibility)',\n                position: 'sidebar'\n            }\n        },\n        {\n            name: 'status',\n            type: 'select',\n            required: true,\n            options: [\n                {\n                    label: 'Draft',\n                    value: 'draft'\n                },\n                {\n                    label: 'Under Review',\n                    value: 'review'\n                },\n                {\n                    label: 'Published',\n                    value: 'published'\n                },\n                {\n                    label: 'Archived',\n                    value: 'archived'\n                }\n            ],\n            defaultValue: 'draft',\n            admin: {\n                description: 'Publication status of this challenge'\n            }\n        },\n        {\n            name: 'publishedAt',\n            type: 'date',\n            admin: {\n                condition: (data)=>data.status === 'published',\n                description: 'When this challenge was published'\n            }\n        },\n        {\n            name: 'createdBy',\n            type: 'relationship',\n            relationTo: 'users',\n            required: true,\n            admin: {\n                description: 'Content creator who made this challenge',\n                position: 'sidebar'\n            },\n            access: {\n                update: ({ req: { user } })=>{\n                    // Only admins can change the creator\n                    return user?.role === 'admin';\n                }\n            }\n        },\n        // Sync-related fields for bidirectional sync\n        {\n            name: 'mainAppId',\n            type: 'text',\n            admin: {\n                description: 'ID from main application (for synced challenges)',\n                readOnly: true,\n                position: 'sidebar'\n            }\n        },\n        {\n            name: 'isUserGenerated',\n            type: 'checkbox',\n            defaultValue: false,\n            admin: {\n                description: 'Whether this challenge was created by users in the main app',\n                readOnly: true,\n                position: 'sidebar'\n            }\n        },\n        {\n            name: 'syncStatus',\n            type: 'select',\n            options: [\n                {\n                    label: 'Not Synced',\n                    value: 'not_synced'\n                },\n                {\n                    label: 'Synced to Main App',\n                    value: 'synced_to_main'\n                },\n                {\n                    label: 'Synced from Main App',\n                    value: 'synced_from_main'\n                },\n                {\n                    label: 'Sync Failed',\n                    value: 'sync_failed'\n                }\n            ],\n            defaultValue: 'not_synced',\n            admin: {\n                description: 'Synchronization status with main application',\n                position: 'sidebar'\n            }\n        }\n    ],\n    hooks: {\n        beforeChange: [\n            ({ data, operation, req })=>{\n                // Auto-set publishedAt when status changes to published\n                if (data.status === 'published' && !data.publishedAt) {\n                    data.publishedAt = new Date();\n                }\n                // Set createdBy on creation if not already set\n                if (operation === 'create' && req.user && !data.createdBy) {\n                    data.createdBy = req.user.id;\n                }\n                return data;\n            }\n        ],\n        beforeValidate: [\n            ({ data, operation, req })=>{\n                // Ensure createdBy is set for new challenges\n                if (operation === 'create' && req.user && data && !data.createdBy) {\n                    data.createdBy = req.user.id;\n                }\n                return data;\n            }\n        ]\n    },\n    versions: {\n        drafts: true,\n        maxPerDoc: 10\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/collections/Challenges.ts\n");

/***/ }),

/***/ "(rsc)/./src/collections/EducationalResources.ts":
/*!*************************************************!*\
  !*** ./src/collections/EducationalResources.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EducationalResources: () => (/* binding */ EducationalResources)\n/* harmony export */ });\nconst EducationalResources = {\n    slug: 'educational-resources',\n    dbName: 'edu_resources',\n    admin: {\n        useAsTitle: 'title',\n        defaultColumns: [\n            'title',\n            'type',\n            'subject',\n            'ageGroup',\n            'status'\n        ],\n        group: 'Content'\n    },\n    access: {\n        read: ()=>true,\n        create: ({ req: { user } })=>{\n            return user?.role === 'content-creator' || user?.role === 'admin';\n        },\n        update: ({ req: { user } })=>{\n            return user?.role === 'content-creator' || user?.role === 'admin';\n        },\n        delete: ({ req: { user } })=>{\n            return user?.role === 'admin';\n        }\n    },\n    fields: [\n        {\n            name: 'title',\n            type: 'text',\n            required: true,\n            admin: {\n                description: 'Title of the educational resource'\n            }\n        },\n        {\n            name: 'slug',\n            type: 'text',\n            required: true,\n            unique: true,\n            hooks: {\n                beforeValidate: [\n                    ({ value, data })=>{\n                        if (!value && data?.title) {\n                            return data.title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');\n                        }\n                        return value;\n                    }\n                ]\n            }\n        },\n        {\n            name: 'description',\n            type: 'textarea',\n            required: true,\n            admin: {\n                description: 'Brief description of the resource'\n            }\n        },\n        {\n            name: 'type',\n            type: 'select',\n            required: true,\n            options: [\n                {\n                    label: 'Tutorial',\n                    value: 'tutorial'\n                },\n                {\n                    label: 'Guide',\n                    value: 'guide'\n                },\n                {\n                    label: 'Reference Sheet',\n                    value: 'reference'\n                },\n                {\n                    label: 'Video Lesson',\n                    value: 'video'\n                },\n                {\n                    label: 'Interactive Activity',\n                    value: 'interactive'\n                },\n                {\n                    label: 'Worksheet',\n                    value: 'worksheet'\n                },\n                {\n                    label: 'Tips & Tricks',\n                    value: 'tips'\n                }\n            ]\n        },\n        {\n            name: 'subject',\n            type: 'select',\n            required: true,\n            hasMany: true,\n            options: [\n                {\n                    label: 'Art Techniques',\n                    value: 'art-techniques'\n                },\n                {\n                    label: 'Color Theory',\n                    value: 'color-theory'\n                },\n                {\n                    label: 'Drawing Basics',\n                    value: 'drawing-basics'\n                },\n                {\n                    label: 'Creative Writing',\n                    value: 'creative-writing'\n                },\n                {\n                    label: 'Storytelling',\n                    value: 'storytelling'\n                },\n                {\n                    label: 'Music Theory',\n                    value: 'music-theory'\n                },\n                {\n                    label: 'Coding Basics',\n                    value: 'coding-basics'\n                },\n                {\n                    label: 'Game Design',\n                    value: 'game-design'\n                },\n                {\n                    label: 'Video Creation',\n                    value: 'video-creation'\n                },\n                {\n                    label: 'Digital Art',\n                    value: 'digital-art'\n                }\n            ]\n        },\n        {\n            name: 'ageGroup',\n            type: 'select',\n            required: true,\n            hasMany: true,\n            options: [\n                {\n                    label: '6-8 years',\n                    value: '6-8'\n                },\n                {\n                    label: '9-11 years',\n                    value: '9-11'\n                },\n                {\n                    label: '12-14 years',\n                    value: '12-14'\n                }\n            ]\n        },\n        {\n            name: 'content',\n            type: 'textarea',\n            required: true,\n            admin: {\n                description: 'Main content of the educational resource',\n                rows: 10\n            }\n        },\n        {\n            name: 'media',\n            type: 'array',\n            fields: [\n                {\n                    name: 'file',\n                    type: 'upload',\n                    relationTo: 'media',\n                    required: true\n                },\n                {\n                    name: 'caption',\n                    type: 'text'\n                },\n                {\n                    name: 'type',\n                    type: 'select',\n                    options: [\n                        {\n                            label: 'Main Image',\n                            value: 'main'\n                        },\n                        {\n                            label: 'Step Image',\n                            value: 'step'\n                        },\n                        {\n                            label: 'Example',\n                            value: 'example'\n                        },\n                        {\n                            label: 'Video',\n                            value: 'video'\n                        },\n                        {\n                            label: 'Audio',\n                            value: 'audio'\n                        },\n                        {\n                            label: 'Downloadable',\n                            value: 'download'\n                        }\n                    ],\n                    required: true\n                }\n            ]\n        },\n        {\n            name: 'downloadableFiles',\n            type: 'array',\n            dbName: 'downloads',\n            fields: [\n                {\n                    name: 'file',\n                    type: 'upload',\n                    relationTo: 'media',\n                    required: true\n                },\n                {\n                    name: 'title',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'description',\n                    type: 'text'\n                },\n                {\n                    name: 'fileType',\n                    type: 'select',\n                    dbName: 'file_type',\n                    options: [\n                        {\n                            label: 'PDF Worksheet',\n                            value: 'pdf'\n                        },\n                        {\n                            label: 'Template',\n                            value: 'template'\n                        },\n                        {\n                            label: 'Reference Chart',\n                            value: 'chart'\n                        },\n                        {\n                            label: 'Audio File',\n                            value: 'audio'\n                        },\n                        {\n                            label: 'Video File',\n                            value: 'video'\n                        }\n                    ]\n                }\n            ],\n            admin: {\n                description: 'Files that children/parents can download'\n            }\n        },\n        {\n            name: 'relatedChallenges',\n            type: 'relationship',\n            relationTo: 'challenges',\n            hasMany: true,\n            admin: {\n                description: 'Challenges that use this educational resource'\n            }\n        },\n        {\n            name: 'prerequisites',\n            type: 'array',\n            fields: [\n                {\n                    name: 'skill',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'level',\n                    type: 'select',\n                    options: [\n                        {\n                            label: 'Beginner',\n                            value: 'beginner'\n                        },\n                        {\n                            label: 'Intermediate',\n                            value: 'intermediate'\n                        },\n                        {\n                            label: 'Advanced',\n                            value: 'advanced'\n                        }\n                    ]\n                }\n            ],\n            admin: {\n                description: 'Skills children should have before using this resource'\n            }\n        },\n        {\n            name: 'learningOutcomes',\n            type: 'array',\n            required: true,\n            fields: [\n                {\n                    name: 'outcome',\n                    type: 'text',\n                    required: true\n                }\n            ],\n            admin: {\n                description: 'What children will learn from this resource'\n            }\n        },\n        {\n            name: 'estimatedReadTime',\n            type: 'number',\n            admin: {\n                description: 'Estimated reading/viewing time in minutes'\n            }\n        },\n        {\n            name: 'difficulty',\n            type: 'select',\n            required: true,\n            options: [\n                {\n                    label: 'Beginner',\n                    value: 'beginner'\n                },\n                {\n                    label: 'Intermediate',\n                    value: 'intermediate'\n                },\n                {\n                    label: 'Advanced',\n                    value: 'advanced'\n                }\n            ]\n        },\n        {\n            name: 'subscriptionTier',\n            type: 'select',\n            required: true,\n            options: [\n                {\n                    label: 'Free',\n                    value: 'free'\n                },\n                {\n                    label: 'Premium',\n                    value: 'premium'\n                }\n            ],\n            defaultValue: 'free'\n        },\n        {\n            name: 'featured',\n            type: 'checkbox',\n            defaultValue: false\n        },\n        {\n            name: 'tags',\n            type: 'array',\n            fields: [\n                {\n                    name: 'tag',\n                    type: 'text',\n                    required: true\n                }\n            ],\n            admin: {\n                description: 'Tags for better searchability'\n            }\n        },\n        {\n            name: 'status',\n            type: 'select',\n            required: true,\n            options: [\n                {\n                    label: 'Draft',\n                    value: 'draft'\n                },\n                {\n                    label: 'Under Review',\n                    value: 'review'\n                },\n                {\n                    label: 'Published',\n                    value: 'published'\n                },\n                {\n                    label: 'Archived',\n                    value: 'archived'\n                }\n            ],\n            defaultValue: 'draft'\n        },\n        {\n            name: 'publishedAt',\n            type: 'date',\n            admin: {\n                condition: (data)=>data.status === 'published'\n            }\n        },\n        {\n            name: 'createdBy',\n            type: 'relationship',\n            relationTo: 'users',\n            admin: {\n                readOnly: true\n            }\n        }\n    ],\n    hooks: {\n        beforeChange: [\n            ({ data, operation, req })=>{\n                if (data.status === 'published' && !data.publishedAt) {\n                    data.publishedAt = new Date();\n                }\n                if (operation === 'create' && req.user) {\n                    data.createdBy = req.user.id;\n                }\n                return data;\n            }\n        ]\n    },\n    versions: {\n        drafts: true,\n        maxPerDoc: 5\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/collections/EducationalResources.ts\n");

/***/ }),

/***/ "(rsc)/./src/collections/Media.ts":
/*!**********************************!*\
  !*** ./src/collections/Media.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Media: () => (/* binding */ Media)\n/* harmony export */ });\nconst Media = {\n    slug: 'media',\n    access: {\n        read: ()=>true\n    },\n    fields: [\n        {\n            name: 'alt',\n            type: 'text',\n            required: true\n        }\n    ],\n    upload: true\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29sbGVjdGlvbnMvTWVkaWEudHMiLCJtYXBwaW5ncyI6Ijs7OztBQUVPLE1BQU1BLFFBQTBCO0lBQ3JDQyxNQUFNO0lBQ05DLFFBQVE7UUFDTkMsTUFBTSxJQUFNO0lBQ2Q7SUFDQUMsUUFBUTtRQUNOO1lBQ0VDLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxVQUFVO1FBQ1o7S0FDRDtJQUNEQyxRQUFRO0FBQ1YsRUFBQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxyYXZpMVxca2F2eWEtZ2l0XFxzcGFyay1uZXdcXGxpdHRsZXNwYXJrLWNtc1xcc3JjXFxjb2xsZWN0aW9uc1xcTWVkaWEudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBDb2xsZWN0aW9uQ29uZmlnIH0gZnJvbSAncGF5bG9hZCdcblxuZXhwb3J0IGNvbnN0IE1lZGlhOiBDb2xsZWN0aW9uQ29uZmlnID0ge1xuICBzbHVnOiAnbWVkaWEnLFxuICBhY2Nlc3M6IHtcbiAgICByZWFkOiAoKSA9PiB0cnVlLFxuICB9LFxuICBmaWVsZHM6IFtcbiAgICB7XG4gICAgICBuYW1lOiAnYWx0JyxcbiAgICAgIHR5cGU6ICd0ZXh0JyxcbiAgICAgIHJlcXVpcmVkOiB0cnVlLFxuICAgIH0sXG4gIF0sXG4gIHVwbG9hZDogdHJ1ZSxcbn1cbiJdLCJuYW1lcyI6WyJNZWRpYSIsInNsdWciLCJhY2Nlc3MiLCJyZWFkIiwiZmllbGRzIiwibmFtZSIsInR5cGUiLCJyZXF1aXJlZCIsInVwbG9hZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/collections/Media.ts\n");

/***/ }),

/***/ "(rsc)/./src/collections/StoryTemplates.ts":
/*!*******************************************!*\
  !*** ./src/collections/StoryTemplates.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StoryTemplates: () => (/* binding */ StoryTemplates)\n/* harmony export */ });\nconst StoryTemplates = {\n    slug: 'story-templates',\n    dbName: 'story_templates',\n    admin: {\n        useAsTitle: 'title',\n        defaultColumns: [\n            'title',\n            'genre',\n            'ageGroup',\n            'status',\n            'publishedAt'\n        ],\n        group: 'Content'\n    },\n    access: {\n        read: ()=>true,\n        create: ({ req: { user } })=>{\n            return user?.role === 'content-creator' || user?.role === 'admin';\n        },\n        update: ({ req: { user } })=>{\n            return user?.role === 'content-creator' || user?.role === 'admin';\n        },\n        delete: ({ req: { user } })=>{\n            return user?.role === 'admin';\n        }\n    },\n    fields: [\n        {\n            name: 'title',\n            type: 'text',\n            required: true,\n            admin: {\n                description: 'Title of the story template'\n            }\n        },\n        {\n            name: 'slug',\n            type: 'text',\n            required: true,\n            unique: true,\n            hooks: {\n                beforeValidate: [\n                    ({ value, data })=>{\n                        if (!value && data?.title) {\n                            return data.title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '');\n                        }\n                        return value;\n                    }\n                ]\n            }\n        },\n        {\n            name: 'description',\n            type: 'textarea',\n            required: true,\n            admin: {\n                description: 'Brief description of the story template'\n            }\n        },\n        {\n            name: 'genre',\n            type: 'select',\n            required: true,\n            hasMany: true,\n            options: [\n                {\n                    label: 'Adventure',\n                    value: 'adventure'\n                },\n                {\n                    label: 'Fantasy',\n                    value: 'fantasy'\n                },\n                {\n                    label: 'Science Fiction',\n                    value: 'sci-fi'\n                },\n                {\n                    label: 'Mystery',\n                    value: 'mystery'\n                },\n                {\n                    label: 'Friendship',\n                    value: 'friendship'\n                },\n                {\n                    label: 'Family',\n                    value: 'family'\n                },\n                {\n                    label: 'Animals',\n                    value: 'animals'\n                },\n                {\n                    label: 'Magic',\n                    value: 'magic'\n                },\n                {\n                    label: 'Space',\n                    value: 'space'\n                },\n                {\n                    label: 'Underwater',\n                    value: 'underwater'\n                }\n            ]\n        },\n        {\n            name: 'ageGroup',\n            type: 'select',\n            required: true,\n            hasMany: true,\n            options: [\n                {\n                    label: '6-8 years',\n                    value: '6-8'\n                },\n                {\n                    label: '9-11 years',\n                    value: '9-11'\n                },\n                {\n                    label: '12-14 years',\n                    value: '12-14'\n                }\n            ]\n        },\n        {\n            name: 'storyPrompt',\n            type: 'textarea',\n            required: true,\n            admin: {\n                description: 'The opening prompt that starts the story',\n                rows: 4\n            }\n        },\n        {\n            name: 'characterOptions',\n            type: 'array',\n            required: true,\n            minRows: 2,\n            maxRows: 8,\n            fields: [\n                {\n                    name: 'name',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'description',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'image',\n                    type: 'upload',\n                    relationTo: 'media'\n                }\n            ],\n            admin: {\n                description: 'Character options children can choose from'\n            }\n        },\n        {\n            name: 'settingOptions',\n            type: 'array',\n            required: true,\n            minRows: 2,\n            maxRows: 6,\n            fields: [\n                {\n                    name: 'name',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'description',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'image',\n                    type: 'upload',\n                    relationTo: 'media'\n                }\n            ],\n            admin: {\n                description: 'Setting options for the story'\n            }\n        },\n        {\n            name: 'plotPoints',\n            type: 'array',\n            required: true,\n            minRows: 3,\n            maxRows: 10,\n            fields: [\n                {\n                    name: 'title',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'description',\n                    type: 'textarea',\n                    required: true\n                },\n                {\n                    name: 'order',\n                    type: 'number',\n                    required: true,\n                    defaultValue: 1\n                },\n                {\n                    name: 'optional',\n                    type: 'checkbox',\n                    defaultValue: false,\n                    admin: {\n                        description: 'Whether this plot point is optional'\n                    }\n                }\n            ],\n            admin: {\n                description: 'Key plot points to guide the story structure'\n            }\n        },\n        {\n            name: 'writingPrompts',\n            type: 'array',\n            fields: [\n                {\n                    name: 'prompt',\n                    type: 'text',\n                    required: true\n                },\n                {\n                    name: 'category',\n                    type: 'select',\n                    options: [\n                        {\n                            label: 'Character Development',\n                            value: 'character'\n                        },\n                        {\n                            label: 'Setting Description',\n                            value: 'setting'\n                        },\n                        {\n                            label: 'Action Scene',\n                            value: 'action'\n                        },\n                        {\n                            label: 'Dialogue',\n                            value: 'dialogue'\n                        },\n                        {\n                            label: 'Emotion',\n                            value: 'emotion'\n                        }\n                    ]\n                }\n            ],\n            admin: {\n                description: 'Additional writing prompts to help children'\n            }\n        },\n        {\n            name: 'estimatedLength',\n            type: 'group',\n            fields: [\n                {\n                    name: 'minWords',\n                    type: 'number',\n                    defaultValue: 100\n                },\n                {\n                    name: 'maxWords',\n                    type: 'number',\n                    defaultValue: 500\n                }\n            ],\n            admin: {\n                description: 'Suggested story length'\n            }\n        },\n        {\n            name: 'learningObjectives',\n            type: 'array',\n            required: true,\n            fields: [\n                {\n                    name: 'objective',\n                    type: 'text',\n                    required: true\n                }\n            ],\n            admin: {\n                description: 'What children will learn from this story template'\n            }\n        },\n        {\n            name: 'subscriptionTier',\n            type: 'select',\n            required: true,\n            options: [\n                {\n                    label: 'Free',\n                    value: 'free'\n                },\n                {\n                    label: 'Premium',\n                    value: 'premium'\n                }\n            ],\n            defaultValue: 'premium'\n        },\n        {\n            name: 'featured',\n            type: 'checkbox',\n            defaultValue: false\n        },\n        {\n            name: 'seasonal',\n            type: 'group',\n            fields: [\n                {\n                    name: 'isSeasonalContent',\n                    type: 'checkbox',\n                    defaultValue: false\n                },\n                {\n                    name: 'season',\n                    type: 'select',\n                    options: [\n                        {\n                            label: 'Spring',\n                            value: 'spring'\n                        },\n                        {\n                            label: 'Summer',\n                            value: 'summer'\n                        },\n                        {\n                            label: 'Fall/Autumn',\n                            value: 'fall'\n                        },\n                        {\n                            label: 'Winter',\n                            value: 'winter'\n                        },\n                        {\n                            label: 'Halloween',\n                            value: 'halloween'\n                        },\n                        {\n                            label: 'Christmas',\n                            value: 'christmas'\n                        },\n                        {\n                            label: 'New Year',\n                            value: 'newyear'\n                        }\n                    ],\n                    admin: {\n                        condition: (data, siblingData)=>siblingData?.isSeasonalContent\n                    }\n                }\n            ]\n        },\n        {\n            name: 'status',\n            type: 'select',\n            required: true,\n            options: [\n                {\n                    label: 'Draft',\n                    value: 'draft'\n                },\n                {\n                    label: 'Under Review',\n                    value: 'review'\n                },\n                {\n                    label: 'Published',\n                    value: 'published'\n                },\n                {\n                    label: 'Archived',\n                    value: 'archived'\n                }\n            ],\n            defaultValue: 'draft'\n        },\n        {\n            name: 'publishedAt',\n            type: 'date',\n            admin: {\n                condition: (data)=>data.status === 'published'\n            }\n        },\n        {\n            name: 'createdBy',\n            type: 'relationship',\n            relationTo: 'users',\n            admin: {\n                readOnly: true\n            }\n        }\n    ],\n    hooks: {\n        beforeChange: [\n            ({ data, operation, req })=>{\n                if (data.status === 'published' && !data.publishedAt) {\n                    data.publishedAt = new Date();\n                }\n                if (operation === 'create' && req.user) {\n                    data.createdBy = req.user.id;\n                }\n                return data;\n            }\n        ]\n    },\n    versions: {\n        drafts: true,\n        maxPerDoc: 5\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/collections/StoryTemplates.ts\n");

/***/ }),

/***/ "(rsc)/./src/collections/Users.ts":
/*!**********************************!*\
  !*** ./src/collections/Users.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Users: () => (/* binding */ Users)\n/* harmony export */ });\nconst Users = {\n    slug: 'users',\n    admin: {\n        useAsTitle: 'email',\n        defaultColumns: [\n            'email',\n            'firstName',\n            'lastName',\n            'role'\n        ],\n        group: 'Admin'\n    },\n    auth: true,\n    access: {\n        create: ({ req: { user } })=>{\n            // Only admins can create new admin users\n            return user?.role === 'admin';\n        },\n        read: ({ req: { user } })=>{\n            // Users can read their own profile, admins can read all\n            if (user?.role === 'admin') return true;\n            return {\n                id: {\n                    equals: user?.id\n                }\n            };\n        },\n        update: ({ req: { user } })=>{\n            // Users can update their own profile, admins can update all\n            if (user?.role === 'admin') return true;\n            return {\n                id: {\n                    equals: user?.id\n                }\n            };\n        },\n        delete: ({ req: { user } })=>{\n            // Only admins can delete users\n            return user?.role === 'admin';\n        }\n    },\n    fields: [\n        {\n            name: 'firstName',\n            type: 'text',\n            required: true\n        },\n        {\n            name: 'lastName',\n            type: 'text',\n            required: true\n        },\n        {\n            name: 'role',\n            type: 'select',\n            required: true,\n            options: [\n                {\n                    label: 'Super Admin',\n                    value: 'admin'\n                },\n                {\n                    label: 'Content Creator',\n                    value: 'content-creator'\n                },\n                {\n                    label: 'Educator',\n                    value: 'educator'\n                },\n                {\n                    label: 'Reviewer',\n                    value: 'reviewer'\n                }\n            ],\n            defaultValue: 'educator',\n            admin: {\n                description: 'User role determines access permissions'\n            }\n        },\n        {\n            name: 'bio',\n            type: 'textarea',\n            admin: {\n                description: 'Brief bio about the content creator/educator'\n            }\n        },\n        {\n            name: 'avatar',\n            type: 'upload',\n            relationTo: 'media',\n            admin: {\n                description: 'Profile picture'\n            }\n        },\n        {\n            name: 'specialties',\n            type: 'array',\n            fields: [\n                {\n                    name: 'specialty',\n                    type: 'select',\n                    options: [\n                        {\n                            label: 'Art & Drawing',\n                            value: 'art'\n                        },\n                        {\n                            label: 'Creative Writing',\n                            value: 'story'\n                        },\n                        {\n                            label: 'Music & Sound',\n                            value: 'music'\n                        },\n                        {\n                            label: 'Coding & Logic',\n                            value: 'coding'\n                        },\n                        {\n                            label: 'Video Creation',\n                            value: 'video'\n                        },\n                        {\n                            label: 'Game Design',\n                            value: 'game'\n                        }\n                    ]\n                }\n            ],\n            admin: {\n                description: 'Areas of expertise for content creators'\n            }\n        },\n        {\n            name: 'isActive',\n            type: 'checkbox',\n            defaultValue: true,\n            admin: {\n                description: 'Whether this user account is active'\n            }\n        }\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/collections/Users.ts\n");

/***/ }),

/***/ "(rsc)/./src/payload.config.ts":
/*!*******************************!*\
  !*** ./src/payload.config.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _payloadcms_db_sqlite__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @payloadcms/db-sqlite */ \"@payloadcms/db-sqlite\");\n/* harmony import */ var _payloadcms_payload_cloud__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @payloadcms/payload-cloud */ \"@payloadcms/payload-cloud\");\n/* harmony import */ var _payloadcms_richtext_lexical__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @payloadcms/richtext-lexical */ \"(rsc)/./node_modules/@payloadcms/richtext-lexical/dist/index.js\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var payload__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! payload */ \"payload\");\n/* harmony import */ var url__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! url */ \"url\");\n/* harmony import */ var url__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(url__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var sharp__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! sharp */ \"sharp\");\n/* harmony import */ var sharp__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(sharp__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _collections_Users__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./collections/Users */ \"(rsc)/./src/collections/Users.ts\");\n/* harmony import */ var _collections_Media__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./collections/Media */ \"(rsc)/./src/collections/Media.ts\");\n/* harmony import */ var _collections_Challenges__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./collections/Challenges */ \"(rsc)/./src/collections/Challenges.ts\");\n/* harmony import */ var _collections_StoryTemplates__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./collections/StoryTemplates */ \"(rsc)/./src/collections/StoryTemplates.ts\");\n/* harmony import */ var _collections_EducationalResources__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./collections/EducationalResources */ \"(rsc)/./src/collections/EducationalResources.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_payloadcms_db_sqlite__WEBPACK_IMPORTED_MODULE_0__, _payloadcms_payload_cloud__WEBPACK_IMPORTED_MODULE_1__, payload__WEBPACK_IMPORTED_MODULE_3__, _payloadcms_richtext_lexical__WEBPACK_IMPORTED_MODULE_11__]);\n([_payloadcms_db_sqlite__WEBPACK_IMPORTED_MODULE_0__, _payloadcms_payload_cloud__WEBPACK_IMPORTED_MODULE_1__, payload__WEBPACK_IMPORTED_MODULE_3__, _payloadcms_richtext_lexical__WEBPACK_IMPORTED_MODULE_11__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n// storage-adapter-import-placeholder\n\n\n\n\n\n\n\n\n\n\n\n\nconst filename = (0,url__WEBPACK_IMPORTED_MODULE_4__.fileURLToPath)(\"file:///C:/Users/<USER>/kavya-git/spark-new/littlespark-cms/src/payload.config.ts\");\nconst dirname = path__WEBPACK_IMPORTED_MODULE_2___default().dirname(filename);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,payload__WEBPACK_IMPORTED_MODULE_3__.buildConfig)({\n    admin: {\n        user: _collections_Users__WEBPACK_IMPORTED_MODULE_6__.Users.slug,\n        importMap: {\n            baseDir: path__WEBPACK_IMPORTED_MODULE_2___default().resolve(dirname)\n        }\n    },\n    collections: [\n        _collections_Users__WEBPACK_IMPORTED_MODULE_6__.Users,\n        _collections_Media__WEBPACK_IMPORTED_MODULE_7__.Media,\n        _collections_Challenges__WEBPACK_IMPORTED_MODULE_8__.Challenges,\n        _collections_StoryTemplates__WEBPACK_IMPORTED_MODULE_9__.StoryTemplates,\n        _collections_EducationalResources__WEBPACK_IMPORTED_MODULE_10__.EducationalResources\n    ],\n    editor: (0,_payloadcms_richtext_lexical__WEBPACK_IMPORTED_MODULE_11__.lexicalEditor)(),\n    secret: process.env.PAYLOAD_SECRET || '',\n    typescript: {\n        outputFile: path__WEBPACK_IMPORTED_MODULE_2___default().resolve(dirname, 'payload-types.ts')\n    },\n    db: (0,_payloadcms_db_sqlite__WEBPACK_IMPORTED_MODULE_0__.sqliteAdapter)({\n        client: {\n            url: process.env.DATABASE_URI || 'file:./cms.db'\n        }\n    }),\n    sharp: (sharp__WEBPACK_IMPORTED_MODULE_5___default()),\n    plugins: [\n        (0,_payloadcms_payload_cloud__WEBPACK_IMPORTED_MODULE_1__.payloadCloudPlugin)()\n    ]\n}));\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/payload.config.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@payloadcms/db-sqlite":
/*!****************************************!*\
  !*** external "@payloadcms/db-sqlite" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@payloadcms/db-sqlite");;

/***/ }),

/***/ "@payloadcms/payload-cloud":
/*!********************************************!*\
  !*** external "@payloadcms/payload-cloud" ***!
  \********************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@payloadcms/payload-cloud");;

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "payload":
/*!**************************!*\
  !*** external "payload" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = import("payload");;

/***/ }),

/***/ "payload/shared":
/*!*********************************!*\
  !*** external "payload/shared" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("payload/shared");;

/***/ }),

/***/ "sharp":
/*!************************!*\
  !*** external "sharp" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("sharp");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@payloadcms","vendor-chunks/next","vendor-chunks/lexical","vendor-chunks/@lexical","vendor-chunks/jsox","vendor-chunks/qs-esm","vendor-chunks/bson-objectid","vendor-chunks/uuid","vendor-chunks/escape-html"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsync%2Fchallenges%2Froute&page=%2Fapi%2Fsync%2Fchallenges%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsync%2Fchallenges%2Froute.ts&appDir=C%3A%5CUsers%5Cravi1%5Ckavya-git%5Cspark-new%5Clittlespark-cms%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cravi1%5Ckavya-git%5Cspark-new%5Clittlespark-cms&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();