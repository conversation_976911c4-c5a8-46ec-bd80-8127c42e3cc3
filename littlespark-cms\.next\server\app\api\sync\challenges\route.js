(()=>{var e={};e.id=2344,e.ids=[2344],e.modules={91043:e=>{"use strict";e.exports=require("@aws-sdk/client-s3")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74552:e=>{"use strict";e.exports=require("pino")},14007:e=>{"use strict";e.exports=require("pino-pretty")},82015:e=>{"use strict";e.exports=require("react")},8732:e=>{"use strict";e.exports=require("react/jsx-runtime")},79428:e=>{"use strict";e.exports=require("buffer")},79646:e=>{"use strict";e.exports=require("child_process")},55511:e=>{"use strict";e.exports=require("crypto")},14985:e=>{"use strict";e.exports=require("dns")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},73496:e=>{"use strict";e.exports=require("http2")},55591:e=>{"use strict";e.exports=require("https")},8086:e=>{"use strict";e.exports=require("module")},91645:e=>{"use strict";e.exports=require("net")},21820:e=>{"use strict";e.exports=require("os")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},11997:e=>{"use strict";e.exports=require("punycode")},4984:e=>{"use strict";e.exports=require("readline")},27910:e=>{"use strict";e.exports=require("stream")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},28855:e=>{"use strict";e.exports=import("@libsql/client")},34589:e=>{"use strict";e.exports=require("node:assert")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},4573:e=>{"use strict";e.exports=require("node:buffer")},37540:e=>{"use strict";e.exports=require("node:console")},77598:e=>{"use strict";e.exports=require("node:crypto")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},40610:e=>{"use strict";e.exports=require("node:dns")},78474:e=>{"use strict";e.exports=require("node:events")},73024:e=>{"use strict";e.exports=require("node:fs")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},37067:e=>{"use strict";e.exports=require("node:http")},32467:e=>{"use strict";e.exports=require("node:http2")},98995:e=>{"use strict";e.exports=require("node:module")},77030:e=>{"use strict";e.exports=require("node:net")},48161:e=>{"use strict";e.exports=require("node:os")},76760:e=>{"use strict";e.exports=require("node:path")},643:e=>{"use strict";e.exports=require("node:perf_hooks")},1708:e=>{"use strict";e.exports=require("node:process")},41792:e=>{"use strict";e.exports=require("node:querystring")},80099:e=>{"use strict";e.exports=require("node:sqlite")},57075:e=>{"use strict";e.exports=require("node:stream")},37830:e=>{"use strict";e.exports=require("node:stream/web")},41692:e=>{"use strict";e.exports=require("node:tls")},73136:e=>{"use strict";e.exports=require("node:url")},57975:e=>{"use strict";e.exports=require("node:util")},73429:e=>{"use strict";e.exports=require("node:util/types")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},38522:e=>{"use strict";e.exports=require("node:zlib")},39727:()=>{},47990:()=>{},84150:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{patchFetch:()=>a,routeModule:()=>l,serverHooks:()=>x,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>d});var o=r(42706),i=r(28203),n=r(45994),c=r(28505),u=e([c]);c=(u.then?(await u)():u)[0];let l=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/sync/challenges/route",pathname:"/api/sync/challenges",filename:"route",bundlePath:"app/api/sync/challenges/route"},resolvedPagePath:"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\sync\\challenges\\route.ts",nextConfigOutput:"standalone",userland:c}),{workAsyncStorage:p,workUnitAsyncStorage:d,serverHooks:x}=l;function a(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:d})}s()}catch(e){s(e)}})},96487:()=>{},78335:()=>{},28505:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{GET:()=>a,POST:()=>u});var o=r(39187),i=r(45415),n=r(17750),c=e([n]);n=(c.then?(await c)():c)[0];let l=process.env.MAIN_APP_BASE_URL||"http://localhost:3000";async function u(e){try{console.log("\uD83D\uDD04 [CMS-SYNC] Challenge sync request received"),console.log("\uD83D\uDD04 [CMS-SYNC] Starting CMS to Main App challenge sync");let e=await (0,i.nm0)({config:n.A}),t=(await e.find({collection:"challenges",where:{status:{equals:"published"}},limit:1e3})).docs;console.log(`📊 [CMS-SYNC] Found ${t.length} published challenges in CMS`);let r=0,s=0;for(let e of t)try{let t={cms_id:e.id,title:e.title,description:e.description,difficulty:e.difficulty,category:e.category,type:e.category,prompt:e.prompt||e.instructions||e.description,is_active:!1!==e.is_active&&"published"===e.status,created_by:"cms",valid_until:null};console.log(`🔄 [CMS-SYNC] Sending challenge to main app:`,{cms_id:t.cms_id,title:t.title,category:t.category});let o=await fetch(`${l}/api/challenges/sync-from-cms`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${process.env.CMS_SYNC_TOKEN}`},body:JSON.stringify(t)});if(o.ok)try{let t=await o.text();try{let s=JSON.parse(t);console.log(`✅ [CMS-SYNC] Synced challenge: ${e.title}`,s),r++}catch(r){console.error(`❌ [CMS-SYNC] Invalid JSON response for ${e.title}:`,t),s++}}catch(t){console.error(`❌ [CMS-SYNC] Failed to read response for ${e.title}:`,t),s++}else{try{let t=await o.text();console.error(`❌ [CMS-SYNC] Failed to sync challenge ${e.title}:`,{status:o.status,statusText:o.statusText,error:t})}catch(t){console.error(`❌ [CMS-SYNC] Failed to read error response for ${e.title}:`,{status:o.status,statusText:o.statusText,readError:t})}s++}}catch(t){console.error(`❌ [CMS-SYNC] Error syncing challenge ${e.title}:`,t),s++}return console.log(`🎉 [CMS-SYNC] Sync complete: ${r} synced, 0 skipped, ${s} errors`),o.NextResponse.json({success:!0,message:"CMS challenges synced to main application",stats:{total:t.length,synced:r,skipped:0,errors:s}})}catch(e){return console.error("❌ [CMS-SYNC] Error syncing CMS challenges:",e),o.NextResponse.json({success:!1,error:"Failed to sync CMS challenges to main application",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function a(){try{console.log("\uD83D\uDCCA [CMS-SYNC] Getting challenge sync stats");let e=await (0,i.nm0)({config:n.A}),t=await e.count({collection:"challenges",where:{status:{equals:"published"}}}),r=0;try{let e=await fetch(`${l}/api/challenges/debug`,{headers:{Authorization:`Bearer ${process.env.CMS_SYNC_TOKEN}`}});if(e.ok){let t=await e.json();r=t.stats?.cms||0,console.log("\uD83D\uDCCA [CMS-SYNC] Main app stats:",t.stats)}else console.log("⚠️ [CMS-SYNC] Main app response not OK:",e.status)}catch(e){console.log("⚠️ [CMS-SYNC] Could not fetch main app count:",e instanceof Error?e.message:"Unknown error")}return o.NextResponse.json({success:!0,stats:{cmsChallenge:t.totalDocs,mainAppCmsChallenge:r,mainAppTotalChallenge:0,lastSync:"Not implemented yet"}})}catch(e){return console.error("Error checking sync status:",e),o.NextResponse.json({success:!1,error:"Failed to check sync status",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}s()}catch(e){s(e)}})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5994,2259,5415,5452,7750],()=>r(84150));module.exports=s})();