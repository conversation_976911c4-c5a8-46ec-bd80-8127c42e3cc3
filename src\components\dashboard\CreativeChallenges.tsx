import React, { useState, useEffect, useCallback } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import { Loader } from "lucide-react";
import { useAuth } from "@/hooks/useAuth";
import { toast } from "sonner";
import ChallengeCompletionCelebration from "./ChallengeCompletionCelebration";

interface Challenge {
    id: string;
    title: string;
    description: string;
    difficulty: "easy" | "medium" | "hard";
    type: "story" | "art" | "music" | "game";
    prompt: string;
    completed?: boolean;
}

const CreativeChallenges = () => {
    const { user, loading: authLoading } = useAuth();
    const [challenges, setChallenges] = useState<Challenge[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [isCompletingChallenge, setIsCompletingChallenge] = useState<string | null>(null);
    const [showCompleted, setShowCompleted] = useState(false);
    const [userChallenges, setUserChallenges] = useState<
        Record<string, boolean>
    >({});
    const [isLoadingUserChallenges, setIsLoadingUserChallenges] = useState(false);
    const [celebrationData, setCelebrationData] = useState<{
        isOpen: boolean;
        challengeTitle: string;
        challengeType: string;
        contentTitle: string;
        newBadgesEarned: Array<{ name: string; icon: string; earned: boolean }>;
        updatedProgress: {
            completedProjects: number;
            skillLevel: number;
            streakDays: number;
            totalBadges: number;
        };
    }>({
        isOpen: false,
        challengeTitle: '',
        challengeType: '',
        contentTitle: '',
        newBadgesEarned: [],
        updatedProgress: {
            completedProjects: 0,
            skillLevel: 1,
            streakDays: 0,
            totalBadges: 0
        }
    });

    const fetchChallenges = async () => {
        setIsLoading(true);
        console.log('🔄 [DASHBOARD] Fetching challenges from both CMS and database');

        try {
            // Fetch database challenges first (these include synced CMS challenges)
            const dbResponse = await fetch('/api/challenges').catch(() => null);
            let allChallenges: any[] = [];

            if (dbResponse && dbResponse.ok) {
                const dbData = await dbResponse.json();
                if (dbData.success && dbData.challenges) {
                    console.log('✅ [DASHBOARD] Database challenges loaded:', dbData.challenges.length);
                    // Convert database challenges to match CMS format
                    const formattedDbChallenges = dbData.challenges.map((challenge: any) => ({
                        ...challenge,
                        category: challenge.type, // Map type to category
                        slug: challenge.id,
                        ageGroup: ['6-8', '9-11', '12-14'], // Default age groups
                        estimatedTime: 30, // Default time
                        subscriptionTier: 'free' // Default tier
                    }));
                    allChallenges = [...formattedDbChallenges];
                }
            }

            // Only fetch CMS challenges if no database challenges found (fallback)
            if (allChallenges.length === 0) {
                console.log('🔄 [DASHBOARD] No database challenges found, trying CMS fallback');
                const cmsResponse = await fetch('/api/cms/challenges').catch(() => null);

                if (cmsResponse && cmsResponse.ok) {
                    const cmsData = await cmsResponse.json();
                    if (cmsData.success && cmsData.challenges) {
                        console.log('✅ [DASHBOARD] CMS fallback challenges loaded:', cmsData.challenges.length);
                        allChallenges = [...cmsData.challenges];
                    }
                }
            }

            // Remove duplicates based on ID (in case there are any)
            const uniqueChallenges = allChallenges.filter((challenge, index, self) =>
                index === self.findIndex(c => c.id === challenge.id)
            );

            console.log('📦 [DASHBOARD] Total challenges loaded:', allChallenges.length);
            console.log('🔧 [DASHBOARD] Unique challenges after deduplication:', uniqueChallenges.length);
            setChallenges(uniqueChallenges);

        } catch (error) {
            console.log('❌ [DASHBOARD] Error fetching challenges:', error);
            setChallenges([]);
        } finally {
            setIsLoading(false);
        }
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
    const fetchUserChallenges = useCallback(async () => {
        if (!user || isLoadingUserChallenges) {
            return;
        }

        setIsLoadingUserChallenges(true);

        try {
            const response = await fetch(`/api/challenges/user-progress/${user.id}`, {
                credentials: 'include' // Include cookies for authentication
            });

            // Check if response is ok and content-type is JSON
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                throw new Error('Response is not JSON');
            }

            const data = await response.json();

            if (data.success) {
                setUserChallenges(data.userChallenges);
            } else {
                console.error('Failed to fetch user challenges:', data.error);
                // Set empty object if no data
                setUserChallenges({});
            }
        } catch (error) {
            console.error("Error in fetchUserChallenges:", error);
            setUserChallenges({});
        } finally {
            setIsLoadingUserChallenges(false);
        }
    }, [user]);

    const markChallengeCompleted = async (challengeId: string) => {
        if (!user) {
            toast.error("Please sign in to track your challenges");
            return;
        }

        if (isCompletingChallenge) {
            toast.info("Please wait, completing another challenge...");
            return;
        }

        setIsCompletingChallenge(challengeId);

        try {
            // Get challenge start timestamp from localStorage
            const startHistory = JSON.parse(localStorage.getItem("challengeStartHistory") || "{}");
            const challengeStartedAt = startHistory[challengeId];

            // First validate if user can complete this challenge
            const validationResponse = await fetch('/api/challenges/validate-completion', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include', // Include cookies for authentication
                body: JSON.stringify({
                    challengeId,
                    challengeStartedAt
                })
            });

            const validationData = await validationResponse.json();

            if (!validationData.success) {
                toast.error(validationData.error || 'Failed to validate challenge completion');
                return;
            }

            if (!validationData.canComplete) {
                toast.error(validationData.message || 'Please complete the required task first!');
                return;
            }

            if (validationData.alreadyCompleted) {
                // Update local state to reflect completion
                setUserChallenges(prev => ({
                    ...prev,
                    [challengeId]: true
                }));
                toast.info('Challenge already completed!');
                return;
            }

            // Mark challenge as complete
            const completionResponse = await fetch('/api/challenges/mark-complete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'include', // Include cookies for authentication
                body: JSON.stringify({
                    challengeId,
                    contentId: validationData.contentFound?.id
                })
            });

            const completionData = await completionResponse.json();

            if (completionData.success) {
                // Update local state
                setUserChallenges(prev => ({
                    ...prev,
                    [challengeId]: true
                }));

                // Fetch updated progress data
                const progressResponse = await fetch('/api/user-progress', {
                    credentials: 'include' // Include cookies for authentication
                });
                const progressData = await progressResponse.json();

                // Find the challenge details
                const challenge = challenges.find(c => c.id === challengeId);

                if (progressData.success && challenge) {
                    // Show celebration modal
                    setCelebrationData({
                        isOpen: true,
                        challengeTitle: challenge.title,
                        challengeType: challenge.type,
                        contentTitle: completionData.completion?.contentTitle || 'Your Creation',
                        newBadgesEarned: progressData.progress.badges.filter((badge: { earned: boolean }) => badge.earned),
                        updatedProgress: {
                            completedProjects: progressData.progress.completedProjects,
                            skillLevel: progressData.progress.skillLevel,
                            streakDays: progressData.progress.streakDays,
                            totalBadges: progressData.progress.badges.filter((badge: { earned: boolean }) => badge.earned).length
                        }
                    });

                    // Trigger a custom event to update other dashboard components
                    window.dispatchEvent(new CustomEvent('challengeCompleted', {
                        detail: {
                            challengeId,
                            progress: progressData.progress
                        }
                    }));
                } else {
                    toast.success(completionData.message || 'Challenge completed successfully!');
                }
            } else {
                toast.error(completionData.error || 'Failed to mark challenge as complete');
            }

        } catch (error) {
            console.error("Error marking challenge as complete:", error);
            toast.error("Failed to complete challenge");
        } finally {
            setIsCompletingChallenge(null);
        }
    };

    const showCompletionDetails = async (challengeId: string, challengeTitle: string) => {
        if (!user) return;

        try {
            // Fetch completion details
            const response = await fetch(`/api/challenges/user-progress/${user.id}`, {
                credentials: 'include' // Include cookies for authentication
            });

            // Check if response is ok and content-type is JSON
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.includes('application/json')) {
                throw new Error('Response is not JSON');
            }

            const data = await response.json();

            if (data.success) {
                const completionDetail = data.challengeDetails.find(
                    (detail: { challengeId: string; contentTitle: string; completedAt: string }) => detail.challengeId === challengeId
                );

                if (completionDetail) {
                    // Find the challenge to get its details
                    const challenge = challenges.find(c => c.id === challengeId);

                    if (challenge) {
                        // Fetch user progress to show current stats
                        const progressResponse = await fetch('/api/user-progress', {
                            credentials: 'include'
                        });

                        // Check progress response as well
                        if (!progressResponse.ok) {
                            throw new Error(`Progress API error! status: ${progressResponse.status}`);
                        }

                        const progressContentType = progressResponse.headers.get('content-type');
                        if (!progressContentType || !progressContentType.includes('application/json')) {
                            throw new Error('Progress response is not JSON');
                        }

                        const progressData = await progressResponse.json();

                        if (progressData.success) {
                            // Show the same celebration popup for completed challenges
                            setCelebrationData({
                                isOpen: true,
                                challengeTitle: challenge.title,
                                challengeType: challenge.type,
                                contentTitle: completionDetail.contentTitle,
                                newBadgesEarned: progressData.progress.badges.filter((badge: { earned: boolean }) => badge.earned),
                                updatedProgress: {
                                    completedProjects: progressData.progress.completedProjects,
                                    skillLevel: progressData.progress.skillLevel,
                                    streakDays: progressData.progress.streakDays,
                                    totalBadges: progressData.progress.badges.filter((badge: { earned: boolean }) => badge.earned).length
                                }
                            });
                        } else {
                            // Fallback to toast if progress fetch fails
                            toast.success(
                                `🎉 Challenge "${challengeTitle}" completed on ${new Date(
                                    completionDetail.completedAt
                                ).toLocaleDateString()}! Content: "${completionDetail.contentTitle}"`,
                                { duration: 5000 }
                            );
                        }
                    }
                } else {
                    toast.info(`Challenge "${challengeTitle}" is completed!`);
                }
            }
        } catch (error) {
            console.error('Error fetching completion details:', error);
            toast.info(`Challenge "${challengeTitle}" is completed!`);
        }
    };

    const getChallengeTypeColor = (type: string) => {
        switch (type) {
            case "story":
                return "bg-spark-blue/10 text-spark-blue";
            case "art":
                return "bg-spark-green/10 text-spark-green";
            case "music":
                return "bg-spark-purple/10 text-spark-purple";
            case "game":
                return "bg-spark-orange/10 text-spark-orange";
            default:
                return "bg-gray-100 text-gray-800";
        }
    };

    const getDifficultyColor = (difficulty: string) => {
        switch (difficulty) {
            case "easy":
                return "bg-green-100 text-green-800";
            case "medium":
                return "bg-yellow-100 text-yellow-800";
            case "hard":
                return "bg-red-100 text-red-800";
            default:
                return "bg-gray-100 text-gray-800";
        }
    };

    useEffect(() => {
        fetchChallenges();
    }, []);

    useEffect(() => {
        if (!authLoading && user) {
            fetchUserChallenges();
        } else if (!authLoading && !user) {
            setUserChallenges({});
        }
    }, [user, authLoading, fetchUserChallenges]);

    const filteredChallenges = challenges.filter(
        (challenge) => showCompleted || !userChallenges[challenge.id]
    );

    return (
        <div className="bg-white rounded-xl shadow-sm p-6">
            <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold">Creative Challenges</h2>
                <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">
                        Show Completed
                    </span>
                    <Switch
                        checked={showCompleted}
                        onCheckedChange={setShowCompleted}
                    />
                </div>
            </div>

            {isLoading ? (
                <div className="flex justify-center items-center py-10">
                    <Loader className="h-8 w-8 animate-spin text-spark-blue" />
                </div>
            ) : filteredChallenges.length === 0 ? (
                <div className="text-center py-8">
                    <h3 className="text-xl font-semibold mb-2">
                        All challenges completed!
                    </h3>
                    <p className="text-gray-600">
                        Great job! You&apos;ve completed all available
                        challenges. Check back soon for new creative challenges.
                    </p>
                </div>
            ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {filteredChallenges.map((challenge) => (
                        <Card
                            key={challenge.id}
                            className={`p-4 transition-all duration-200 ${
                                userChallenges[challenge.id]
                                    ? 'bg-green-50 border-green-200 shadow-md'
                                    : 'hover:shadow-md border-gray-200'
                            }`}
                        >
                            <div className="flex justify-between mb-3">
                                <div className="flex items-center gap-2">
                                    <span
                                        className={`px-2 py-1 rounded-full text-xs font-medium ${getChallengeTypeColor(
                                            challenge.type
                                        )}`}
                                    >
                                        {challenge.type.charAt(0).toUpperCase() +
                                            challenge.type.slice(1)}
                                    </span>
                                    {userChallenges[challenge.id] && (
                                        <span className="text-green-600 text-sm font-medium">
                                            ✓ Completed
                                        </span>
                                    )}
                                </div>
                                <span
                                    className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(
                                        challenge.difficulty
                                    )}`}
                                >
                                    {challenge.difficulty
                                        .charAt(0)
                                        .toUpperCase() +
                                        challenge.difficulty.slice(1)}
                                </span>
                            </div>
                            <h3 className="text-lg font-semibold mb-2">
                                {challenge.title}
                            </h3>
                            <p className="text-gray-600 text-sm mb-4">
                                {challenge.description}
                            </p>
                            <p className="text-gray-700 italic text-sm mb-4">
                                &quot;{challenge.prompt}&quot;
                            </p>

                            <div className="flex justify-between items-center">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                        // Store challenge information with start timestamp in localStorage
                                        const challengeInfo = {
                                            id: challenge.id,
                                            title: challenge.title,
                                            description: challenge.description,
                                            type: challenge.type,
                                            difficulty: challenge.difficulty,
                                            startedAt: new Date().toISOString()
                                        };
                                        localStorage.setItem("currentChallenge", JSON.stringify(challengeInfo));

                                        // Also store in challenge start history for validation
                                        const startHistory = JSON.parse(localStorage.getItem("challengeStartHistory") || "{}");
                                        startHistory[challenge.id] = new Date().toISOString();
                                        localStorage.setItem("challengeStartHistory", JSON.stringify(startHistory));

                                        // Navigate to the appropriate creation tool based on challenge type
                                        const routeMap: Record<string, string> =
                                            {
                                                story: "/create/story",
                                                art: "/create/art",
                                                music: "/create/music",
                                                game: "/create/game",
                                            };
                                        window.location.href =
                                            routeMap[challenge.type] ||
                                            "/dashboard";
                                    }}
                                >
                                    Start Challenge
                                </Button>
                                <Button
                                    variant={
                                        userChallenges[challenge.id]
                                            ? "outline"
                                            : "default"
                                    }
                                    size="sm"
                                    className={
                                        userChallenges[challenge.id]
                                            ? "text-green-600 border-green-200 bg-green-50 hover:bg-green-100"
                                            : ""
                                    }
                                    onClick={() => {
                                        if (!userChallenges[challenge.id]) {
                                            // Mark challenge as complete only if not already completed
                                            markChallengeCompleted(challenge.id);
                                        }
                                    }}
                                    disabled={isCompletingChallenge === challenge.id || userChallenges[challenge.id]}
                                >
                                    {isCompletingChallenge === challenge.id ? (
                                        <>
                                            <Loader className="h-4 w-4 mr-2 animate-spin" />
                                            Completing...
                                        </>
                                    ) : userChallenges[challenge.id] ? (
                                        "Completed"
                                    ) : (
                                        "Mark Complete"
                                    )}
                                </Button>
                            </div>
                        </Card>
                    ))}
                </div>
            )}

            {/* Challenge Completion Celebration Modal */}
            <ChallengeCompletionCelebration
                isOpen={celebrationData.isOpen}
                onClose={() => setCelebrationData(prev => ({ ...prev, isOpen: false }))}
                challengeTitle={celebrationData.challengeTitle}
                challengeType={celebrationData.challengeType}
                contentTitle={celebrationData.contentTitle}
                newBadgesEarned={celebrationData.newBadgesEarned}
                updatedProgress={celebrationData.updatedProgress}
            />
        </div>
    );
};

export default CreativeChallenges;
