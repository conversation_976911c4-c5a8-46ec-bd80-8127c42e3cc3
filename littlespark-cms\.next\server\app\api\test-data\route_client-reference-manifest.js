globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/api/test-data/route"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\node_modules\\@payloadcms\\next\\dist\\prod\\styles.css":{"id":2280,"name":"*","chunks":["951","static/chunks/app/(payload)/api/%5B...slug%5D/route-2358d03eb608f167.js"],"async":false},"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\node_modules\\@payloadcms\\next\\dist\\esm\\prod\\styles.css":{"id":2280,"name":"*","chunks":["951","static/chunks/app/(payload)/api/%5B...slug%5D/route-2358d03eb608f167.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\":[],"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\api\\[...slug]\\route":["static/css/5491896ccd21f128.css"],"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\test-data\\route":[]},"rscModuleMapping":{"2280":{"*":{"id":"63046","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}