"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_payloadcms_ui_dist_exports_client_DatePicker-QBWPYX2E_js"],{

/***/ "(app-pages-browser)/./node_modules/@payloadcms/ui/dist/exports/client/DatePicker-QBWPYX2E.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@payloadcms/ui/dist/exports/client/DatePicker-QBWPYX2E.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;
var react_dom__WEBPACK_IMPORTED_MODULE_1___namespace_cache;
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* reexport safe */ _chunk_TIQCV7VX_js__WEBPACK_IMPORTED_MODULE_2__.n)
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js");
/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ "(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/index.js");
/* harmony import */ var _chunk_TIQCV7VX_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./chunk-TIQCV7VX.js */ "(app-pages-browser)/./node_modules/@payloadcms/ui/dist/exports/client/chunk-TIQCV7VX.js");
/* __next_internal_client_entry_do_not_use__ default auto */ // Workaround for react-datepicker and other cjs dependencies potentially inserting require("react") statements


function require(m) {
    if (m === 'react') return /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)));
    if (m === 'react-dom') return /*#__PURE__*/ (react_dom__WEBPACK_IMPORTED_MODULE_1___namespace_cache || (react_dom__WEBPACK_IMPORTED_MODULE_1___namespace_cache = __webpack_require__.t(react_dom__WEBPACK_IMPORTED_MODULE_1__, 2)));
    throw new Error("Unknown module ".concat(m));
}
// Workaround end


 //# sourceMappingURL=DatePicker-QBWPYX2E.js.map


/***/ })

}]);