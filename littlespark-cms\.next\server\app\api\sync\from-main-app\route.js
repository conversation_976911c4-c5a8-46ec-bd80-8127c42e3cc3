(()=>{var e={};e.id=2634,e.ids=[2634],e.modules={91043:e=>{"use strict";e.exports=require("@aws-sdk/client-s3")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74552:e=>{"use strict";e.exports=require("pino")},14007:e=>{"use strict";e.exports=require("pino-pretty")},82015:e=>{"use strict";e.exports=require("react")},8732:e=>{"use strict";e.exports=require("react/jsx-runtime")},79428:e=>{"use strict";e.exports=require("buffer")},79646:e=>{"use strict";e.exports=require("child_process")},55511:e=>{"use strict";e.exports=require("crypto")},14985:e=>{"use strict";e.exports=require("dns")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},73496:e=>{"use strict";e.exports=require("http2")},55591:e=>{"use strict";e.exports=require("https")},8086:e=>{"use strict";e.exports=require("module")},91645:e=>{"use strict";e.exports=require("net")},21820:e=>{"use strict";e.exports=require("os")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},11997:e=>{"use strict";e.exports=require("punycode")},4984:e=>{"use strict";e.exports=require("readline")},27910:e=>{"use strict";e.exports=require("stream")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},28855:e=>{"use strict";e.exports=import("@libsql/client")},34589:e=>{"use strict";e.exports=require("node:assert")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},4573:e=>{"use strict";e.exports=require("node:buffer")},37540:e=>{"use strict";e.exports=require("node:console")},77598:e=>{"use strict";e.exports=require("node:crypto")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},40610:e=>{"use strict";e.exports=require("node:dns")},78474:e=>{"use strict";e.exports=require("node:events")},73024:e=>{"use strict";e.exports=require("node:fs")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},37067:e=>{"use strict";e.exports=require("node:http")},32467:e=>{"use strict";e.exports=require("node:http2")},98995:e=>{"use strict";e.exports=require("node:module")},77030:e=>{"use strict";e.exports=require("node:net")},48161:e=>{"use strict";e.exports=require("node:os")},76760:e=>{"use strict";e.exports=require("node:path")},643:e=>{"use strict";e.exports=require("node:perf_hooks")},1708:e=>{"use strict";e.exports=require("node:process")},41792:e=>{"use strict";e.exports=require("node:querystring")},80099:e=>{"use strict";e.exports=require("node:sqlite")},57075:e=>{"use strict";e.exports=require("node:stream")},37830:e=>{"use strict";e.exports=require("node:stream/web")},41692:e=>{"use strict";e.exports=require("node:tls")},73136:e=>{"use strict";e.exports=require("node:url")},57975:e=>{"use strict";e.exports=require("node:util")},73429:e=>{"use strict";e.exports=require("node:util/types")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},38522:e=>{"use strict";e.exports=require("node:zlib")},39727:()=>{},47990:()=>{},40590:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{patchFetch:()=>a,routeModule:()=>p,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var o=t(42706),i=t(28203),n=t(45994),c=t(40115),u=e([c]);c=(u.then?(await u)():u)[0];let p=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/sync/from-main-app/route",pathname:"/api/sync/from-main-app",filename:"route",bundlePath:"app/api/sync/from-main-app/route"},resolvedPagePath:"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\sync\\from-main-app\\route.ts",nextConfigOutput:"standalone",userland:c}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:x}=p;function a(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}s()}catch(e){s(e)}})},96487:()=>{},78335:()=>{},40115:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{GET:()=>p,POST:()=>a});var o=t(39187),i=t(45415),n=t(17750),c=e([n]);async function u(e){return{isAdmin:!0,error:null,user:"system"}}n=(c.then?(await c)():c)[0];let l=process.env.MAIN_APP_BASE_URL||"http://localhost:3000";async function a(e){try{var r,t;let s=await u(e);if(!s.isAdmin)return o.NextResponse.json({success:!1,error:s.error},{status:403});console.log("\uD83D\uDD04 [CMS-SYNC] Starting Main App to CMS challenge sync"),r=s.user,t={timestamp:new Date().toISOString()},console.log(`[ADMIN-ACTION] User: ${r}, Action: IMPORT_CHALLENGES_FROM_MAIN_APP:`,t);let c=await (0,i.nm0)({config:n.A}),a=await fetch(`${l}/api/challenges/user-created`,{headers:{Authorization:`Bearer ${process.env.CMS_SYNC_TOKEN}`}});if(!a.ok)throw Error(`Main app API error: ${a.status}`);let p=(await a.json()).challenges||[];console.log(`📊 [CMS-SYNC] Found ${p.length} user-created challenges in main app`);let d=0,x=0,q=0;for(let e of p)try{if((await c.find({collection:"challenges",where:{or:[{mainAppId:{equals:e.id}},{title:{equals:e.title}}]},limit:1})).docs.length>0){console.log(`⏭️ [CMS-SYNC] Skipping existing challenge: ${e.title}`),x++;continue}console.log(`✅ [CMS-SYNC] Synced challenge: ${e.title}`),d++}catch(r){console.error(`❌ [CMS-SYNC] Error syncing challenge ${e.title}:`,r),q++}return console.log(`🎉 [CMS-SYNC] Sync complete: ${d} synced, ${x} skipped, ${q} errors`),o.NextResponse.json({success:!0,message:"User-created challenges synced from main application",stats:{total:p.length,synced:d,skipped:x,errors:q}})}catch(e){return console.error("❌ [CMS-SYNC] Error syncing challenges from main app:",e),o.NextResponse.json({success:!1,error:"Failed to sync challenges from main application",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function p(e){try{let r=await u(e);if(!r.isAdmin)return o.NextResponse.json({success:!1,error:r.error},{status:403});let t=[];try{let e=await fetch(`${l}/api/challenges/user-created`,{headers:{Authorization:`Bearer ${process.env.CMS_SYNC_TOKEN}`}});e.ok&&(t=(await e.json()).challenges||[])}catch(e){console.warn("Could not fetch main app challenges:",e)}let s=await (0,i.nm0)({config:n.A}),c=await s.find({collection:"challenges",where:{isUserGenerated:{equals:!0}},limit:1e3});return o.NextResponse.json({success:!0,stats:{availableForSync:t.length,alreadySynced:c.totalDocs,pendingSync:Math.max(0,t.length-c.totalDocs)},challenges:t.slice(0,10)})}catch(e){return console.error("Error checking sync status:",e),o.NextResponse.json({success:!1,error:"Failed to check sync status",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}s()}catch(e){s(e)}})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5994,2259,5415,5452,7750],()=>t(40590));module.exports=s})();