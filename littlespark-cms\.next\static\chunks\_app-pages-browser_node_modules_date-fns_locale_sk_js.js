"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_date-fns_locale_sk_js"],{

/***/ "(app-pages-browser)/./node_modules/date-fns/isSameWeek.js":
/*!*********************************************!*\
  !*** ./node_modules/date-fns/isSameWeek.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isSameWeek: () => (/* binding */ isSameWeek)\n/* harmony export */ });\n/* harmony import */ var _lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_lib/normalizeDates.js */ \"(app-pages-browser)/./node_modules/date-fns/_lib/normalizeDates.js\");\n/* harmony import */ var _startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./startOfWeek.js */ \"(app-pages-browser)/./node_modules/date-fns/startOfWeek.js\");\n\n\n/**\n * The {@link isSameWeek} function options.\n */ /**\n * @name isSameWeek\n * @category Week Helpers\n * @summary Are the given dates in the same week (and month and year)?\n *\n * @description\n * Are the given dates in the same week (and month and year)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same week (and month and year)\n *\n * @example\n * // Are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4))\n * //=> true\n *\n * @example\n * // If week starts with Monday,\n * // are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4), {\n *   weekStartsOn: 1\n * })\n * //=> false\n *\n * @example\n * // Are 1 January 2014 and 1 January 2015 in the same week?\n * const result = isSameWeek(new Date(2014, 0, 1), new Date(2015, 0, 1))\n * //=> false\n */ function isSameWeek(laterDate, earlierDate, options) {\n    const [laterDate_, earlierDate_] = (0,_lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__.normalizeDates)(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate);\n    return +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(laterDate_, options) === +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(earlierDate_, options);\n}\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (isSameWeek);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9kYXRlLWZucy9pc1NhbWVXZWVrLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMEQ7QUFDWDtBQUUvQzs7Q0FFQyxHQUVEOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0NBK0JDLEdBQ00sU0FBU0UsV0FBV0MsU0FBUyxFQUFFQyxXQUFXLEVBQUVDLE9BQU87SUFDeEQsTUFBTSxDQUFDQyxZQUFZQyxhQUFhLEdBQUdQLHNFQUFjQSxDQUMvQ0ssb0JBQUFBLDhCQUFBQSxRQUFTRyxFQUFFLEVBQ1hMLFdBQ0FDO0lBRUYsT0FDRSxDQUFDSCw0REFBV0EsQ0FBQ0ssWUFBWUQsYUFBYSxDQUFDSiw0REFBV0EsQ0FBQ00sY0FBY0Y7QUFFckU7QUFFQSxvQ0FBb0M7QUFDcEMsaUVBQWVILFVBQVVBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmF2aTFcXGthdnlhLWdpdFxcc3BhcmstbmV3XFxsaXR0bGVzcGFyay1jbXNcXG5vZGVfbW9kdWxlc1xcZGF0ZS1mbnNcXGlzU2FtZVdlZWsuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgbm9ybWFsaXplRGF0ZXMgfSBmcm9tIFwiLi9fbGliL25vcm1hbGl6ZURhdGVzLmpzXCI7XG5pbXBvcnQgeyBzdGFydE9mV2VlayB9IGZyb20gXCIuL3N0YXJ0T2ZXZWVrLmpzXCI7XG5cbi8qKlxuICogVGhlIHtAbGluayBpc1NhbWVXZWVrfSBmdW5jdGlvbiBvcHRpb25zLlxuICovXG5cbi8qKlxuICogQG5hbWUgaXNTYW1lV2Vla1xuICogQGNhdGVnb3J5IFdlZWsgSGVscGVyc1xuICogQHN1bW1hcnkgQXJlIHRoZSBnaXZlbiBkYXRlcyBpbiB0aGUgc2FtZSB3ZWVrIChhbmQgbW9udGggYW5kIHllYXIpP1xuICpcbiAqIEBkZXNjcmlwdGlvblxuICogQXJlIHRoZSBnaXZlbiBkYXRlcyBpbiB0aGUgc2FtZSB3ZWVrIChhbmQgbW9udGggYW5kIHllYXIpP1xuICpcbiAqIEBwYXJhbSBsYXRlckRhdGUgLSBUaGUgZmlyc3QgZGF0ZSB0byBjaGVja1xuICogQHBhcmFtIGVhcmxpZXJEYXRlIC0gVGhlIHNlY29uZCBkYXRlIHRvIGNoZWNrXG4gKiBAcGFyYW0gb3B0aW9ucyAtIEFuIG9iamVjdCB3aXRoIG9wdGlvbnNcbiAqXG4gKiBAcmV0dXJucyBUaGUgZGF0ZXMgYXJlIGluIHRoZSBzYW1lIHdlZWsgKGFuZCBtb250aCBhbmQgeWVhcilcbiAqXG4gKiBAZXhhbXBsZVxuICogLy8gQXJlIDMxIEF1Z3VzdCAyMDE0IGFuZCA0IFNlcHRlbWJlciAyMDE0IGluIHRoZSBzYW1lIHdlZWs/XG4gKiBjb25zdCByZXN1bHQgPSBpc1NhbWVXZWVrKG5ldyBEYXRlKDIwMTQsIDcsIDMxKSwgbmV3IERhdGUoMjAxNCwgOCwgNCkpXG4gKiAvLz0+IHRydWVcbiAqXG4gKiBAZXhhbXBsZVxuICogLy8gSWYgd2VlayBzdGFydHMgd2l0aCBNb25kYXksXG4gKiAvLyBhcmUgMzEgQXVndXN0IDIwMTQgYW5kIDQgU2VwdGVtYmVyIDIwMTQgaW4gdGhlIHNhbWUgd2Vlaz9cbiAqIGNvbnN0IHJlc3VsdCA9IGlzU2FtZVdlZWsobmV3IERhdGUoMjAxNCwgNywgMzEpLCBuZXcgRGF0ZSgyMDE0LCA4LCA0KSwge1xuICogICB3ZWVrU3RhcnRzT246IDFcbiAqIH0pXG4gKiAvLz0+IGZhbHNlXG4gKlxuICogQGV4YW1wbGVcbiAqIC8vIEFyZSAxIEphbnVhcnkgMjAxNCBhbmQgMSBKYW51YXJ5IDIwMTUgaW4gdGhlIHNhbWUgd2Vlaz9cbiAqIGNvbnN0IHJlc3VsdCA9IGlzU2FtZVdlZWsobmV3IERhdGUoMjAxNCwgMCwgMSksIG5ldyBEYXRlKDIwMTUsIDAsIDEpKVxuICogLy89PiBmYWxzZVxuICovXG5leHBvcnQgZnVuY3Rpb24gaXNTYW1lV2VlayhsYXRlckRhdGUsIGVhcmxpZXJEYXRlLCBvcHRpb25zKSB7XG4gIGNvbnN0IFtsYXRlckRhdGVfLCBlYXJsaWVyRGF0ZV9dID0gbm9ybWFsaXplRGF0ZXMoXG4gICAgb3B0aW9ucz8uaW4sXG4gICAgbGF0ZXJEYXRlLFxuICAgIGVhcmxpZXJEYXRlLFxuICApO1xuICByZXR1cm4gKFxuICAgICtzdGFydE9mV2VlayhsYXRlckRhdGVfLCBvcHRpb25zKSA9PT0gK3N0YXJ0T2ZXZWVrKGVhcmxpZXJEYXRlXywgb3B0aW9ucylcbiAgKTtcbn1cblxuLy8gRmFsbGJhY2sgZm9yIG1vZHVsYXJpemVkIGltcG9ydHM6XG5leHBvcnQgZGVmYXVsdCBpc1NhbWVXZWVrO1xuIl0sIm5hbWVzIjpbIm5vcm1hbGl6ZURhdGVzIiwic3RhcnRPZldlZWsiLCJpc1NhbWVXZWVrIiwibGF0ZXJEYXRlIiwiZWFybGllckRhdGUiLCJvcHRpb25zIiwibGF0ZXJEYXRlXyIsImVhcmxpZXJEYXRlXyIsImluIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/isSameWeek.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/sk.js":
/*!********************************************!*\
  !*** ./node_modules/date-fns/locale/sk.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   sk: () => (/* binding */ sk)\n/* harmony export */ });\n/* harmony import */ var _sk_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sk/_lib/formatDistance.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/sk/_lib/formatDistance.js\");\n/* harmony import */ var _sk_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./sk/_lib/formatLong.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/sk/_lib/formatLong.js\");\n/* harmony import */ var _sk_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./sk/_lib/formatRelative.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/sk/_lib/formatRelative.js\");\n/* harmony import */ var _sk_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./sk/_lib/localize.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/sk/_lib/localize.js\");\n/* harmony import */ var _sk_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./sk/_lib/match.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/sk/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary Slovak locale.\n * @language Slovak\n * @iso-639-2 slk\n * <AUTHOR> Suscak [@mareksuscak](https://github.com/mareksuscak)\n */ const sk = {\n    code: \"sk\",\n    formatDistance: _sk_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _sk_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _sk_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _sk_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _sk_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 1 /* Monday */ ,\n        firstWeekContainsDate: 4\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (sk);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9kYXRlLWZucy9sb2NhbGUvc2suanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE2RDtBQUNSO0FBQ1E7QUFDWjtBQUNOO0FBRTNDOzs7Ozs7Q0FNQyxHQUNNLE1BQU1LLEtBQUs7SUFDaEJDLE1BQU07SUFDTk4sZ0JBQWdCQSxxRUFBY0E7SUFDOUJDLFlBQVlBLDZEQUFVQTtJQUN0QkMsZ0JBQWdCQSxxRUFBY0E7SUFDOUJDLFVBQVVBLHlEQUFRQTtJQUNsQkMsT0FBT0EsbURBQUtBO0lBQ1pHLFNBQVM7UUFDUEMsY0FBYyxFQUFFLFVBQVU7UUFDMUJDLHVCQUF1QjtJQUN6QjtBQUNGLEVBQUU7QUFFRixvQ0FBb0M7QUFDcEMsaUVBQWVKLEVBQUVBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmF2aTFcXGthdnlhLWdpdFxcc3BhcmstbmV3XFxsaXR0bGVzcGFyay1jbXNcXG5vZGVfbW9kdWxlc1xcZGF0ZS1mbnNcXGxvY2FsZVxcc2suanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZm9ybWF0RGlzdGFuY2UgfSBmcm9tIFwiLi9zay9fbGliL2Zvcm1hdERpc3RhbmNlLmpzXCI7XG5pbXBvcnQgeyBmb3JtYXRMb25nIH0gZnJvbSBcIi4vc2svX2xpYi9mb3JtYXRMb25nLmpzXCI7XG5pbXBvcnQgeyBmb3JtYXRSZWxhdGl2ZSB9IGZyb20gXCIuL3NrL19saWIvZm9ybWF0UmVsYXRpdmUuanNcIjtcbmltcG9ydCB7IGxvY2FsaXplIH0gZnJvbSBcIi4vc2svX2xpYi9sb2NhbGl6ZS5qc1wiO1xuaW1wb3J0IHsgbWF0Y2ggfSBmcm9tIFwiLi9zay9fbGliL21hdGNoLmpzXCI7XG5cbi8qKlxuICogQGNhdGVnb3J5IExvY2FsZXNcbiAqIEBzdW1tYXJ5IFNsb3ZhayBsb2NhbGUuXG4gKiBAbGFuZ3VhZ2UgU2xvdmFrXG4gKiBAaXNvLTYzOS0yIHNsa1xuICogQGF1dGhvciBNYXJlayBTdXNjYWsgW0BtYXJla3N1c2Nha10oaHR0cHM6Ly9naXRodWIuY29tL21hcmVrc3VzY2FrKVxuICovXG5leHBvcnQgY29uc3Qgc2sgPSB7XG4gIGNvZGU6IFwic2tcIixcbiAgZm9ybWF0RGlzdGFuY2U6IGZvcm1hdERpc3RhbmNlLFxuICBmb3JtYXRMb25nOiBmb3JtYXRMb25nLFxuICBmb3JtYXRSZWxhdGl2ZTogZm9ybWF0UmVsYXRpdmUsXG4gIGxvY2FsaXplOiBsb2NhbGl6ZSxcbiAgbWF0Y2g6IG1hdGNoLFxuICBvcHRpb25zOiB7XG4gICAgd2Vla1N0YXJ0c09uOiAxIC8qIE1vbmRheSAqLyxcbiAgICBmaXJzdFdlZWtDb250YWluc0RhdGU6IDQsXG4gIH0sXG59O1xuXG4vLyBGYWxsYmFjayBmb3IgbW9kdWxhcml6ZWQgaW1wb3J0czpcbmV4cG9ydCBkZWZhdWx0IHNrO1xuIl0sIm5hbWVzIjpbImZvcm1hdERpc3RhbmNlIiwiZm9ybWF0TG9uZyIsImZvcm1hdFJlbGF0aXZlIiwibG9jYWxpemUiLCJtYXRjaCIsInNrIiwiY29kZSIsIm9wdGlvbnMiLCJ3ZWVrU3RhcnRzT24iLCJmaXJzdFdlZWtDb250YWluc0RhdGUiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/sk.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/sk/_lib/formatDistance.js":
/*!****************************************************************!*\
  !*** ./node_modules/date-fns/locale/sk/_lib/formatDistance.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\nfunction declensionGroup(scheme, count) {\n    if (count === 1 && scheme.one) {\n        return scheme.one;\n    }\n    if (count >= 2 && count <= 4 && scheme.twoFour) {\n        return scheme.twoFour;\n    }\n    // if count === null || count === 0 || count >= 5\n    return scheme.other;\n}\nfunction declension(scheme, count, time) {\n    const group = declensionGroup(scheme, count);\n    const finalText = group[time];\n    return finalText.replace(\"{{count}}\", String(count));\n}\nfunction extractPreposition(token) {\n    const result = [\n        \"lessThan\",\n        \"about\",\n        \"over\",\n        \"almost\"\n    ].filter(function(preposition) {\n        return !!token.match(new RegExp(\"^\" + preposition));\n    });\n    return result[0];\n}\nfunction prefixPreposition(preposition) {\n    let translation = \"\";\n    if (preposition === \"almost\") {\n        translation = \"takmer\";\n    }\n    if (preposition === \"about\") {\n        translation = \"približne\";\n    }\n    return translation.length > 0 ? translation + \" \" : \"\";\n}\nfunction suffixPreposition(preposition) {\n    let translation = \"\";\n    if (preposition === \"lessThan\") {\n        translation = \"menej než\";\n    }\n    if (preposition === \"over\") {\n        translation = \"viac než\";\n    }\n    return translation.length > 0 ? translation + \" \" : \"\";\n}\nfunction lowercaseFirstLetter(string) {\n    return string.charAt(0).toLowerCase() + string.slice(1);\n}\nconst formatDistanceLocale = {\n    xSeconds: {\n        one: {\n            present: \"sekunda\",\n            past: \"sekundou\",\n            future: \"sekundu\"\n        },\n        twoFour: {\n            present: \"{{count}} sekundy\",\n            past: \"{{count}} sekundami\",\n            future: \"{{count}} sekundy\"\n        },\n        other: {\n            present: \"{{count}} sekúnd\",\n            past: \"{{count}} sekundami\",\n            future: \"{{count}} sekúnd\"\n        }\n    },\n    halfAMinute: {\n        other: {\n            present: \"pol minúty\",\n            past: \"pol minútou\",\n            future: \"pol minúty\"\n        }\n    },\n    xMinutes: {\n        one: {\n            present: \"minúta\",\n            past: \"minútou\",\n            future: \"minútu\"\n        },\n        twoFour: {\n            present: \"{{count}} minúty\",\n            past: \"{{count}} minútami\",\n            future: \"{{count}} minúty\"\n        },\n        other: {\n            present: \"{{count}} minút\",\n            past: \"{{count}} minútami\",\n            future: \"{{count}} minút\"\n        }\n    },\n    xHours: {\n        one: {\n            present: \"hodina\",\n            past: \"hodinou\",\n            future: \"hodinu\"\n        },\n        twoFour: {\n            present: \"{{count}} hodiny\",\n            past: \"{{count}} hodinami\",\n            future: \"{{count}} hodiny\"\n        },\n        other: {\n            present: \"{{count}} hodín\",\n            past: \"{{count}} hodinami\",\n            future: \"{{count}} hodín\"\n        }\n    },\n    xDays: {\n        one: {\n            present: \"deň\",\n            past: \"dňom\",\n            future: \"deň\"\n        },\n        twoFour: {\n            present: \"{{count}} dni\",\n            past: \"{{count}} dňami\",\n            future: \"{{count}} dni\"\n        },\n        other: {\n            present: \"{{count}} dní\",\n            past: \"{{count}} dňami\",\n            future: \"{{count}} dní\"\n        }\n    },\n    xWeeks: {\n        one: {\n            present: \"týždeň\",\n            past: \"týždňom\",\n            future: \"týždeň\"\n        },\n        twoFour: {\n            present: \"{{count}} týždne\",\n            past: \"{{count}} týždňami\",\n            future: \"{{count}} týždne\"\n        },\n        other: {\n            present: \"{{count}} týždňov\",\n            past: \"{{count}} týždňami\",\n            future: \"{{count}} týždňov\"\n        }\n    },\n    xMonths: {\n        one: {\n            present: \"mesiac\",\n            past: \"mesiacom\",\n            future: \"mesiac\"\n        },\n        twoFour: {\n            present: \"{{count}} mesiace\",\n            past: \"{{count}} mesiacmi\",\n            future: \"{{count}} mesiace\"\n        },\n        other: {\n            present: \"{{count}} mesiacov\",\n            past: \"{{count}} mesiacmi\",\n            future: \"{{count}} mesiacov\"\n        }\n    },\n    xYears: {\n        one: {\n            present: \"rok\",\n            past: \"rokom\",\n            future: \"rok\"\n        },\n        twoFour: {\n            present: \"{{count}} roky\",\n            past: \"{{count}} rokmi\",\n            future: \"{{count}} roky\"\n        },\n        other: {\n            present: \"{{count}} rokov\",\n            past: \"{{count}} rokmi\",\n            future: \"{{count}} rokov\"\n        }\n    }\n};\nconst formatDistance = (token, count, options)=>{\n    const preposition = extractPreposition(token) || \"\";\n    const key = lowercaseFirstLetter(token.substring(preposition.length));\n    const scheme = formatDistanceLocale[key];\n    if (!(options === null || options === void 0 ? void 0 : options.addSuffix)) {\n        return prefixPreposition(preposition) + suffixPreposition(preposition) + declension(scheme, count, \"present\");\n    }\n    if (options.comparison && options.comparison > 0) {\n        return prefixPreposition(preposition) + \"o \" + suffixPreposition(preposition) + declension(scheme, count, \"future\");\n    } else {\n        return prefixPreposition(preposition) + \"pred \" + suffixPreposition(preposition) + declension(scheme, count, \"past\");\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/sk/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/sk/_lib/formatLong.js":
/*!************************************************************!*\
  !*** ./node_modules/date-fns/locale/sk/_lib/formatLong.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html?hide#1986\nconst dateFormats = {\n    full: \"EEEE d. MMMM y\",\n    long: \"d. MMMM y\",\n    medium: \"d. M. y\",\n    short: \"d. M. y\"\n};\n// https://www.unicode.org/cldr/charts/32/summary/sk.html?hide#2149\nconst timeFormats = {\n    full: \"H:mm:ss zzzz\",\n    long: \"H:mm:ss z\",\n    medium: \"H:mm:ss\",\n    short: \"H:mm\"\n};\n// https://www.unicode.org/cldr/charts/32/summary/sk.html?hide#1994\nconst dateTimeFormats = {\n    full: \"{{date}}, {{time}}\",\n    long: \"{{date}}, {{time}}\",\n    medium: \"{{date}}, {{time}}\",\n    short: \"{{date}} {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"full\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"full\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/sk/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/sk/_lib/formatRelative.js":
/*!****************************************************************!*\
  !*** ./node_modules/date-fns/locale/sk/_lib/formatRelative.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\n/* harmony import */ var _isSameWeek_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../isSameWeek.js */ \"(app-pages-browser)/./node_modules/date-fns/isSameWeek.js\");\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html?hide#1308\nconst accusativeWeekdays = [\n    \"nedeľu\",\n    \"pondelok\",\n    \"utorok\",\n    \"stredu\",\n    \"štvrtok\",\n    \"piatok\",\n    \"sobotu\"\n];\nfunction lastWeek(day) {\n    const weekday = accusativeWeekdays[day];\n    switch(day){\n        case 0:\n        /* Sun */ case 3:\n        /* Wed */ case 6 /* Sat */ :\n            return \"'minulú \" + weekday + \" o' p\";\n        default:\n            return \"'minulý' eeee 'o' p\";\n    }\n}\nfunction thisWeek(day) {\n    const weekday = accusativeWeekdays[day];\n    if (day === 4 /* Thu */ ) {\n        return \"'vo' eeee 'o' p\";\n    } else {\n        return \"'v \" + weekday + \" o' p\";\n    }\n}\nfunction nextWeek(day) {\n    const weekday = accusativeWeekdays[day];\n    switch(day){\n        case 0:\n        /* Sun */ case 4:\n        /* Wed */ case 6 /* Sat */ :\n            return \"'budúcu \" + weekday + \" o' p\";\n        default:\n            return \"'budúci' eeee 'o' p\";\n    }\n}\nconst formatRelativeLocale = {\n    lastWeek: (date, baseDate, options)=>{\n        const day = date.getDay();\n        if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_0__.isSameWeek)(date, baseDate, options)) {\n            return thisWeek(day);\n        } else {\n            return lastWeek(day);\n        }\n    },\n    yesterday: \"'včera o' p\",\n    today: \"'dnes o' p\",\n    tomorrow: \"'zajtra o' p\",\n    nextWeek: (date, baseDate, options)=>{\n        const day = date.getDay();\n        if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_0__.isSameWeek)(date, baseDate, options)) {\n            return thisWeek(day);\n        } else {\n            return nextWeek(day);\n        }\n    },\n    other: \"P\"\n};\nconst formatRelative = (token, date, baseDate, options)=>{\n    const format = formatRelativeLocale[token];\n    if (typeof format === \"function\") {\n        return format(date, baseDate, options);\n    }\n    return format;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/sk/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/sk/_lib/localize.js":
/*!**********************************************************!*\
  !*** ./node_modules/date-fns/locale/sk/_lib/localize.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1772\nconst eraValues = {\n    narrow: [\n        \"pred Kr.\",\n        \"po Kr.\"\n    ],\n    abbreviated: [\n        \"pred Kr.\",\n        \"po Kr.\"\n    ],\n    wide: [\n        \"pred Kristom\",\n        \"po Kristovi\"\n    ]\n};\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1780\nconst quarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"Q1\",\n        \"Q2\",\n        \"Q3\",\n        \"Q4\"\n    ],\n    wide: [\n        \"1. štvrťrok\",\n        \"2. štvrťrok\",\n        \"3. štvrťrok\",\n        \"4. štvrťrok\"\n    ]\n};\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1804\nconst monthValues = {\n    narrow: [\n        \"j\",\n        \"f\",\n        \"m\",\n        \"a\",\n        \"m\",\n        \"j\",\n        \"j\",\n        \"a\",\n        \"s\",\n        \"o\",\n        \"n\",\n        \"d\"\n    ],\n    abbreviated: [\n        \"jan\",\n        \"feb\",\n        \"mar\",\n        \"apr\",\n        \"máj\",\n        \"jún\",\n        \"júl\",\n        \"aug\",\n        \"sep\",\n        \"okt\",\n        \"nov\",\n        \"dec\"\n    ],\n    wide: [\n        \"január\",\n        \"február\",\n        \"marec\",\n        \"apríl\",\n        \"máj\",\n        \"jún\",\n        \"júl\",\n        \"august\",\n        \"september\",\n        \"október\",\n        \"november\",\n        \"december\"\n    ]\n};\nconst formattingMonthValues = {\n    narrow: [\n        \"j\",\n        \"f\",\n        \"m\",\n        \"a\",\n        \"m\",\n        \"j\",\n        \"j\",\n        \"a\",\n        \"s\",\n        \"o\",\n        \"n\",\n        \"d\"\n    ],\n    abbreviated: [\n        \"jan\",\n        \"feb\",\n        \"mar\",\n        \"apr\",\n        \"máj\",\n        \"jún\",\n        \"júl\",\n        \"aug\",\n        \"sep\",\n        \"okt\",\n        \"nov\",\n        \"dec\"\n    ],\n    wide: [\n        \"januára\",\n        \"februára\",\n        \"marca\",\n        \"apríla\",\n        \"mája\",\n        \"júna\",\n        \"júla\",\n        \"augusta\",\n        \"septembra\",\n        \"októbra\",\n        \"novembra\",\n        \"decembra\"\n    ]\n};\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1876\nconst dayValues = {\n    narrow: [\n        \"n\",\n        \"p\",\n        \"u\",\n        \"s\",\n        \"š\",\n        \"p\",\n        \"s\"\n    ],\n    short: [\n        \"ne\",\n        \"po\",\n        \"ut\",\n        \"st\",\n        \"št\",\n        \"pi\",\n        \"so\"\n    ],\n    abbreviated: [\n        \"ne\",\n        \"po\",\n        \"ut\",\n        \"st\",\n        \"št\",\n        \"pi\",\n        \"so\"\n    ],\n    wide: [\n        \"nedeľa\",\n        \"pondelok\",\n        \"utorok\",\n        \"streda\",\n        \"štvrtok\",\n        \"piatok\",\n        \"sobota\"\n    ]\n};\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1932\nconst dayPeriodValues = {\n    narrow: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"poln.\",\n        noon: \"pol.\",\n        morning: \"ráno\",\n        afternoon: \"pop.\",\n        evening: \"več.\",\n        night: \"noc\"\n    },\n    abbreviated: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"poln.\",\n        noon: \"pol.\",\n        morning: \"ráno\",\n        afternoon: \"popol.\",\n        evening: \"večer\",\n        night: \"noc\"\n    },\n    wide: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"polnoc\",\n        noon: \"poludnie\",\n        morning: \"ráno\",\n        afternoon: \"popoludnie\",\n        evening: \"večer\",\n        night: \"noc\"\n    }\n};\nconst formattingDayPeriodValues = {\n    narrow: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"o poln.\",\n        noon: \"nap.\",\n        morning: \"ráno\",\n        afternoon: \"pop.\",\n        evening: \"več.\",\n        night: \"v n.\"\n    },\n    abbreviated: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"o poln.\",\n        noon: \"napol.\",\n        morning: \"ráno\",\n        afternoon: \"popol.\",\n        evening: \"večer\",\n        night: \"v noci\"\n    },\n    wide: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"o polnoci\",\n        noon: \"napoludnie\",\n        morning: \"ráno\",\n        afternoon: \"popoludní\",\n        evening: \"večer\",\n        night: \"v noci\"\n    }\n};\nconst ordinalNumber = (dirtyNumber, _options)=>{\n    const number = Number(dirtyNumber);\n    return number + \".\";\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        defaultWidth: \"wide\",\n        formattingValues: formattingMonthValues,\n        defaultFormattingWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"wide\",\n        formattingValues: formattingDayPeriodValues,\n        defaultFormattingWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/sk/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/sk/_lib/match.js":
/*!*******************************************************!*\
  !*** ./node_modules/date-fns/locale/sk/_lib/match.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^(\\d+)\\.?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^(pred Kr\\.|pred n\\. l\\.|po Kr\\.|n\\. l\\.)/i,\n    abbreviated: /^(pred Kr\\.|pred n\\. l\\.|po Kr\\.|n\\. l\\.)/i,\n    wide: /^(pred Kristom|pred na[šs][íi]m letopo[čc]tom|po Kristovi|n[áa][šs]ho letopo[čc]tu)/i\n};\nconst parseEraPatterns = {\n    any: [\n        /^pr/i,\n        /^(po|n)/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]/i,\n    abbreviated: /^q[1234]/i,\n    wide: /^[1234]\\. [šs]tvr[ťt]rok/i\n};\nconst parseQuarterPatterns = {\n    any: [\n        /1/i,\n        /2/i,\n        /3/i,\n        /4/i\n    ]\n};\nconst matchMonthPatterns = {\n    narrow: /^[jfmasond]/i,\n    abbreviated: /^(jan|feb|mar|apr|m[áa]j|j[úu]n|j[úu]l|aug|sep|okt|nov|dec)/i,\n    wide: /^(janu[áa]ra?|febru[áa]ra?|(marec|marca)|apr[íi]la?|m[áa]ja?|j[úu]na?|j[úu]la?|augusta?|(september|septembra)|(okt[óo]ber|okt[óo]bra)|(november|novembra)|(december|decembra))/i\n};\nconst parseMonthPatterns = {\n    narrow: [\n        /^j/i,\n        /^f/i,\n        /^m/i,\n        /^a/i,\n        /^m/i,\n        /^j/i,\n        /^j/i,\n        /^a/i,\n        /^s/i,\n        /^o/i,\n        /^n/i,\n        /^d/i\n    ],\n    any: [\n        /^ja/i,\n        /^f/i,\n        /^mar/i,\n        /^ap/i,\n        /^m[áa]j/i,\n        /^j[úu]n/i,\n        /^j[úu]l/i,\n        /^au/i,\n        /^s/i,\n        /^o/i,\n        /^n/i,\n        /^d/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^[npusšp]/i,\n    short: /^(ne|po|ut|st|št|pi|so)/i,\n    abbreviated: /^(ne|po|ut|st|št|pi|so)/i,\n    wide: /^(nede[ľl]a|pondelok|utorok|streda|[šs]tvrtok|piatok|sobota])/i\n};\nconst parseDayPatterns = {\n    narrow: [\n        /^n/i,\n        /^p/i,\n        /^u/i,\n        /^s/i,\n        /^š/i,\n        /^p/i,\n        /^s/i\n    ],\n    any: [\n        /^n/i,\n        /^po/i,\n        /^u/i,\n        /^st/i,\n        /^(št|stv)/i,\n        /^pi/i,\n        /^so/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    narrow: /^(am|pm|(o )?poln\\.?|(nap\\.?|pol\\.?)|r[áa]no|pop\\.?|ve[čc]\\.?|(v n\\.?|noc))/i,\n    abbreviated: /^(am|pm|(o )?poln\\.?|(napol\\.?|pol\\.?)|r[áa]no|pop\\.?|ve[čc]er|(v )?noci?)/i,\n    any: /^(am|pm|(o )?polnoci?|(na)?poludnie|r[áa]no|popoludn(ie|í|i)|ve[čc]er|(v )?noci?)/i\n};\nconst parseDayPeriodPatterns = {\n    any: {\n        am: /^am/i,\n        pm: /^pm/i,\n        midnight: /poln/i,\n        noon: /^(nap|(na)?pol(\\.|u))/i,\n        morning: /^r[áa]no/i,\n        afternoon: /^pop/i,\n        evening: /^ve[čc]/i,\n        night: /^(noc|v n\\.)/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value, 10)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"any\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9kYXRlLWZucy9sb2NhbGUvc2svX2xpYi9tYXRjaC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBMEQ7QUFDYztBQUV4RSxNQUFNRSw0QkFBNEI7QUFDbEMsTUFBTUMsNEJBQTRCO0FBRWxDLE1BQU1DLG1CQUFtQjtJQUN2QkMsUUFBUTtJQUNSQyxhQUFhO0lBQ2JDLE1BQU07QUFDUjtBQUNBLE1BQU1DLG1CQUFtQjtJQUN2QkMsS0FBSztRQUFDO1FBQVE7S0FBVztBQUMzQjtBQUVBLE1BQU1DLHVCQUF1QjtJQUMzQkwsUUFBUTtJQUNSQyxhQUFhO0lBQ2JDLE1BQU07QUFDUjtBQUNBLE1BQU1JLHVCQUF1QjtJQUMzQkYsS0FBSztRQUFDO1FBQU07UUFBTTtRQUFNO0tBQUs7QUFDL0I7QUFFQSxNQUFNRyxxQkFBcUI7SUFDekJQLFFBQVE7SUFDUkMsYUFBYTtJQUNiQyxNQUFNO0FBQ1I7QUFDQSxNQUFNTSxxQkFBcUI7SUFDekJSLFFBQVE7UUFDTjtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7S0FDRDtJQUVESSxLQUFLO1FBQ0g7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0Q7QUFDSDtBQUVBLE1BQU1LLG1CQUFtQjtJQUN2QlQsUUFBUTtJQUNSVSxPQUFPO0lBQ1BULGFBQWE7SUFDYkMsTUFBTTtBQUNSO0FBQ0EsTUFBTVMsbUJBQW1CO0lBQ3ZCWCxRQUFRO1FBQUM7UUFBTztRQUFPO1FBQU87UUFBTztRQUFPO1FBQU87S0FBTTtJQUN6REksS0FBSztRQUFDO1FBQU87UUFBUTtRQUFPO1FBQVE7UUFBYztRQUFRO0tBQU87QUFDbkU7QUFFQSxNQUFNUSx5QkFBeUI7SUFDN0JaLFFBQ0U7SUFDRkMsYUFDRTtJQUNGRyxLQUFLO0FBQ1A7QUFDQSxNQUFNUyx5QkFBeUI7SUFDN0JULEtBQUs7UUFDSFUsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLFVBQVU7UUFDVkMsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFdBQVc7UUFDWEMsU0FBUztRQUNUQyxPQUFPO0lBQ1Q7QUFDRjtBQUVPLE1BQU1DLFFBQVE7SUFDbkJDLGVBQWUzQixnRkFBbUJBLENBQUM7UUFDakM0QixjQUFjM0I7UUFDZDRCLGNBQWMzQjtRQUNkNEIsZUFBZSxDQUFDQyxRQUFVQyxTQUFTRCxPQUFPO0lBQzVDO0lBRUFFLEtBQUtsQyxrRUFBWUEsQ0FBQztRQUNoQm1DLGVBQWUvQjtRQUNmZ0MsbUJBQW1CO1FBQ25CQyxlQUFlN0I7UUFDZjhCLG1CQUFtQjtJQUNyQjtJQUVBQyxTQUFTdkMsa0VBQVlBLENBQUM7UUFDcEJtQyxlQUFlekI7UUFDZjBCLG1CQUFtQjtRQUNuQkMsZUFBZTFCO1FBQ2YyQixtQkFBbUI7UUFDbkJQLGVBQWUsQ0FBQ1MsUUFBVUEsUUFBUTtJQUNwQztJQUVBQyxPQUFPekMsa0VBQVlBLENBQUM7UUFDbEJtQyxlQUFldkI7UUFDZndCLG1CQUFtQjtRQUNuQkMsZUFBZXhCO1FBQ2Z5QixtQkFBbUI7SUFDckI7SUFFQUksS0FBSzFDLGtFQUFZQSxDQUFDO1FBQ2hCbUMsZUFBZXJCO1FBQ2ZzQixtQkFBbUI7UUFDbkJDLGVBQWVyQjtRQUNmc0IsbUJBQW1CO0lBQ3JCO0lBRUFLLFdBQVczQyxrRUFBWUEsQ0FBQztRQUN0Qm1DLGVBQWVsQjtRQUNmbUIsbUJBQW1CO1FBQ25CQyxlQUFlbkI7UUFDZm9CLG1CQUFtQjtJQUNyQjtBQUNGLEVBQUUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmF2aTFcXGthdnlhLWdpdFxcc3BhcmstbmV3XFxsaXR0bGVzcGFyay1jbXNcXG5vZGVfbW9kdWxlc1xcZGF0ZS1mbnNcXGxvY2FsZVxcc2tcXF9saWJcXG1hdGNoLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGJ1aWxkTWF0Y2hGbiB9IGZyb20gXCIuLi8uLi9fbGliL2J1aWxkTWF0Y2hGbi5qc1wiO1xuaW1wb3J0IHsgYnVpbGRNYXRjaFBhdHRlcm5GbiB9IGZyb20gXCIuLi8uLi9fbGliL2J1aWxkTWF0Y2hQYXR0ZXJuRm4uanNcIjtcblxuY29uc3QgbWF0Y2hPcmRpbmFsTnVtYmVyUGF0dGVybiA9IC9eKFxcZCspXFwuPy9pO1xuY29uc3QgcGFyc2VPcmRpbmFsTnVtYmVyUGF0dGVybiA9IC9cXGQrL2k7XG5cbmNvbnN0IG1hdGNoRXJhUGF0dGVybnMgPSB7XG4gIG5hcnJvdzogL14ocHJlZCBLclxcLnxwcmVkIG5cXC4gbFxcLnxwbyBLclxcLnxuXFwuIGxcXC4pL2ksXG4gIGFiYnJldmlhdGVkOiAvXihwcmVkIEtyXFwufHByZWQgblxcLiBsXFwufHBvIEtyXFwufG5cXC4gbFxcLikvaSxcbiAgd2lkZTogL14ocHJlZCBLcmlzdG9tfHByZWQgbmFbxaFzXVvDrWldbSBsZXRvcG9bxI1jXXRvbXxwbyBLcmlzdG92aXxuW8OhYV1bxaFzXWhvIGxldG9wb1vEjWNddHUpL2ksXG59O1xuY29uc3QgcGFyc2VFcmFQYXR0ZXJucyA9IHtcbiAgYW55OiBbL15wci9pLCAvXihwb3xuKS9pXSxcbn07XG5cbmNvbnN0IG1hdGNoUXVhcnRlclBhdHRlcm5zID0ge1xuICBuYXJyb3c6IC9eWzEyMzRdL2ksXG4gIGFiYnJldmlhdGVkOiAvXnFbMTIzNF0vaSxcbiAgd2lkZTogL15bMTIzNF1cXC4gW8Whc110dnJbxaV0XXJvay9pLFxufTtcbmNvbnN0IHBhcnNlUXVhcnRlclBhdHRlcm5zID0ge1xuICBhbnk6IFsvMS9pLCAvMi9pLCAvMy9pLCAvNC9pXSxcbn07XG5cbmNvbnN0IG1hdGNoTW9udGhQYXR0ZXJucyA9IHtcbiAgbmFycm93OiAvXltqZm1hc29uZF0vaSxcbiAgYWJicmV2aWF0ZWQ6IC9eKGphbnxmZWJ8bWFyfGFwcnxtW8OhYV1qfGpbw7p1XW58alvDunVdbHxhdWd8c2VwfG9rdHxub3Z8ZGVjKS9pLFxuICB3aWRlOiAvXihqYW51W8OhYV1yYT98ZmVicnVbw6FhXXJhP3wobWFyZWN8bWFyY2EpfGFwclvDrWldbGE/fG1bw6FhXWphP3xqW8O6dV1uYT98alvDunVdbGE/fGF1Z3VzdGE/fChzZXB0ZW1iZXJ8c2VwdGVtYnJhKXwob2t0W8Ozb11iZXJ8b2t0W8Ozb11icmEpfChub3ZlbWJlcnxub3ZlbWJyYSl8KGRlY2VtYmVyfGRlY2VtYnJhKSkvaSxcbn07XG5jb25zdCBwYXJzZU1vbnRoUGF0dGVybnMgPSB7XG4gIG5hcnJvdzogW1xuICAgIC9eai9pLFxuICAgIC9eZi9pLFxuICAgIC9ebS9pLFxuICAgIC9eYS9pLFxuICAgIC9ebS9pLFxuICAgIC9eai9pLFxuICAgIC9eai9pLFxuICAgIC9eYS9pLFxuICAgIC9ecy9pLFxuICAgIC9eby9pLFxuICAgIC9ebi9pLFxuICAgIC9eZC9pLFxuICBdLFxuXG4gIGFueTogW1xuICAgIC9eamEvaSxcbiAgICAvXmYvaSxcbiAgICAvXm1hci9pLFxuICAgIC9eYXAvaSxcbiAgICAvXm1bw6FhXWovaSxcbiAgICAvXmpbw7p1XW4vaSxcbiAgICAvXmpbw7p1XWwvaSxcbiAgICAvXmF1L2ksXG4gICAgL15zL2ksXG4gICAgL15vL2ksXG4gICAgL15uL2ksXG4gICAgL15kL2ksXG4gIF0sXG59O1xuXG5jb25zdCBtYXRjaERheVBhdHRlcm5zID0ge1xuICBuYXJyb3c6IC9eW25wdXPFoXBdL2ksXG4gIHNob3J0OiAvXihuZXxwb3x1dHxzdHzFoXR8cGl8c28pL2ksXG4gIGFiYnJldmlhdGVkOiAvXihuZXxwb3x1dHxzdHzFoXR8cGl8c28pL2ksXG4gIHdpZGU6IC9eKG5lZGVbxL5sXWF8cG9uZGVsb2t8dXRvcm9rfHN0cmVkYXxbxaFzXXR2cnRva3xwaWF0b2t8c29ib3RhXSkvaSxcbn07XG5jb25zdCBwYXJzZURheVBhdHRlcm5zID0ge1xuICBuYXJyb3c6IFsvXm4vaSwgL15wL2ksIC9edS9pLCAvXnMvaSwgL17FoS9pLCAvXnAvaSwgL15zL2ldLFxuICBhbnk6IFsvXm4vaSwgL15wby9pLCAvXnUvaSwgL15zdC9pLCAvXijFoXR8c3R2KS9pLCAvXnBpL2ksIC9ec28vaV0sXG59O1xuXG5jb25zdCBtYXRjaERheVBlcmlvZFBhdHRlcm5zID0ge1xuICBuYXJyb3c6XG4gICAgL14oYW18cG18KG8gKT9wb2xuXFwuP3wobmFwXFwuP3xwb2xcXC4/KXxyW8OhYV1ub3xwb3BcXC4/fHZlW8SNY11cXC4/fCh2IG5cXC4/fG5vYykpL2ksXG4gIGFiYnJldmlhdGVkOlxuICAgIC9eKGFtfHBtfChvICk/cG9sblxcLj98KG5hcG9sXFwuP3xwb2xcXC4/KXxyW8OhYV1ub3xwb3BcXC4/fHZlW8SNY11lcnwodiApP25vY2k/KS9pLFxuICBhbnk6IC9eKGFtfHBtfChvICk/cG9sbm9jaT98KG5hKT9wb2x1ZG5pZXxyW8OhYV1ub3xwb3BvbHVkbihpZXzDrXxpKXx2ZVvEjWNdZXJ8KHYgKT9ub2NpPykvaSxcbn07XG5jb25zdCBwYXJzZURheVBlcmlvZFBhdHRlcm5zID0ge1xuICBhbnk6IHtcbiAgICBhbTogL15hbS9pLFxuICAgIHBtOiAvXnBtL2ksXG4gICAgbWlkbmlnaHQ6IC9wb2xuL2ksXG4gICAgbm9vbjogL14obmFwfChuYSk/cG9sKFxcLnx1KSkvaSxcbiAgICBtb3JuaW5nOiAvXnJbw6FhXW5vL2ksXG4gICAgYWZ0ZXJub29uOiAvXnBvcC9pLFxuICAgIGV2ZW5pbmc6IC9edmVbxI1jXS9pLFxuICAgIG5pZ2h0OiAvXihub2N8diBuXFwuKS9pLFxuICB9LFxufTtcblxuZXhwb3J0IGNvbnN0IG1hdGNoID0ge1xuICBvcmRpbmFsTnVtYmVyOiBidWlsZE1hdGNoUGF0dGVybkZuKHtcbiAgICBtYXRjaFBhdHRlcm46IG1hdGNoT3JkaW5hbE51bWJlclBhdHRlcm4sXG4gICAgcGFyc2VQYXR0ZXJuOiBwYXJzZU9yZGluYWxOdW1iZXJQYXR0ZXJuLFxuICAgIHZhbHVlQ2FsbGJhY2s6ICh2YWx1ZSkgPT4gcGFyc2VJbnQodmFsdWUsIDEwKSxcbiAgfSksXG5cbiAgZXJhOiBidWlsZE1hdGNoRm4oe1xuICAgIG1hdGNoUGF0dGVybnM6IG1hdGNoRXJhUGF0dGVybnMsXG4gICAgZGVmYXVsdE1hdGNoV2lkdGg6IFwid2lkZVwiLFxuICAgIHBhcnNlUGF0dGVybnM6IHBhcnNlRXJhUGF0dGVybnMsXG4gICAgZGVmYXVsdFBhcnNlV2lkdGg6IFwiYW55XCIsXG4gIH0pLFxuXG4gIHF1YXJ0ZXI6IGJ1aWxkTWF0Y2hGbih7XG4gICAgbWF0Y2hQYXR0ZXJuczogbWF0Y2hRdWFydGVyUGF0dGVybnMsXG4gICAgZGVmYXVsdE1hdGNoV2lkdGg6IFwid2lkZVwiLFxuICAgIHBhcnNlUGF0dGVybnM6IHBhcnNlUXVhcnRlclBhdHRlcm5zLFxuICAgIGRlZmF1bHRQYXJzZVdpZHRoOiBcImFueVwiLFxuICAgIHZhbHVlQ2FsbGJhY2s6IChpbmRleCkgPT4gaW5kZXggKyAxLFxuICB9KSxcblxuICBtb250aDogYnVpbGRNYXRjaEZuKHtcbiAgICBtYXRjaFBhdHRlcm5zOiBtYXRjaE1vbnRoUGF0dGVybnMsXG4gICAgZGVmYXVsdE1hdGNoV2lkdGg6IFwid2lkZVwiLFxuICAgIHBhcnNlUGF0dGVybnM6IHBhcnNlTW9udGhQYXR0ZXJucyxcbiAgICBkZWZhdWx0UGFyc2VXaWR0aDogXCJhbnlcIixcbiAgfSksXG5cbiAgZGF5OiBidWlsZE1hdGNoRm4oe1xuICAgIG1hdGNoUGF0dGVybnM6IG1hdGNoRGF5UGF0dGVybnMsXG4gICAgZGVmYXVsdE1hdGNoV2lkdGg6IFwid2lkZVwiLFxuICAgIHBhcnNlUGF0dGVybnM6IHBhcnNlRGF5UGF0dGVybnMsXG4gICAgZGVmYXVsdFBhcnNlV2lkdGg6IFwiYW55XCIsXG4gIH0pLFxuXG4gIGRheVBlcmlvZDogYnVpbGRNYXRjaEZuKHtcbiAgICBtYXRjaFBhdHRlcm5zOiBtYXRjaERheVBlcmlvZFBhdHRlcm5zLFxuICAgIGRlZmF1bHRNYXRjaFdpZHRoOiBcImFueVwiLFxuICAgIHBhcnNlUGF0dGVybnM6IHBhcnNlRGF5UGVyaW9kUGF0dGVybnMsXG4gICAgZGVmYXVsdFBhcnNlV2lkdGg6IFwiYW55XCIsXG4gIH0pLFxufTtcbiJdLCJuYW1lcyI6WyJidWlsZE1hdGNoRm4iLCJidWlsZE1hdGNoUGF0dGVybkZuIiwibWF0Y2hPcmRpbmFsTnVtYmVyUGF0dGVybiIsInBhcnNlT3JkaW5hbE51bWJlclBhdHRlcm4iLCJtYXRjaEVyYVBhdHRlcm5zIiwibmFycm93IiwiYWJicmV2aWF0ZWQiLCJ3aWRlIiwicGFyc2VFcmFQYXR0ZXJucyIsImFueSIsIm1hdGNoUXVhcnRlclBhdHRlcm5zIiwicGFyc2VRdWFydGVyUGF0dGVybnMiLCJtYXRjaE1vbnRoUGF0dGVybnMiLCJwYXJzZU1vbnRoUGF0dGVybnMiLCJtYXRjaERheVBhdHRlcm5zIiwic2hvcnQiLCJwYXJzZURheVBhdHRlcm5zIiwibWF0Y2hEYXlQZXJpb2RQYXR0ZXJucyIsInBhcnNlRGF5UGVyaW9kUGF0dGVybnMiLCJhbSIsInBtIiwibWlkbmlnaHQiLCJub29uIiwibW9ybmluZyIsImFmdGVybm9vbiIsImV2ZW5pbmciLCJuaWdodCIsIm1hdGNoIiwib3JkaW5hbE51bWJlciIsIm1hdGNoUGF0dGVybiIsInBhcnNlUGF0dGVybiIsInZhbHVlQ2FsbGJhY2siLCJ2YWx1ZSIsInBhcnNlSW50IiwiZXJhIiwibWF0Y2hQYXR0ZXJucyIsImRlZmF1bHRNYXRjaFdpZHRoIiwicGFyc2VQYXR0ZXJucyIsImRlZmF1bHRQYXJzZVdpZHRoIiwicXVhcnRlciIsImluZGV4IiwibW9udGgiLCJkYXkiLCJkYXlQZXJpb2QiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/sk/_lib/match.js\n"));

/***/ })

}]);