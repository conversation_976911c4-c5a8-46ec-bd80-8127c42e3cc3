'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { RefreshCw, Database, Cloud, CheckCircle, AlertCircle } from 'lucide-react';

interface SyncStats {
  cmsChallenge: number;
  totalChallenge: number;
  lastSync: string;
}

interface SyncResult {
  total: number;
  synced: number;
  skipped: number;
  errors: number;
}

export function CMSSyncPanel() {
  const [isLoading, setIsLoading] = useState(false);
  const [stats, setStats] = useState<SyncStats | null>(null);
  const [lastSyncResult, setLastSyncResult] = useState<SyncResult | null>(null);

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/challenges/sync-cms');
      const data = await response.json();
      
      if (data.success) {
        setStats(data.stats);
      }
    } catch (error) {
      console.error('Error fetching sync stats:', error);
    }
  };

  const handleSync = async () => {
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/challenges/sync-cms', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (data.success) {
        setLastSyncResult(data.stats);
        toast.success(`Sync completed! ${data.stats.synced} challenges synced.`);
        await fetchStats(); // Refresh stats
      } else {
        toast.error(data.error || 'Sync failed');
      }
    } catch (error) {
      console.error('Error syncing challenges:', error);
      toast.error('Failed to sync challenges');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          CMS to Supabase Sync
        </CardTitle>
        <CardDescription>
          Sync challenges from Payload CMS to Supabase database for unified schema compatibility
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Stats */}
        {stats && (
          <div className="grid grid-cols-2 gap-4">
            <div className="p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center gap-2">
                <Cloud className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-900">CMS Challenges</span>
              </div>
              <p className="text-2xl font-bold text-blue-600">{stats.cmsChallenge}</p>
            </div>
            <div className="p-3 bg-green-50 rounded-lg">
              <div className="flex items-center gap-2">
                <Database className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-green-900">Total in DB</span>
              </div>
              <p className="text-2xl font-bold text-green-600">{stats.totalChallenge}</p>
            </div>
          </div>
        )}

        {/* Last Sync Result */}
        {lastSyncResult && (
          <div className="p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-2 flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              Last Sync Results
            </h4>
            <div className="grid grid-cols-4 gap-2 text-sm">
              <div>
                <span className="text-gray-600">Total:</span>
                <span className="ml-1 font-medium">{lastSyncResult.total}</span>
              </div>
              <div>
                <span className="text-gray-600">Synced:</span>
                <span className="ml-1 font-medium text-green-600">{lastSyncResult.synced}</span>
              </div>
              <div>
                <span className="text-gray-600">Skipped:</span>
                <span className="ml-1 font-medium text-yellow-600">{lastSyncResult.skipped}</span>
              </div>
              <div>
                <span className="text-gray-600">Errors:</span>
                <span className="ml-1 font-medium text-red-600">{lastSyncResult.errors}</span>
              </div>
            </div>
          </div>
        )}

        {/* Sync Actions */}
        <div className="flex gap-2">
          <Button
            onClick={handleSync}
            disabled={isLoading}
            className="flex-1"
          >
            {isLoading ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Syncing...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Sync CMS Challenges
              </>
            )}
          </Button>
          
          <Button
            variant="outline"
            onClick={fetchStats}
            disabled={isLoading}
          >
            Refresh Stats
          </Button>
        </div>

        {/* Info */}
        <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-start gap-2">
            <AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5" />
            <div className="text-sm text-yellow-800">
              <p className="font-medium mb-1">How it works:</p>
              <ul className="list-disc list-inside space-y-1 text-xs">
                <li>Fetches all published challenges from CMS</li>
                <li>Maps CMS fields to Supabase schema (category → type, etc.)</li>
                <li>Creates/updates challenges with prefix "cms-" + original ID</li>
                <li>Enables portfolio saving and completion tracking for CMS challenges</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
