import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// Helper function to verify sync token
function verifySyncToken(req: NextRequest) {
  const authHeader = req.headers.get('authorization');
  const expectedToken = process.env.CMS_SYNC_TOKEN;
  
  if (!authHeader || !authHeader.startsWith('Bearer ') || !expectedToken) {
    return false;
  }
  
  const token = authHeader.substring(7);
  return token === expectedToken;
}

// GET /api/challenges/user-created - Get user-created challenges for CMS sync
export async function GET(request: NextRequest) {
  try {
    // Verify sync token
    if (!verifySyncToken(request)) {
      return NextResponse.json(
        { error: 'Unauthorized sync request' },
        { status: 401 }
      );
    }

    console.log('📊 [MAIN-APP] Fetching user-created challenges for CMS sync');

    // Get challenges that were created by users (not from CMS)
    const userCreatedChallenges = await prisma.challenge.findMany({
      where: {
        AND: [
          {
            created_by: {
              not: 'cms' // Exclude CMS-originated challenges
            }
          },
          {
            created_by: {
              not: 'system' // Exclude system challenges
            }
          },
          {
            is_active: true // Only active challenges
          }
        ]
      },
      orderBy: {
        created_at: 'desc'
      },
      take: 100 // Limit to prevent large responses
    });

    console.log(`✅ [MAIN-APP] Found ${userCreatedChallenges.length} user-created challenges`);

    // Transform challenges for CMS consumption
    const transformedChallenges = userCreatedChallenges.map(challenge => ({
      id: challenge.id,
      title: challenge.title,
      description: challenge.description,
      type: challenge.type,
      difficulty: challenge.difficulty,
      prompt: challenge.prompt,
      created_by: challenge.created_by,
      created_at: challenge.created_at,
      updated_at: challenge.updated_at,
      // Add metadata for sync tracking
      sync_metadata: {
        source: 'main_app',
        original_id: challenge.id,
        created_by_type: challenge.created_by
      }
    }));

    return NextResponse.json({
      success: true,
      challenges: transformedChallenges,
      total: userCreatedChallenges.length,
      message: `Found ${userCreatedChallenges.length} user-created challenges`
    });

  } catch (error) {
    console.error('❌ [MAIN-APP] Error fetching user-created challenges:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch user-created challenges',
        details: error.message
      },
      { status: 500 }
    );
  }
}

// POST /api/challenges/user-created - Mark a challenge as synced to CMS
export async function POST(request: NextRequest) {
  try {
    // Verify sync token
    if (!verifySyncToken(request)) {
      return NextResponse.json(
        { error: 'Unauthorized sync request' },
        { status: 401 }
      );
    }

    const { challengeId, cmsId } = await request.json();

    if (!challengeId || !cmsId) {
      return NextResponse.json(
        { error: 'Missing required fields: challengeId, cmsId' },
        { status: 400 }
      );
    }

    console.log(`🔄 [MAIN-APP] Marking challenge ${challengeId} as synced to CMS (${cmsId})`);

    // Update challenge to mark it as synced
    const updatedChallenge = await prisma.challenge.update({
      where: { id: challengeId },
      data: {
        // Add a field to track CMS sync status
        // Note: This would require a database migration to add these fields
        // For now, we'll just log the sync
        updated_at: new Date(),
      }
    });

    console.log(`✅ [MAIN-APP] Challenge ${challengeId} marked as synced`);

    return NextResponse.json({
      success: true,
      message: 'Challenge marked as synced to CMS',
      challenge: updatedChallenge
    });

  } catch (error) {
    console.error('❌ [MAIN-APP] Error marking challenge as synced:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to mark challenge as synced',
        details: error.message
      },
      { status: 500 }
    );
  }
}
