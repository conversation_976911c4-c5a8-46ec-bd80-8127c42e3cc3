(()=>{var e={};e.id=2588,e.ids=[2588],e.modules={91043:e=>{"use strict";e.exports=require("@aws-sdk/client-s3")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74552:e=>{"use strict";e.exports=require("pino")},14007:e=>{"use strict";e.exports=require("pino-pretty")},79428:e=>{"use strict";e.exports=require("buffer")},79646:e=>{"use strict";e.exports=require("child_process")},55511:e=>{"use strict";e.exports=require("crypto")},14985:e=>{"use strict";e.exports=require("dns")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},73496:e=>{"use strict";e.exports=require("http2")},55591:e=>{"use strict";e.exports=require("https")},8086:e=>{"use strict";e.exports=require("module")},91645:e=>{"use strict";e.exports=require("net")},21820:e=>{"use strict";e.exports=require("os")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},11997:e=>{"use strict";e.exports=require("punycode")},4984:e=>{"use strict";e.exports=require("readline")},27910:e=>{"use strict";e.exports=require("stream")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},28855:e=>{"use strict";e.exports=import("@libsql/client")},34589:e=>{"use strict";e.exports=require("node:assert")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},4573:e=>{"use strict";e.exports=require("node:buffer")},37540:e=>{"use strict";e.exports=require("node:console")},77598:e=>{"use strict";e.exports=require("node:crypto")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},40610:e=>{"use strict";e.exports=require("node:dns")},78474:e=>{"use strict";e.exports=require("node:events")},73024:e=>{"use strict";e.exports=require("node:fs")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},37067:e=>{"use strict";e.exports=require("node:http")},32467:e=>{"use strict";e.exports=require("node:http2")},98995:e=>{"use strict";e.exports=require("node:module")},77030:e=>{"use strict";e.exports=require("node:net")},48161:e=>{"use strict";e.exports=require("node:os")},76760:e=>{"use strict";e.exports=require("node:path")},643:e=>{"use strict";e.exports=require("node:perf_hooks")},1708:e=>{"use strict";e.exports=require("node:process")},41792:e=>{"use strict";e.exports=require("node:querystring")},80099:e=>{"use strict";e.exports=require("node:sqlite")},57075:e=>{"use strict";e.exports=require("node:stream")},37830:e=>{"use strict";e.exports=require("node:stream/web")},41692:e=>{"use strict";e.exports=require("node:tls")},73136:e=>{"use strict";e.exports=require("node:url")},57975:e=>{"use strict";e.exports=require("node:util")},73429:e=>{"use strict";e.exports=require("node:util/types")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},38522:e=>{"use strict";e.exports=require("node:zlib")},6446:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{patchFetch:()=>a,routeModule:()=>p,serverHooks:()=>q,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>x});var i=t(42706),o=t(28203),u=t(45994),n=t(90989),c=e([n]);n=(c.then?(await c)():c)[0];let p=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/auth/verify/route",pathname:"/api/auth/verify",filename:"route",bundlePath:"app/api/auth/verify/route"},resolvedPagePath:"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\auth\\verify\\route.ts",nextConfigOutput:"standalone",userland:n}),{workAsyncStorage:d,workUnitAsyncStorage:x,serverHooks:q}=p;function a(){return(0,u.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:x})}s()}catch(e){s(e)}})},96487:()=>{},78335:()=>{},90989:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{GET:()=>c});var i=t(39187),o=t(66280),u=t(17750),n=e([u]);async function c(e){try{let r=await (0,o.nm0)({config:u.A}),t=e.headers.get("authorization");if(!t||!t.startsWith("Bearer "))return i.NextResponse.json({success:!1,error:"No valid authorization token provided",authenticated:!1},{status:401});let s=new Headers;s.set("authorization",t);let{user:n}=await r.auth({headers:s});if(!n)return i.NextResponse.json({success:!1,error:"Invalid or expired token",authenticated:!1},{status:401});let c={id:n.id,email:n.email,role:n.role,isActive:!1!==n.isActive,firstName:n.firstName,lastName:n.lastName};return i.NextResponse.json({success:!0,authenticated:!0,user:c,permissions:{isAdmin:"admin"===n.role,canSync:"admin"===n.role,canManageUsers:"admin"===n.role,canManageContent:["admin","content-creator"].includes(n.role)}})}catch(e){return console.error("Auth verification error:",e),i.NextResponse.json({success:!1,error:"Authentication verification failed",authenticated:!1},{status:500})}}u=(n.then?(await n)():n)[0],s()}catch(e){s(e)}})}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9572,9187,6425],()=>t(6446));module.exports=s})();