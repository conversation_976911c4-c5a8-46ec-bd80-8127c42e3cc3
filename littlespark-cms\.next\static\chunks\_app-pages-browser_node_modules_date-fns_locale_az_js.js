"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_date-fns_locale_az_js"],{

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/az.js":
/*!********************************************!*\
  !*** ./node_modules/date-fns/locale/az.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   az: () => (/* binding */ az),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _az_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./az/_lib/formatDistance.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/az/_lib/formatDistance.js\");\n/* harmony import */ var _az_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./az/_lib/formatLong.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/az/_lib/formatLong.js\");\n/* harmony import */ var _az_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./az/_lib/formatRelative.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/az/_lib/formatRelative.js\");\n/* harmony import */ var _az_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./az/_lib/localize.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/az/_lib/localize.js\");\n/* harmony import */ var _az_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./az/_lib/match.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/az/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary Azerbaijani locale.\n * @language Azerbaijani\n * @iso-639-2 aze\n */ const az = {\n    code: \"az\",\n    formatDistance: _az_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _az_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _az_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _az_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _az_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 1,\n        firstWeekContainsDate: 1\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (az);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9kYXRlLWZucy9sb2NhbGUvYXouanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE2RDtBQUNSO0FBQ1E7QUFDWjtBQUNOO0FBRTNDOzs7OztDQUtDLEdBRU0sTUFBTUssS0FBSztJQUNoQkMsTUFBTTtJQUNOTixnQkFBZ0JBLHFFQUFjQTtJQUM5QkMsWUFBWUEsNkRBQVVBO0lBQ3RCQyxnQkFBZ0JBLHFFQUFjQTtJQUM5QkMsVUFBVUEseURBQVFBO0lBQ2xCQyxPQUFPQSxtREFBS0E7SUFDWkcsU0FBUztRQUNQQyxjQUFjO1FBQ2RDLHVCQUF1QjtJQUN6QjtBQUNGLEVBQUU7QUFFRixvQ0FBb0M7QUFDcEMsaUVBQWVKLEVBQUVBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmF2aTFcXGthdnlhLWdpdFxcc3BhcmstbmV3XFxsaXR0bGVzcGFyay1jbXNcXG5vZGVfbW9kdWxlc1xcZGF0ZS1mbnNcXGxvY2FsZVxcYXouanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZm9ybWF0RGlzdGFuY2UgfSBmcm9tIFwiLi9hei9fbGliL2Zvcm1hdERpc3RhbmNlLmpzXCI7XG5pbXBvcnQgeyBmb3JtYXRMb25nIH0gZnJvbSBcIi4vYXovX2xpYi9mb3JtYXRMb25nLmpzXCI7XG5pbXBvcnQgeyBmb3JtYXRSZWxhdGl2ZSB9IGZyb20gXCIuL2F6L19saWIvZm9ybWF0UmVsYXRpdmUuanNcIjtcbmltcG9ydCB7IGxvY2FsaXplIH0gZnJvbSBcIi4vYXovX2xpYi9sb2NhbGl6ZS5qc1wiO1xuaW1wb3J0IHsgbWF0Y2ggfSBmcm9tIFwiLi9hei9fbGliL21hdGNoLmpzXCI7XG5cbi8qKlxuICogQGNhdGVnb3J5IExvY2FsZXNcbiAqIEBzdW1tYXJ5IEF6ZXJiYWlqYW5pIGxvY2FsZS5cbiAqIEBsYW5ndWFnZSBBemVyYmFpamFuaVxuICogQGlzby02MzktMiBhemVcbiAqL1xuXG5leHBvcnQgY29uc3QgYXogPSB7XG4gIGNvZGU6IFwiYXpcIixcbiAgZm9ybWF0RGlzdGFuY2U6IGZvcm1hdERpc3RhbmNlLFxuICBmb3JtYXRMb25nOiBmb3JtYXRMb25nLFxuICBmb3JtYXRSZWxhdGl2ZTogZm9ybWF0UmVsYXRpdmUsXG4gIGxvY2FsaXplOiBsb2NhbGl6ZSxcbiAgbWF0Y2g6IG1hdGNoLFxuICBvcHRpb25zOiB7XG4gICAgd2Vla1N0YXJ0c09uOiAxLFxuICAgIGZpcnN0V2Vla0NvbnRhaW5zRGF0ZTogMSxcbiAgfSxcbn07XG5cbi8vIEZhbGxiYWNrIGZvciBtb2R1bGFyaXplZCBpbXBvcnRzOlxuZXhwb3J0IGRlZmF1bHQgYXo7XG4iXSwibmFtZXMiOlsiZm9ybWF0RGlzdGFuY2UiLCJmb3JtYXRMb25nIiwiZm9ybWF0UmVsYXRpdmUiLCJsb2NhbGl6ZSIsIm1hdGNoIiwiYXoiLCJjb2RlIiwib3B0aW9ucyIsIndlZWtTdGFydHNPbiIsImZpcnN0V2Vla0NvbnRhaW5zRGF0ZSJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/az.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/az/_lib/formatDistance.js":
/*!****************************************************************!*\
  !*** ./node_modules/date-fns/locale/az/_lib/formatDistance.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\nconst formatDistanceLocale = {\n    lessThanXSeconds: {\n        one: \"bir saniyədən az\",\n        other: \"{{count}} bir saniyədən az\"\n    },\n    xSeconds: {\n        one: \"1 saniyə\",\n        other: \"{{count}} saniyə\"\n    },\n    halfAMinute: \"yarım dəqiqə\",\n    lessThanXMinutes: {\n        one: \"bir dəqiqədən az\",\n        other: \"{{count}} bir dəqiqədən az\"\n    },\n    xMinutes: {\n        one: \"bir dəqiqə\",\n        other: \"{{count}} dəqiqə\"\n    },\n    aboutXHours: {\n        one: \"təxminən 1 saat\",\n        other: \"təxminən {{count}} saat\"\n    },\n    xHours: {\n        one: \"1 saat\",\n        other: \"{{count}} saat\"\n    },\n    xDays: {\n        one: \"1 gün\",\n        other: \"{{count}} gün\"\n    },\n    aboutXWeeks: {\n        one: \"təxminən 1 həftə\",\n        other: \"təxminən {{count}} həftə\"\n    },\n    xWeeks: {\n        one: \"1 həftə\",\n        other: \"{{count}} həftə\"\n    },\n    aboutXMonths: {\n        one: \"təxminən 1 ay\",\n        other: \"təxminən {{count}} ay\"\n    },\n    xMonths: {\n        one: \"1 ay\",\n        other: \"{{count}} ay\"\n    },\n    aboutXYears: {\n        one: \"təxminən 1 il\",\n        other: \"təxminən {{count}} il\"\n    },\n    xYears: {\n        one: \"1 il\",\n        other: \"{{count}} il\"\n    },\n    overXYears: {\n        one: \"1 ildən çox\",\n        other: \"{{count}} ildən çox\"\n    },\n    almostXYears: {\n        one: \"demək olar ki 1 il\",\n        other: \"demək olar ki {{count}} il\"\n    }\n};\nconst formatDistance = (token, count, options)=>{\n    let result;\n    const tokenValue = formatDistanceLocale[token];\n    if (typeof tokenValue === \"string\") {\n        result = tokenValue;\n    } else if (count === 1) {\n        result = tokenValue.one;\n    } else {\n        result = tokenValue.other.replace(\"{{count}}\", String(count));\n    }\n    if (options === null || options === void 0 ? void 0 : options.addSuffix) {\n        if (options.comparison && options.comparison > 0) {\n            return result + \" sonra\";\n        } else {\n            return result + \" əvvəl\";\n        }\n    }\n    return result;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/az/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/az/_lib/formatLong.js":
/*!************************************************************!*\
  !*** ./node_modules/date-fns/locale/az/_lib/formatLong.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\nconst dateFormats = {\n    full: \"EEEE, do MMMM y 'il'\",\n    long: \"do MMMM y 'il'\",\n    medium: \"d MMM y 'il'\",\n    short: \"dd.MM.yyyy\"\n};\nconst timeFormats = {\n    full: \"H:mm:ss zzzz\",\n    long: \"H:mm:ss z\",\n    medium: \"H:mm:ss\",\n    short: \"H:mm\"\n};\nconst dateTimeFormats = {\n    full: \"{{date}} {{time}} - 'də'\",\n    long: \"{{date}} {{time}} - 'də'\",\n    medium: \"{{date}}, {{time}}\",\n    short: \"{{date}}, {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"full\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"full\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/az/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/az/_lib/formatRelative.js":
/*!****************************************************************!*\
  !*** ./node_modules/date-fns/locale/az/_lib/formatRelative.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\nconst formatRelativeLocale = {\n    lastWeek: \"'sonuncu' eeee p -'də'\",\n    yesterday: \"'dünən' p -'də'\",\n    today: \"'bugün' p -'də'\",\n    tomorrow: \"'sabah' p -'də'\",\n    nextWeek: \"eeee p -'də'\",\n    other: \"P\"\n};\nconst formatRelative = (token, _date, _baseDate, _options)=>formatRelativeLocale[token];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9kYXRlLWZucy9sb2NhbGUvYXovX2xpYi9mb3JtYXRSZWxhdGl2ZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsTUFBTUEsdUJBQXVCO0lBQzNCQyxVQUFVO0lBQ1ZDLFdBQVc7SUFDWEMsT0FBTztJQUNQQyxVQUFVO0lBQ1ZDLFVBQVU7SUFDVkMsT0FBTztBQUNUO0FBRU8sTUFBTUMsaUJBQWlCLENBQUNDLE9BQU9DLE9BQU9DLFdBQVdDLFdBQ3REWCxvQkFBb0IsQ0FBQ1EsTUFBTSxDQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJhdmkxXFxrYXZ5YS1naXRcXHNwYXJrLW5ld1xcbGl0dGxlc3BhcmstY21zXFxub2RlX21vZHVsZXNcXGRhdGUtZm5zXFxsb2NhbGVcXGF6XFxfbGliXFxmb3JtYXRSZWxhdGl2ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBmb3JtYXRSZWxhdGl2ZUxvY2FsZSA9IHtcbiAgbGFzdFdlZWs6IFwiJ3NvbnVuY3UnIGVlZWUgcCAtJ2TJmSdcIixcbiAgeWVzdGVyZGF5OiBcIidkw7xuyZluJyBwIC0nZMmZJ1wiLFxuICB0b2RheTogXCInYnVnw7xuJyBwIC0nZMmZJ1wiLFxuICB0b21vcnJvdzogXCInc2FiYWgnIHAgLSdkyZknXCIsXG4gIG5leHRXZWVrOiBcImVlZWUgcCAtJ2TJmSdcIixcbiAgb3RoZXI6IFwiUFwiLFxufTtcblxuZXhwb3J0IGNvbnN0IGZvcm1hdFJlbGF0aXZlID0gKHRva2VuLCBfZGF0ZSwgX2Jhc2VEYXRlLCBfb3B0aW9ucykgPT5cbiAgZm9ybWF0UmVsYXRpdmVMb2NhbGVbdG9rZW5dO1xuIl0sIm5hbWVzIjpbImZvcm1hdFJlbGF0aXZlTG9jYWxlIiwibGFzdFdlZWsiLCJ5ZXN0ZXJkYXkiLCJ0b2RheSIsInRvbW9ycm93IiwibmV4dFdlZWsiLCJvdGhlciIsImZvcm1hdFJlbGF0aXZlIiwidG9rZW4iLCJfZGF0ZSIsIl9iYXNlRGF0ZSIsIl9vcHRpb25zIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/az/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/az/_lib/localize.js":
/*!**********************************************************!*\
  !*** ./node_modules/date-fns/locale/az/_lib/localize.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\nconst eraValues = {\n    narrow: [\n        \"e.ə\",\n        \"b.e\"\n    ],\n    abbreviated: [\n        \"e.ə\",\n        \"b.e\"\n    ],\n    wide: [\n        \"eramızdan əvvəl\",\n        \"bizim era\"\n    ]\n};\nconst quarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"K1\",\n        \"K2\",\n        \"K3\",\n        \"K4\"\n    ],\n    wide: [\n        \"1ci kvartal\",\n        \"2ci kvartal\",\n        \"3cü kvartal\",\n        \"4cü kvartal\"\n    ]\n};\nconst monthValues = {\n    narrow: [\n        \"Y\",\n        \"F\",\n        \"M\",\n        \"A\",\n        \"M\",\n        \"İ\",\n        \"İ\",\n        \"A\",\n        \"S\",\n        \"O\",\n        \"N\",\n        \"D\"\n    ],\n    abbreviated: [\n        \"Yan\",\n        \"Fev\",\n        \"Mar\",\n        \"Apr\",\n        \"May\",\n        \"İyun\",\n        \"İyul\",\n        \"Avq\",\n        \"Sen\",\n        \"Okt\",\n        \"Noy\",\n        \"Dek\"\n    ],\n    wide: [\n        \"Yanvar\",\n        \"Fevral\",\n        \"Mart\",\n        \"Aprel\",\n        \"May\",\n        \"İyun\",\n        \"İyul\",\n        \"Avqust\",\n        \"Sentyabr\",\n        \"Oktyabr\",\n        \"Noyabr\",\n        \"Dekabr\"\n    ]\n};\nconst dayValues = {\n    narrow: [\n        \"B.\",\n        \"B.e\",\n        \"Ç.a\",\n        \"Ç.\",\n        \"C.a\",\n        \"C.\",\n        \"Ş.\"\n    ],\n    short: [\n        \"B.\",\n        \"B.e\",\n        \"Ç.a\",\n        \"Ç.\",\n        \"C.a\",\n        \"C.\",\n        \"Ş.\"\n    ],\n    abbreviated: [\n        \"Baz\",\n        \"Baz.e\",\n        \"Çər.a\",\n        \"Çər\",\n        \"Cüm.a\",\n        \"Cüm\",\n        \"Şə\"\n    ],\n    wide: [\n        \"Bazar\",\n        \"Bazar ertəsi\",\n        \"Çərşənbə axşamı\",\n        \"Çərşənbə\",\n        \"Cümə axşamı\",\n        \"Cümə\",\n        \"Şənbə\"\n    ]\n};\nconst dayPeriodValues = {\n    narrow: {\n        am: \"am\",\n        pm: \"pm\",\n        midnight: \"gecəyarı\",\n        noon: \"gün\",\n        morning: \"səhər\",\n        afternoon: \"gündüz\",\n        evening: \"axşam\",\n        night: \"gecə\"\n    },\n    abbreviated: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"gecəyarı\",\n        noon: \"gün\",\n        morning: \"səhər\",\n        afternoon: \"gündüz\",\n        evening: \"axşam\",\n        night: \"gecə\"\n    },\n    wide: {\n        am: \"a.m.\",\n        pm: \"p.m.\",\n        midnight: \"gecəyarı\",\n        noon: \"gün\",\n        morning: \"səhər\",\n        afternoon: \"gündüz\",\n        evening: \"axşam\",\n        night: \"gecə\"\n    }\n};\nconst formattingDayPeriodValues = {\n    narrow: {\n        am: \"a\",\n        pm: \"p\",\n        midnight: \"gecəyarı\",\n        noon: \"gün\",\n        morning: \"səhər\",\n        afternoon: \"gündüz\",\n        evening: \"axşam\",\n        night: \"gecə\"\n    },\n    abbreviated: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"gecəyarı\",\n        noon: \"gün\",\n        morning: \"səhər\",\n        afternoon: \"gündüz\",\n        evening: \"axşam\",\n        night: \"gecə\"\n    },\n    wide: {\n        am: \"a.m.\",\n        pm: \"p.m.\",\n        midnight: \"gecəyarı\",\n        noon: \"gün\",\n        morning: \"səhər\",\n        afternoon: \"gündüz\",\n        evening: \"axşam\",\n        night: \"gecə\"\n    }\n};\nconst suffixes = {\n    1: \"-inci\",\n    5: \"-inci\",\n    8: \"-inci\",\n    70: \"-inci\",\n    80: \"-inci\",\n    2: \"-nci\",\n    7: \"-nci\",\n    20: \"-nci\",\n    50: \"-nci\",\n    3: \"-üncü\",\n    4: \"-üncü\",\n    100: \"-üncü\",\n    6: \"-ncı\",\n    9: \"-uncu\",\n    10: \"-uncu\",\n    30: \"-uncu\",\n    60: \"-ıncı\",\n    90: \"-ıncı\"\n};\nconst getSuffix = (number)=>{\n    if (number === 0) {\n        // special case for zero\n        return number + \"-ıncı\";\n    }\n    const a = number % 10;\n    const b = number % 100 - a;\n    const c = number >= 100 ? 100 : null;\n    if (suffixes[a]) {\n        return suffixes[a];\n    } else if (suffixes[b]) {\n        return suffixes[b];\n    } else if (c !== null) {\n        return suffixes[c];\n    }\n    return \"\";\n};\nconst ordinalNumber = (dirtyNumber, _options)=>{\n    const number = Number(dirtyNumber);\n    const suffix = getSuffix(number);\n    return number + suffix;\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        defaultWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"wide\",\n        formattingValues: formattingDayPeriodValues,\n        defaultFormattingWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/az/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/az/_lib/match.js":
/*!*******************************************************!*\
  !*** ./node_modules/date-fns/locale/az/_lib/match.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^(\\d+)(-?(ci|inci|nci|uncu|üncü|ncı))?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^(b|a)$/i,\n    abbreviated: /^(b\\.?\\s?c\\.?|b\\.?\\s?c\\.?\\s?e\\.?|a\\.?\\s?d\\.?|c\\.?\\s?e\\.?)$/i,\n    wide: /^(bizim eradan əvvəl|bizim era)$/i\n};\nconst parseEraPatterns = {\n    any: [\n        /^b$/i,\n        /^(a|c)$/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]$/i,\n    abbreviated: /^K[1234]$/i,\n    wide: /^[1234](ci)? kvartal$/i\n};\nconst parseQuarterPatterns = {\n    any: [\n        /1/i,\n        /2/i,\n        /3/i,\n        /4/i\n    ]\n};\nconst matchMonthPatterns = {\n    narrow: /^[(?-i)yfmaisond]$/i,\n    abbreviated: /^(Yan|Fev|Mar|Apr|May|İyun|İyul|Avq|Sen|Okt|Noy|Dek)$/i,\n    wide: /^(Yanvar|Fevral|Mart|Aprel|May|İyun|İyul|Avgust|Sentyabr|Oktyabr|Noyabr|Dekabr)$/i\n};\nconst parseMonthPatterns = {\n    narrow: [\n        /^[(?-i)y]$/i,\n        /^[(?-i)f]$/i,\n        /^[(?-i)m]$/i,\n        /^[(?-i)a]$/i,\n        /^[(?-i)m]$/i,\n        /^[(?-i)i]$/i,\n        /^[(?-i)i]$/i,\n        /^[(?-i)a]$/i,\n        /^[(?-i)s]$/i,\n        /^[(?-i)o]$/i,\n        /^[(?-i)n]$/i,\n        /^[(?-i)d]$/i\n    ],\n    abbreviated: [\n        /^Yan$/i,\n        /^Fev$/i,\n        /^Mar$/i,\n        /^Apr$/i,\n        /^May$/i,\n        /^İyun$/i,\n        /^İyul$/i,\n        /^Avg$/i,\n        /^Sen$/i,\n        /^Okt$/i,\n        /^Noy$/i,\n        /^Dek$/i\n    ],\n    wide: [\n        /^Yanvar$/i,\n        /^Fevral$/i,\n        /^Mart$/i,\n        /^Aprel$/i,\n        /^May$/i,\n        /^İyun$/i,\n        /^İyul$/i,\n        /^Avgust$/i,\n        /^Sentyabr$/i,\n        /^Oktyabr$/i,\n        /^Noyabr$/i,\n        /^Dekabr$/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^(B\\.|B\\.e|Ç\\.a|Ç\\.|C\\.a|C\\.|Ş\\.)$/i,\n    short: /^(B\\.|B\\.e|Ç\\.a|Ç\\.|C\\.a|C\\.|Ş\\.)$/i,\n    abbreviated: /^(Baz\\.e|Çər|Çər\\.a|Cüm|Cüm\\.a|Şə)$/i,\n    wide: /^(Bazar|Bazar ertəsi|Çərşənbə axşamı|Çərşənbə|Cümə axşamı|Cümə|Şənbə)$/i\n};\nconst parseDayPatterns = {\n    narrow: [\n        /^B\\.$/i,\n        /^B\\.e$/i,\n        /^Ç\\.a$/i,\n        /^Ç\\.$/i,\n        /^C\\.a$/i,\n        /^C\\.$/i,\n        /^Ş\\.$/i\n    ],\n    abbreviated: [\n        /^Baz$/i,\n        /^Baz\\.e$/i,\n        /^Çər\\.a$/i,\n        /^Çər$/i,\n        /^Cüm\\.a$/i,\n        /^Cüm$/i,\n        /^Şə$/i\n    ],\n    wide: [\n        /^Bazar$/i,\n        /^Bazar ertəsi$/i,\n        /^Çərşənbə axşamı$/i,\n        /^Çərşənbə$/i,\n        /^Cümə axşamı$/i,\n        /^Cümə$/i,\n        /^Şənbə$/i\n    ],\n    any: [\n        /^B\\.$/i,\n        /^B\\.e$/i,\n        /^Ç\\.a$/i,\n        /^Ç\\.$/i,\n        /^C\\.a$/i,\n        /^C\\.$/i,\n        /^Ş\\.$/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    narrow: /^(a|p|gecəyarı|gün|səhər|gündüz|axşam|gecə)$/i,\n    any: /^(am|pm|a\\.m\\.|p\\.m\\.|AM|PM|gecəyarı|gün|səhər|gündüz|axşam|gecə)$/i\n};\nconst parseDayPeriodPatterns = {\n    any: {\n        am: /^a$/i,\n        pm: /^p$/i,\n        midnight: /^gecəyarı$/i,\n        noon: /^gün$/i,\n        morning: /səhər$/i,\n        afternoon: /gündüz$/i,\n        evening: /axşam$/i,\n        night: /gecə$/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value, 10)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"narrow\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"any\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/az/_lib/match.js\n"));

/***/ })

}]);