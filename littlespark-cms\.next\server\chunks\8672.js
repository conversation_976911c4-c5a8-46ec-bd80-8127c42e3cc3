"use strict";exports.id=8672,exports.ids=[8672],exports.modules={22232:(a,b,c)=>{c.d(b,{CG:()=>f,Y2:()=>e,cJ:()=>g});var d=c(70525);let e=(a,b)=>(0,d.w)(a,b).then(a=>{if(a.length)try{return JSON.parse(a)}catch(b){throw b?.name==="SyntaxError"&&Object.defineProperty(b,"$responseBodyText",{value:a}),b}return{}}),f=async(a,b)=>{let c=await e(a,b);return c.message=c.message??c.Message,c},g=(a,b)=>{let c=(a,b)=>Object.keys(a).find(a=>a.toLowerCase()===b.toLowerCase()),d=a=>{let b=a;return"number"==typeof b&&(b=b.toString()),b.indexOf(",")>=0&&(b=b.split(",")[0]),b.indexOf(":")>=0&&(b=b.split(":")[0]),b.indexOf("#")>=0&&(b=b.split("#")[1]),b},e=c(a.headers,"x-amzn-errortype");if(void 0!==e)return d(a.headers[e]);if(b&&"object"==typeof b){let a=c(b,"code");if(a&&void 0!==b[a])return d(b[a]);if(void 0!==b.__type)return d(b.__type)}}},28125:(a,b,c)=>{c.d(b,{l:()=>f});var d=c(31734),e=c(26719);function f(a,b){return new g(a,b)}class g{constructor(a,b){this.input=a,this.context=b,this.query={},this.method="",this.headers={},this.path="",this.body=null,this.hostname="",this.resolvePathStack=[]}async build(){let{hostname:a,protocol:b="https",port:c,path:e}=await this.context.endpoint();for(let a of(this.path=e,this.resolvePathStack))a(this.path);return new d.Kd({protocol:b,hostname:this.hostname||a,port:c,method:this.method,path:this.path,query:this.query,body:this.body,headers:this.headers})}hn(a){return this.hostname=a,this}bp(a){return this.resolvePathStack.push(b=>{this.path=`${b?.endsWith("/")?b.slice(0,-1):b||""}`+a}),this}p(a,b,c,d){return this.resolvePathStack.push(f=>{this.path=((a,b,c,d,f,g)=>{if(null!=b&&void 0!==b[c]){let b=d();if(b.length<=0)throw Error("Empty value provided for input HTTP label: "+c+".");a=a.replace(f,g?b.split("/").map(a=>(0,e.$)(a)).join("/"):(0,e.$)(b))}else throw Error("No value provided for input HTTP label: "+c+".");return a})(f,this.input,a,b,c,d)}),this}h(a){return this.headers=a,this}q(a){return this.query=a,this}b(a){return this.body=a,this}m(a){return this.method=a,this}}},43279:a=>{a.exports={rE:"3.855.0"}},58672:(a,b,c)=>{c.d(b,{CreateTokenCommand:()=>aR,SSOOIDCClient:()=>aj});var d=c(36576),e=c(43208),f=c(22218),g=c(59883),h=c(6284),i=c(7821),j=c(60043),k=c(18154),l=c(68270),m=c(59727),n=c(71508),o=c(35639),p=c(78577),q=c(90378);let r=async(a,b,c)=>({operation:(0,q.u)(b).operation,region:await (0,q.t)(a.region)()||(()=>{throw Error("expected `region` to be configured for `aws.auth#sigv4`")})()}),s=a=>{let b=[];if("CreateToken"===a.operation)b.push({schemeId:"smithy.api#noAuth"});else b.push({schemeId:"aws.auth#sigv4",signingProperties:{name:"sso-oauth",region:a.region},propertiesExtractor:(a,b)=>({signingProperties:{config:a,context:b}})});return b},t={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}};var u=c(43279),v=c(95941),w=c(30438),x=c(54285),y=c(5334),z=c(1226),A=c(66426),B=c(11396),C=c(75843),D=c(75089),E=c(4553),F=c(68378),G=c(139),H=c(37979),I=c(92171),J=c(90599);let K="required",L="argv",M="isSet",N="booleanEquals",O="error",P="endpoint",Q="tree",R="PartitionResult",S="getAttr",T={[K]:!1,type:"String"},U={[K]:!0,default:!1,type:"Boolean"},V={ref:"Endpoint"},W={fn:N,[L]:[{ref:"UseFIPS"},!0]},X={fn:N,[L]:[{ref:"UseDualStack"},!0]},Y={},Z={fn:S,[L]:[{ref:R},"supportsFIPS"]},$={ref:R},_={fn:N,[L]:[!0,{fn:S,[L]:[$,"supportsDualStack"]}]},aa=[W],ab=[X],ac=[{ref:"Region"}],ad={version:"1.0",parameters:{Region:T,UseDualStack:U,UseFIPS:U,Endpoint:T},rules:[{conditions:[{fn:M,[L]:[V]}],rules:[{conditions:aa,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:O},{conditions:ab,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:O},{endpoint:{url:V,properties:Y,headers:Y},type:P}],type:Q},{conditions:[{fn:M,[L]:ac}],rules:[{conditions:[{fn:"aws.partition",[L]:ac,assign:R}],rules:[{conditions:[W,X],rules:[{conditions:[{fn:N,[L]:[!0,Z]},_],rules:[{endpoint:{url:"https://oidc-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:Y,headers:Y},type:P}],type:Q},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:O}],type:Q},{conditions:aa,rules:[{conditions:[{fn:N,[L]:[Z,!0]}],rules:[{conditions:[{fn:"stringEquals",[L]:[{fn:S,[L]:[$,"name"]},"aws-us-gov"]}],endpoint:{url:"https://oidc.{Region}.amazonaws.com",properties:Y,headers:Y},type:P},{endpoint:{url:"https://oidc-fips.{Region}.{PartitionResult#dnsSuffix}",properties:Y,headers:Y},type:P}],type:Q},{error:"FIPS is enabled but this partition does not support FIPS",type:O}],type:Q},{conditions:ab,rules:[{conditions:[_],rules:[{endpoint:{url:"https://oidc.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:Y,headers:Y},type:P}],type:Q},{error:"DualStack is enabled but this partition does not support DualStack",type:O}],type:Q},{endpoint:{url:"https://oidc.{Region}.{PartitionResult#dnsSuffix}",properties:Y,headers:Y},type:P}],type:Q}],type:Q},{error:"Invalid Configuration: Missing Region",type:O}]},ae=new J.kS({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),af=(a,b={})=>ae.get(a,()=>(0,J.sO)(ad,{endpointParams:a,logger:b.logger}));J.mw.aws=I.UF;var ag=c(52262),ah=c(41352),ai=c(31734);class aj extends o.Kj{config;constructor(...[a]){let b=(a=>{(0,o.I9)(process.version);let b=(0,ag.I)(a),c=()=>b().then(o.lT),d=(a=>({apiVersion:"2019-06-10",base64Decoder:a?.base64Decoder??G.E,base64Encoder:a?.base64Encoder??G.n,disableHostPrefix:a?.disableHostPrefix??!1,endpointProvider:a?.endpointProvider??af,extensions:a?.extensions??[],httpAuthSchemeProvider:a?.httpAuthSchemeProvider??s,httpAuthSchemes:a?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:a=>a.getIdentityProvider("aws.auth#sigv4"),signer:new D.f2},{schemeId:"smithy.api#noAuth",identityProvider:a=>a.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new E.m}],logger:a?.logger??new o.N4,serviceId:a?.serviceId??"SSO OIDC",urlParser:a?.urlParser??F.D,utf8Decoder:a?.utf8Decoder??H.ar,utf8Encoder:a?.utf8Encoder??H.Pq}))(a);(0,v.I)(process.version);let e={profile:a?.profile,logger:d.logger};return{...d,...a,runtime:"node",defaultsMode:b,authSchemePreference:a?.authSchemePreference??(0,z.Z)(w.$,e),bodyLengthChecker:a?.bodyLengthChecker??B.n,defaultUserAgentProvider:a?.defaultUserAgentProvider??(0,x.pf)({serviceId:d.serviceId,clientVersion:u.rE}),maxAttempts:a?.maxAttempts??(0,z.Z)(n.qs,a),region:a?.region??(0,z.Z)(h.GG,{...h.zH,...e}),requestHandler:A.$c.create(a?.requestHandler??c),retryMode:a?.retryMode??(0,z.Z)({...n.kN,default:async()=>(await c()).retryMode||C.L0},a),sha256:a?.sha256??y.V.bind(null,"sha256"),streamCollector:a?.streamCollector??A.kv,useDualstackEndpoint:a?.useDualstackEndpoint??(0,z.Z)(h.e$,e),useFipsEndpoint:a?.useFipsEndpoint??(0,z.Z)(h.Ko,e),userAgentAppId:a?.userAgentAppId??(0,z.Z)(x.hV,e)}})(a||{});super(b),this.initConfig=b;let c=(a=>Object.assign(a,{useDualstackEndpoint:a.useDualstackEndpoint??!1,useFipsEndpoint:a.useFipsEndpoint??!1,defaultSigningName:"sso-oauth"}))(b),t=(0,g.Dc)(c),I=(0,n.$z)(t),J=(0,h.TD)(I),K=(0,d.OV)(J),L=((a,b)=>{let c=Object.assign((0,ah.Rq)(a),(0,o.xA)(a),(0,ai.eS)(a),(a=>{let b=a.httpAuthSchemes,c=a.httpAuthSchemeProvider,d=a.credentials;return{setHttpAuthScheme(a){let c=b.findIndex(b=>b.schemeId===a.schemeId);-1===c?b.push(a):b.splice(c,1,a)},httpAuthSchemes:()=>b,setHttpAuthSchemeProvider(a){c=a},httpAuthSchemeProvider:()=>c,setCredentials(a){d=a},credentials:()=>d}})(a));return b.forEach(a=>a.configure(c)),Object.assign(a,(0,ah.$3)(c),(0,o.uv)(c),(0,ai.jt)(c),(a=>({httpAuthSchemes:a.httpAuthSchemes(),httpAuthSchemeProvider:a.httpAuthSchemeProvider(),credentials:a.credentials()}))(c))})((a=>Object.assign((0,p.h)(a),{authSchemePreference:(0,q.t)(a.authSchemePreference??[])}))((0,m.Co)(K)),a?.extensions||[]);this.config=L,this.middlewareStack.use((0,g.sM)(this.config)),this.middlewareStack.use((0,n.ey)(this.config)),this.middlewareStack.use((0,l.vK)(this.config)),this.middlewareStack.use((0,d.TC)(this.config)),this.middlewareStack.use((0,e.Y7)(this.config)),this.middlewareStack.use((0,f.n4)(this.config)),this.middlewareStack.use((0,i.w)(this.config,{httpAuthSchemeParametersProvider:r,identityProviderConfigProvider:async a=>new j.h({"aws.auth#sigv4":a.credentials})})),this.middlewareStack.use((0,k.l)(this.config))}destroy(){super.destroy()}}var ak=c(8839);class al extends o.TJ{constructor(a){super(a),Object.setPrototypeOf(this,al.prototype)}}class am extends al{name="AccessDeniedException";$fault="client";error;error_description;constructor(a){super({name:"AccessDeniedException",$fault:"client",...a}),Object.setPrototypeOf(this,am.prototype),this.error=a.error,this.error_description=a.error_description}}class an extends al{name="AuthorizationPendingException";$fault="client";error;error_description;constructor(a){super({name:"AuthorizationPendingException",$fault:"client",...a}),Object.setPrototypeOf(this,an.prototype),this.error=a.error,this.error_description=a.error_description}}let ao=a=>({...a,...a.clientSecret&&{clientSecret:o.$H},...a.refreshToken&&{refreshToken:o.$H},...a.codeVerifier&&{codeVerifier:o.$H}}),ap=a=>({...a,...a.accessToken&&{accessToken:o.$H},...a.refreshToken&&{refreshToken:o.$H},...a.idToken&&{idToken:o.$H}});class aq extends al{name="ExpiredTokenException";$fault="client";error;error_description;constructor(a){super({name:"ExpiredTokenException",$fault:"client",...a}),Object.setPrototypeOf(this,aq.prototype),this.error=a.error,this.error_description=a.error_description}}class ar extends al{name="InternalServerException";$fault="server";error;error_description;constructor(a){super({name:"InternalServerException",$fault:"server",...a}),Object.setPrototypeOf(this,ar.prototype),this.error=a.error,this.error_description=a.error_description}}class as extends al{name="InvalidClientException";$fault="client";error;error_description;constructor(a){super({name:"InvalidClientException",$fault:"client",...a}),Object.setPrototypeOf(this,as.prototype),this.error=a.error,this.error_description=a.error_description}}class at extends al{name="InvalidGrantException";$fault="client";error;error_description;constructor(a){super({name:"InvalidGrantException",$fault:"client",...a}),Object.setPrototypeOf(this,at.prototype),this.error=a.error,this.error_description=a.error_description}}class au extends al{name="InvalidRequestException";$fault="client";error;error_description;constructor(a){super({name:"InvalidRequestException",$fault:"client",...a}),Object.setPrototypeOf(this,au.prototype),this.error=a.error,this.error_description=a.error_description}}class av extends al{name="InvalidScopeException";$fault="client";error;error_description;constructor(a){super({name:"InvalidScopeException",$fault:"client",...a}),Object.setPrototypeOf(this,av.prototype),this.error=a.error,this.error_description=a.error_description}}class aw extends al{name="SlowDownException";$fault="client";error;error_description;constructor(a){super({name:"SlowDownException",$fault:"client",...a}),Object.setPrototypeOf(this,aw.prototype),this.error=a.error,this.error_description=a.error_description}}class ax extends al{name="UnauthorizedClientException";$fault="client";error;error_description;constructor(a){super({name:"UnauthorizedClientException",$fault:"client",...a}),Object.setPrototypeOf(this,ax.prototype),this.error=a.error,this.error_description=a.error_description}}class ay extends al{name="UnsupportedGrantTypeException";$fault="client";error;error_description;constructor(a){super({name:"UnsupportedGrantTypeException",$fault:"client",...a}),Object.setPrototypeOf(this,ay.prototype),this.error=a.error,this.error_description=a.error_description}}var az=c(22232),aA=c(28125);let aB=async(a,b)=>{let c,d=(0,aA.l)(a,b);return d.bp("/token"),c=JSON.stringify((0,o.s)(a,{clientId:[],clientSecret:[],code:[],codeVerifier:[],deviceCode:[],grantType:[],redirectUri:[],refreshToken:[],scope:a=>(0,o.Ss)(a)})),d.m("POST").h({"content-type":"application/json"}).b(c),d.build()},aC=async(a,b)=>{if(200!==a.statusCode&&a.statusCode>=300)return aD(a,b);let c=(0,o.Tj)({$metadata:aQ(a)}),d=(0,o.Y0)((0,o.Xk)(await (0,az.Y2)(a.body,b)),"body");return Object.assign(c,(0,o.s)(d,{accessToken:o.lK,expiresIn:o.ET,idToken:o.lK,refreshToken:o.lK,tokenType:o.lK})),c},aD=async(a,b)=>{let c={...a,body:await (0,az.CG)(a.body,b)},d=(0,az.cJ)(a,c.body);switch(d){case"AccessDeniedException":case"com.amazonaws.ssooidc#AccessDeniedException":throw await aF(c,b);case"AuthorizationPendingException":case"com.amazonaws.ssooidc#AuthorizationPendingException":throw await aG(c,b);case"ExpiredTokenException":case"com.amazonaws.ssooidc#ExpiredTokenException":throw await aH(c,b);case"InternalServerException":case"com.amazonaws.ssooidc#InternalServerException":throw await aI(c,b);case"InvalidClientException":case"com.amazonaws.ssooidc#InvalidClientException":throw await aJ(c,b);case"InvalidGrantException":case"com.amazonaws.ssooidc#InvalidGrantException":throw await aK(c,b);case"InvalidRequestException":case"com.amazonaws.ssooidc#InvalidRequestException":throw await aL(c,b);case"InvalidScopeException":case"com.amazonaws.ssooidc#InvalidScopeException":throw await aM(c,b);case"SlowDownException":case"com.amazonaws.ssooidc#SlowDownException":throw await aN(c,b);case"UnauthorizedClientException":case"com.amazonaws.ssooidc#UnauthorizedClientException":throw await aO(c,b);case"UnsupportedGrantTypeException":case"com.amazonaws.ssooidc#UnsupportedGrantTypeException":throw await aP(c,b);default:return aE({output:a,parsedBody:c.body,errorCode:d})}},aE=(0,o.jr)(al),aF=async(a,b)=>{let c=(0,o.Tj)({}),d=a.body;Object.assign(c,(0,o.s)(d,{error:o.lK,error_description:o.lK}));let e=new am({$metadata:aQ(a),...c});return(0,o.Mw)(e,a.body)},aG=async(a,b)=>{let c=(0,o.Tj)({}),d=a.body;Object.assign(c,(0,o.s)(d,{error:o.lK,error_description:o.lK}));let e=new an({$metadata:aQ(a),...c});return(0,o.Mw)(e,a.body)},aH=async(a,b)=>{let c=(0,o.Tj)({}),d=a.body;Object.assign(c,(0,o.s)(d,{error:o.lK,error_description:o.lK}));let e=new aq({$metadata:aQ(a),...c});return(0,o.Mw)(e,a.body)},aI=async(a,b)=>{let c=(0,o.Tj)({}),d=a.body;Object.assign(c,(0,o.s)(d,{error:o.lK,error_description:o.lK}));let e=new ar({$metadata:aQ(a),...c});return(0,o.Mw)(e,a.body)},aJ=async(a,b)=>{let c=(0,o.Tj)({}),d=a.body;Object.assign(c,(0,o.s)(d,{error:o.lK,error_description:o.lK}));let e=new as({$metadata:aQ(a),...c});return(0,o.Mw)(e,a.body)},aK=async(a,b)=>{let c=(0,o.Tj)({}),d=a.body;Object.assign(c,(0,o.s)(d,{error:o.lK,error_description:o.lK}));let e=new at({$metadata:aQ(a),...c});return(0,o.Mw)(e,a.body)},aL=async(a,b)=>{let c=(0,o.Tj)({}),d=a.body;Object.assign(c,(0,o.s)(d,{error:o.lK,error_description:o.lK}));let e=new au({$metadata:aQ(a),...c});return(0,o.Mw)(e,a.body)},aM=async(a,b)=>{let c=(0,o.Tj)({}),d=a.body;Object.assign(c,(0,o.s)(d,{error:o.lK,error_description:o.lK}));let e=new av({$metadata:aQ(a),...c});return(0,o.Mw)(e,a.body)},aN=async(a,b)=>{let c=(0,o.Tj)({}),d=a.body;Object.assign(c,(0,o.s)(d,{error:o.lK,error_description:o.lK}));let e=new aw({$metadata:aQ(a),...c});return(0,o.Mw)(e,a.body)},aO=async(a,b)=>{let c=(0,o.Tj)({}),d=a.body;Object.assign(c,(0,o.s)(d,{error:o.lK,error_description:o.lK}));let e=new ax({$metadata:aQ(a),...c});return(0,o.Mw)(e,a.body)},aP=async(a,b)=>{let c=(0,o.Tj)({}),d=a.body;Object.assign(c,(0,o.s)(d,{error:o.lK,error_description:o.lK}));let e=new ay({$metadata:aQ(a),...c});return(0,o.Mw)(e,a.body)},aQ=a=>({httpStatusCode:a.statusCode,requestId:a.headers["x-amzn-requestid"]??a.headers["x-amzn-request-id"]??a.headers["x-amz-request-id"],extendedRequestId:a.headers["x-amz-id-2"],cfId:a.headers["x-amz-cf-id"]});class aR extends o.uB.classBuilder().ep(t).m(function(a,b,c,d){return[(0,ak.TM)(c,this.serialize,this.deserialize),(0,m.rD)(c,a.getEndpointParameterInstructions())]}).s("AWSSSOOIDCService","CreateToken",{}).n("SSOOIDCClient","CreateTokenCommand").f(ao,ap).ser(aB).de(aC).build(){}class aS extends aj{}(0,o.J1)({CreateTokenCommand:aR},aS)},70525:(a,b,c)=>{c.d(b,{w:()=>e});var d=c(35639);let e=(a,b)=>(0,d.Px)(a,b).then(a=>b.utf8Encoder(a))}};