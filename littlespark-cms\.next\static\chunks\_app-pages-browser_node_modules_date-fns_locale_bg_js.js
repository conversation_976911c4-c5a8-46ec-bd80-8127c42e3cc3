"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_date-fns_locale_bg_js"],{

/***/ "(app-pages-browser)/./node_modules/date-fns/isSameWeek.js":
/*!*********************************************!*\
  !*** ./node_modules/date-fns/isSameWeek.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isSameWeek: () => (/* binding */ isSameWeek)\n/* harmony export */ });\n/* harmony import */ var _lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_lib/normalizeDates.js */ \"(app-pages-browser)/./node_modules/date-fns/_lib/normalizeDates.js\");\n/* harmony import */ var _startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./startOfWeek.js */ \"(app-pages-browser)/./node_modules/date-fns/startOfWeek.js\");\n\n\n/**\n * The {@link isSameWeek} function options.\n */ /**\n * @name isSameWeek\n * @category Week Helpers\n * @summary Are the given dates in the same week (and month and year)?\n *\n * @description\n * Are the given dates in the same week (and month and year)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same week (and month and year)\n *\n * @example\n * // Are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4))\n * //=> true\n *\n * @example\n * // If week starts with Monday,\n * // are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4), {\n *   weekStartsOn: 1\n * })\n * //=> false\n *\n * @example\n * // Are 1 January 2014 and 1 January 2015 in the same week?\n * const result = isSameWeek(new Date(2014, 0, 1), new Date(2015, 0, 1))\n * //=> false\n */ function isSameWeek(laterDate, earlierDate, options) {\n    const [laterDate_, earlierDate_] = (0,_lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__.normalizeDates)(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate);\n    return +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(laterDate_, options) === +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(earlierDate_, options);\n}\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (isSameWeek);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/isSameWeek.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/bg.js":
/*!********************************************!*\
  !*** ./node_modules/date-fns/locale/bg.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bg: () => (/* binding */ bg),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _bg_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./bg/_lib/formatDistance.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/bg/_lib/formatDistance.js\");\n/* harmony import */ var _bg_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./bg/_lib/formatLong.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/bg/_lib/formatLong.js\");\n/* harmony import */ var _bg_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./bg/_lib/formatRelative.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/bg/_lib/formatRelative.js\");\n/* harmony import */ var _bg_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./bg/_lib/localize.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/bg/_lib/localize.js\");\n/* harmony import */ var _bg_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./bg/_lib/match.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/bg/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary Bulgarian locale.\n * @language Bulgarian\n * @iso-639-2 bul\n * <AUTHOR> Stoynov [@arvigeus](https://github.com/arvigeus)\n * <AUTHOR> Ovedenski [@fintara](https://github.com/fintara)\n */ const bg = {\n    code: \"bg\",\n    formatDistance: _bg_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _bg_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _bg_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _bg_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _bg_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 1 /* Monday */ ,\n        firstWeekContainsDate: 1\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (bg);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/bg.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/bg/_lib/formatDistance.js":
/*!****************************************************************!*\
  !*** ./node_modules/date-fns/locale/bg/_lib/formatDistance.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\nconst formatDistanceLocale = {\n    lessThanXSeconds: {\n        one: \"по-малко от секунда\",\n        other: \"по-малко от {{count}} секунди\"\n    },\n    xSeconds: {\n        one: \"1 секунда\",\n        other: \"{{count}} секунди\"\n    },\n    halfAMinute: \"половин минута\",\n    lessThanXMinutes: {\n        one: \"по-малко от минута\",\n        other: \"по-малко от {{count}} минути\"\n    },\n    xMinutes: {\n        one: \"1 минута\",\n        other: \"{{count}} минути\"\n    },\n    aboutXHours: {\n        one: \"около час\",\n        other: \"около {{count}} часа\"\n    },\n    xHours: {\n        one: \"1 час\",\n        other: \"{{count}} часа\"\n    },\n    xDays: {\n        one: \"1 ден\",\n        other: \"{{count}} дни\"\n    },\n    aboutXWeeks: {\n        one: \"около седмица\",\n        other: \"около {{count}} седмици\"\n    },\n    xWeeks: {\n        one: \"1 седмица\",\n        other: \"{{count}} седмици\"\n    },\n    aboutXMonths: {\n        one: \"около месец\",\n        other: \"около {{count}} месеца\"\n    },\n    xMonths: {\n        one: \"1 месец\",\n        other: \"{{count}} месеца\"\n    },\n    aboutXYears: {\n        one: \"около година\",\n        other: \"около {{count}} години\"\n    },\n    xYears: {\n        one: \"1 година\",\n        other: \"{{count}} години\"\n    },\n    overXYears: {\n        one: \"над година\",\n        other: \"над {{count}} години\"\n    },\n    almostXYears: {\n        one: \"почти година\",\n        other: \"почти {{count}} години\"\n    }\n};\nconst formatDistance = (token, count, options)=>{\n    let result;\n    const tokenValue = formatDistanceLocale[token];\n    if (typeof tokenValue === \"string\") {\n        result = tokenValue;\n    } else if (count === 1) {\n        result = tokenValue.one;\n    } else {\n        result = tokenValue.other.replace(\"{{count}}\", String(count));\n    }\n    if (options === null || options === void 0 ? void 0 : options.addSuffix) {\n        if (options.comparison && options.comparison > 0) {\n            return \"след \" + result;\n        } else {\n            return \"преди \" + result;\n        }\n    }\n    return result;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/bg/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/bg/_lib/formatLong.js":
/*!************************************************************!*\
  !*** ./node_modules/date-fns/locale/bg/_lib/formatLong.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\nconst dateFormats = {\n    full: \"EEEE, dd MMMM yyyy\",\n    long: \"dd MMMM yyyy\",\n    medium: \"dd MMM yyyy\",\n    short: \"dd.MM.yyyy\"\n};\nconst timeFormats = {\n    full: \"HH:mm:ss zzzz\",\n    long: \"HH:mm:ss z\",\n    medium: \"HH:mm:ss\",\n    short: \"H:mm\"\n};\nconst dateTimeFormats = {\n    any: \"{{date}} {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"full\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/bg/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/bg/_lib/formatRelative.js":
/*!****************************************************************!*\
  !*** ./node_modules/date-fns/locale/bg/_lib/formatRelative.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\n/* harmony import */ var _isSameWeek_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../isSameWeek.js */ \"(app-pages-browser)/./node_modules/date-fns/isSameWeek.js\");\n/* harmony import */ var _toDate_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../toDate.js */ \"(app-pages-browser)/./node_modules/date-fns/toDate.js\");\n\n\n// Adapted from the `ru` translation\nconst weekdays = [\n    \"неделя\",\n    \"понеделник\",\n    \"вторник\",\n    \"сряда\",\n    \"четвъртък\",\n    \"петък\",\n    \"събота\"\n];\nfunction lastWeek(day) {\n    const weekday = weekdays[day];\n    switch(day){\n        case 0:\n        case 3:\n        case 6:\n            return \"'миналата \" + weekday + \" в' p\";\n        case 1:\n        case 2:\n        case 4:\n        case 5:\n            return \"'миналия \" + weekday + \" в' p\";\n    }\n}\nfunction thisWeek(day) {\n    const weekday = weekdays[day];\n    if (day === 2 /* Tue */ ) {\n        return \"'във \" + weekday + \" в' p\";\n    } else {\n        return \"'в \" + weekday + \" в' p\";\n    }\n}\nfunction nextWeek(day) {\n    const weekday = weekdays[day];\n    switch(day){\n        case 0:\n        case 3:\n        case 6:\n            return \"'следващата \" + weekday + \" в' p\";\n        case 1:\n        case 2:\n        case 4:\n        case 5:\n            return \"'следващия \" + weekday + \" в' p\";\n    }\n}\nconst lastWeekFormatToken = (dirtyDate, baseDate, options)=>{\n    const date = (0,_toDate_js__WEBPACK_IMPORTED_MODULE_0__.toDate)(dirtyDate);\n    const day = date.getDay();\n    if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_1__.isSameWeek)(date, baseDate, options)) {\n        return thisWeek(day);\n    } else {\n        return lastWeek(day);\n    }\n};\nconst nextWeekFormatToken = (dirtyDate, baseDate, options)=>{\n    const date = (0,_toDate_js__WEBPACK_IMPORTED_MODULE_0__.toDate)(dirtyDate);\n    const day = date.getDay();\n    if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_1__.isSameWeek)(date, baseDate, options)) {\n        return thisWeek(day);\n    } else {\n        return nextWeek(day);\n    }\n};\nconst formatRelativeLocale = {\n    lastWeek: lastWeekFormatToken,\n    yesterday: \"'вчера в' p\",\n    today: \"'днес в' p\",\n    tomorrow: \"'утре в' p\",\n    nextWeek: nextWeekFormatToken,\n    other: \"P\"\n};\nconst formatRelative = (token, date, baseDate, options)=>{\n    const format = formatRelativeLocale[token];\n    if (typeof format === \"function\") {\n        return format(date, baseDate, options);\n    }\n    return format;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/bg/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/bg/_lib/localize.js":
/*!**********************************************************!*\
  !*** ./node_modules/date-fns/locale/bg/_lib/localize.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\nconst eraValues = {\n    narrow: [\n        \"пр.н.е.\",\n        \"н.е.\"\n    ],\n    abbreviated: [\n        \"преди н. е.\",\n        \"н. е.\"\n    ],\n    wide: [\n        \"преди новата ера\",\n        \"новата ера\"\n    ]\n};\nconst quarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"1-во тримес.\",\n        \"2-ро тримес.\",\n        \"3-то тримес.\",\n        \"4-то тримес.\"\n    ],\n    wide: [\n        \"1-во тримесечие\",\n        \"2-ро тримесечие\",\n        \"3-то тримесечие\",\n        \"4-то тримесечие\"\n    ]\n};\nconst monthValues = {\n    abbreviated: [\n        \"яну\",\n        \"фев\",\n        \"мар\",\n        \"апр\",\n        \"май\",\n        \"юни\",\n        \"юли\",\n        \"авг\",\n        \"сеп\",\n        \"окт\",\n        \"ное\",\n        \"дек\"\n    ],\n    wide: [\n        \"януари\",\n        \"февруари\",\n        \"март\",\n        \"април\",\n        \"май\",\n        \"юни\",\n        \"юли\",\n        \"август\",\n        \"септември\",\n        \"октомври\",\n        \"ноември\",\n        \"декември\"\n    ]\n};\nconst dayValues = {\n    narrow: [\n        \"Н\",\n        \"П\",\n        \"В\",\n        \"С\",\n        \"Ч\",\n        \"П\",\n        \"С\"\n    ],\n    short: [\n        \"нд\",\n        \"пн\",\n        \"вт\",\n        \"ср\",\n        \"чт\",\n        \"пт\",\n        \"сб\"\n    ],\n    abbreviated: [\n        \"нед\",\n        \"пон\",\n        \"вто\",\n        \"сря\",\n        \"чет\",\n        \"пет\",\n        \"съб\"\n    ],\n    wide: [\n        \"неделя\",\n        \"понеделник\",\n        \"вторник\",\n        \"сряда\",\n        \"четвъртък\",\n        \"петък\",\n        \"събота\"\n    ]\n};\nconst dayPeriodValues = {\n    wide: {\n        am: \"преди обяд\",\n        pm: \"след обяд\",\n        midnight: \"в полунощ\",\n        noon: \"на обяд\",\n        morning: \"сутринта\",\n        afternoon: \"следобед\",\n        evening: \"вечерта\",\n        night: \"през нощта\"\n    }\n};\nfunction isFeminine(unit) {\n    return unit === \"year\" || unit === \"week\" || unit === \"minute\" || unit === \"second\";\n}\nfunction isNeuter(unit) {\n    return unit === \"quarter\";\n}\nfunction numberWithSuffix(number, unit, masculine, feminine, neuter) {\n    const suffix = isNeuter(unit) ? neuter : isFeminine(unit) ? feminine : masculine;\n    return number + \"-\" + suffix;\n}\nconst ordinalNumber = (dirtyNumber, options)=>{\n    const number = Number(dirtyNumber);\n    const unit = options === null || options === void 0 ? void 0 : options.unit;\n    if (number === 0) {\n        return numberWithSuffix(0, unit, \"ев\", \"ева\", \"ево\");\n    } else if (number % 1000 === 0) {\n        return numberWithSuffix(number, unit, \"ен\", \"на\", \"но\");\n    } else if (number % 100 === 0) {\n        return numberWithSuffix(number, unit, \"тен\", \"тна\", \"тно\");\n    }\n    const rem100 = number % 100;\n    if (rem100 > 20 || rem100 < 10) {\n        switch(rem100 % 10){\n            case 1:\n                return numberWithSuffix(number, unit, \"ви\", \"ва\", \"во\");\n            case 2:\n                return numberWithSuffix(number, unit, \"ри\", \"ра\", \"ро\");\n            case 7:\n            case 8:\n                return numberWithSuffix(number, unit, \"ми\", \"ма\", \"мо\");\n        }\n    }\n    return numberWithSuffix(number, unit, \"ти\", \"та\", \"то\");\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        defaultWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/bg/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/bg/_lib/match.js":
/*!*******************************************************!*\
  !*** ./node_modules/date-fns/locale/bg/_lib/match.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^(\\d+)(-?[врмт][аи]|-?т?(ен|на)|-?(ев|ева))?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^((пр)?н\\.?\\s?е\\.?)/i,\n    abbreviated: /^((пр)?н\\.?\\s?е\\.?)/i,\n    wide: /^(преди новата ера|новата ера|нова ера)/i\n};\nconst parseEraPatterns = {\n    any: [\n        /^п/i,\n        /^н/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]/i,\n    abbreviated: /^[1234](-?[врт]?o?)? тримес.?/i,\n    wide: /^[1234](-?[врт]?о?)? тримесечие/i\n};\nconst parseQuarterPatterns = {\n    any: [\n        /1/i,\n        /2/i,\n        /3/i,\n        /4/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^[нпвсч]/i,\n    short: /^(нд|пн|вт|ср|чт|пт|сб)/i,\n    abbreviated: /^(нед|пон|вто|сря|чет|пет|съб)/i,\n    wide: /^(неделя|понеделник|вторник|сряда|четвъртък|петък|събота)/i\n};\nconst parseDayPatterns = {\n    narrow: [\n        /^н/i,\n        /^п/i,\n        /^в/i,\n        /^с/i,\n        /^ч/i,\n        /^п/i,\n        /^с/i\n    ],\n    any: [\n        /^н[ед]/i,\n        /^п[он]/i,\n        /^вт/i,\n        /^ср/i,\n        /^ч[ет]/i,\n        /^п[ет]/i,\n        /^с[ъб]/i\n    ]\n};\nconst matchMonthPatterns = {\n    abbreviated: /^(яну|фев|мар|апр|май|юни|юли|авг|сеп|окт|ное|дек)/i,\n    wide: /^(януари|февруари|март|април|май|юни|юли|август|септември|октомври|ноември|декември)/i\n};\nconst parseMonthPatterns = {\n    any: [\n        /^я/i,\n        /^ф/i,\n        /^мар/i,\n        /^ап/i,\n        /^май/i,\n        /^юн/i,\n        /^юл/i,\n        /^ав/i,\n        /^се/i,\n        /^окт/i,\n        /^но/i,\n        /^де/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    any: /^(преди о|след о|в по|на о|през|веч|сут|следо)/i\n};\nconst parseDayPeriodPatterns = {\n    any: {\n        am: /^преди о/i,\n        pm: /^след о/i,\n        midnight: /^в пол/i,\n        noon: /^на об/i,\n        morning: /^сут/i,\n        afternoon: /^следо/i,\n        evening: /^веч/i,\n        night: /^през н/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value, 10)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"any\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/bg/_lib/match.js\n"));

/***/ })

}]);