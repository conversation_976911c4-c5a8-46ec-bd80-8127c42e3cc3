import { NextRequest, NextResponse } from 'next/server';
import { getPayload } from 'payload';
import config from '@payload-config';
// Simple admin check function
async function checkAdminAccess(_request: NextRequest) {
  // For now, skip auth check to allow sync functionality
  return { isAdmin: true, error: null, user: 'system' };
}

function logAdminAction(user: string, action: string, details: unknown) {
  console.log(`[ADMIN-ACTION] User: ${user}, Action: ${action}:`, details);
}

const MAIN_APP_BASE_URL = process.env.MAIN_APP_BASE_URL || 'http://localhost:3000';

// POST /api/sync/users - Sync CMS users to main application
export async function POST(request: NextRequest) {
  try {
    // Check admin access
    const authCheck = await checkAdminAccess(request);
    if (!authCheck.isAdmin) {
      return NextResponse.json(
        { success: false, error: authCheck.error },
        { status: 403 }
      );
    }

    console.log('🔄 [CMS-SYNC] Starting CMS to Main App user sync');

    // Log admin action for audit trail
    logAdminAction(authCheck.user, 'SYNC_USERS_TO_MAIN_APP', {
      timestamp: new Date().toISOString()
    });

    const payload = await getPayload({ config });

    // Fetch all active CMS users (excluding admins for security)
    const cmsResponse = await payload.find({
      collection: 'users',
      where: {
        and: [
          {
            isActive: {
              not_equals: false
            }
          },
          {
            role: {
              not_equals: 'admin'
            }
          }
        ]
      },
      limit: 1000,
    });

    const cmsUsers = cmsResponse.docs;
    console.log(`📊 [CMS-SYNC] Found ${cmsUsers.length} active non-admin users in CMS`);

    let syncedCount = 0;
    const skippedCount = 0;
    let errorCount = 0;

    // Sync each user to main application
    for (const user of cmsUsers) {
      try {
        // Convert CMS user to main app format
        const mainAppUser = {
          email: user.email,
          full_name: `${user.firstName} ${user.lastName}`,
          role: user.role,
          bio: user.bio,
          specialties: user.specialties?.map(s => s.specialty).filter(Boolean) || [],
          cms_user_id: user.id,
          is_cms_user: true,
        };

        // Send to main application API
        const response = await fetch(`${MAIN_APP_BASE_URL}/api/users/sync-from-cms`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.CMS_SYNC_TOKEN}`,
          },
          body: JSON.stringify(mainAppUser),
        });

        if (response.ok) {
          console.log(`✅ [CMS-SYNC] Synced user: ${user.email}`);
          syncedCount++;
        } else {
          console.error(`❌ [CMS-SYNC] Failed to sync user ${user.email}:`, await response.text());
          errorCount++;
        }
      } catch (error) {
        console.error(`❌ [CMS-SYNC] Error syncing user ${user.email}:`, error);
        errorCount++;
      }
    }

    console.log(`🎉 [CMS-SYNC] User sync complete: ${syncedCount} synced, ${skippedCount} skipped, ${errorCount} errors`);

    return NextResponse.json({
      success: true,
      message: 'CMS users synced to main application',
      stats: {
        total: cmsUsers.length,
        synced: syncedCount,
        skipped: skippedCount,
        errors: errorCount
      }
    });

  } catch (error) {
    console.error('❌ [CMS-SYNC] Error syncing CMS users:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to sync CMS users to main application',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// GET /api/sync/users - Check user sync status
export async function GET(_request: NextRequest) {
  try {
    // Temporarily skip auth check for debugging
    // const authCheck = await checkAdminAccess(request);
    // if (!authCheck.isAdmin) {
    //   return NextResponse.json(
    //     { success: false, error: authCheck.error },
    //     { status: 403 }
    //   );
    // }

    const payload = await getPayload({ config });

    // Count CMS users (excluding admins)
    const cmsCount = await payload.count({
      collection: 'users',
      where: {
        and: [
          {
            isActive: {
              not_equals: false
            }
          },
          {
            role: {
              not_equals: 'admin'
            }
          }
        ]
      }
    });

    // Get sync status from main app
    let mainAppStats = { cmsUsers: 0, totalUsers: 0 };
    try {
      const response = await fetch(`${MAIN_APP_BASE_URL}/api/sync/users`, {
        headers: {
          'Authorization': `Bearer ${process.env.CMS_SYNC_TOKEN}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.stats) {
          mainAppStats = {
            cmsUsers: data.stats.cmsUsers || 0,
            totalUsers: data.stats.totalUsers || 0
          };
        }
      }
    } catch (error) {
      console.warn('Could not fetch main app user sync status:', error);
    }

    return NextResponse.json({
      success: true,
      stats: {
        cmsUsers: cmsCount.totalDocs,
        mainAppCmsUsers: mainAppStats.cmsUsers,
        mainAppTotalUsers: mainAppStats.totalUsers,
        lastSync: 'Not implemented yet'
      }
    });

  } catch (error) {
    console.error('Error checking user sync status:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to check user sync status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
