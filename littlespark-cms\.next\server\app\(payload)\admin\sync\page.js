(()=>{var a={};a.id=2362,a.ids=[2362],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},643:a=>{"use strict";a.exports=require("node:perf_hooks")},1708:a=>{"use strict";a.exports=require("node:process")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:a=>{"use strict";a.exports=require("node:buffer")},4984:a=>{"use strict";a.exports=require("readline")},7120:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>f});var d=c(60687),e=c(43210);function f(){let[a,b]=(0,e.useState)(!1),[c,f]=(0,e.useState)({cmsChallenge:0,mainAppCmsChallenge:0}),g=async()=>{try{let a=await fetch("/api/sync/challenges"),b=await a.json();b.success&&b.stats&&f({cmsChallenge:b.stats.cmsChallenge||0,mainAppCmsChallenge:b.stats.mainAppCmsChallenge||0})}catch(a){console.error("Error fetching stats:",a)}},h=async()=>{try{let a=await fetch("/api/test-data",{method:"POST",headers:{"Content-Type":"application/json"}}),b=await a.json();alert(`Test data result: ${b.stats?.created||0} challenges created, ${b.stats?.skipped||0} skipped`),await g()}catch(a){alert("Error: "+(a instanceof Error?a.message:"Unknown error"))}},i=async()=>{try{let a=await fetch("/api/sync/challenges",{method:"POST",headers:{"Content-Type":"application/json"}}),b=await a.json();alert(`Sync result: ${b.stats?.synced||0} challenges synced successfully`),await g()}catch(a){alert("Error: "+(a instanceof Error?a.message:"Unknown error"))}};return a?(0,d.jsx)("div",{className:"sync-page",children:(0,d.jsxs)("div",{style:{padding:"20px",maxWidth:"1200px",margin:"0 auto"},children:[(0,d.jsxs)("div",{style:{marginBottom:"30px"},children:[(0,d.jsx)("h1",{style:{fontSize:"28px",fontWeight:"bold",marginBottom:"10px"},children:"\uD83D\uDD04 Sync Management"}),(0,d.jsx)("p",{style:{color:"#666",fontSize:"16px"},children:"Synchronize content and users between CMS and main application"}),(0,d.jsx)("div",{style:{display:"inline-block",backgroundColor:"#f0f9ff",color:"#0369a1",padding:"4px 12px",borderRadius:"6px",fontSize:"14px",fontWeight:"500",marginTop:"10px"},children:"\uD83D\uDEE1️ Admin Only Feature"})]}),(0,d.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr 1fr",gap:"20px",marginBottom:"30px"},children:[(0,d.jsxs)("div",{style:{border:"1px solid #e5e7eb",borderRadius:"8px",padding:"20px",backgroundColor:"#fff"},children:[(0,d.jsx)("h2",{style:{fontSize:"20px",fontWeight:"600",marginBottom:"10px"},children:"\uD83D\uDCDD Challenge Sync"}),(0,d.jsx)("p",{style:{color:"#666",marginBottom:"20px",fontSize:"14px"},children:"Sync published challenges from CMS to main application"}),(0,d.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"15px",marginBottom:"20px"},children:[(0,d.jsxs)("div",{style:{backgroundColor:"#eff6ff",padding:"15px",borderRadius:"6px",textAlign:"center"},children:[(0,d.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#2563eb"},children:(0,d.jsx)("span",{id:"cms-challenges-count",children:c.cmsChallenge})}),(0,d.jsx)("div",{style:{fontSize:"12px",color:"#1e40af",fontWeight:"500"},children:"CMS Challenges"})]}),(0,d.jsxs)("div",{style:{backgroundColor:"#f0fdf4",padding:"15px",borderRadius:"6px",textAlign:"center"},children:[(0,d.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#16a34a"},children:(0,d.jsx)("span",{id:"synced-challenges-count",children:c.mainAppCmsChallenge})}),(0,d.jsx)("div",{style:{fontSize:"12px",color:"#15803d",fontWeight:"500"},children:"Synced to Main App"})]})]}),(0,d.jsx)("button",{id:"sync-challenges-btn",onClick:i,style:{width:"100%",backgroundColor:"#2563eb",color:"white",border:"none",padding:"12px 20px",borderRadius:"6px",fontSize:"14px",fontWeight:"500",cursor:"pointer",marginBottom:"10px"},onMouseOver:a=>a.currentTarget.style.backgroundColor="#1d4ed8",onMouseOut:a=>a.currentTarget.style.backgroundColor="#2563eb",children:"\uD83D\uDD04 Sync Challenges to Main App"}),(0,d.jsx)("div",{id:"challenge-sync-result",style:{fontSize:"12px",color:"#666"}})]}),(0,d.jsxs)("div",{style:{border:"1px solid #e5e7eb",borderRadius:"8px",padding:"20px",backgroundColor:"#fff"},children:[(0,d.jsx)("h2",{style:{fontSize:"20px",fontWeight:"600",marginBottom:"10px"},children:"\uD83D\uDC65 User Sync"}),(0,d.jsx)("p",{style:{color:"#666",marginBottom:"20px",fontSize:"14px"},children:"Sync CMS users to main application for content access"}),(0,d.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"15px",marginBottom:"20px"},children:[(0,d.jsxs)("div",{style:{backgroundColor:"#faf5ff",padding:"15px",borderRadius:"6px",textAlign:"center"},children:[(0,d.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#9333ea"},children:(0,d.jsx)("span",{id:"cms-users-count",children:"-"})}),(0,d.jsx)("div",{style:{fontSize:"12px",color:"#7c3aed",fontWeight:"500"},children:"CMS Users"})]}),(0,d.jsxs)("div",{style:{backgroundColor:"#fff7ed",padding:"15px",borderRadius:"6px",textAlign:"center"},children:[(0,d.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#ea580c"},children:(0,d.jsx)("span",{id:"synced-users-count",children:"-"})}),(0,d.jsx)("div",{style:{fontSize:"12px",color:"#c2410c",fontWeight:"500"},children:"Synced to Main App"})]})]}),(0,d.jsx)("button",{id:"sync-users-btn",style:{width:"100%",backgroundColor:"#9333ea",color:"white",border:"none",padding:"12px 20px",borderRadius:"6px",fontSize:"14px",fontWeight:"500",cursor:"pointer",marginBottom:"10px"},onMouseOver:a=>a.currentTarget.style.backgroundColor="#7c3aed",onMouseOut:a=>a.currentTarget.style.backgroundColor="#9333ea",children:"\uD83D\uDD04 Sync Users to Main App"}),(0,d.jsx)("div",{id:"user-sync-result",style:{fontSize:"12px",color:"#666"}})]}),(0,d.jsxs)("div",{style:{border:"1px solid #e5e7eb",borderRadius:"8px",padding:"20px",backgroundColor:"#fff"},children:[(0,d.jsx)("h2",{style:{fontSize:"20px",fontWeight:"600",marginBottom:"10px"},children:"⬅️ Import User Challenges"}),(0,d.jsx)("p",{style:{color:"#666",marginBottom:"20px",fontSize:"14px"},children:"Import user-created challenges from main app to CMS for review"}),(0,d.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"15px",marginBottom:"20px"},children:[(0,d.jsxs)("div",{style:{backgroundColor:"#f0f9ff",padding:"15px",borderRadius:"6px",textAlign:"center"},children:[(0,d.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#0369a1"},children:(0,d.jsx)("span",{id:"available-user-challenges-count",children:"-"})}),(0,d.jsx)("div",{style:{fontSize:"12px",color:"#0c4a6e",fontWeight:"500"},children:"Available to Import"})]}),(0,d.jsxs)("div",{style:{backgroundColor:"#f0fdf4",padding:"15px",borderRadius:"6px",textAlign:"center"},children:[(0,d.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#16a34a"},children:(0,d.jsx)("span",{id:"imported-challenges-count",children:"-"})}),(0,d.jsx)("div",{style:{fontSize:"12px",color:"#15803d",fontWeight:"500"},children:"Already Imported"})]})]}),(0,d.jsx)("button",{id:"import-challenges-btn",style:{width:"100%",backgroundColor:"#0369a1",color:"white",border:"none",padding:"12px 20px",borderRadius:"6px",fontSize:"14px",fontWeight:"500",cursor:"pointer",marginBottom:"10px"},onMouseOver:a=>a.currentTarget.style.backgroundColor="#0c4a6e",onMouseOut:a=>a.currentTarget.style.backgroundColor="#0369a1",children:"⬅️ Import User Challenges from Main App"}),(0,d.jsx)("div",{id:"import-sync-result",style:{fontSize:"12px",color:"#666"}})]})]}),(0,d.jsx)("div",{style:{textAlign:"center",marginBottom:"30px"},children:(0,d.jsxs)("div",{style:{display:"flex",gap:"15px",justifyContent:"center",flexWrap:"wrap"},children:[(0,d.jsx)("button",{id:"refresh-stats-btn",onClick:g,style:{backgroundColor:"#f3f4f6",color:"#374151",border:"1px solid #d1d5db",padding:"10px 20px",borderRadius:"6px",fontSize:"14px",fontWeight:"500",cursor:"pointer"},onMouseOver:a=>a.currentTarget.style.backgroundColor="#e5e7eb",onMouseOut:a=>a.currentTarget.style.backgroundColor="#f3f4f6",children:"\uD83D\uDD04 Refresh Statistics"}),(0,d.jsx)("button",{id:"add-test-data-btn",onClick:h,style:{backgroundColor:"#10b981",color:"white",border:"none",padding:"10px 20px",borderRadius:"6px",fontSize:"14px",fontWeight:"500",cursor:"pointer"},onMouseOver:a=>a.currentTarget.style.backgroundColor="#059669",onMouseOut:a=>a.currentTarget.style.backgroundColor="#10b981",children:"\uD83E\uDDEA Add Sample Challenges"})]})}),(0,d.jsxs)("div",{id:"error-display",style:{display:"none",backgroundColor:"#fef2f2",border:"1px solid #fecaca",color:"#dc2626",padding:"15px",borderRadius:"6px",marginBottom:"20px"},children:[(0,d.jsx)("strong",{children:"Error:"})," ",(0,d.jsx)("span",{id:"error-message"})]}),(0,d.jsxs)("div",{style:{backgroundColor:"#fffbeb",border:"1px solid #fed7aa",borderRadius:"8px",padding:"20px"},children:[(0,d.jsx)("h3",{style:{fontSize:"16px",fontWeight:"600",marginBottom:"10px",color:"#92400e"},children:"ℹ️ Sync Information"}),(0,d.jsxs)("ul",{style:{fontSize:"14px",color:"#92400e",lineHeight:"1.6"},children:[(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{children:"Challenge Sync:"})," Publishes CMS challenges to the main application for users to access"]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{children:"User Sync:"})," Creates accounts in the main application for CMS content creators and educators"]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{children:"Admin Only:"})," Only CMS administrators can perform sync operations"]}),(0,d.jsxs)("li",{children:[(0,d.jsx)("strong",{children:"Safe Operation:"})," Sync operations are idempotent and won't create duplicates"]})]})]}),(0,d.jsx)("script",{src:"/admin/sync/sync.js",defer:!0}),(0,d.jsx)("script",{dangerouslySetInnerHTML:{__html:`
            console.log('🔄 Sync page loaded');

            // Fetch and update stats
            window.fetchStats = async function() {
              try {
                console.log('Fetching stats...');
                const response = await fetch('/api/sync/challenges');
                const data = await response.json();
                console.log('Stats data:', data);

                if (data.success && data.stats) {
                  document.getElementById('cms-challenges-count').textContent = data.stats.cmsChallenge || '0';
                  document.getElementById('synced-challenges-count').textContent = data.stats.mainAppCmsChallenge || '0';
                }
              } catch (error) {
                console.error('Error fetching stats:', error);
              }
            };

            // Simple test data function
            window.addTestDataSimple = async function() {
              try {
                console.log('Adding test data...');
                const response = await fetch('/api/test-data', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' }
                });
                const data = await response.json();
                console.log('Test data result:', data);
                alert('Test data result: ' + JSON.stringify(data, null, 2));

                // Refresh stats
                await window.fetchStats();
              } catch (error) {
                console.error('Error:', error);
                alert('Error: ' + (error instanceof Error ? error.message : 'Unknown error'));
              }
            };

            // Simple sync function
            window.syncChallengesSimple = async function() {
              try {
                console.log('Syncing challenges...');
                const response = await fetch('/api/sync/challenges', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' }
                });
                const data = await response.json();
                console.log('Sync result:', data);
                alert('Sync result: ' + JSON.stringify(data, null, 2));

                // Refresh stats
                await window.fetchStats();
              } catch (error) {
                console.error('Error:', error);
                alert('Error: ' + (error instanceof Error ? error.message : 'Unknown error'));
              }
            };

            // Add click handlers when DOM is ready
            document.addEventListener('DOMContentLoaded', function() {
              const addTestBtn = document.getElementById('add-test-data-btn');
              const syncBtn = document.getElementById('sync-challenges-btn');
              const refreshBtn = document.getElementById('refresh-stats-btn');

              if (addTestBtn) {
                addTestBtn.onclick = window.addTestDataSimple;
                console.log('✅ Added test data button handler');
              }

              if (syncBtn) {
                syncBtn.onclick = window.syncChallengesSimple;
                console.log('✅ Added sync button handler');
              }

              if (refreshBtn) {
                refreshBtn.onclick = window.fetchStats;
                console.log('✅ Added refresh stats button handler');
              }

              // Load initial stats
              window.fetchStats();
            });
          `}}),(0,d.jsx)("script",{dangerouslySetInnerHTML:{__html:`
            // Add floating sync button to CMS admin panel
            (function() {
              function addSyncButtonToOtherPages() {
                // Only add on non-sync pages
                if (window.location.pathname.includes('/admin/sync')) return;

                // Check if button already exists
                if (document.getElementById('floating-sync-btn')) return;

                const syncButton = document.createElement('div');
                syncButton.id = 'floating-sync-btn';
                syncButton.style.cssText = \`
                  position: fixed;
                  bottom: 20px;
                  right: 20px;
                  z-index: 9999;
                  background: linear-gradient(135deg, #0070f3, #0056b3);
                  color: white;
                  width: 60px;
                  height: 60px;
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  cursor: pointer;
                  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                  transition: all 0.3s ease;
                  font-size: 24px;
                  border: 2px solid white;
                \`;

                syncButton.innerHTML = '🔄';
                syncButton.title = 'Open Sync Management Panel';

                syncButton.addEventListener('mouseenter', function() {
                  this.style.transform = 'scale(1.1)';
                });

                syncButton.addEventListener('mouseleave', function() {
                  this.style.transform = 'scale(1)';
                });

                syncButton.addEventListener('click', function() {
                  window.location.href = '/admin/sync';
                });

                document.body.appendChild(syncButton);
              }

              // Try to add button to parent window (if in iframe)
              try {
                if (window.parent && window.parent !== window) {
                  window.parent.postMessage({type: 'ADD_SYNC_BUTTON'}, '*');
                }
              } catch(e) {}

              // Add to current window
              setTimeout(addSyncButtonToOtherPages, 1000);
            })();
          `}})]})}):(0,d.jsx)("div",{style:{padding:"20px",textAlign:"center"},children:(0,d.jsx)("div",{children:"Loading sync panel..."})})}},8086:a=>{"use strict";a.exports=require("module")},9288:a=>{"use strict";a.exports=require("sharp")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},14007:a=>{"use strict";a.exports=require("pino-pretty")},16141:a=>{"use strict";a.exports=require("node:zlib")},16698:a=>{"use strict";a.exports=require("node:async_hooks")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:a=>{"use strict";a.exports=require("process")},21820:a=>{"use strict";a.exports=require("os")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},28855:a=>{"use strict";a.exports=import("@libsql/client")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:a=>{"use strict";a.exports=require("node:http2")},33873:a=>{"use strict";a.exports=require("path")},34589:a=>{"use strict";a.exports=require("node:assert")},34631:a=>{"use strict";a.exports=require("tls")},37067:a=>{"use strict";a.exports=require("node:http")},37366:a=>{"use strict";a.exports=require("dns")},37540:a=>{"use strict";a.exports=require("node:console")},37830:a=>{"use strict";a.exports=require("node:stream/web")},40610:a=>{"use strict";a.exports=require("node:dns")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41692:a=>{"use strict";a.exports=require("node:tls")},41792:a=>{"use strict";a.exports=require("node:querystring")},42775:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(payload)",{children:["admin",{children:["sync",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,82010)),"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\sync\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,22441)),"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\sync\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(payload)/admin/sync/page",pathname:"/admin/sync",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(payload)/admin/sync/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},48161:a=>{"use strict";a.exports=require("node:os")},51455:a=>{"use strict";a.exports=require("node:fs/promises")},53053:a=>{"use strict";a.exports=require("node:diagnostics_channel")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},57075:a=>{"use strict";a.exports=require("node:stream")},57975:a=>{"use strict";a.exports=require("node:util")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72527:(a,b,c)=>{Promise.resolve().then(c.bind(c,82010))},73024:a=>{"use strict";a.exports=require("node:fs")},73136:a=>{"use strict";a.exports=require("node:url")},73429:a=>{"use strict";a.exports=require("node:util/types")},73496:a=>{"use strict";a.exports=require("http2")},74075:a=>{"use strict";a.exports=require("zlib")},74552:a=>{"use strict";a.exports=require("pino")},75919:a=>{"use strict";a.exports=require("node:worker_threads")},76760:a=>{"use strict";a.exports=require("node:path")},77030:a=>{"use strict";a.exports=require("node:net")},77598:a=>{"use strict";a.exports=require("node:crypto")},78474:a=>{"use strict";a.exports=require("node:events")},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},79748:a=>{"use strict";a.exports=require("fs/promises")},80099:a=>{"use strict";a.exports=require("node:sqlite")},80775:(a,b,c)=>{Promise.resolve().then(c.bind(c,7120))},81630:a=>{"use strict";a.exports=require("http")},82010:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\sync\\page.tsx","default")},84297:a=>{"use strict";a.exports=require("async_hooks")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91043:a=>{"use strict";a.exports=require("@aws-sdk/client-s3")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},98995:a=>{"use strict";a.exports=require("node:module")}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[1103,9598,1769,9436,5590,9533,6622,750],()=>b(b.s=42775));module.exports=c})();