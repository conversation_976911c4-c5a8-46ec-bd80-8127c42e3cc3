"use strict";exports.id=8824,exports.ids=[8824],exports.modules={99702:(e,t,n)=>{n.d(t,{w:()=>a});var o=n(14723);let a=(e,t)=>(0,o.Px)(e,t).then(e=>t.utf8Encoder(e))},66251:(e,t,n)=>{n.d(t,{CG:()=>i,Y2:()=>a,cJ:()=>r});var o=n(99702);let a=(e,t)=>(0,o.w)(e,t).then(e=>{if(e.length)try{return JSON.parse(e)}catch(t){throw t?.name==="SyntaxError"&&Object.defineProperty(t,"$responseBodyText",{value:e}),t}return{}}),i=async(e,t)=>{let n=await a(e,t);return n.message=n.message??n.Message,n},r=(e,t)=>{let n=(e,t)=>Object.keys(e).find(e=>e.toLowerCase()===t.toLowerCase()),o=e=>{let t=e;return"number"==typeof t&&(t=t.toString()),t.indexOf(",")>=0&&(t=t.split(",")[0]),t.indexOf(":")>=0&&(t=t.split(":")[0]),t.indexOf("#")>=0&&(t=t.split("#")[1]),t},a=n(e.headers,"x-amzn-errortype");if(void 0!==a)return o(e.headers[a]);if(t&&"object"==typeof t){let e=n(t,"code");if(e&&void 0!==t[e])return o(t[e]);if(void 0!==t.__type)return o(t.__type)}}},68824:(e,t,n)=>{n.d(t,{CognitoIdentityClient:()=>k.D,GetCredentialsForIdentityCommand:()=>_,GetIdCommand:()=>H});var o=n(39775),a=n(20318),i=n(14723),r=n(84391);class s extends i.TJ{constructor(e){super(e),Object.setPrototypeOf(this,s.prototype)}}class c extends s{name="InternalErrorException";$fault="server";constructor(e){super({name:"InternalErrorException",$fault:"server",...e}),Object.setPrototypeOf(this,c.prototype)}}class d extends s{name="InvalidParameterException";$fault="client";constructor(e){super({name:"InvalidParameterException",$fault:"client",...e}),Object.setPrototypeOf(this,d.prototype)}}class l extends s{name="LimitExceededException";$fault="client";constructor(e){super({name:"LimitExceededException",$fault:"client",...e}),Object.setPrototypeOf(this,l.prototype)}}class u extends s{name="NotAuthorizedException";$fault="client";constructor(e){super({name:"NotAuthorizedException",$fault:"client",...e}),Object.setPrototypeOf(this,u.prototype)}}class p extends s{name="ResourceConflictException";$fault="client";constructor(e){super({name:"ResourceConflictException",$fault:"client",...e}),Object.setPrototypeOf(this,p.prototype)}}class y extends s{name="TooManyRequestsException";$fault="client";constructor(e){super({name:"TooManyRequestsException",$fault:"client",...e}),Object.setPrototypeOf(this,y.prototype)}}class m extends s{name="ResourceNotFoundException";$fault="client";constructor(e){super({name:"ResourceNotFoundException",$fault:"client",...e}),Object.setPrototypeOf(this,m.prototype)}}class x extends s{name="ExternalServiceException";$fault="client";constructor(e){super({name:"ExternalServiceException",$fault:"client",...e}),Object.setPrototypeOf(this,x.prototype)}}class f extends s{name="InvalidIdentityPoolConfigurationException";$fault="client";constructor(e){super({name:"InvalidIdentityPoolConfigurationException",$fault:"client",...e}),Object.setPrototypeOf(this,f.prototype)}}class w extends s{name="DeveloperUserAlreadyRegisteredException";$fault="client";constructor(e){super({name:"DeveloperUserAlreadyRegisteredException",$fault:"client",...e}),Object.setPrototypeOf(this,w.prototype)}}class E extends s{name="ConcurrentModificationException";$fault="client";constructor(e){super({name:"ConcurrentModificationException",$fault:"client",...e}),Object.setPrototypeOf(this,E.prototype)}}let h=e=>({...e,...e.Logins&&{Logins:i.$H}}),C=e=>({...e,...e.SecretKey&&{SecretKey:i.$H}}),$=e=>({...e,...e.Credentials&&{Credentials:C(e.Credentials)}}),g=e=>({...e,...e.Logins&&{Logins:i.$H}});var I=n(66251),S=n(37250);let b=async(e,t)=>W(t,Y("GetCredentialsForIdentity"),"/",void 0,JSON.stringify((0,i.Ss)(e))),v=async(e,t)=>W(t,Y("GetId"),"/",void 0,JSON.stringify((0,i.Ss)(e))),O=async(e,t)=>{if(e.statusCode>=300)return P(e,t);let n=await (0,I.Y2)(e.body,t),o={};return o=B(n,t),{$metadata:J(e),...o}},z=async(e,t)=>{if(e.statusCode>=300)return P(e,t);let n=await (0,I.Y2)(e.body,t),o={};return o=(0,i.Ss)(n),{$metadata:J(e),...o}},P=async(e,t)=>{let n={...e,body:await (0,I.CG)(e.body,t)},o=(0,I.cJ)(e,n.body);switch(o){case"InternalErrorException":case"com.amazonaws.cognitoidentity#InternalErrorException":throw await A(n,t);case"InvalidParameterException":case"com.amazonaws.cognitoidentity#InvalidParameterException":throw await G(n,t);case"LimitExceededException":case"com.amazonaws.cognitoidentity#LimitExceededException":throw await L(n,t);case"NotAuthorizedException":case"com.amazonaws.cognitoidentity#NotAuthorizedException":throw await T(n,t);case"ResourceConflictException":case"com.amazonaws.cognitoidentity#ResourceConflictException":throw await q(n,t);case"TooManyRequestsException":case"com.amazonaws.cognitoidentity#TooManyRequestsException":throw await F(n,t);case"ResourceNotFoundException":case"com.amazonaws.cognitoidentity#ResourceNotFoundException":throw await K(n,t);case"ExternalServiceException":case"com.amazonaws.cognitoidentity#ExternalServiceException":throw await R(n,t);case"InvalidIdentityPoolConfigurationException":case"com.amazonaws.cognitoidentity#InvalidIdentityPoolConfigurationException":throw await N(n,t);case"DeveloperUserAlreadyRegisteredException":case"com.amazonaws.cognitoidentity#DeveloperUserAlreadyRegisteredException":throw await j(n,t);case"ConcurrentModificationException":case"com.amazonaws.cognitoidentity#ConcurrentModificationException":throw await M(n,t);default:return U({output:e,parsedBody:n.body,errorCode:o})}},M=async(e,t)=>{let n=e.body,o=(0,i.Ss)(n),a=new E({$metadata:J(e),...o});return(0,i.Mw)(a,n)},j=async(e,t)=>{let n=e.body,o=(0,i.Ss)(n),a=new w({$metadata:J(e),...o});return(0,i.Mw)(a,n)},R=async(e,t)=>{let n=e.body,o=(0,i.Ss)(n),a=new x({$metadata:J(e),...o});return(0,i.Mw)(a,n)},A=async(e,t)=>{let n=e.body,o=(0,i.Ss)(n),a=new c({$metadata:J(e),...o});return(0,i.Mw)(a,n)},N=async(e,t)=>{let n=e.body,o=(0,i.Ss)(n),a=new f({$metadata:J(e),...o});return(0,i.Mw)(a,n)},G=async(e,t)=>{let n=e.body,o=(0,i.Ss)(n),a=new d({$metadata:J(e),...o});return(0,i.Mw)(a,n)},L=async(e,t)=>{let n=e.body,o=(0,i.Ss)(n),a=new l({$metadata:J(e),...o});return(0,i.Mw)(a,n)},T=async(e,t)=>{let n=e.body,o=(0,i.Ss)(n),a=new u({$metadata:J(e),...o});return(0,i.Mw)(a,n)},q=async(e,t)=>{let n=e.body,o=(0,i.Ss)(n),a=new p({$metadata:J(e),...o});return(0,i.Mw)(a,n)},K=async(e,t)=>{let n=e.body,o=(0,i.Ss)(n),a=new m({$metadata:J(e),...o});return(0,i.Mw)(a,n)},F=async(e,t)=>{let n=e.body,o=(0,i.Ss)(n),a=new y({$metadata:J(e),...o});return(0,i.Mw)(a,n)},D=(e,t)=>(0,i.s)(e,{AccessKeyId:i.lK,Expiration:e=>(0,i.Y0)((0,i.l3)((0,i.r$)(e))),SecretKey:i.lK,SessionToken:i.lK}),B=(e,t)=>(0,i.s)(e,{Credentials:e=>D(e,t),IdentityId:i.lK}),J=e=>({httpStatusCode:e.statusCode,requestId:e.headers["x-amzn-requestid"]??e.headers["x-amzn-request-id"]??e.headers["x-amz-request-id"],extendedRequestId:e.headers["x-amz-id-2"],cfId:e.headers["x-amz-cf-id"]}),U=(0,i.jr)(s),W=async(e,t,n,o,a)=>{let{hostname:i,protocol:r="https",port:s,path:c}=await e.endpoint(),d={protocol:r,hostname:i,port:s,method:"POST",path:c.endsWith("/")?c.slice(0,-1)+n:c+n,headers:t};return void 0!==o&&(d.hostname=o),void 0!==a&&(d.body=a),new S.Kd(d)};function Y(e){return{"content-type":"application/x-amz-json-1.1","x-amz-target":`AWSCognitoIdentityService.${e}`}}class _ extends i.uB.classBuilder().ep(r.S).m(function(e,t,n,i){return[(0,a.TM)(n,this.serialize,this.deserialize),(0,o.rD)(n,e.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","GetCredentialsForIdentity",{}).n("CognitoIdentityClient","GetCredentialsForIdentityCommand").f(h,$).ser(b).de(O).build(){}class H extends i.uB.classBuilder().ep(r.S).m(function(e,t,n,i){return[(0,a.TM)(n,this.serialize,this.deserialize),(0,o.rD)(n,e.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","GetId",{}).n("CognitoIdentityClient","GetIdCommand").f(g,void 0).ser(v).de(z).build(){}var k=n(75166)}};