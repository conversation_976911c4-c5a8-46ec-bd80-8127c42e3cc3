(()=>{var a={};a.id=3271,a.ids=[3271],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},643:a=>{"use strict";a.exports=require("node:perf_hooks")},1708:a=>{"use strict";a.exports=require("node:process")},4573:a=>{"use strict";a.exports=require("node:buffer")},4984:a=>{"use strict";a.exports=require("readline")},8086:a=>{"use strict";a.exports=require("module")},9288:a=>{"use strict";a.exports=require("sharp")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},14007:a=>{"use strict";a.exports=require("pino-pretty")},16141:a=>{"use strict";a.exports=require("node:zlib")},16698:a=>{"use strict";a.exports=require("node:async_hooks")},19771:a=>{"use strict";a.exports=require("process")},21820:a=>{"use strict";a.exports=require("os")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},28855:a=>{"use strict";a.exports=import("@libsql/client")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:a=>{"use strict";a.exports=require("node:http2")},33873:a=>{"use strict";a.exports=require("path")},34589:a=>{"use strict";a.exports=require("node:assert")},34631:a=>{"use strict";a.exports=require("tls")},37067:a=>{"use strict";a.exports=require("node:http")},37366:a=>{"use strict";a.exports=require("dns")},37540:a=>{"use strict";a.exports=require("node:console")},37830:a=>{"use strict";a.exports=require("node:stream/web")},40610:a=>{"use strict";a.exports=require("node:dns")},41692:a=>{"use strict";a.exports=require("node:tls")},41792:a=>{"use strict";a.exports=require("node:querystring")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:a=>{"use strict";a.exports=require("node:os")},51455:a=>{"use strict";a.exports=require("node:fs/promises")},53053:a=>{"use strict";a.exports=require("node:diagnostics_channel")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},57075:a=>{"use strict";a.exports=require("node:stream")},57975:a=>{"use strict";a.exports=require("node:util")},58778:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{handler:()=>x,patchFetch:()=>w,routeModule:()=>y,serverHooks:()=>B,workAsyncStorage:()=>z,workUnitAsyncStorage:()=>A});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(81457),v=a([u]);u=(v.then?(await v)():v)[0];let y=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/(payload)/api/graphql/route",pathname:"/api/graphql",filename:"route",bundlePath:"app/(payload)/api/graphql/route"},distDir:".next",projectDir:"",resolvedPagePath:"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\api\\graphql\\route.ts",nextConfigOutput:"",userland:u}),{workAsyncStorage:z,workUnitAsyncStorage:A,serverHooks:B}=y;function w(){return(0,g.patchFetch)({workAsyncStorage:z,workUnitAsyncStorage:A})}async function x(a,b,c){var d;let e="/(payload)/api/graphql/route";"/index"===e&&(e="/");let g=await y.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:z,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(z.dynamicRoutes[E]||z.routes[D]);if(F&&!x){let a=!!z.routes[D],b=z.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||y.isDev||x||(G=D,G="/index"===G?"/":G);let H=!0===y.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:z,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>y.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>y.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await y.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await y.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:z,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await y.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}d()}catch(a){d(a)}})},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:a=>{"use strict";a.exports=require("node:fs")},73136:a=>{"use strict";a.exports=require("node:url")},73429:a=>{"use strict";a.exports=require("node:util/types")},73496:a=>{"use strict";a.exports=require("http2")},74075:a=>{"use strict";a.exports=require("zlib")},74552:a=>{"use strict";a.exports=require("pino")},75919:a=>{"use strict";a.exports=require("node:worker_threads")},76760:a=>{"use strict";a.exports=require("node:path")},77030:a=>{"use strict";a.exports=require("node:net")},77598:a=>{"use strict";a.exports=require("node:crypto")},78335:()=>{},78474:a=>{"use strict";a.exports=require("node:events")},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},79748:a=>{"use strict";a.exports=require("fs/promises")},80099:a=>{"use strict";a.exports=require("node:sqlite")},81457:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{OPTIONS:()=>j,POST:()=>i});var e=c(81329),f=c(81570),g=c(57631),h=a([e]);e=(h.then?(await h)():h)[0];let i=(0,f.L)(e.A),j=(0,g.lw)(e.A);d()}catch(a){d(a)}})},81570:(a,b,c)=>{"use strict";c.d(b,{L:()=>a4});let d=require("graphql");var e=c.t(d,2);let f=require("graphql/execution/values.js");class g{complexity;context;estimators;includeDirectiveDef;OperationDefinition;options;requestContext;skipDirectiveDef;variableValues;constructor(a,b){if(!("number"==typeof b.maximumComplexity&&b.maximumComplexity>0))throw Error("Maximum query complexity must be a positive number");this.context=a,this.complexity=0,this.options=b,this.includeDirectiveDef=this.context.getSchema().getDirective("include"),this.skipDirectiveDef=this.context.getSchema().getDirective("skip"),this.estimators=b.estimators,this.variableValues={},this.requestContext=b.context,this.OperationDefinition={enter:this.onOperationDefinitionEnter,leave:this.onOperationDefinitionLeave}}createError(){var a,b;return"function"==typeof this.options.createError?this.options.createError(this.options.maximumComplexity,this.complexity):new d.GraphQLError((a=this.options.maximumComplexity,b=this.complexity,`The query exceeds the maximum complexity of ${a}. Actual complexity is ${b}`))}nodeComplexity(a,b){if(a.selectionSet){let c,e={};(b instanceof d.GraphQLObjectType||b instanceof d.GraphQLInterfaceType)&&(e=b.getFields()),c=(0,d.isAbstractType)(b)?this.context.getSchema().getPossibleTypes(b).map(a=>a.name):[b.name];let g=a.selectionSet.selections.reduce((a,g)=>{let i=a,j=!0,k=!1;for(let a of g.directives??[])switch(a.name.value){case"include":{let a=(0,f.getDirectiveValues)(this.includeDirectiveDef,g,this.variableValues||{});"boolean"==typeof a.if&&(j=a.if);break}case"skip":{let a=(0,f.getDirectiveValues)(this.skipDirectiveDef,g,this.variableValues||{});"boolean"==typeof a.if&&(k=a.if)}}if(!j||k)return a;switch(g.kind){case d.Kind.FIELD:{let j,k=e[g.name.value];if(!k)break;let l=(0,d.getNamedType)(k.type);try{j=(0,f.getArgumentValues)(k,g,this.variableValues||{})}catch(b){return this.context.reportError(b),a}let m=0;(0,d.isCompositeType)(l)&&(m=this.nodeComplexity(g,l));let n={type:b,args:j,childComplexity:m,context:this.requestContext,field:k,node:g};if(!this.estimators.find(b=>{let d=b(n);return!("number"!=typeof d||isNaN(d))&&(i=h(d,a,c),!0)}))return this.context.reportError(new d.GraphQLError(`No complexity could be calculated for field ${b.name}.${k.name}. At least one complexity estimator has to return a complexity score.`)),a;break}case d.Kind.FRAGMENT_SPREAD:{let b=this.context.getFragment(g.name.value);if(!b)break;let c=this.context.getSchema().getType(b.typeCondition.name.value);if(!(0,d.isCompositeType)(c))break;let e=this.nodeComplexity(b,c);i=(0,d.isAbstractType)(c)?h(e,a,this.context.getSchema().getPossibleTypes(c).map(a=>a.name)):h(e,a,[c.name]);break}case d.Kind.INLINE_FRAGMENT:{let c=b;if(g.typeCondition&&g.typeCondition.name&&(c=this.context.getSchema().getType(g.typeCondition.name.value),!(0,d.isCompositeType)(c)))break;let e=this.nodeComplexity(g,c);i=(0,d.isAbstractType)(c)?h(e,a,this.context.getSchema().getPossibleTypes(c).map(a=>a.name)):h(e,a,[c.name]);break}default:i=h(this.nodeComplexity(g,b),a,c)}return i},{});return g?Math.max(...Object.values(g),0):NaN}return 0}onOperationDefinitionEnter(a){if("string"==typeof this.options.operationName&&this.options.operationName!==a.name.value)return;let{coerced:b,errors:c}=(0,f.getVariableValues)(this.context.getSchema(),a.variableDefinitions?[...a.variableDefinitions]:[],this.options.variables??{});if(c&&c.length)return void c.forEach(a=>this.context.reportError(a));switch(this.variableValues=b,a.operation){case"mutation":this.complexity+=this.nodeComplexity(a,this.context.getSchema().getMutationType());break;case"query":this.complexity+=this.nodeComplexity(a,this.context.getSchema().getQueryType());break;case"subscription":this.complexity+=this.nodeComplexity(a,this.context.getSchema().getSubscriptionType());break;default:throw Error(`Query complexity could not be calculated for operation of type ${a.operation}`)}}onOperationDefinitionLeave(a){if(("string"!=typeof this.options.operationName||this.options.operationName===a.name.value)&&(this.options.onComplete&&this.options.onComplete(this.complexity),this.complexity>this.options.maximumComplexity))return this.context.reportError(this.createError())}}function h(a,b,c){for(let d of c)Object.prototype.hasOwnProperty.call(b,d)?b[d]+=a:b[d]=a;return b}var i=c(43489),j=c(34765);let k=["0","1","2","3","4","5","6","7","8","9"],l=a=>{let b=String(a),c=b.substring(0,1);return k.indexOf(c)>-1&&(b=`_${b}`),b.normalize("NFKD").replace(/[\u0300-\u036f]/g,"").replace(/\./g,"_").replace(/-|\//g,"_").replace(/\+/g,"_").replace(/,/g,"_").replace(/\(/g,"_").replace(/\)/g,"_").replace(/'/g,"_").replace(/ /g,"")||"_"},m=(a,b)=>{let c={...a};return b.forEach(({slug:a})=>{let b={...c[a]||{}};delete c[a],c[l(a)]=b}),c};var n=c(73380);let o=require("graphql/language/index.js");function p(a){return a}function q(a){if("object"!=typeof a||null===a||Array.isArray(a))throw TypeError(`JSONObject cannot represent non-object value: ${a}`);return a}function r(a,b,c){let d=Object.create(null);return b.fields.forEach(b=>{d[b.name.value]=s(a,b.value,c)}),d}function s(a,b,c){switch(b.kind){case o.Kind.BOOLEAN:case o.Kind.STRING:return b.value;case o.Kind.FLOAT:case o.Kind.INT:return parseFloat(b.value);case o.Kind.LIST:return b.values.map(b=>s(a,b,c));case o.Kind.NULL:return null;case o.Kind.OBJECT:return r(a,b,c);case o.Kind.VARIABLE:return c?c[b.name.value]:void 0;default:throw TypeError(`${a} cannot represent value: ${(0,o.print)(b)}`)}}let t=new d.GraphQLScalarType({name:"JSON",description:"The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).",parseLiteral:(a,b)=>s("JSON",a,b),parseValue:p,serialize:p,specifiedByURL:"http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf"}),u=new d.GraphQLScalarType({name:"JSONObject",description:"The `JSONObject` scalar type represents JSON objects as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).",parseLiteral:(a,b)=>{if(a.kind!==o.Kind.OBJECT)throw TypeError(`JSONObject cannot represent non-object value: ${(0,o.print)(a)}`);return r("JSONObject",a,b)},parseValue:q,serialize:q,specifiedByURL:"http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf"}),v=(a,b)=>b.reduce((b,c)=>{if(!c.hidden&&"ui"!==c.type){if(c.name){let e=l(c.name),f=["create","read","update","delete"].reduce((b,c)=>{let f=c.charAt(0).toUpperCase()+c.slice(1);return{...b,[c]:{type:new d.GraphQLObjectType({name:`${a}_${e}_${f}`,fields:{permission:{type:new d.GraphQLNonNull(d.GraphQLBoolean)}}})}}},{});return c.fields&&(f.fields={type:new d.GraphQLObjectType({name:`${a}_${e}_Fields`,fields:v(`${a}_${e}`,c.fields)})}),{...b,[l(c.name)]:{type:new d.GraphQLObjectType({name:`${a}_${e}`,fields:f})}}}if(!c.name&&c.fields){let d=v(a,c.fields);return{...b,...d}}if("tabs"===c.type)return c.tabs.reduce((b,c)=>({...b,...v(a,c.fields)}),{...b})}return b},{}),w=a=>{let{name:b,entityFields:c,operations:e,scope:f}=a,g=(0,n.x4)(`${b}-${f||""}-Fields`,!0),h={fields:{type:new d.GraphQLObjectType({name:g,fields:v(g,c)})}};return e.forEach(a=>{let c=(0,n.x4)(`${b}-${a}-${f||"Access"}`,!0);h[a]={type:new d.GraphQLObjectType({name:c,fields:{permission:{type:new d.GraphQLNonNull(d.GraphQLBoolean)},where:{type:u}}})}}),h};function x(a){let{type:b,entity:c,scope:e,typeSuffix:f}=a,{slug:g,fields:h,graphQL:i,versions:j}=c,k=[];if(!1===i)return null;if("collection"===b){k=["create","read","update","delete"],c.auth&&"object"==typeof c.auth&&void 0!==c.auth.maxLoginAttempts&&0!==c.auth.maxLoginAttempts&&k.push("unlock"),j&&k.push("readVersions");let a=l(`${g}${f||""}`);return new d.GraphQLObjectType({name:a,fields:w({name:g,entityFields:h,operations:k,scope:e})})}k=["read","update"],c.versions&&k.push("readVersions");let m=l(`${global?.graphQL?.name||g}${f||""}`);return new d.GraphQLObjectType({name:m,fields:w({name:c.graphQL&&c?.graphQL?.name||g,entityFields:c.fields,operations:k,scope:e})})}var y=c(14214),z=c(42450),A=c(92735),B=c(19305),C=c(27689),D=c(5705),E=c(53435),F=c(22315),G=c(25261),H=c(45768),I=c(17611),J=c(7812),K=c(28525),L=c(43443),M=c(6120),N=c(66829),O=c(47519),P=c(20547),Q=c(70354),R=c(90775),S=c(38619),T=c(47099),U=c(47559),V=c(59522),W=c(21098),X=c(34833);let Y=(a,b)=>l(`${a?`${a}_`:""}${b}`),Z=a=>"type"in a&&"group"===a.type?a.fields.some(a=>(0,A.Z7)(a)&&"required"in a&&a.required||Z(a)):"fields"in a&&"name"in a&&a.fields.some(a=>Z(a)),$=({type:a,field:b,forceNullable:c,parentIsLocalized:e})=>{let f=b.access&&b.access.read,g=b.admin&&b.admin.condition,h="createdAt"===b.name||"updatedAt"===b.name;return!c&&"required"in b&&b.required&&(!b.localized||e)&&!g&&!f&&!h?new d.GraphQLNonNull(a):a},_={number:d.GraphQLInt,text:d.GraphQLString},aa=(a,b)=>{let c=(0,y.L)(b.fields).find(a=>(0,A.Z7)(a)&&"id"===a.name);return c?_[c.type]:_[a]};function ab({name:a,config:b,fields:c,forceNullable:e=!1,graphqlResult:f,parentIsLocalized:g,parentName:h}){let i={array:(a,c)=>{let i=Y(h,(0,n.x4)(c.name,!0)),j=ab({name:i,config:b,fields:c.fields,graphqlResult:f,parentIsLocalized:g||c.localized,parentName:i});return j?(j=new d.GraphQLList($({type:j,field:c,forceNullable:e,parentIsLocalized:g})),{...a,[l(c.name)]:{type:j}}):a},blocks:(a,b)=>({...a,[l(b.name)]:{type:t}}),checkbox:(a,b)=>({...a,[l(b.name)]:{type:d.GraphQLBoolean}}),code:(a,b)=>({...a,[l(b.name)]:{type:$({type:d.GraphQLString,field:b,forceNullable:e,parentIsLocalized:g})}}),collapsible:(a,b)=>b.fields.reduce((a,b)=>{let c=i[b.type];return c?c(a,b):a},a),date:(a,b)=>({...a,[l(b.name)]:{type:$({type:d.GraphQLString,field:b,forceNullable:e,parentIsLocalized:g})}}),email:(a,b)=>({...a,[l(b.name)]:{type:$({type:d.GraphQLString,field:b,forceNullable:e,parentIsLocalized:g})}}),group:(a,c)=>{if(!(0,A.Z7)(c))return c.fields.reduce((a,b)=>{let c=i[b.type];return c?c(a,b):a},a);{let e=Z(c),i=Y(h,(0,n.x4)(c.name,!0)),j=ab({name:i,config:b,fields:c.fields,graphqlResult:f,parentIsLocalized:g||c.localized,parentName:i});return j?(e&&(j=new d.GraphQLNonNull(j)),{...a,[l(c.name)]:{type:j}}):a}},json:(a,b)=>({...a,[l(b.name)]:{type:$({type:t,field:b,forceNullable:e,parentIsLocalized:g})}}),number:(a,b)=>{let c="id"===b.name?d.GraphQLInt:d.GraphQLFloat;return{...a,[l(b.name)]:{type:$({type:!0===b.hasMany?new d.GraphQLList(c):c,field:b,forceNullable:e,parentIsLocalized:g})}}},point:(a,b)=>({...a,[l(b.name)]:{type:$({type:new d.GraphQLList(d.GraphQLFloat),field:b,forceNullable:e,parentIsLocalized:g})}}),radio:(a,b)=>({...a,[l(b.name)]:{type:$({type:d.GraphQLString,field:b,forceNullable:e,parentIsLocalized:g})}}),relationship:(a,c)=>{let e,{relationTo:g}=c;if(Array.isArray(g)){let a=`${Y(h,(0,n.x4)(c.name,!0))}RelationshipInput`;e=new d.GraphQLInputObjectType({name:a,fields:{relationTo:{type:new d.GraphQLEnumType({name:`${a}RelationTo`,values:g.reduce((a,b)=>({...a,[l(b)]:{value:b}}),{})})},value:{type:t}}})}else e=aa(b.db.defaultIDType,f.collections[g].config);return{...a,[l(c.name)]:{type:c.hasMany?new d.GraphQLList(e):e}}},richText:(a,b)=>({...a,[l(b.name)]:{type:$({type:t,field:b,forceNullable:e,parentIsLocalized:g})}}),row:(a,b)=>b.fields.reduce((a,b)=>{let c=i[b.type];return c?c(a,b):a},a),select:(a,b)=>{let c=`${Y(h,b.name)}_MutationInput`,f=new d.GraphQLEnumType({name:c,values:b.options.reduce((a,b)=>(0,A.vs)(b)?{...a,[l(b.value)]:{value:b.value}}:{...a,[l(b)]:{value:b}},{})});return f=$({type:f=b.hasMany?new d.GraphQLList(f):f,field:b,forceNullable:e,parentIsLocalized:g}),{...a,[l(b.name)]:{type:f}}},tabs:(a,c)=>c.tabs.reduce((a,e)=>{if((0,A.pz)(e)){let i=Y(h,(0,n.x4)(e.name,!0)),j=Z(c),k=ab({name:i,config:b,fields:e.fields,graphqlResult:f,parentIsLocalized:g||e.localized,parentName:i});return k?(j&&(k=new d.GraphQLNonNull(k)),{...a,[e.name]:{type:k}}):a}return{...a,...e.fields.reduce((a,b)=>{let c=i[b.type];return c?c(a,b):a},a)}},a),text:(a,b)=>({...a,[l(b.name)]:{type:$({type:!0===b.hasMany?new d.GraphQLList(d.GraphQLString):d.GraphQLString,field:b,forceNullable:e,parentIsLocalized:g})}}),textarea:(a,b)=>({...a,[l(b.name)]:{type:$({type:d.GraphQLString,field:b,forceNullable:e,parentIsLocalized:g})}}),upload:(a,c)=>{let e,{relationTo:g}=c;if(Array.isArray(g)){let a=`${Y(h,(0,n.x4)(c.name,!0))}RelationshipInput`;e=new d.GraphQLInputObjectType({name:a,fields:{relationTo:{type:new d.GraphQLEnumType({name:`${a}RelationTo`,values:g.reduce((a,b)=>({...a,[l(b)]:{value:b}}),{})})},value:{type:t}}})}else e=aa(b.db.defaultIDType,f.collections[g].config);return{...a,[l(c.name)]:{type:c.hasMany?new d.GraphQLList(e):e}}}},j=l(a),k=c.reduce((a,b)=>{let c=i[b.type];return"function"!=typeof c||0===Object.keys(c(a,b)).length?a:{...a,...c(a,b)}},{});return 0===Object.keys(k).length?null:new d.GraphQLInputObjectType({name:`mutation${j}Input`,fields:k})}function ac(a,b){return d.versionInfo.major>=17?new d.GraphQLError(a,b):new d.GraphQLError(a,null==b?void 0:b.nodes,null==b?void 0:b.source,null==b?void 0:b.positions,null==b?void 0:b.path,null==b?void 0:b.originalError,null==b?void 0:b.extensions)}let ad=a=>new Date(a),ae=a=>a%4==0&&a%100!=0||a%400==0,af=a=>{if(a=null==a?void 0:a.toUpperCase(),!/^(\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9]|60))(\.\d{1,})?(([Z])|([+|-]([01][0-9]|2[0-3]):[0-5][0-9]))$/.test(a))return!1;let b=Date.parse(a);if(b!=b)return!1;let c=a.indexOf("T"),d=a.substr(0,c),e=a.substr(c+1);return(a=>{if(!/^(\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01]))$/.test(a))return!1;let b=Number(a.substr(0,4)),c=Number(a.substr(5,2)),d=Number(a.substr(8,2));switch(c){case 2:if(ae(b)&&d>29||!ae(b)&&d>28)return!1;break;case 4:case 6:case 9:case 11:if(d>30)return!1}return!0})(d)&&(a=>(a=null==a?void 0:a.toUpperCase(),/^([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\.\d{1,})?(([Z])|([+|-]([01][0-9]|2[0-3]):[0-5][0-9]))$/.test(a)))(e)},ag=a=>{let b=a.getTime();return b==b},ah=new d.GraphQLScalarType({name:"DateTime",description:"A date-time string at UTC, such as 2007-12-03T10:15:30Z, compliant with the `date-time` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar.",serialize(a){if(a instanceof Date){if(ag(a))return a;throw ac("DateTime cannot represent an invalid Date instance")}if("string"==typeof a){if(af(a))return ad(a);throw ac(`DateTime cannot represent an invalid date-time-string ${a}.`)}if("number"==typeof a)try{return new Date(a)}catch(b){throw ac("DateTime cannot represent an invalid Unix timestamp "+a)}throw ac("DateTime cannot be serialized from a non string, non numeric or non Date type "+JSON.stringify(a))},parseValue(a){if(a instanceof Date){if(ag(a))return a;throw ac("DateTime cannot represent an invalid Date instance")}if("string"==typeof a){if(af(a))return ad(a);throw ac(`DateTime cannot represent an invalid date-time-string ${a}.`)}throw ac(`DateTime cannot represent non string or Date type ${JSON.stringify(a)}`)},parseLiteral(a){if(a.kind!==d.Kind.STRING)throw ac(`DateTime cannot represent non string or Date type ${"value"in a&&a.value}`,{nodes:a});let{value:b}=a;if(af(b))return ad(b);throw ac(`DateTime cannot represent an invalid date-time-string ${String(b)}.`,{nodes:a})},extensions:{codegenScalarType:"Date | string",jsonSchema:{type:"string",format:"date-time"}}}),ai=(a,b)=>{if("string"!=typeof a)throw ac(`Value is not string: ${a}`,{nodes:b});if(!/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(a))throw ac(`Value is not a valid email address: ${a}`,{nodes:b});return a},aj="https://html.spec.whatwg.org/multipage/input.html#valid-e-mail-address",ak=new d.GraphQLScalarType({name:"EmailAddress",description:"A field whose value conforms to the standard internet email address format as specified in HTML Spec: https://html.spec.whatwg.org/multipage/input.html#valid-e-mail-address.",serialize:ai,parseValue:ai,parseLiteral(a){if(a.kind!==d.Kind.STRING)throw ac(`Can only validate strings as email addresses but got a: ${a.kind}`,{nodes:a});return ai(a.value,a)},specifiedByURL:aj,specifiedByUrl:aj,extensions:{codegenScalarType:"string",jsonSchema:{type:"string",format:"email"}}});var al=c(49792),am=c(60656),an=c(42549);let ao=a=>a.options.reduce((a,b)=>"object"==typeof b?{...a,[l(b.value)]:{value:b.value}}:{...a,[l(b)]:{value:b}},{}),ap=({field:a,forceNullable:b,parentIsLocalized:c})=>{let d=a.access&&a.access.read,e=a.admin&&a.admin.condition;return!(b&&(0,A.Z7)(a)&&"required"in a&&a.required&&(!a.localized||c)&&!e&&!d)};function aq({field:a,...b}){return"name"in a&&l(a.name)!==a.name?{...b,resolve:b=>b[a.name]}:b}let ar={array:({config:a,field:b,forceNullable:c,graphqlResult:e,objectTypeConfig:f,parentIsLocalized:g,parentName:h})=>{let i=b?.interfaceName||Y(h,(0,n.x4)(b.name,!0));if(!e.types.arrayTypes[i]){let d=as({name:i,config:a,fields:b.fields,forceNullable:ap({field:b,forceNullable:c,parentIsLocalized:g}),graphqlResult:e,parentIsLocalized:b.localized||g,parentName:i});Object.keys(d.getFields()).length&&(e.types.arrayTypes[i]=d)}if(!e.types.arrayTypes[i])return f;let j=new d.GraphQLList(new d.GraphQLNonNull(e.types.arrayTypes[i]));return{...f,[l(b.name)]:aq({type:$({type:j,field:b,parentIsLocalized:g}),field:b})}},blocks:({config:a,field:b,forceNullable:c,graphqlResult:e,objectTypeConfig:f,parentIsLocalized:g,parentName:h})=>{let i=(b.blockReferences??b.blocks).reduce((b,d)=>{let f="string"==typeof d?d:d.slug;if(!e.types.blockTypes[f]){let b="string"==typeof d?a.blocks.find(a=>a.slug===d):d,f=b?.interfaceName||b?.graphQL?.singularName||(0,n.x4)(b.slug,!0),h=as({name:f,config:a,fields:[...b.fields,{name:"blockType",type:"text"}],forceNullable:c,graphqlResult:e,parentIsLocalized:g,parentName:f});Object.keys(h.getFields()).length&&(e.types.blockTypes[b.slug]=h)}return e.types.blockTypes[f]&&b.push(e.types.blockTypes[f]),b},[]);if(0===i.length)return f;let j=Y(h,(0,n.x4)(b.name,!0)),k=new d.GraphQLList(new d.GraphQLNonNull(new d.GraphQLUnionType({name:j,resolveType:a=>e.types.blockTypes[a.blockType].name,types:i})));return{...f,[l(b.name)]:aq({type:$({type:k,field:b,parentIsLocalized:g}),field:b})}},checkbox:({field:a,forceNullable:b,objectTypeConfig:c,parentIsLocalized:e})=>({...c,[l(a.name)]:aq({type:$({type:d.GraphQLBoolean,field:a,forceNullable:b,parentIsLocalized:e}),field:a})}),code:({field:a,forceNullable:b,objectTypeConfig:c,parentIsLocalized:e})=>({...c,[l(a.name)]:aq({type:$({type:d.GraphQLString,field:a,forceNullable:b,parentIsLocalized:e}),field:a})}),collapsible:({config:a,field:b,forceNullable:c,graphqlResult:d,newlyCreatedBlockType:e,objectTypeConfig:f,parentIsLocalized:g,parentName:h})=>b.fields.reduce((b,f)=>{let i=ar[f.type];return i?i({config:a,field:f,forceNullable:c,graphqlResult:d,newlyCreatedBlockType:e,objectTypeConfig:b,parentIsLocalized:g,parentName:h}):b},f),date:({field:a,forceNullable:b,objectTypeConfig:c,parentIsLocalized:d})=>({...c,[l(a.name)]:aq({type:$({type:ah,field:a,forceNullable:b,parentIsLocalized:d}),field:a})}),email:({field:a,forceNullable:b,objectTypeConfig:c,parentIsLocalized:d})=>({...c,[l(a.name)]:aq({type:$({type:ak,field:a,forceNullable:b,parentIsLocalized:d}),field:a})}),group:({config:a,field:b,forceNullable:c,graphqlResult:d,newlyCreatedBlockType:e,objectTypeConfig:f,parentIsLocalized:g,parentName:h})=>{if(!(0,A.Z7)(b))return b.fields.reduce((b,f)=>{let i=ar[f.type];return i?i({config:a,field:f,forceNullable:c,graphqlResult:d,newlyCreatedBlockType:e,objectTypeConfig:b,parentIsLocalized:g,parentName:h}):b},f);{let e=b?.interfaceName||Y(h,(0,n.x4)(b.name,!0));if(!d.types.groupTypes[e]){let f=as({name:e,config:a,fields:b.fields,forceNullable:ap({field:b,forceNullable:c,parentIsLocalized:g}),graphqlResult:d,parentIsLocalized:b.localized||g,parentName:e});Object.keys(f.getFields()).length&&(d.types.groupTypes[e]=f)}return d.types.groupTypes[e]?{...f,[l(b.name)]:{type:d.types.groupTypes[e],resolve:(a,c,d)=>({...a[b.name],_id:a._id??a.id})}}:f}},join:({collectionSlug:a,field:b,graphqlResult:c,objectTypeConfig:e,parentName:f})=>{let g=Y(f,(0,n.x4)(b.name,!0)),h={type:new d.GraphQLObjectType({name:g,fields:{docs:{type:new d.GraphQLNonNull(Array.isArray(b.collection)?t:new d.GraphQLList(new d.GraphQLNonNull(c.collections[b.collection].graphQL.type)))},hasNextPage:{type:new d.GraphQLNonNull(d.GraphQLBoolean)}}}),args:{limit:{type:d.GraphQLInt},page:{type:d.GraphQLInt},sort:{type:d.GraphQLString},where:{type:Array.isArray(b.collection)?t:c.collections[b.collection].graphQL.whereInputType}},extensions:{complexity:"number"==typeof b?.graphQL?.complexity?b.graphQL.complexity:10},async resolve(c,d,e){let{collection:f}=b,{limit:g,page:h,sort:i,where:j}=d,{req:k}=e,l=!!(d.draft??e.req.query?.draft),m=b.targetField,n=(0,al.m)(j,Array.isArray(m.relationTo)?{[b.on]:{equals:{relationTo:a,value:c._id??c.id}}}:{[b.on]:{equals:c._id??c.id}});if(Array.isArray(f))throw Error("GraphQL with array of join.field.collection is not implemented");let{docs:o}=await k.payload.find({collection:f,depth:0,draft:l,fallbackLocale:k.fallbackLocale,limit:"number"==typeof g&&g>0?g+1:0,locale:k.locale,overrideAccess:!1,page:h,pagination:!1,req:k,sort:i,where:n}),p=!1;return"number"==typeof g&&0!==g&&g<o.length&&(p=!0),{docs:p?o.slice(0,-1):o,hasNextPage:0!==g&&g<o.length}}};return{...e,[l(b.name)]:h}},json:({field:a,forceNullable:b,objectTypeConfig:c,parentIsLocalized:d})=>({...c,[l(a.name)]:aq({type:$({type:t,field:a,forceNullable:b,parentIsLocalized:d}),field:a})}),number:({field:a,forceNullable:b,objectTypeConfig:c,parentIsLocalized:e})=>{let f=a?.name==="id"?d.GraphQLInt:d.GraphQLFloat;return{...c,[l(a.name)]:aq({type:$({type:a?.hasMany===!0?new d.GraphQLList(new d.GraphQLNonNull(f)):f,field:a,forceNullable:b,parentIsLocalized:e}),field:a})}},point:({field:a,forceNullable:b,objectTypeConfig:c,parentIsLocalized:e})=>({...c,[l(a.name)]:aq({type:$({type:new d.GraphQLList(new d.GraphQLNonNull(d.GraphQLFloat)),field:a,forceNullable:b,parentIsLocalized:e}),field:a})}),radio:({field:a,forceNullable:b,objectTypeConfig:c,parentIsLocalized:e,parentName:f})=>({...c,[l(a.name)]:aq({type:$({type:new d.GraphQLEnumType({name:Y(f,a.name),values:ao(a)}),field:a,forceNullable:b,parentIsLocalized:e}),field:a})}),relationship:({config:a,field:b,forceNullable:c,graphqlResult:e,newlyCreatedBlockType:f,objectTypeConfig:g,parentIsLocalized:h,parentName:i})=>{let j,{relationTo:k}=b,m=Array.isArray(k),o=b.hasMany,p=Y(i,(0,n.x4)(b.name,!0)),q=null,r=a.collections.filter(a=>!1!==a.graphQL);if(Array.isArray(k)){q=new d.GraphQLEnumType({name:`${p}_RelationTo`,values:k.filter(a=>r.some(b=>b.slug===a)).reduce((a,b)=>({...a,[l(b)]:{value:b}}),{})});let a=k.filter(a=>r.some(b=>b.slug===a)).map(a=>e.collections[a]?.graphQL.type);j=new d.GraphQLObjectType({name:`${p}_Relationship`,fields:{relationTo:{type:q},value:{type:new d.GraphQLUnionType({name:p,resolveType:a=>e.collections[a.collection].graphQL.type.name,types:a})}}})}else({type:j}=e.collections[k].graphQL);j=j||f;let s={};(Array.isArray(k)?k:[k]).filter(a=>r.some(b=>b.slug===a)).some(a=>e.collections[a].config.versions?.drafts)&&(s.draft={type:d.GraphQLBoolean}),a.localization&&(s.locale={type:e.types.localeInputType},s.fallbackLocale={type:e.types.fallbackLocaleInputType});let t={type:$({type:o?new d.GraphQLList(new d.GraphQLNonNull(j)):j,field:b,forceNullable:c,parentIsLocalized:h}),args:s,extensions:{complexity:"number"==typeof b?.graphQL?.complexity?b.graphQL.complexity:10},async resolve(a,c,d){let e=a[b.name],f=c.locale||d.req.locale,g=c.fallbackLocale||d.req.fallbackLocale,h=b.relationTo,i=!!(c.draft??d.req.query?.draft);if(o){let a=[],c=[],h=async(c,e)=>{let h=c,j=b.relationTo;if(m?r.some(a=>j.includes(a.slug)):r.some(a=>j===a.slug)){m&&(j=c.relationTo,h=c.value);let b=await d.req.payloadDataLoader.load((0,am.h)({collectionSlug:j,currentDepth:0,depth:0,docID:h,draft:i,fallbackLocale:g,locale:f,overrideAccess:!1,showHiddenFields:!1,transactionID:d.req.transactionID}));b&&(m?a[e]={relationTo:j,value:{...b,collection:j}}:a[e]=b)}};return e&&e.forEach((a,b)=>{c.push(h(a,b))}),await Promise.all(c),a}let j=e;if(m&&e&&(j=e.value,h=e.relationTo),j&&r.some(a=>a.slug===h)){let a=await d.req.payloadDataLoader.load((0,am.h)({collectionSlug:h,currentDepth:0,depth:0,docID:j,draft:i,fallbackLocale:g,locale:f,overrideAccess:!1,showHiddenFields:!1,transactionID:d.req.transactionID}));if(a)return m?{relationTo:h,value:{...a,collection:h}}:a}return null}};return{...g,[l(b.name)]:t}},richText:({config:a,field:b,forceNullable:c,objectTypeConfig:e,parentIsLocalized:f})=>({...e,[l(b.name)]:{type:$({type:t,field:b,forceNullable:c,parentIsLocalized:f}),args:{depth:{type:d.GraphQLInt}},async resolve(c,d,e){let g=a.defaultDepth;if(void 0!==d.depth&&(g=d.depth),!b?.editor)throw new an.X(b);if("function"==typeof b?.editor)throw Error("Attempted to access unsanitized rich text editor.");let h=b?.editor;if(h?.graphQLPopulationPromises){let a=[],i=[],j=b?.maxDepth!==void 0&&b?.maxDepth<g?b?.maxDepth:g;h?.graphQLPopulationPromises({context:e,depth:j,draft:d.draft,field:b,fieldPromises:a,findMany:!1,flattenLocales:!1,overrideAccess:!1,parentIsLocalized:f,populationPromises:i,req:e.req,showHiddenFields:!1,siblingDoc:c}),await Promise.all(a),await Promise.all(i)}return c[b.name]}}}),row:({field:a,objectTypeConfig:b,...c})=>a.fields.reduce((a,b)=>{let d=ar[b.type];return d?d({field:b,objectTypeConfig:a,...c}):a},b),select:({field:a,forceNullable:b,objectTypeConfig:c,parentIsLocalized:e,parentName:f})=>{let g=Y(f,a.name),h=new d.GraphQLEnumType({name:g,values:ao(a)});return h=$({type:h=a.hasMany?new d.GraphQLList(new d.GraphQLNonNull(h)):h,field:a,forceNullable:b,parentIsLocalized:e}),{...c,[l(a.name)]:aq({type:h,field:a})}},tabs:({config:a,field:b,forceNullable:c,graphqlResult:d,newlyCreatedBlockType:e,objectTypeConfig:f,parentIsLocalized:g,parentName:h})=>b.tabs.reduce((b,f)=>{if((0,A.pz)(f)){let e=f?.interfaceName||Y(h,(0,n.x4)(f.name,!0));if(!d.types.groupTypes[e]){let b=as({name:e,config:a,fields:f.fields,forceNullable:c,graphqlResult:d,parentIsLocalized:f.localized||g,parentName:e});Object.keys(b.getFields()).length&&(d.types.groupTypes[e]=b)}return d.types.groupTypes[e]?{...b,[f.name]:{type:d.types.groupTypes[e],resolve:(a,b,c)=>({...a[f.name],_id:a._id??a.id})}}:b}return{...b,...f.fields.reduce((b,f)=>{let i=ar[f.type];return i?i({config:a,field:f,forceNullable:c,graphqlResult:d,newlyCreatedBlockType:e,objectTypeConfig:b,parentIsLocalized:g,parentName:h}):b},b)}},f),text:({field:a,forceNullable:b,objectTypeConfig:c,parentIsLocalized:e})=>({...c,[l(a.name)]:aq({type:$({type:!0===a.hasMany?new d.GraphQLList(new d.GraphQLNonNull(d.GraphQLString)):d.GraphQLString,field:a,forceNullable:b,parentIsLocalized:e}),field:a})}),textarea:({field:a,forceNullable:b,objectTypeConfig:c,parentIsLocalized:e})=>({...c,[l(a.name)]:aq({type:$({type:d.GraphQLString,field:a,forceNullable:b,parentIsLocalized:e}),field:a})}),upload:({config:a,field:b,forceNullable:c,graphqlResult:e,newlyCreatedBlockType:f,objectTypeConfig:g,parentIsLocalized:h,parentName:i})=>{let j,{relationTo:k}=b,m=Array.isArray(k),o=b.hasMany,p=Y(i,(0,n.x4)(b.name,!0)),q=null;if(Array.isArray(k)){q=new d.GraphQLEnumType({name:`${p}_RelationTo`,values:k.reduce((a,b)=>({...a,[l(b)]:{value:b}}),{})});let a=k.map(a=>e.collections[a].graphQL.type);j=new d.GraphQLObjectType({name:`${p}_Relationship`,fields:{relationTo:{type:q},value:{type:new d.GraphQLUnionType({name:p,resolveType:a=>e.collections[a.collection].graphQL.type.name,types:a})}}})}else({type:j}=e.collections[k].graphQL);j=j||f;let r={};(Array.isArray(k)?k:[k]).some(a=>e.collections[a].config.versions?.drafts)&&(r.draft={type:d.GraphQLBoolean}),a.localization&&(r.locale={type:e.types.localeInputType},r.fallbackLocale={type:e.types.fallbackLocaleInputType});let s={type:$({type:o?new d.GraphQLList(new d.GraphQLNonNull(j)):j,field:b,forceNullable:c,parentIsLocalized:h}),args:r,extensions:{complexity:"number"==typeof b?.graphQL?.complexity?b.graphQL.complexity:10},async resolve(a,c,d){let e=a[b.name],f=c.locale||d.req.locale,g=c.fallbackLocale||d.req.fallbackLocale,h=b.relationTo,i=!!(c.draft??d.req.query?.draft);if(o){let a=[],c=[],h=async(c,e)=>{let h=c,j=b.relationTo;m&&(j=c.relationTo,h=c.value);let k=await d.req.payloadDataLoader.load((0,am.h)({collectionSlug:j,currentDepth:0,depth:0,docID:h,draft:i,fallbackLocale:g,locale:f,overrideAccess:!1,showHiddenFields:!1,transactionID:d.req.transactionID}));k&&(m?a[e]={relationTo:j,value:{...k,collection:j}}:a[e]=k)};return e&&e.forEach((a,b)=>{c.push(h(a,b))}),await Promise.all(c),a}let j=e;if(m&&e&&(j=e.value,h=e.relationTo),j){let a=await d.req.payloadDataLoader.load((0,am.h)({collectionSlug:h,currentDepth:0,depth:0,docID:j,draft:i,fallbackLocale:g,locale:f,overrideAccess:!1,showHiddenFields:!1,transactionID:d.req.transactionID}));if(a)return m?{relationTo:h,value:{...a,collection:h}}:a}return null}};return{...g,[l(b.name)]:s}}};function as({name:a,baseFields:b={},collectionSlug:c,config:e,fields:f,forceNullable:g,graphqlResult:h,parentIsLocalized:i,parentName:j}){let k=new d.GraphQLObjectType({name:a,fields:()=>f.reduce((a,b)=>{let d=ar[b.type];return"function"!=typeof d?a:{...a,...d({collectionSlug:c,config:e,field:b,forceNullable:g,graphqlResult:h,newlyCreatedBlockType:k,objectTypeConfig:a,parentIsLocalized:i,parentName:j})}},b)});return k}let at=(a,b)=>new d.GraphQLObjectType({name:a,fields:{docs:{type:new d.GraphQLNonNull(new d.GraphQLList(new d.GraphQLNonNull(b)))},hasNextPage:{type:new d.GraphQLNonNull(d.GraphQLBoolean)},hasPrevPage:{type:new d.GraphQLNonNull(d.GraphQLBoolean)},limit:{type:new d.GraphQLNonNull(d.GraphQLInt)},nextPage:{type:d.GraphQLInt},offset:{type:d.GraphQLInt},page:{type:new d.GraphQLNonNull(d.GraphQLInt)},pagingCounter:{type:new d.GraphQLNonNull(d.GraphQLInt)},prevPage:{type:d.GraphQLInt},totalDocs:{type:new d.GraphQLNonNull(d.GraphQLInt)},totalPages:{type:new d.GraphQLNonNull(d.GraphQLInt)}}}),au=({field:a,nestedFieldName2:b,parentName:c})=>{let d=((0,A.Z7)(a)?a.name:void 0)||b;return"tabs"===a.type?a.tabs.reduce((a,b)=>(a.push(...au({field:{...b,type:"name"in b?"group":"row"},nestedFieldName2:d,parentName:c})),a),[]):a.fields.reduce((a,b)=>{if(!(0,A.aO)(b)){if(!(0,A.Z7)(b))return[...a,...au({field:b,nestedFieldName2:d,parentName:c})];let e=(0,A.Z7)(b)?`${d?`${d}__`:""}${b.name}`:void 0,f=aB({nestedFieldName:d,parentName:c})[b.type];if(f){let c=f({...b,name:e});return Array.isArray(c)?[...a,...c]:[...a,{type:c,key:e}]}}return a},[])},av={comparison:["greater_than_equal","greater_than","less_than_equal","less_than"],contains:["in","not_in","all"],equality:["equals","not_equals"],geo:["near"],geojson:["within","intersects"],partial:["like","contains"]},aw=new d.GraphQLInputObjectType({name:"GeoJSONObject",fields:{type:{type:d.GraphQLString},coordinates:{type:t}}}),ax={checkbox:{operators:[...av.equality.map(a=>({name:a,type:d.GraphQLBoolean}))]},code:{operators:[...[...av.equality,...av.partial].map(a=>({name:a,type:d.GraphQLString}))]},date:{operators:[...[...av.equality,...av.comparison,"like"].map(a=>({name:a,type:ah}))]},email:{operators:[...[...av.equality,...av.partial,...av.contains].map(a=>({name:a,type:ak}))]},json:{operators:[...[...av.equality,...av.partial,...av.geojson].map(a=>({name:a,type:t}))]},number:{operators:[...[...av.equality,...av.comparison].map(a=>({name:a,type:a=>a?.name==="id"?d.GraphQLInt:d.GraphQLFloat}))]},point:{operators:[...[...av.equality,...av.comparison,...av.geo].map(a=>({name:a,type:new d.GraphQLList(d.GraphQLFloat)})),...av.geojson.map(a=>({name:a,type:aw}))]},radio:{operators:[...[...av.equality,...av.partial].map(a=>({name:a,type:(a,b)=>new d.GraphQLEnumType({name:`${Y(b,a.name)}_Input`,values:a.options.reduce((a,b)=>(0,A.vs)(b)?{...a,[l(b.value)]:{value:b.value}}:{...a,[l(b)]:{value:b}},{})})}))]},relationship:{operators:[...[...av.equality,...av.contains].map(a=>({name:a,type:t}))]},richText:{operators:[...[...av.equality,...av.partial].map(a=>({name:a,type:t}))]},select:{operators:[...[...av.equality,...av.contains].map(a=>({name:a,type:(a,b)=>new d.GraphQLEnumType({name:`${Y(b,a.name)}_Input`,values:a.options.reduce((a,b)=>(0,A.vs)(b)?{...a,[l(b.value)]:{value:b.value}}:{...a,[l(b)]:{value:b}},{})})}))]},text:{operators:[...[...av.equality,...av.partial,...av.contains].map(a=>({name:a,type:d.GraphQLString}))]},textarea:{operators:[...[...av.equality,...av.partial].map(a=>({name:a,type:d.GraphQLString}))]},upload:{operators:[...[...av.equality,...av.contains].map(a=>({name:a,type:t}))]}},ay=["in","not_in","all"],az={},aA=(a,b)=>{if(!ax?.[a.type])throw Error(`Error: ${a.type} has no defaults configured.`);let c=`${Y(b,a.name)}_operator`,e=[...ax[a.type].operators];return"required"in a&&a.required||e.push({name:"exists",type:e[0].type}),new d.GraphQLInputObjectType({name:c,fields:e.reduce((c,e)=>{let f="function"==typeof e.type?e.type(a,b):e.type;return"function"==typeof e.type&&"name"in f&&(az[f.name]?f=az[f.name]:az[f.name]=f),ay.includes(e.name)?f=new d.GraphQLList(f):"exists"===e.name&&(f=d.GraphQLBoolean),{...c,[e.name]:{type:f}}},{})})},aB=({nestedFieldName:a,parentName:b})=>({array:c=>au({field:c,nestedFieldName2:a,parentName:b}),checkbox:a=>({type:aA(a,b)}),code:a=>({type:aA(a,b)}),collapsible:c=>au({field:c,nestedFieldName2:a,parentName:b}),date:a=>({type:aA(a,b)}),email:a=>({type:aA(a,b)}),group:c=>au({field:c,nestedFieldName2:a,parentName:b}),json:a=>({type:aA(a,b)}),number:a=>({type:aA(a,b)}),point:a=>({type:aA(a,b)}),radio:a=>({type:aA(a,b)}),relationship:a=>Array.isArray(a.relationTo)?{type:new d.GraphQLInputObjectType({name:`${Y(b,a.name)}_Relation`,fields:{relationTo:{type:new d.GraphQLEnumType({name:`${Y(b,a.name)}_Relation_RelationTo`,values:a.relationTo.reduce((a,b)=>({...a,[l(b)]:{value:b}}),{})})},value:{type:t}}})}:{type:aA(a,b)},richText:a=>({type:aA(a,b)}),row:c=>au({field:c,nestedFieldName2:a,parentName:b}),select:a=>({type:aA(a,b)}),tabs:c=>au({field:c,nestedFieldName2:a,parentName:b}),text:a=>({type:aA(a,b)}),textarea:a=>({type:aA(a,b)}),upload:a=>Array.isArray(a.relationTo)?{type:new d.GraphQLInputObjectType({name:`${Y(b,a.name)}_Relation`,fields:{relationTo:{type:new d.GraphQLEnumType({name:`${Y(b,a.name)}_Relation_RelationTo`,values:a.relationTo.reduce((a,b)=>({...a,[l(b)]:{value:b}}),{})})},value:{type:t}}})}:{type:aA(a,b)}}),aC=({name:a,fields:b,parentName:c})=>{let e=(0,y.L)(b).find(a=>(0,A.Z7)(a)&&"id"===a.name),f=b.reduce((a,b)=>{if(!(0,A.aO)(b)&&!b.hidden){let d=aB({parentName:c})[b.type];if(d){let c=d(b);return(0,A.sd)(b)||"tabs"===b.type?{...a,...c.reduce((a,b)=>({...a,[l(b.key)]:b.type}),{})}:{...a,[l(b.name)]:c}}}return a},{});e||(f.id={type:aA({name:"id",type:"text"},c)});let g=l(a),h={AND:{type:new d.GraphQLList(new d.GraphQLInputObjectType({name:`${g}_where_and`,fields:()=>({...f,...h})}))},OR:{type:new d.GraphQLList(new d.GraphQLInputObjectType({name:`${g}_where_or`,fields:()=>({...f,...h})}))}};return new d.GraphQLInputObjectType({name:`${g}_where`,fields:{...f,...h}})};var aD=c(78544),aE=c(23277),aF=c(63313),aG=c(81140),aH=c(4148),aI=c(42511),aJ=c(49039),aK=c(1825);let{singular:aL}=aD;function aM(a){for(let b in a)a[b].resolve&&(a[b].resolve=function(a){return(b,c,d,e)=>a(b,c,{...d,req:(0,i.i)(d.req,"transactionID")},e)}(a[b].resolve));return a}let aN=a=>({Field(b){("__schema"===b.name.value||"__type"===b.name.value)&&a.reportError(new d.GraphQLError("GraphQL introspection is not allowed, but the query contained __schema or __type",{nodes:[b]}))}});function aO(a){return"object"==typeof a&&null!==a}function aP(a){if(!Array.isArray(a)||"string"!=typeof a[0]&&null!==a[0]||!aO(a[1]))return!1;let b=a[1];return(!b.status||"number"==typeof b.status)&&(!b.statusText||"string"==typeof b.statusText)&&(!b.headers||!!aO(b.headers))}async function aQ(a){var b,c;let d=a.method;if("GET"!==d&&"POST"!==d)return[null,{status:405,statusText:"Method Not Allowed",headers:{allow:"GET, POST"}}];let[e,f="charset=utf-8"]=(aS(a,"content-type")||"").replace(/\s/g,"").toLowerCase().split(";"),g={};switch(!0){case"GET"===d:try{let[,d]=a.url.split("?"),e=new URLSearchParams(d);g.operationName=null!=(b=e.get("operationName"))?b:void 0,g.query=null!=(c=e.get("query"))?c:void 0;let f=e.get("variables");f&&(g.variables=JSON.parse(f));let h=e.get("extensions");h&&(g.extensions=JSON.parse(h))}catch(a){throw Error("Unparsable URL")}break;case"POST"===d&&"application/json"===e&&"charset=utf-8"===f:{let b;if(!a.body)throw Error("Missing body");try{let c="function"==typeof a.body?await a.body():a.body;b="string"==typeof c?JSON.parse(c):c}catch(a){throw Error("Unparsable JSON body")}if(!aO(b))throw Error("JSON body must be an object");g.operationName=b.operationName,g.query=b.query,g.variables=b.variables,g.extensions=b.extensions;break}default:return[null,{status:415,statusText:"Unsupported Media Type"}]}if(null==g.query)throw Error("Missing query");if("string"!=typeof g.query)throw Error("Invalid query");if(null!=g.variables&&("object"!=typeof g.variables||Array.isArray(g.variables)))throw Error("Invalid variables");if(null!=g.operationName&&"string"!=typeof g.operationName)throw Error("Invalid operationName");if(null!=g.extensions&&("object"!=typeof g.extensions||Array.isArray(g.extensions)))throw Error("Invalid extensions");return g}function aR(a,b,c){if(a instanceof Error&&!aU(a))return[JSON.stringify({errors:[c(a)]},aV),{status:400,statusText:"Bad Request",headers:{"content-type":"application/json; charset=utf-8"}}];let d=aU(a)?[a]:aT(a)?a:null;return d?[JSON.stringify({errors:d.map(c)},aV),Object.assign(Object.assign({},"application/json"===b?{status:200,statusText:"OK"}:{status:400,statusText:"Bad Request"}),{headers:{"content-type":"application/json"===b?"application/json; charset=utf-8":"application/graphql-response+json; charset=utf-8"}})]:[JSON.stringify("errors"in a&&a.errors?Object.assign(Object.assign({},a),{errors:a.errors.map(c)}):a,aV),{status:200,statusText:"OK",headers:{"content-type":"application/json"===b?"application/json; charset=utf-8":"application/graphql-response+json; charset=utf-8"}}]}function aS(a,b){return"function"==typeof a.headers.get?a.headers.get(b):Object(a.headers)[b]}function aT(a){return Array.isArray(a)&&a.length>0&&a.some(aU)}function aU(a){return a instanceof d.GraphQLError}function aV(a,b){return b instanceof Error&&!aU(b)?{message:b.message}:b}var aW=c(26250),aX=c(64932),aY=c(18963),aZ=c(95993),a$=c(91506),a_=c(60441),a0=c(67862);let a1=async({err:a,payload:b,req:c})=>{let d=a.originalError.status||aW.h.INTERNAL_SERVER_ERROR,e=a.message;(0,aX.v)({err:a,payload:b}),b.config.debug||d!==aW.h.INTERNAL_SERVER_ERROR||(e="Something went wrong.");let f={extensions:{name:a?.originalError?.name||void 0,data:a&&a.originalError&&a.originalError.data||void 0,stack:b.config.debug?a.stack:void 0,statusCode:d},locations:a.locations,message:e,path:a.path};return await b.config.hooks.afterError?.reduce(async(b,d)=>{await b;let e=await d({context:c.context,error:a,graphqlResult:f,req:c});e&&(f=e.graphqlResult||f)},Promise.resolve()),f},a2=global._payload_graphql;a2||(a2=global._payload_graphql={graphql:null,promise:null});let a3=async a=>{if(a2.graphql)return a2.graphql;if(!a2.promise){let b=await a;a2.promise=new Promise(a=>{let c=function(a){let b={collections:a.collections.reduce((a,b)=>(a[b.slug]={config:b},a),{}),globals:{config:a.globals},Mutation:{name:"Mutation",fields:{}},Query:{name:"Query",fields:{}},types:{arrayTypes:{},blockInputTypes:{},blockTypes:{},groupTypes:{},tabTypes:{}}};if(a.localization){let c,e;b.types.localeInputType=(c=a.localization,new d.GraphQLEnumType({name:"LocaleInputType",values:c.localeCodes.reduce((a,b)=>({...a,[l(b)]:{value:b}}),{})})),b.types.fallbackLocaleInputType=(e=a.localization,new d.GraphQLEnumType({name:"FallbackLocaleInputType",values:[...e.localeCodes,"none"].reduce((a,b)=>({...a,[l(b)]:{value:b}}),{})}))}if(!function({config:a,graphqlResult:b}){Object.keys(b.collections).forEach(c=>{var e;let f,g,h=b.collections[c],{config:j,config:{fields:k,graphQL:m={},versions:o}}=h;if(!m)return;let p=(0,n.EI)(h.config.slug);(f=m.singularName?(0,n.x4)(m.singularName,!0):p.singular)===(g=m.pluralName?(0,n.x4)(m.pluralName,!0):p.plural)&&(g=`all${f}`),h.graphQL={};let q=(0,y.L)(k).findIndex(a=>(0,A.Z7)(a)&&"id"===a.name)>-1,r=aa(a.db.defaultIDType,j),s={},t=[...k];q||(s.id={type:new d.GraphQLNonNull(r)},t.push({name:"id",type:a.db.defaultIDType}));let u=!!o?.drafts;h.graphQL.type=as({name:f,baseFields:s,collectionSlug:j.slug,config:a,fields:k,forceNullable:u,graphqlResult:b,parentName:f}),h.graphQL.paginatedType=at(g,h.graphQL.type),h.graphQL.whereInputType=aC({name:f,fields:t,parentName:f});let v=[...k];j.auth&&(!j.auth.disableLocalStrategy||"object"==typeof j.auth.disableLocalStrategy&&j.auth.disableLocalStrategy.optionalPassword)&&v.push({name:"password",type:"text",label:"Password",required:!("object"==typeof j.auth.disableLocalStrategy&&j.auth.disableLocalStrategy.optionalPassword)});let w=v;a.db.allowIDOnCreate&&!j.flattenedFields.some(a=>"id"===a.name)&&(w=[...w,{name:"id",type:a.db.defaultIDType}]);let Y=ab({name:f,config:a,fields:w,graphqlResult:b,parentIsLocalized:!1,parentName:f});Y&&(h.graphQL.mutationInputType=new d.GraphQLNonNull(Y));let Z=ab({name:`${f}Update`,config:a,fields:v.filter(a=>!((0,A.Z7)(a)&&"id"===a.name)),forceNullable:!0,graphqlResult:b,parentIsLocalized:!1,parentName:`${f}Update`});Z&&(h.graphQL.updateMutationInputType=new d.GraphQLNonNull(Z));let $="object"!=typeof j.graphQL||!j.graphQL.disableQueries,_="object"!=typeof j.graphQL||!j.graphQL.disableMutations;if($&&(b.Query.fields[f]={type:h.graphQL.type,args:{id:{type:new d.GraphQLNonNull(r)},draft:{type:d.GraphQLBoolean},...a.localization?{fallbackLocale:{type:b.types.fallbackLocaleInputType},locale:{type:b.types.localeInputType}}:{},trash:{type:d.GraphQLBoolean}},resolve:async function(a,b,c){let{req:d}=c,e=d.locale,f=d.fallbackLocale;d=(0,i.i)(d,"locale"),(d=(0,i.i)(d,"fallbackLocale")).locale=b.locale||e,d.fallbackLocale=b.fallbackLocale||f,d.query||(d.query={});let g=!(b.draft??d.query?.draft==="false")&&(d.query?.draft==="true"||void 0);"boolean"==typeof g&&(d.query.draft=String(g)),c.req=d;let j={id:b.id,collection:h,depth:0,draft:b.draft,req:(0,i.i)(d,"transactionID"),trash:b.trash};return await (0,T.$)(j)}},b.Query.fields[g]={type:at(g,h.graphQL.type),args:{draft:{type:d.GraphQLBoolean},where:{type:h.graphQL.whereInputType},...a.localization?{fallbackLocale:{type:b.types.fallbackLocaleInputType},locale:{type:b.types.localeInputType}}:{},limit:{type:d.GraphQLInt},page:{type:d.GraphQLInt},pagination:{type:d.GraphQLBoolean},sort:{type:d.GraphQLString},trash:{type:d.GraphQLBoolean}},resolve:async function(a,b,c){let{req:d}=c,e=d.locale,f=d.fallbackLocale;(d=(0,i.i)(d,["locale","fallbackLocale","transactionID"])).locale=b.locale||e,d.fallbackLocale=b.fallbackLocale||f,d.query||(d.query={});let g=!(b.draft??d.query?.draft==="false")&&(d.query?.draft==="true"||void 0);"boolean"==typeof g&&(d.query.draft=String(g)),c.req=d;let j={collection:h,depth:0,draft:b.draft,limit:b.limit,page:b.page,pagination:b.pagination,req:d,sort:b.sort,trash:b.trash,where:b.where};return await (0,S.L)(j)}},b.Query.fields[`count${g}`]={type:new d.GraphQLObjectType({name:`count${g}`,fields:{totalDocs:{type:d.GraphQLInt}}}),args:{draft:{type:d.GraphQLBoolean},where:{type:h.graphQL.whereInputType},...a.localization?{locale:{type:b.types.localeInputType}}:{}},resolve:async function(a,b,c){let{req:d}=c,e=d.locale,f=d.fallbackLocale;d=(0,i.i)(d,"locale"),(d=(0,i.i)(d,"fallbackLocale")).locale=b.locale||e,d.fallbackLocale=f,c.req=d;let g={collection:h,req:(0,i.i)(d,"transactionID"),where:b.where};return await (0,N.R)(g)}},b.Query.fields[`docAccess${f}`]={type:x({type:"collection",entity:j,scope:"docAccess",typeSuffix:"DocAccess"}),args:{id:{type:new d.GraphQLNonNull(r)}},resolve:async function(a,b,c){return(0,Q.O)({id:b.id,collection:h,req:(0,i.i)(c.req,"transactionID")})}}),_&&(b.Mutation.fields[`create${f}`]={type:h.graphQL.type,args:{...Y?{data:{type:h.graphQL.mutationInputType}}:{},draft:{type:d.GraphQLBoolean},...a.localization?{locale:{type:b.types.localeInputType}}:{}},resolve:async function(a,b,c){return b.locale&&(c.req.locale=b.locale),await (0,O.k)({collection:h,data:b.data,depth:0,draft:b.draft,req:(0,i.i)(c.req,"transactionID")})}},b.Mutation.fields[`update${f}`]={type:h.graphQL.type,args:{id:{type:new d.GraphQLNonNull(r)},autosave:{type:d.GraphQLBoolean},...Z?{data:{type:h.graphQL.updateMutationInputType}}:{},draft:{type:d.GraphQLBoolean},...a.localization?{locale:{type:b.types.localeInputType}}:{},trash:{type:d.GraphQLBoolean}},resolve:async function(a,b,c){let{req:d}=c,e=d.locale,f=d.fallbackLocale;d=(0,i.i)(d,"locale"),(d=(0,i.i)(d,"fallbackLocale")).locale=b.locale||e,d.fallbackLocale=b.fallbackLocale||f,d.query||(d.query={});let g=!(b.draft??d.query?.draft==="false")&&(d.query?.draft==="true"||void 0);"boolean"==typeof g&&(d.query.draft=String(g)),c.req=d;let j={id:b.id,autosave:b.autosave,collection:h,data:b.data,depth:0,draft:b.draft,req:(0,i.i)(d,"transactionID"),trash:b.trash};return await (0,X.Z)(j)}},b.Mutation.fields[`delete${f}`]={type:h.graphQL.type,args:{id:{type:new d.GraphQLNonNull(r)},trash:{type:d.GraphQLBoolean}},resolve:async function(a,b,c){let{req:d}=c,e=d.locale,f=d.fallbackLocale;d=(0,i.i)(d,"locale"),(d=(0,i.i)(d,"fallbackLocale")).locale=b.locale||e,d.fallbackLocale=b.fallbackLocale||f,d.query||(d.query={});let g=!(b.draft??d.query?.draft==="false")&&(d.query?.draft==="true"||void 0);"boolean"==typeof g&&(d.query.draft=String(g)),c.req=d;let j={id:b.id,collection:h,depth:0,req:(0,i.i)(d,"transactionID"),trash:b.trash};return await (0,P.l)(j)}},!0!==j.disableDuplicate&&(b.Mutation.fields[`duplicate${f}`]={type:h.graphQL.type,args:{id:{type:new d.GraphQLNonNull(r)},...Y?{data:{type:h.graphQL.mutationInputType}}:{}},resolve:async function(a,b,c){let{req:d}=c,e=d.locale,f=d.fallbackLocale;return d.locale=b.locale||e,d.fallbackLocale=b.fallbackLocale||f,c.req=d,await (0,R.h)({id:b.id,collection:h,data:b.data,depth:0,draft:b.draft,req:(0,i.i)(d,"transactionID")})}})),j.versions){let c="text"===a.db.defaultIDType?d.GraphQLString:d.GraphQLInt,e=[...(0,z.c)(a,j),{name:"id",type:a.db.defaultIDType},{name:"createdAt",type:"date",label:({t:a})=>a("general:createdAt")},{name:"updatedAt",type:"date",label:({t:a})=>a("general:updatedAt")}];h.graphQL.versionType=as({name:`${f}Version`,collectionSlug:j.slug,config:a,fields:e,forceNullable:u,graphqlResult:b,parentName:`${f}Version`}),$&&(b.Query.fields[`version${l(f)}`]={type:h.graphQL.versionType,args:{id:{type:c},...a.localization?{fallbackLocale:{type:b.types.fallbackLocaleInputType},locale:{type:b.types.localeInputType}}:{},trash:{type:d.GraphQLBoolean}},resolve:async function(a,b,c){let{req:d}=c,e=d.locale,f=d.fallbackLocale;d=(0,i.i)(d,"locale"),(d=(0,i.i)(d,"fallbackLocale")).locale=b.locale||e,d.fallbackLocale=b.fallbackLocale||f,c.req=d;let g={id:b.id,collection:h,depth:0,req:(0,i.i)(d,"transactionID"),trash:b.trash};return await (0,U.L)(g)}},b.Query.fields[`versions${g}`]={type:at(`versions${l(g)}`,h.graphQL.versionType),args:{where:{type:aC({name:`versions${f}`,fields:e,parentName:`versions${f}`})},...a.localization?{fallbackLocale:{type:b.types.fallbackLocaleInputType},locale:{type:b.types.localeInputType}}:{},limit:{type:d.GraphQLInt},page:{type:d.GraphQLInt},pagination:{type:d.GraphQLBoolean},sort:{type:d.GraphQLString},trash:{type:d.GraphQLBoolean}},resolve:async function(a,b,c){let{req:d}=c,e=d.locale,f=d.fallbackLocale;d=(0,i.i)(d,"locale"),(d=(0,i.i)(d,"fallbackLocale")).locale=b.locale||e,d.fallbackLocale=b.fallbackLocale||f,d.query||(d.query={});let g=!(b.draft??d.query?.draft==="false")&&(d.query?.draft==="true"||void 0);"boolean"==typeof g&&(d.query.draft=String(g)),c.req=d;let j={collection:h,depth:0,limit:b.limit,page:b.page,pagination:b.pagination,req:(0,i.i)(d,"transactionID"),sort:b.sort,trash:b.trash,where:b.where};return await (0,V.s)(j)}}),_&&(b.Mutation.fields[`restoreVersion${l(f)}`]={type:h.graphQL.type,args:{id:{type:c},draft:{type:d.GraphQLBoolean}},resolve:async function(a,b,c){let d={id:b.id,collection:h,depth:0,draft:b.draft,req:(0,i.i)(c.req,"transactionID")};return await (0,W.c)(d)}})}if(j.auth){let g=j.auth.disableLocalStrategy||j.auth.loginWithUsername&&!j.auth.loginWithUsername.allowEmailLogin&&!j.auth.loginWithUsername.requireEmail?[]:[{name:"email",type:"email",required:!0}];if((h.graphQL.JWT=as({name:l(`${c}JWT`),config:a,fields:[...j.fields.filter(a=>(0,A.Z7)(a)&&a.saveToJWT),...g,{name:"collection",type:"text",required:!0}],graphqlResult:b,parentName:l(`${c}JWT`)}),$&&(b.Query.fields[`me${f}`]={type:new d.GraphQLObjectType({name:l(`${c}Me`),fields:{collection:{type:d.GraphQLString},exp:{type:d.GraphQLInt},strategy:{type:d.GraphQLString},token:{type:d.GraphQLString},user:{type:h.graphQL.type}}}),resolve:async function(a,b,c){let d={collection:h,currentToken:(0,H.p)(c.req),depth:0,req:(0,i.i)(c.req,"transactionID")},e=await (0,I.M)(d);return h.config.auth.removeTokenFromResponses&&delete e.token,e}},b.Query.fields[`initialized${f}`]={type:d.GraphQLBoolean,resolve:(e=h.config.slug,async function(a,b,c){let d={collection:e,req:(0,i.i)(c.req,"transactionID")};return(0,D.M)(d)})}),_)&&(b.Mutation.fields[`refreshToken${f}`]={type:new d.GraphQLObjectType({name:l(`${c}Refreshed${f}`),fields:{exp:{type:d.GraphQLInt},refreshedToken:{type:d.GraphQLString},strategy:{type:d.GraphQLString},user:{type:h.graphQL.JWT}}}),resolve:async function(a,b,c){let d={collection:h,depth:0,req:(0,i.i)(c.req,"transactionID")},e=await (0,J.r)(d),f=(0,F.IS)({collectionAuthConfig:h.config.auth,cookiePrefix:c.req.payload.config.cookiePrefix,token:e.refreshedToken});return c.headers["Set-Cookie"]=f,h.config.auth.removeTokenFromResponses&&delete e.refreshedToken,e}},b.Mutation.fields[`logout${f}`]={type:d.GraphQLString,args:{allSessions:{type:d.GraphQLBoolean}},resolve:async function(a,b,c){let d={allSessions:b.allSessions,collection:h,req:(0,i.i)(c.req,"transactionID")},e=await (0,G.u)(d),f=(0,F.DN)({collectionAuthConfig:h.config.auth,config:c.req.payload.config,cookiePrefix:c.req.payload.config.cookiePrefix});return c.headers["Set-Cookie"]=f,e}},!j.auth.disableLocalStrategy)){let a={},{canLoginWithEmail:e,canLoginWithUsername:g}=(0,B.Y)(j.auth.loginWithUsername);e&&(a.email={type:new d.GraphQLNonNull(d.GraphQLString)}),g&&(a.username={type:new d.GraphQLNonNull(d.GraphQLString)}),j.auth.maxLoginAttempts>0&&(b.Mutation.fields[`unlock${f}`]={type:new d.GraphQLNonNull(d.GraphQLBoolean),args:a,resolve:async function(a,b,c){let d={collection:h,data:{email:b.email,username:b.username},req:(0,i.i)(c.req,"transactionID")};return await (0,L.k)(d)}}),b.Mutation.fields[`login${f}`]={type:new d.GraphQLObjectType({name:l(`${c}LoginResult`),fields:{exp:{type:d.GraphQLInt},token:{type:d.GraphQLString},user:{type:h.graphQL.type}}}),args:{...a,password:{type:d.GraphQLString}},resolve:async function(a,b,c){let d={collection:h,data:{email:b.email,password:b.password,username:b.username},depth:0,req:(0,i.i)(c.req,"transactionID")},e=await (0,E.k)(d),f=(0,F.IS)({collectionAuthConfig:h.config.auth,cookiePrefix:c.req.payload.config.cookiePrefix,token:e.token});return c.headers["Set-Cookie"]=f,h.config.auth.removeTokenFromResponses&&delete e.token,e}},b.Mutation.fields[`forgotPassword${f}`]={type:new d.GraphQLNonNull(d.GraphQLBoolean),args:{disableEmail:{type:d.GraphQLBoolean},expiration:{type:d.GraphQLInt},...a},resolve:async function(a,b,c){let d={collection:h,data:{email:b.email,username:b.username},disableEmail:b.disableEmail,expiration:b.expiration,req:(0,i.i)(c.req,"transactionID")};return await (0,C.g)(d),!0}},b.Mutation.fields[`resetPassword${f}`]={type:new d.GraphQLObjectType({name:l(`${c}ResetPassword`),fields:{token:{type:d.GraphQLString},user:{type:h.graphQL.type}}}),args:{password:{type:d.GraphQLString},token:{type:d.GraphQLString}},resolve:async function(a,b,c){b.locale&&(c.req.locale=b.locale),b.fallbackLocale&&(c.req.fallbackLocale=b.fallbackLocale);let d={api:"GraphQL",collection:h,data:b,depth:0,req:(0,i.i)(c.req,"transactionID")},e=await (0,K.q)(d),f=(0,F.IS)({collectionAuthConfig:h.config.auth,cookiePrefix:c.req.payload.config.cookiePrefix,token:e.token});return c.headers["Set-Cookie"]=f,h.config.auth.removeTokenFromResponses&&delete e.token,e}},b.Mutation.fields[`verifyEmail${f}`]={type:d.GraphQLBoolean,args:{token:{type:d.GraphQLString}},resolve:async function(a,b,c){b.locale&&(c.req.locale=b.locale),b.fallbackLocale&&(c.req.fallbackLocale=b.fallbackLocale);let d={api:"GraphQL",collection:h,req:(0,i.i)(c.req,"transactionID"),token:b.token};return await (0,M.p)(d)}}}}})}({config:a,graphqlResult:b}),!function({config:a,graphqlResult:b}){Object.keys(b.globals.config).forEach(c=>{let e=b.globals.config[c],{fields:f,graphQL:g,versions:h}=e;if(!1===g)return;let j=g?.name?g.name:aL((0,n.x4)(e.slug,!0)),k=!!h?.drafts;b.globals.graphQL||(b.globals.graphQL={});let m=ab({name:j,config:a,fields:f,graphqlResult:b,parentIsLocalized:!1,parentName:j});b.globals.graphQL[c]={type:as({name:j,config:a,fields:f,forceNullable:k,graphqlResult:b,parentName:j}),mutationInputType:m?new d.GraphQLNonNull(m):null};let o="object"!=typeof e.graphQL||!e.graphQL.disableQueries,p="object"!=typeof e.graphQL||!e.graphQL.disableMutations;if(o&&(b.Query.fields[j]={type:b.globals.graphQL[c].type,args:{draft:{type:d.GraphQLBoolean},...a.localization?{fallbackLocale:{type:b.types.fallbackLocaleInputType},locale:{type:b.types.localeInputType}}:{}},resolve:async function(a,b,c){b.locale&&(c.req.locale=b.locale),b.fallbackLocale&&(c.req.fallbackLocale=b.fallbackLocale);let{slug:d}=e,f={slug:d,depth:0,draft:b.draft,globalConfig:e,req:(0,i.i)(c.req,"transactionID")};return await (0,aG.j)(f)}},b.Query.fields[`docAccess${j}`]={type:x({type:"global",entity:e,scope:"docAccess",typeSuffix:"DocAccess"}),resolve:async function(a,b){return(0,aF.O)({globalConfig:e,req:(0,i.i)(b.req,"transactionID")})}}),p&&(b.Mutation.fields[`update${j}`]={type:b.globals.graphQL[c].type,args:{...m?{data:{type:b.globals.graphQL[c].mutationInputType}}:{},draft:{type:d.GraphQLBoolean},...a.localization?{locale:{type:b.types.localeInputType}}:{}},resolve:async function(a,b,c){b.locale&&(c.req.locale=b.locale),b.fallbackLocale&&(c.req.fallbackLocale=b.fallbackLocale);let{slug:d}=e,f={slug:d,data:b.data,depth:0,draft:b.draft,globalConfig:e,req:(0,i.i)(c.req,"transactionID")};return await (0,aK.L)(f)}}),e.versions){let f="number"===a.db.defaultIDType?d.GraphQLInt:d.GraphQLString,g=[...(0,aE.p)(a,e),{name:"id",type:a.db.defaultIDType},{name:"createdAt",type:"date",label:"Created At"},{name:"updatedAt",type:"date",label:"Updated At"}];b.globals.graphQL[c].versionType=as({name:`${j}Version`,config:a,fields:g,forceNullable:k,graphqlResult:b,parentName:`${j}Version`}),o&&(b.Query.fields[`version${l(j)}`]={type:b.globals.graphQL[c].versionType,args:{id:{type:f},draft:{type:d.GraphQLBoolean},...a.localization?{fallbackLocale:{type:b.types.fallbackLocaleInputType},locale:{type:b.types.localeInputType}}:{}},resolve:async function(a,b,c){b.locale&&(c.req.locale=b.locale),b.fallbackLocale&&(c.req.fallbackLocale=b.fallbackLocale);let d={id:b.id,depth:0,draft:b.draft,globalConfig:e,req:(0,i.i)(c.req,"transactionID")};return await (0,aH.L)(d)}},b.Query.fields[`versions${j}`]={type:at(`versions${l(j)}`,b.globals.graphQL[c].versionType),args:{where:{type:aC({name:`versions${j}`,fields:g,parentName:`versions${j}`})},...a.localization?{fallbackLocale:{type:b.types.fallbackLocaleInputType},locale:{type:b.types.localeInputType}}:{},limit:{type:d.GraphQLInt},page:{type:d.GraphQLInt},pagination:{type:d.GraphQLBoolean},sort:{type:d.GraphQLString}},resolve:async function(a,b,c){let d={depth:0,globalConfig:e,limit:b.limit,page:b.page,pagination:b.pagination,req:(0,i.i)(c.req,"transactionID"),sort:b.sort,where:b.where};return await (0,aI.s)(d)}}),p&&(b.Mutation.fields[`restoreVersion${l(j)}`]={type:b.globals.graphQL[c].type,args:{id:{type:f},draft:{type:d.GraphQLBoolean}},resolve:async function(a,b,c){let d={id:b.id,depth:0,draft:b.draft,globalConfig:e,req:(0,i.i)(c.req,"transactionID")};return await (0,aJ.c)(d)}})}})}({config:a,graphqlResult:b}),b.Query.fields.Access={type:function(a){let b={canAccessAdmin:{type:new d.GraphQLNonNull(d.GraphQLBoolean)}};return Object.values(a.collections).forEach(a=>{if(!1===a.graphQL)return;let c=x({type:"collection",entity:a,typeSuffix:"Access"});b[l(a.slug)]={type:c}}),Object.values(a.globals).forEach(a=>{if(!1===a.graphQL)return;let c=x({type:"global",entity:a,typeSuffix:"Access"});b[l(a.slug)]={type:c}}),new d.GraphQLObjectType({name:"Access",fields:b})}(a),resolve:async function(b,c,d){let e={req:(0,i.i)(d.req,"transactionID")},f=await (0,j.m)(e);return{...f,...m(f.collections,a.collections),...m(f.globals,a.globals)}}},"function"==typeof a.graphQL.queries){let c=a.graphQL.queries(e,{...b,config:a});b.Query={...b.Query,fields:{...b.Query.fields,...aM(c||{})}}}if("function"==typeof a.graphQL.mutations){let c=a.graphQL.mutations(e,{...b,config:a});b.Mutation={...b.Mutation,fields:{...b.Mutation.fields,...aM(c||{})}}}let c=new d.GraphQLObjectType(b.Query),f=new d.GraphQLObjectType(b.Mutation);return{schema:new d.GraphQLSchema({mutation:f,query:c}),validationRules:b=>{var c;return[(c={estimators:[a=>{if(a.field.extensions){if("number"==typeof a.field.extensions.complexity)return a.childComplexity+a.field.extensions.complexity;else if("function"==typeof a.field.extensions.complexity)return a.field.extensions.complexity(a)}},(a=>{let b=a&&"number"==typeof a.defaultComplexity?a.defaultComplexity:1;return a=>b+a.childComplexity})({defaultComplexity:1})],maximumComplexity:a.graphQL.maxComplexity,variables:b.variableValues},a=>new g(a,c)),...a.graphQL.disableIntrospectionInProduction?[aN]:[],..."function"==typeof a?.graphQL?.validationRules?a.graphQL.validationRules(b):[]]}}}(b);a(a2.graphql||c)})}try{a2.graphql=await a2.promise}catch(a){throw a2.promise=null,a}return a2.graphql},a4=a=>async b=>{let c=b.clone(),e=await (0,aY.o)({canSetHeaders:!0,config:a,request:b});await (0,aZ.z)(e),(0,a$.r)(e);let{schema:f,validationRules:g}=await a3(a),{payload:h}=e,i={},j=await (function(a,b={}){let c={Response:b.Response||Response,TextEncoder:b.TextEncoder||TextEncoder,ReadableStream:b.ReadableStream||ReadableStream},e=function(a){let{schema:b,context:c,validate:e=d.validate,validationRules:f=[],execute:g=d.execute,parse:h=d.parse,getOperationAST:i=d.getOperationAST,rootValue:j,onSubscribe:k,onOperation:l,formatError:m=a=>a,parseRequestParams:n=aQ}=a;return async function(a){let o,p,q,r=null;for(let b of(aS(a,"accept")||"*/*").replace(/\s/g,"").toLowerCase().split(",")){let[a,...c]=b.split(";"),d=(null==c?void 0:c.find(a=>a.includes("charset=")))||"charset=utf-8";if("application/graphql-response+json"===a&&"charset=utf-8"===d){r="application/graphql-response+json";break}if(("application/json"===a||"application/*"===a||"*/*"===a)&&("charset=utf-8"===d||"charset=utf8"===d)){r="application/json";break}}if(!r)return[null,{status:406,statusText:"Not Acceptable",headers:{accept:"application/graphql-response+json; charset=utf-8, application/json; charset=utf-8"}}];try{let b=await n(a);if(b||(b=await aQ(a)),aP(b))return b;o=b}catch(a){return aR(a,r,m)}let s=await (null==k?void 0:k(a,o));if(aP(s))return s;if(aO(s)&&("data"in s||"data"in s&&null==s.data&&"errors"in s)||aT(s))return aR(s,r,m);if(s)p=s;else{let g;if(!b)throw Error("The GraphQL schema is not provided");let{operationName:i,query:j,variables:k}=o;try{g=h(j)}catch(a){return aR(a,r,m)}let l="function"==typeof c?await c(a,o):c;if(aP(l))return l;let n={operationName:i,document:g,variableValues:k,contextValue:l};if("function"==typeof b){let c=await b(a,n);if(aP(c))return c;p=Object.assign(Object.assign({},n),{schema:c})}else p=Object.assign(Object.assign({},n),{schema:b});let q=d.specifiedRules;q="function"==typeof f?await f(a,p,d.specifiedRules):[...q,...f];let s=e(p.schema,p.document,q);if(s.length)return aR(s,r,m)}try{let a=i(p.document,p.operationName);if(!a)throw null;q=a.operation}catch(a){return aR(new d.GraphQLError("Unable to detect operation AST"),r,m)}if("subscription"===q)return aR(new d.GraphQLError("Subscriptions are not supported"),r,m);if("mutation"===q&&"GET"===a.method)return[JSON.stringify({errors:[new d.GraphQLError("Cannot perform mutations over GET")]}),{status:405,statusText:"Method Not Allowed",headers:{allow:"POST"}}];if("rootValue"in p||(p.rootValue=j),!("contextValue"in p)){let b="function"==typeof c?await c(a,o):c;if(aP(b))return b;p.contextValue=b}let t=await g(p),u=await (null==l?void 0:l(a,p,t));return aP(u)?u:(u&&(t=u),"function"==typeof Object(t)[Symbol.asyncIterator]?aR(new d.GraphQLError("Subscriptions are not supported"),r,m):aR(t,r,m))}}(a);return async function(a){try{let[b,d]=await e(function(a,b={}){return{method:a.method,url:a.url,headers:a.headers,body:()=>a.text(),raw:a,context:{Response:b.Response||Response,TextEncoder:b.TextEncoder||TextEncoder,ReadableStream:b.ReadableStream||ReadableStream}}}(a,c));return new c.Response(b,d)}catch(a){return console.error("Internal error occurred during request handling. Please check your implementation.",a),new c.Response(null,{status:500})}}})({context:{headers:i,req:e},onOperation:async(a,b,c)=>{let d="function"==typeof h.extensions?await h.extensions({args:b,req:a,result:c}):c;if(d.errors){let a=await Promise.all(c.errors.map(a=>a1({err:a,payload:h,req:e})));return{...d,errors:a}}return d},schema:f,validationRules:(a,b,c)=>c.concat(g(b))})(c),k=(0,a_.y)({headers:new Headers(j.headers),req:e});for(let a in i)k.append(a,i[a]);return new Response(j.body,{headers:e.responseHeaders?(0,a0.l)(e.responseHeaders,k):k,status:j.status})}},81630:a=>{"use strict";a.exports=require("http")},83725:a=>{"use strict";a.exports=import("next/dist/compiled/@vercel/og/index.node.js")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91043:a=>{"use strict";a.exports=require("@aws-sdk/client-s3")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},96487:()=>{},98995:a=>{"use strict";a.exports=require("node:module")}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[1103,9598,9436,6476,6622],()=>b(b.s=58778));module.exports=c})();