(()=>{var e={};e.id=3271,e.ids=[3271],e.modules={91043:e=>{"use strict";e.exports=require("@aws-sdk/client-s3")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74552:e=>{"use strict";e.exports=require("pino")},14007:e=>{"use strict";e.exports=require("pino-pretty")},79428:e=>{"use strict";e.exports=require("buffer")},79646:e=>{"use strict";e.exports=require("child_process")},55511:e=>{"use strict";e.exports=require("crypto")},14985:e=>{"use strict";e.exports=require("dns")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},73496:e=>{"use strict";e.exports=require("http2")},55591:e=>{"use strict";e.exports=require("https")},8086:e=>{"use strict";e.exports=require("module")},91645:e=>{"use strict";e.exports=require("net")},21820:e=>{"use strict";e.exports=require("os")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},11997:e=>{"use strict";e.exports=require("punycode")},4984:e=>{"use strict";e.exports=require("readline")},27910:e=>{"use strict";e.exports=require("stream")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},28855:e=>{"use strict";e.exports=import("@libsql/client")},83725:e=>{"use strict";e.exports=import("next/dist/compiled/@vercel/og/index.node.js")},34589:e=>{"use strict";e.exports=require("node:assert")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},4573:e=>{"use strict";e.exports=require("node:buffer")},37540:e=>{"use strict";e.exports=require("node:console")},77598:e=>{"use strict";e.exports=require("node:crypto")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},40610:e=>{"use strict";e.exports=require("node:dns")},78474:e=>{"use strict";e.exports=require("node:events")},73024:e=>{"use strict";e.exports=require("node:fs")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},37067:e=>{"use strict";e.exports=require("node:http")},32467:e=>{"use strict";e.exports=require("node:http2")},98995:e=>{"use strict";e.exports=require("node:module")},77030:e=>{"use strict";e.exports=require("node:net")},48161:e=>{"use strict";e.exports=require("node:os")},76760:e=>{"use strict";e.exports=require("node:path")},643:e=>{"use strict";e.exports=require("node:perf_hooks")},1708:e=>{"use strict";e.exports=require("node:process")},41792:e=>{"use strict";e.exports=require("node:querystring")},80099:e=>{"use strict";e.exports=require("node:sqlite")},57075:e=>{"use strict";e.exports=require("node:stream")},37830:e=>{"use strict";e.exports=require("node:stream/web")},41692:e=>{"use strict";e.exports=require("node:tls")},73136:e=>{"use strict";e.exports=require("node:url")},57975:e=>{"use strict";e.exports=require("node:util")},73429:e=>{"use strict";e.exports=require("node:util/types")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},38522:e=>{"use strict";e.exports=require("node:zlib")},31486:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{patchFetch:()=>p,routeModule:()=>c,serverHooks:()=>d,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>y});var n=a(42706),o=a(28203),l=a(45994),i=a(83844),s=e([i]);i=(s.then?(await s)():s)[0];let c=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/(payload)/api/graphql/route",pathname:"/api/graphql",filename:"route",bundlePath:"app/(payload)/api/graphql/route"},resolvedPagePath:"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\api\\graphql\\route.ts",nextConfigOutput:"standalone",userland:i}),{workAsyncStorage:u,workUnitAsyncStorage:y,serverHooks:d}=c;function p(){return(0,l.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:y})}r()}catch(e){r(e)}})},96487:()=>{},78335:()=>{},83844:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{OPTIONS:()=>p,POST:()=>s});var n=a(17750),o=a(96851),l=a(74970),i=e([n]);n=(i.then?(await i)():i)[0];let s=(0,o.L)(n.A),p=(0,l.lw)(n.A);r()}catch(e){r(e)}})},96851:(e,t,a)=>{"use strict";a.d(t,{L:()=>te});let r=require("graphql");var n=a.t(r,2);let o=require("graphql/execution/values.js");class l{complexity;context;estimators;includeDirectiveDef;OperationDefinition;options;requestContext;skipDirectiveDef;variableValues;constructor(e,t){if(!("number"==typeof t.maximumComplexity&&t.maximumComplexity>0))throw Error("Maximum query complexity must be a positive number");this.context=e,this.complexity=0,this.options=t,this.includeDirectiveDef=this.context.getSchema().getDirective("include"),this.skipDirectiveDef=this.context.getSchema().getDirective("skip"),this.estimators=t.estimators,this.variableValues={},this.requestContext=t.context,this.OperationDefinition={enter:this.onOperationDefinitionEnter,leave:this.onOperationDefinitionLeave}}createError(){var e,t;return"function"==typeof this.options.createError?this.options.createError(this.options.maximumComplexity,this.complexity):new r.GraphQLError((e=this.options.maximumComplexity,t=this.complexity,`The query exceeds the maximum complexity of ${e}. Actual complexity is ${t}`))}nodeComplexity(e,t){if(e.selectionSet){let a,n={};(t instanceof r.GraphQLObjectType||t instanceof r.GraphQLInterfaceType)&&(n=t.getFields()),a=(0,r.isAbstractType)(t)?this.context.getSchema().getPossibleTypes(t).map(e=>e.name):[t.name];let l=e.selectionSet.selections.reduce((e,l)=>{let s=e,p=!0,c=!1;for(let e of l.directives??[])switch(e.name.value){case"include":{let e=(0,o.getDirectiveValues)(this.includeDirectiveDef,l,this.variableValues||{});"boolean"==typeof e.if&&(p=e.if);break}case"skip":{let e=(0,o.getDirectiveValues)(this.skipDirectiveDef,l,this.variableValues||{});"boolean"==typeof e.if&&(c=e.if)}}if(!p||c)return e;switch(l.kind){case r.Kind.FIELD:{let p;let c=n[l.name.value];if(!c)break;let u=(0,r.getNamedType)(c.type);try{p=(0,o.getArgumentValues)(c,l,this.variableValues||{})}catch(t){return this.context.reportError(t),e}let y=0;(0,r.isCompositeType)(u)&&(y=this.nodeComplexity(l,u));let d={type:t,args:p,childComplexity:y,context:this.requestContext,field:c,node:l};if(!this.estimators.find(t=>{let r=t(d);return!("number"!=typeof r||isNaN(r))&&(s=i(r,e,a),!0)}))return this.context.reportError(new r.GraphQLError(`No complexity could be calculated for field ${t.name}.${c.name}. At least one complexity estimator has to return a complexity score.`)),e;break}case r.Kind.FRAGMENT_SPREAD:{let t=this.context.getFragment(l.name.value);if(!t)break;let a=this.context.getSchema().getType(t.typeCondition.name.value);if(!(0,r.isCompositeType)(a))break;let n=this.nodeComplexity(t,a);s=(0,r.isAbstractType)(a)?i(n,e,this.context.getSchema().getPossibleTypes(a).map(e=>e.name)):i(n,e,[a.name]);break}case r.Kind.INLINE_FRAGMENT:{let a=t;if(l.typeCondition&&l.typeCondition.name&&(a=this.context.getSchema().getType(l.typeCondition.name.value),!(0,r.isCompositeType)(a)))break;let n=this.nodeComplexity(l,a);s=(0,r.isAbstractType)(a)?i(n,e,this.context.getSchema().getPossibleTypes(a).map(e=>e.name)):i(n,e,[a.name]);break}default:s=i(this.nodeComplexity(l,t),e,a)}return s},{});return l?Math.max(...Object.values(l),0):NaN}return 0}onOperationDefinitionEnter(e){if("string"==typeof this.options.operationName&&this.options.operationName!==e.name.value)return;let{coerced:t,errors:a}=(0,o.getVariableValues)(this.context.getSchema(),e.variableDefinitions?[...e.variableDefinitions]:[],this.options.variables??{});if(a&&a.length){a.forEach(e=>this.context.reportError(e));return}switch(this.variableValues=t,e.operation){case"mutation":this.complexity+=this.nodeComplexity(e,this.context.getSchema().getMutationType());break;case"query":this.complexity+=this.nodeComplexity(e,this.context.getSchema().getQueryType());break;case"subscription":this.complexity+=this.nodeComplexity(e,this.context.getSchema().getSubscriptionType());break;default:throw Error(`Query complexity could not be calculated for operation of type ${e.operation}`)}}onOperationDefinitionLeave(e){if(("string"!=typeof this.options.operationName||this.options.operationName===e.name.value)&&(this.options.onComplete&&this.options.onComplete(this.complexity),this.complexity>this.options.maximumComplexity))return this.context.reportError(this.createError())}}function i(e,t,a){for(let r of a)Object.prototype.hasOwnProperty.call(t,r)?t[r]+=e:t[r]=e;return t}let s=()=>e=>{if(e.field.extensions){if("number"==typeof e.field.extensions.complexity)return e.childComplexity+e.field.extensions.complexity;if("function"==typeof e.field.extensions.complexity)return e.field.extensions.complexity(e)}},p=e=>{let t=e&&"number"==typeof e.defaultComplexity?e.defaultComplexity:1;return e=>t+e.childComplexity};var c=a(44532),u=a(90133);let y=["0","1","2","3","4","5","6","7","8","9"],d=e=>{let t=String(e),a=t.substring(0,1);return y.indexOf(a)>-1&&(t=`_${t}`),t.normalize("NFKD").replace(/[\u0300-\u036f]/g,"").replace(/\./g,"_").replace(/-|\//g,"_").replace(/\+/g,"_").replace(/,/g,"_").replace(/\(/g,"_").replace(/\)/g,"_").replace(/'/g,"_").replace(/ /g,"")||"_"},h=(e,t)=>{let a={...e};return t.forEach(({slug:e})=>{let t={...a[e]||{}};delete a[e],a[d(e)]=t}),a},f=e=>new r.GraphQLEnumType({name:"FallbackLocaleInputType",values:[...e.localeCodes,"none"].reduce((e,t)=>({...e,[d(t)]:{value:t}}),{})}),m=e=>new r.GraphQLEnumType({name:"LocaleInputType",values:e.localeCodes.reduce((e,t)=>({...e,[d(t)]:{value:t}}),{})});var g=a(22543);let L=require("graphql/language/index.js");function b(e){return e}function Q(e){if("object"!=typeof e||null===e||Array.isArray(e))throw TypeError(`JSONObject cannot represent non-object value: ${e}`);return e}function w(e,t,a){let r=Object.create(null);return t.fields.forEach(t=>{r[t.name.value]=x(e,t.value,a)}),r}function x(e,t,a){switch(t.kind){case L.Kind.BOOLEAN:case L.Kind.STRING:return t.value;case L.Kind.FLOAT:case L.Kind.INT:return parseFloat(t.value);case L.Kind.LIST:return t.values.map(t=>x(e,t,a));case L.Kind.NULL:return null;case L.Kind.OBJECT:return w(e,t,a);case L.Kind.VARIABLE:return a?a[t.name.value]:void 0;default:throw TypeError(`${e} cannot represent value: ${(0,L.print)(t)}`)}}let v=new r.GraphQLScalarType({name:"JSON",description:"The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).",parseLiteral:(e,t)=>x("JSON",e,t),parseValue:b,serialize:b,specifiedByURL:"http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf"}),T=new r.GraphQLScalarType({name:"JSONObject",description:"The `JSONObject` scalar type represents JSON objects as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).",parseLiteral:(e,t)=>{if(e.kind!==L.Kind.OBJECT)throw TypeError(`JSONObject cannot represent non-object value: ${(0,L.print)(e)}`);return w("JSONObject",e,t)},parseValue:Q,serialize:Q,specifiedByURL:"http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf"}),q=(e,t)=>t.reduce((t,a)=>{if(!a.hidden&&"ui"!==a.type){if(a.name){let n=d(a.name),o=["create","read","update","delete"].reduce((t,a)=>{let o=a.charAt(0).toUpperCase()+a.slice(1);return{...t,[a]:{type:new r.GraphQLObjectType({name:`${e}_${n}_${o}`,fields:{permission:{type:new r.GraphQLNonNull(r.GraphQLBoolean)}}})}}},{});return a.fields&&(o.fields={type:new r.GraphQLObjectType({name:`${e}_${n}_Fields`,fields:q(`${e}_${n}`,a.fields)})}),{...t,[d(a.name)]:{type:new r.GraphQLObjectType({name:`${e}_${n}`,fields:o})}}}if(!a.name&&a.fields){let r=q(e,a.fields);return{...t,...r}}if("tabs"===a.type)return a.tabs.reduce((t,a)=>({...t,...q(e,a.fields)}),{...t})}return t},{}),G=e=>{let{name:t,entityFields:a,operations:n,scope:o}=e,l=(0,g.x4)(`${t}-${o||""}-Fields`,!0),i={fields:{type:new r.GraphQLObjectType({name:l,fields:q(l,a)})}};return n.forEach(e=>{let a=(0,g.x4)(`${t}-${e}-${o||"Access"}`,!0);i[e]={type:new r.GraphQLObjectType({name:a,fields:{permission:{type:new r.GraphQLNonNull(r.GraphQLBoolean)},where:{type:T}}})}}),i};function k(e){let{type:t,entity:a,scope:n,typeSuffix:o}=e,{slug:l,fields:i,graphQL:s,versions:p}=a,c=[];if(!1===s)return null;if("collection"===t){c=["create","read","update","delete"],a.auth&&"object"==typeof a.auth&&void 0!==a.auth.maxLoginAttempts&&0!==a.auth.maxLoginAttempts&&c.push("unlock"),p&&c.push("readVersions");let e=d(`${l}${o||""}`);return new r.GraphQLObjectType({name:e,fields:G({name:l,entityFields:i,operations:c,scope:n})})}c=["read","update"],a.versions&&c.push("readVersions");let u=d(`${global?.graphQL?.name||l}${o||""}`);return new r.GraphQLObjectType({name:u,fields:G({name:a.graphQL&&a?.graphQL?.name||l,entityFields:a.fields,operations:c,scope:n})})}var N=a(45559),I=a(64429),S=a(51022),A=a(92530),D=a(55882),E=a(86214),O=a(87472),j=a(8416),$=a(30478),C=a(68065),R=a(48146),_=a(20053),M=a(15792),z=a(25180),P=a(75337),B=a(50125),F=a(6158),V=a(50386),J=a(23805),U=a(77039),Z=a(82390),K=a(5834),H=a(75832),W=a(71907),X=a(89851),Y=a(3252);let ee=(e,t)=>d(`${e?`${e}_`:""}${t}`),et=e=>"type"in e&&"group"===e.type?e.fields.some(e=>(0,S.Z7)(e)&&"required"in e&&e.required||et(e)):"fields"in e&&"name"in e&&e.fields.some(e=>et(e)),ea=({type:e,field:t,forceNullable:a,parentIsLocalized:n})=>{let o=t.access&&t.access.read,l=t.admin&&t.admin.condition,i="createdAt"===t.name||"updatedAt"===t.name;return!a&&"required"in t&&t.required&&(!t.localized||n)&&!l&&!o&&!i?new r.GraphQLNonNull(e):e},er={number:r.GraphQLInt,text:r.GraphQLString},en=(e,t)=>{let a=(0,N.L)(t.fields).find(e=>(0,S.Z7)(e)&&"id"===e.name);return a?er[a.type]:er[e]};function eo({name:e,config:t,fields:a,forceNullable:n=!1,graphqlResult:o,parentIsLocalized:l,parentName:i}){let s={array:(e,a)=>{let s=ee(i,(0,g.x4)(a.name,!0)),p=eo({name:s,config:t,fields:a.fields,graphqlResult:o,parentIsLocalized:l||a.localized,parentName:s});return p?(p=new r.GraphQLList(ea({type:p,field:a,forceNullable:n,parentIsLocalized:l})),{...e,[d(a.name)]:{type:p}}):e},blocks:(e,t)=>({...e,[d(t.name)]:{type:v}}),checkbox:(e,t)=>({...e,[d(t.name)]:{type:r.GraphQLBoolean}}),code:(e,t)=>({...e,[d(t.name)]:{type:ea({type:r.GraphQLString,field:t,forceNullable:n,parentIsLocalized:l})}}),collapsible:(e,t)=>t.fields.reduce((e,t)=>{let a=s[t.type];return a?a(e,t):e},e),date:(e,t)=>({...e,[d(t.name)]:{type:ea({type:r.GraphQLString,field:t,forceNullable:n,parentIsLocalized:l})}}),email:(e,t)=>({...e,[d(t.name)]:{type:ea({type:r.GraphQLString,field:t,forceNullable:n,parentIsLocalized:l})}}),group:(e,a)=>{if(!(0,S.Z7)(a))return a.fields.reduce((e,t)=>{let a=s[t.type];return a?a(e,t):e},e);{let n=et(a),s=ee(i,(0,g.x4)(a.name,!0)),p=eo({name:s,config:t,fields:a.fields,graphqlResult:o,parentIsLocalized:l||a.localized,parentName:s});return p?(n&&(p=new r.GraphQLNonNull(p)),{...e,[d(a.name)]:{type:p}}):e}},json:(e,t)=>({...e,[d(t.name)]:{type:ea({type:v,field:t,forceNullable:n,parentIsLocalized:l})}}),number:(e,t)=>{let a="id"===t.name?r.GraphQLInt:r.GraphQLFloat;return{...e,[d(t.name)]:{type:ea({type:!0===t.hasMany?new r.GraphQLList(a):a,field:t,forceNullable:n,parentIsLocalized:l})}}},point:(e,t)=>({...e,[d(t.name)]:{type:ea({type:new r.GraphQLList(r.GraphQLFloat),field:t,forceNullable:n,parentIsLocalized:l})}}),radio:(e,t)=>({...e,[d(t.name)]:{type:ea({type:r.GraphQLString,field:t,forceNullable:n,parentIsLocalized:l})}}),relationship:(e,a)=>{let n;let{relationTo:l}=a;if(Array.isArray(l)){let e=`${ee(i,(0,g.x4)(a.name,!0))}RelationshipInput`;n=new r.GraphQLInputObjectType({name:e,fields:{relationTo:{type:new r.GraphQLEnumType({name:`${e}RelationTo`,values:l.reduce((e,t)=>({...e,[d(t)]:{value:t}}),{})})},value:{type:v}}})}else n=en(t.db.defaultIDType,o.collections[l].config);return{...e,[d(a.name)]:{type:a.hasMany?new r.GraphQLList(n):n}}},richText:(e,t)=>({...e,[d(t.name)]:{type:ea({type:v,field:t,forceNullable:n,parentIsLocalized:l})}}),row:(e,t)=>t.fields.reduce((e,t)=>{let a=s[t.type];return a?a(e,t):e},e),select:(e,t)=>{let a=`${ee(i,t.name)}_MutationInput`,o=new r.GraphQLEnumType({name:a,values:t.options.reduce((e,t)=>(0,S.vs)(t)?{...e,[d(t.value)]:{value:t.value}}:{...e,[d(t)]:{value:t}},{})});return o=ea({type:o=t.hasMany?new r.GraphQLList(o):o,field:t,forceNullable:n,parentIsLocalized:l}),{...e,[d(t.name)]:{type:o}}},tabs:(e,a)=>a.tabs.reduce((e,n)=>{if((0,S.pz)(n)){let s=ee(i,(0,g.x4)(n.name,!0)),p=et(a),c=eo({name:s,config:t,fields:n.fields,graphqlResult:o,parentIsLocalized:l||n.localized,parentName:s});return c?(p&&(c=new r.GraphQLNonNull(c)),{...e,[n.name]:{type:c}}):e}return{...e,...n.fields.reduce((e,t)=>{let a=s[t.type];return a?a(e,t):e},e)}},e),text:(e,t)=>({...e,[d(t.name)]:{type:ea({type:!0===t.hasMany?new r.GraphQLList(r.GraphQLString):r.GraphQLString,field:t,forceNullable:n,parentIsLocalized:l})}}),textarea:(e,t)=>({...e,[d(t.name)]:{type:ea({type:r.GraphQLString,field:t,forceNullable:n,parentIsLocalized:l})}}),upload:(e,a)=>{let n;let{relationTo:l}=a;if(Array.isArray(l)){let e=`${ee(i,(0,g.x4)(a.name,!0))}RelationshipInput`;n=new r.GraphQLInputObjectType({name:e,fields:{relationTo:{type:new r.GraphQLEnumType({name:`${e}RelationTo`,values:l.reduce((e,t)=>({...e,[d(t)]:{value:t}}),{})})},value:{type:v}}})}else n=en(t.db.defaultIDType,o.collections[l].config);return{...e,[d(a.name)]:{type:a.hasMany?new r.GraphQLList(n):n}}}},p=d(e),c=a.reduce((e,t)=>{let a=s[t.type];return"function"!=typeof a||0===Object.keys(a(e,t)).length?e:{...e,...a(e,t)}},{});return 0===Object.keys(c).length?null:new r.GraphQLInputObjectType({name:`mutation${p}Input`,fields:c})}function el(e,t){return r.versionInfo.major>=17?new r.GraphQLError(e,t):new r.GraphQLError(e,null==t?void 0:t.nodes,null==t?void 0:t.source,null==t?void 0:t.positions,null==t?void 0:t.path,null==t?void 0:t.originalError,null==t?void 0:t.extensions)}let ei=e=>new Date(e),es=e=>e%4==0&&e%100!=0||e%400==0,ep=e=>(e=null==e?void 0:e.toUpperCase(),/^([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\.\d{1,})?(([Z])|([+|-]([01][0-9]|2[0-3]):[0-5][0-9]))$/.test(e)),ec=e=>{if(!/^(\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01]))$/.test(e))return!1;let t=Number(e.substr(0,4)),a=Number(e.substr(5,2)),r=Number(e.substr(8,2));switch(a){case 2:if(es(t)&&r>29||!es(t)&&r>28)return!1;break;case 4:case 6:case 9:case 11:if(r>30)return!1}return!0},eu=e=>{if(e=null==e?void 0:e.toUpperCase(),!/^(\d{4}-(0[1-9]|1[012])-(0[1-9]|[12][0-9]|3[01])T([01][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9]|60))(\.\d{1,})?(([Z])|([+|-]([01][0-9]|2[0-3]):[0-5][0-9]))$/.test(e))return!1;let t=Date.parse(e);if(t!=t)return!1;let a=e.indexOf("T"),r=e.substr(0,a),n=e.substr(a+1);return ec(r)&&ep(n)},ey=e=>{let t=e.getTime();return t==t},ed=new r.GraphQLScalarType({name:"DateTime",description:"A date-time string at UTC, such as 2007-12-03T10:15:30Z, compliant with the `date-time` format outlined in section 5.6 of the RFC 3339 profile of the ISO 8601 standard for representation of dates and times using the Gregorian calendar.",serialize(e){if(e instanceof Date){if(ey(e))return e;throw el("DateTime cannot represent an invalid Date instance")}if("string"==typeof e){if(eu(e))return ei(e);throw el(`DateTime cannot represent an invalid date-time-string ${e}.`)}if("number"==typeof e)try{return new Date(e)}catch(t){throw el("DateTime cannot represent an invalid Unix timestamp "+e)}else throw el("DateTime cannot be serialized from a non string, non numeric or non Date type "+JSON.stringify(e))},parseValue(e){if(e instanceof Date){if(ey(e))return e;throw el("DateTime cannot represent an invalid Date instance")}if("string"==typeof e){if(eu(e))return ei(e);throw el(`DateTime cannot represent an invalid date-time-string ${e}.`)}throw el(`DateTime cannot represent non string or Date type ${JSON.stringify(e)}`)},parseLiteral(e){if(e.kind!==r.Kind.STRING)throw el(`DateTime cannot represent non string or Date type ${"value"in e&&e.value}`,{nodes:e});let{value:t}=e;if(eu(t))return ei(t);throw el(`DateTime cannot represent an invalid date-time-string ${String(t)}.`,{nodes:e})},extensions:{codegenScalarType:"Date | string",jsonSchema:{type:"string",format:"date-time"}}}),eh=(e,t)=>{if("string"!=typeof e)throw el(`Value is not string: ${e}`,{nodes:t});if(!/^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(e))throw el(`Value is not a valid email address: ${e}`,{nodes:t});return e},ef="https://html.spec.whatwg.org/multipage/input.html#valid-e-mail-address",em=new r.GraphQLScalarType({name:"EmailAddress",description:"A field whose value conforms to the standard internet email address format as specified in HTML Spec: https://html.spec.whatwg.org/multipage/input.html#valid-e-mail-address.",serialize:eh,parseValue:eh,parseLiteral(e){if(e.kind!==r.Kind.STRING)throw el(`Can only validate strings as email addresses but got a: ${e.kind}`,{nodes:e});return eh(e.value,e)},specifiedByURL:ef,specifiedByUrl:ef,extensions:{codegenScalarType:"string",jsonSchema:{type:"string",format:"email"}}});var eg=a(11533),eL=a(83887),eb=a(44478);let eQ=e=>e.options.reduce((e,t)=>"object"==typeof t?{...e,[d(t.value)]:{value:t.value}}:{...e,[d(t)]:{value:t}},{}),ew=({field:e,forceNullable:t,parentIsLocalized:a})=>{let r=e.access&&e.access.read,n=e.admin&&e.admin.condition;return!(t&&(0,S.Z7)(e)&&"required"in e&&e.required&&(!e.localized||a)&&!n&&!r)};function ex({field:e,...t}){return"name"in e&&d(e.name)!==e.name?{...t,resolve:t=>t[e.name]}:t}let ev={array:({config:e,field:t,forceNullable:a,graphqlResult:n,objectTypeConfig:o,parentIsLocalized:l,parentName:i})=>{let s=t?.interfaceName||ee(i,(0,g.x4)(t.name,!0));if(!n.types.arrayTypes[s]){let r=eT({name:s,config:e,fields:t.fields,forceNullable:ew({field:t,forceNullable:a,parentIsLocalized:l}),graphqlResult:n,parentIsLocalized:t.localized||l,parentName:s});Object.keys(r.getFields()).length&&(n.types.arrayTypes[s]=r)}if(!n.types.arrayTypes[s])return o;let p=new r.GraphQLList(new r.GraphQLNonNull(n.types.arrayTypes[s]));return{...o,[d(t.name)]:ex({type:ea({type:p,field:t,parentIsLocalized:l}),field:t})}},blocks:({config:e,field:t,forceNullable:a,graphqlResult:n,objectTypeConfig:o,parentIsLocalized:l,parentName:i})=>{let s=(t.blockReferences??t.blocks).reduce((t,r)=>{let o="string"==typeof r?r:r.slug;if(!n.types.blockTypes[o]){let t="string"==typeof r?e.blocks.find(e=>e.slug===r):r,o=t?.interfaceName||t?.graphQL?.singularName||(0,g.x4)(t.slug,!0),i=eT({name:o,config:e,fields:[...t.fields,{name:"blockType",type:"text"}],forceNullable:a,graphqlResult:n,parentIsLocalized:l,parentName:o});Object.keys(i.getFields()).length&&(n.types.blockTypes[t.slug]=i)}return n.types.blockTypes[o]&&t.push(n.types.blockTypes[o]),t},[]);if(0===s.length)return o;let p=ee(i,(0,g.x4)(t.name,!0)),c=new r.GraphQLList(new r.GraphQLNonNull(new r.GraphQLUnionType({name:p,resolveType:e=>n.types.blockTypes[e.blockType].name,types:s})));return{...o,[d(t.name)]:ex({type:ea({type:c,field:t,parentIsLocalized:l}),field:t})}},checkbox:({field:e,forceNullable:t,objectTypeConfig:a,parentIsLocalized:n})=>({...a,[d(e.name)]:ex({type:ea({type:r.GraphQLBoolean,field:e,forceNullable:t,parentIsLocalized:n}),field:e})}),code:({field:e,forceNullable:t,objectTypeConfig:a,parentIsLocalized:n})=>({...a,[d(e.name)]:ex({type:ea({type:r.GraphQLString,field:e,forceNullable:t,parentIsLocalized:n}),field:e})}),collapsible:({config:e,field:t,forceNullable:a,graphqlResult:r,newlyCreatedBlockType:n,objectTypeConfig:o,parentIsLocalized:l,parentName:i})=>t.fields.reduce((t,o)=>{let s=ev[o.type];return s?s({config:e,field:o,forceNullable:a,graphqlResult:r,newlyCreatedBlockType:n,objectTypeConfig:t,parentIsLocalized:l,parentName:i}):t},o),date:({field:e,forceNullable:t,objectTypeConfig:a,parentIsLocalized:r})=>({...a,[d(e.name)]:ex({type:ea({type:ed,field:e,forceNullable:t,parentIsLocalized:r}),field:e})}),email:({field:e,forceNullable:t,objectTypeConfig:a,parentIsLocalized:r})=>({...a,[d(e.name)]:ex({type:ea({type:em,field:e,forceNullable:t,parentIsLocalized:r}),field:e})}),group:({config:e,field:t,forceNullable:a,graphqlResult:r,newlyCreatedBlockType:n,objectTypeConfig:o,parentIsLocalized:l,parentName:i})=>{if(!(0,S.Z7)(t))return t.fields.reduce((t,o)=>{let s=ev[o.type];return s?s({config:e,field:o,forceNullable:a,graphqlResult:r,newlyCreatedBlockType:n,objectTypeConfig:t,parentIsLocalized:l,parentName:i}):t},o);{let n=t?.interfaceName||ee(i,(0,g.x4)(t.name,!0));if(!r.types.groupTypes[n]){let o=eT({name:n,config:e,fields:t.fields,forceNullable:ew({field:t,forceNullable:a,parentIsLocalized:l}),graphqlResult:r,parentIsLocalized:t.localized||l,parentName:n});Object.keys(o.getFields()).length&&(r.types.groupTypes[n]=o)}return r.types.groupTypes[n]?{...o,[d(t.name)]:{type:r.types.groupTypes[n],resolve:(e,a,r)=>({...e[t.name],_id:e._id??e.id})}}:o}},join:({collectionSlug:e,field:t,graphqlResult:a,objectTypeConfig:n,parentName:o})=>{let l=ee(o,(0,g.x4)(t.name,!0)),i={type:new r.GraphQLObjectType({name:l,fields:{docs:{type:new r.GraphQLNonNull(Array.isArray(t.collection)?v:new r.GraphQLList(new r.GraphQLNonNull(a.collections[t.collection].graphQL.type)))},hasNextPage:{type:new r.GraphQLNonNull(r.GraphQLBoolean)}}}),args:{limit:{type:r.GraphQLInt},page:{type:r.GraphQLInt},sort:{type:r.GraphQLString},where:{type:Array.isArray(t.collection)?v:a.collections[t.collection].graphQL.whereInputType}},extensions:{complexity:"number"==typeof t?.graphQL?.complexity?t.graphQL.complexity:10},async resolve(a,r,n){let{collection:o}=t,{limit:l,page:i,sort:s,where:p}=r,{req:c}=n,u=!!(r.draft??n.req.query?.draft),y=t.targetField,d=(0,eg.m)(p,Array.isArray(y.relationTo)?{[t.on]:{equals:{relationTo:e,value:a._id??a.id}}}:{[t.on]:{equals:a._id??a.id}});if(Array.isArray(o))throw Error("GraphQL with array of join.field.collection is not implemented");let{docs:h}=await c.payload.find({collection:o,depth:0,draft:u,fallbackLocale:c.fallbackLocale,limit:"number"==typeof l&&l>0?l+1:0,locale:c.locale,overrideAccess:!1,page:i,pagination:!1,req:c,sort:s,where:d}),f=!1;return"number"==typeof l&&0!==l&&l<h.length&&(f=!0),{docs:f?h.slice(0,-1):h,hasNextPage:0!==l&&l<h.length}}};return{...n,[d(t.name)]:i}},json:({field:e,forceNullable:t,objectTypeConfig:a,parentIsLocalized:r})=>({...a,[d(e.name)]:ex({type:ea({type:v,field:e,forceNullable:t,parentIsLocalized:r}),field:e})}),number:({field:e,forceNullable:t,objectTypeConfig:a,parentIsLocalized:n})=>{let o=e?.name==="id"?r.GraphQLInt:r.GraphQLFloat;return{...a,[d(e.name)]:ex({type:ea({type:e?.hasMany===!0?new r.GraphQLList(new r.GraphQLNonNull(o)):o,field:e,forceNullable:t,parentIsLocalized:n}),field:e})}},point:({field:e,forceNullable:t,objectTypeConfig:a,parentIsLocalized:n})=>({...a,[d(e.name)]:ex({type:ea({type:new r.GraphQLList(new r.GraphQLNonNull(r.GraphQLFloat)),field:e,forceNullable:t,parentIsLocalized:n}),field:e})}),radio:({field:e,forceNullable:t,objectTypeConfig:a,parentIsLocalized:n,parentName:o})=>({...a,[d(e.name)]:ex({type:ea({type:new r.GraphQLEnumType({name:ee(o,e.name),values:eQ(e)}),field:e,forceNullable:t,parentIsLocalized:n}),field:e})}),relationship:({config:e,field:t,forceNullable:a,graphqlResult:n,newlyCreatedBlockType:o,objectTypeConfig:l,parentIsLocalized:i,parentName:s})=>{let p;let{relationTo:c}=t,u=Array.isArray(c),y=t.hasMany,h=ee(s,(0,g.x4)(t.name,!0)),f=null,m=e.collections.filter(e=>!1!==e.graphQL);if(Array.isArray(c)){f=new r.GraphQLEnumType({name:`${h}_RelationTo`,values:c.filter(e=>m.some(t=>t.slug===e)).reduce((e,t)=>({...e,[d(t)]:{value:t}}),{})});let e=c.filter(e=>m.some(t=>t.slug===e)).map(e=>n.collections[e]?.graphQL.type);p=new r.GraphQLObjectType({name:`${h}_Relationship`,fields:{relationTo:{type:f},value:{type:new r.GraphQLUnionType({name:h,resolveType:e=>n.collections[e.collection].graphQL.type.name,types:e})}}})}else({type:p}=n.collections[c].graphQL);p=p||o;let L={};(Array.isArray(c)?c:[c]).filter(e=>m.some(t=>t.slug===e)).some(e=>n.collections[e].config.versions?.drafts)&&(L.draft={type:r.GraphQLBoolean}),e.localization&&(L.locale={type:n.types.localeInputType},L.fallbackLocale={type:n.types.fallbackLocaleInputType});let b={type:ea({type:y?new r.GraphQLList(new r.GraphQLNonNull(p)):p,field:t,forceNullable:a,parentIsLocalized:i}),args:L,extensions:{complexity:"number"==typeof t?.graphQL?.complexity?t.graphQL.complexity:10},async resolve(e,a,r){let n=e[t.name],o=a.locale||r.req.locale,l=a.fallbackLocale||r.req.fallbackLocale,i=t.relationTo,s=!!(a.draft??r.req.query?.draft);if(y){let e=[],a=[],i=async(a,n)=>{let i=a,p=t.relationTo;if(u?m.some(e=>p.includes(e.slug)):m.some(e=>p===e.slug)){u&&(p=a.relationTo,i=a.value);let t=await r.req.payloadDataLoader.load((0,eL.h)({collectionSlug:p,currentDepth:0,depth:0,docID:i,draft:s,fallbackLocale:l,locale:o,overrideAccess:!1,showHiddenFields:!1,transactionID:r.req.transactionID}));t&&(u?e[n]={relationTo:p,value:{...t,collection:p}}:e[n]=t)}};return n&&n.forEach((e,t)=>{a.push(i(e,t))}),await Promise.all(a),e}let p=n;if(u&&n&&(p=n.value,i=n.relationTo),p&&m.some(e=>e.slug===i)){let e=await r.req.payloadDataLoader.load((0,eL.h)({collectionSlug:i,currentDepth:0,depth:0,docID:p,draft:s,fallbackLocale:l,locale:o,overrideAccess:!1,showHiddenFields:!1,transactionID:r.req.transactionID}));if(e)return u?{relationTo:i,value:{...e,collection:i}}:e}return null}};return{...l,[d(t.name)]:b}},richText:({config:e,field:t,forceNullable:a,objectTypeConfig:n,parentIsLocalized:o})=>({...n,[d(t.name)]:{type:ea({type:v,field:t,forceNullable:a,parentIsLocalized:o}),args:{depth:{type:r.GraphQLInt}},async resolve(a,r,n){let l=e.defaultDepth;if(void 0!==r.depth&&(l=r.depth),!t?.editor)throw new eb.X(t);if("function"==typeof t?.editor)throw Error("Attempted to access unsanitized rich text editor.");let i=t?.editor;if(i?.graphQLPopulationPromises){let e=[],s=[],p=t?.maxDepth!==void 0&&t?.maxDepth<l?t?.maxDepth:l;i?.graphQLPopulationPromises({context:n,depth:p,draft:r.draft,field:t,fieldPromises:e,findMany:!1,flattenLocales:!1,overrideAccess:!1,parentIsLocalized:o,populationPromises:s,req:n.req,showHiddenFields:!1,siblingDoc:a}),await Promise.all(e),await Promise.all(s)}return a[t.name]}}}),row:({field:e,objectTypeConfig:t,...a})=>e.fields.reduce((e,t)=>{let r=ev[t.type];return r?r({field:t,objectTypeConfig:e,...a}):e},t),select:({field:e,forceNullable:t,objectTypeConfig:a,parentIsLocalized:n,parentName:o})=>{let l=ee(o,e.name),i=new r.GraphQLEnumType({name:l,values:eQ(e)});return i=ea({type:i=e.hasMany?new r.GraphQLList(new r.GraphQLNonNull(i)):i,field:e,forceNullable:t,parentIsLocalized:n}),{...a,[d(e.name)]:ex({type:i,field:e})}},tabs:({config:e,field:t,forceNullable:a,graphqlResult:r,newlyCreatedBlockType:n,objectTypeConfig:o,parentIsLocalized:l,parentName:i})=>t.tabs.reduce((t,o)=>{if((0,S.pz)(o)){let n=o?.interfaceName||ee(i,(0,g.x4)(o.name,!0));if(!r.types.groupTypes[n]){let t=eT({name:n,config:e,fields:o.fields,forceNullable:a,graphqlResult:r,parentIsLocalized:o.localized||l,parentName:n});Object.keys(t.getFields()).length&&(r.types.groupTypes[n]=t)}return r.types.groupTypes[n]?{...t,[o.name]:{type:r.types.groupTypes[n],resolve:(e,t,a)=>({...e[o.name],_id:e._id??e.id})}}:t}return{...t,...o.fields.reduce((t,o)=>{let s=ev[o.type];return s?s({config:e,field:o,forceNullable:a,graphqlResult:r,newlyCreatedBlockType:n,objectTypeConfig:t,parentIsLocalized:l,parentName:i}):t},t)}},o),text:({field:e,forceNullable:t,objectTypeConfig:a,parentIsLocalized:n})=>({...a,[d(e.name)]:ex({type:ea({type:!0===e.hasMany?new r.GraphQLList(new r.GraphQLNonNull(r.GraphQLString)):r.GraphQLString,field:e,forceNullable:t,parentIsLocalized:n}),field:e})}),textarea:({field:e,forceNullable:t,objectTypeConfig:a,parentIsLocalized:n})=>({...a,[d(e.name)]:ex({type:ea({type:r.GraphQLString,field:e,forceNullable:t,parentIsLocalized:n}),field:e})}),upload:({config:e,field:t,forceNullable:a,graphqlResult:n,newlyCreatedBlockType:o,objectTypeConfig:l,parentIsLocalized:i,parentName:s})=>{let p;let{relationTo:c}=t,u=Array.isArray(c),y=t.hasMany,h=ee(s,(0,g.x4)(t.name,!0)),f=null;if(Array.isArray(c)){f=new r.GraphQLEnumType({name:`${h}_RelationTo`,values:c.reduce((e,t)=>({...e,[d(t)]:{value:t}}),{})});let e=c.map(e=>n.collections[e].graphQL.type);p=new r.GraphQLObjectType({name:`${h}_Relationship`,fields:{relationTo:{type:f},value:{type:new r.GraphQLUnionType({name:h,resolveType:e=>n.collections[e.collection].graphQL.type.name,types:e})}}})}else({type:p}=n.collections[c].graphQL);p=p||o;let m={};(Array.isArray(c)?c:[c]).some(e=>n.collections[e].config.versions?.drafts)&&(m.draft={type:r.GraphQLBoolean}),e.localization&&(m.locale={type:n.types.localeInputType},m.fallbackLocale={type:n.types.fallbackLocaleInputType});let L={type:ea({type:y?new r.GraphQLList(new r.GraphQLNonNull(p)):p,field:t,forceNullable:a,parentIsLocalized:i}),args:m,extensions:{complexity:"number"==typeof t?.graphQL?.complexity?t.graphQL.complexity:10},async resolve(e,a,r){let n=e[t.name],o=a.locale||r.req.locale,l=a.fallbackLocale||r.req.fallbackLocale,i=t.relationTo,s=!!(a.draft??r.req.query?.draft);if(y){let e=[],a=[],i=async(a,n)=>{let i=a,p=t.relationTo;u&&(p=a.relationTo,i=a.value);let c=await r.req.payloadDataLoader.load((0,eL.h)({collectionSlug:p,currentDepth:0,depth:0,docID:i,draft:s,fallbackLocale:l,locale:o,overrideAccess:!1,showHiddenFields:!1,transactionID:r.req.transactionID}));c&&(u?e[n]={relationTo:p,value:{...c,collection:p}}:e[n]=c)};return n&&n.forEach((e,t)=>{a.push(i(e,t))}),await Promise.all(a),e}let p=n;if(u&&n&&(p=n.value,i=n.relationTo),p){let e=await r.req.payloadDataLoader.load((0,eL.h)({collectionSlug:i,currentDepth:0,depth:0,docID:p,draft:s,fallbackLocale:l,locale:o,overrideAccess:!1,showHiddenFields:!1,transactionID:r.req.transactionID}));if(e)return u?{relationTo:i,value:{...e,collection:i}}:e}return null}};return{...l,[d(t.name)]:L}}};function eT({name:e,baseFields:t={},collectionSlug:a,config:n,fields:o,forceNullable:l,graphqlResult:i,parentIsLocalized:s,parentName:p}){let c=new r.GraphQLObjectType({name:e,fields:()=>o.reduce((e,t)=>{let r=ev[t.type];return"function"!=typeof r?e:{...e,...r({collectionSlug:a,config:n,field:t,forceNullable:l,graphqlResult:i,newlyCreatedBlockType:c,objectTypeConfig:e,parentIsLocalized:s,parentName:p})}},t)});return c}let eq=(e,t)=>new r.GraphQLObjectType({name:e,fields:{docs:{type:new r.GraphQLNonNull(new r.GraphQLList(new r.GraphQLNonNull(t)))},hasNextPage:{type:new r.GraphQLNonNull(r.GraphQLBoolean)},hasPrevPage:{type:new r.GraphQLNonNull(r.GraphQLBoolean)},limit:{type:new r.GraphQLNonNull(r.GraphQLInt)},nextPage:{type:r.GraphQLInt},offset:{type:r.GraphQLInt},page:{type:new r.GraphQLNonNull(r.GraphQLInt)},pagingCounter:{type:new r.GraphQLNonNull(r.GraphQLInt)},prevPage:{type:r.GraphQLInt},totalDocs:{type:new r.GraphQLNonNull(r.GraphQLInt)},totalPages:{type:new r.GraphQLNonNull(r.GraphQLInt)}}}),eG=({field:e,nestedFieldName2:t,parentName:a})=>{let r=((0,S.Z7)(e)?e.name:void 0)||t;return"tabs"===e.type?e.tabs.reduce((e,t)=>(e.push(...eG({field:{...t,type:"name"in t?"group":"row"},nestedFieldName2:r,parentName:a})),e),[]):e.fields.reduce((e,t)=>{if(!(0,S.aO)(t)){if(!(0,S.Z7)(t))return[...e,...eG({field:t,nestedFieldName2:r,parentName:a})];let n=(0,S.Z7)(t)?`${r?`${r}__`:""}${t.name}`:void 0,o=eE({nestedFieldName:r,parentName:a})[t.type];if(o){let a=o({...t,name:n});return Array.isArray(a)?[...e,...a]:[...e,{type:a,key:n}]}}return e},[])},ek={comparison:["greater_than_equal","greater_than","less_than_equal","less_than"],contains:["in","not_in","all"],equality:["equals","not_equals"],geo:["near"],geojson:["within","intersects"],partial:["like","contains"]},eN=new r.GraphQLInputObjectType({name:"GeoJSONObject",fields:{type:{type:r.GraphQLString},coordinates:{type:v}}}),eI={checkbox:{operators:[...ek.equality.map(e=>({name:e,type:r.GraphQLBoolean}))]},code:{operators:[...[...ek.equality,...ek.partial].map(e=>({name:e,type:r.GraphQLString}))]},date:{operators:[...[...ek.equality,...ek.comparison,"like"].map(e=>({name:e,type:ed}))]},email:{operators:[...[...ek.equality,...ek.partial,...ek.contains].map(e=>({name:e,type:em}))]},json:{operators:[...[...ek.equality,...ek.partial,...ek.geojson].map(e=>({name:e,type:v}))]},number:{operators:[...[...ek.equality,...ek.comparison].map(e=>({name:e,type:e=>e?.name==="id"?r.GraphQLInt:r.GraphQLFloat}))]},point:{operators:[...[...ek.equality,...ek.comparison,...ek.geo].map(e=>({name:e,type:new r.GraphQLList(r.GraphQLFloat)})),...ek.geojson.map(e=>({name:e,type:eN}))]},radio:{operators:[...[...ek.equality,...ek.partial].map(e=>({name:e,type:(e,t)=>new r.GraphQLEnumType({name:`${ee(t,e.name)}_Input`,values:e.options.reduce((e,t)=>(0,S.vs)(t)?{...e,[d(t.value)]:{value:t.value}}:{...e,[d(t)]:{value:t}},{})})}))]},relationship:{operators:[...[...ek.equality,...ek.contains].map(e=>({name:e,type:v}))]},richText:{operators:[...[...ek.equality,...ek.partial].map(e=>({name:e,type:v}))]},select:{operators:[...[...ek.equality,...ek.contains].map(e=>({name:e,type:(e,t)=>new r.GraphQLEnumType({name:`${ee(t,e.name)}_Input`,values:e.options.reduce((e,t)=>(0,S.vs)(t)?{...e,[d(t.value)]:{value:t.value}}:{...e,[d(t)]:{value:t}},{})})}))]},text:{operators:[...[...ek.equality,...ek.partial,...ek.contains].map(e=>({name:e,type:r.GraphQLString}))]},textarea:{operators:[...[...ek.equality,...ek.partial].map(e=>({name:e,type:r.GraphQLString}))]},upload:{operators:[...[...ek.equality,...ek.contains].map(e=>({name:e,type:v}))]}},eS=["in","not_in","all"],eA={},eD=(e,t)=>{if(!eI?.[e.type])throw Error(`Error: ${e.type} has no defaults configured.`);let a=`${ee(t,e.name)}_operator`,n=[...eI[e.type].operators];return"required"in e&&e.required||n.push({name:"exists",type:n[0].type}),new r.GraphQLInputObjectType({name:a,fields:n.reduce((a,n)=>{let o="function"==typeof n.type?n.type(e,t):n.type;return"function"==typeof n.type&&"name"in o&&(eA[o.name]?o=eA[o.name]:eA[o.name]=o),eS.includes(n.name)?o=new r.GraphQLList(o):"exists"===n.name&&(o=r.GraphQLBoolean),{...a,[n.name]:{type:o}}},{})})},eE=({nestedFieldName:e,parentName:t})=>({array:a=>eG({field:a,nestedFieldName2:e,parentName:t}),checkbox:e=>({type:eD(e,t)}),code:e=>({type:eD(e,t)}),collapsible:a=>eG({field:a,nestedFieldName2:e,parentName:t}),date:e=>({type:eD(e,t)}),email:e=>({type:eD(e,t)}),group:a=>eG({field:a,nestedFieldName2:e,parentName:t}),json:e=>({type:eD(e,t)}),number:e=>({type:eD(e,t)}),point:e=>({type:eD(e,t)}),radio:e=>({type:eD(e,t)}),relationship:e=>Array.isArray(e.relationTo)?{type:new r.GraphQLInputObjectType({name:`${ee(t,e.name)}_Relation`,fields:{relationTo:{type:new r.GraphQLEnumType({name:`${ee(t,e.name)}_Relation_RelationTo`,values:e.relationTo.reduce((e,t)=>({...e,[d(t)]:{value:t}}),{})})},value:{type:v}}})}:{type:eD(e,t)},richText:e=>({type:eD(e,t)}),row:a=>eG({field:a,nestedFieldName2:e,parentName:t}),select:e=>({type:eD(e,t)}),tabs:a=>eG({field:a,nestedFieldName2:e,parentName:t}),text:e=>({type:eD(e,t)}),textarea:e=>({type:eD(e,t)}),upload:e=>Array.isArray(e.relationTo)?{type:new r.GraphQLInputObjectType({name:`${ee(t,e.name)}_Relation`,fields:{relationTo:{type:new r.GraphQLEnumType({name:`${ee(t,e.name)}_Relation_RelationTo`,values:e.relationTo.reduce((e,t)=>({...e,[d(t)]:{value:t}}),{})})},value:{type:v}}})}:{type:eD(e,t)}}),eO=({name:e,fields:t,parentName:a})=>{let n=(0,N.L)(t).find(e=>(0,S.Z7)(e)&&"id"===e.name),o=t.reduce((e,t)=>{if(!(0,S.aO)(t)&&!t.hidden){let r=eE({parentName:a})[t.type];if(r){let a=r(t);return(0,S.sd)(t)||"tabs"===t.type?{...e,...a.reduce((e,t)=>({...e,[d(t.key)]:t.type}),{})}:{...e,[d(t.name)]:a}}}return e},{});n||(o.id={type:eD({name:"id",type:"text"},a)});let l=d(e),i={AND:{type:new r.GraphQLList(new r.GraphQLInputObjectType({name:`${l}_where_and`,fields:()=>({...o,...i})}))},OR:{type:new r.GraphQLList(new r.GraphQLInputObjectType({name:`${l}_where_or`,fields:()=>({...o,...i})}))}};return new r.GraphQLInputObjectType({name:`${l}_where`,fields:{...o,...i}})};var ej=a(2019),e$=a(54098),eC=a(87318),eR=a(8795),e_=a(81491),eM=a(19422),ez=a(34334),eP=a(54803);let{singular:eB}=ej;function eF(e){for(let t in e)e[t].resolve&&(e[t].resolve=function(e){return(t,a,r,n)=>e(t,a,{...r,req:(0,c.i)(r.req,"transactionID")},n)}(e[t].resolve));return e}let eV=e=>({Field(t){("__schema"===t.name.value||"__type"===t.name.value)&&e.reportError(new r.GraphQLError("GraphQL introspection is not allowed, but the query contained __schema or __type",{nodes:[t]}))}});function eJ(e){return"object"==typeof e&&null!==e}function eU(e){if(!Array.isArray(e)||"string"!=typeof e[0]&&null!==e[0]||!eJ(e[1]))return!1;let t=e[1];return(!t.status||"number"==typeof t.status)&&(!t.statusText||"string"==typeof t.statusText)&&(!t.headers||!!eJ(t.headers))}async function eZ(e){var t,a;let r=e.method;if("GET"!==r&&"POST"!==r)return[null,{status:405,statusText:"Method Not Allowed",headers:{allow:"GET, POST"}}];let[n,o="charset=utf-8"]=(eH(e,"content-type")||"").replace(/\s/g,"").toLowerCase().split(";"),l={};switch(!0){case"GET"===r:try{let[,r]=e.url.split("?"),n=new URLSearchParams(r);l.operationName=null!==(t=n.get("operationName"))&&void 0!==t?t:void 0,l.query=null!==(a=n.get("query"))&&void 0!==a?a:void 0;let o=n.get("variables");o&&(l.variables=JSON.parse(o));let i=n.get("extensions");i&&(l.extensions=JSON.parse(i))}catch(e){throw Error("Unparsable URL")}break;case"POST"===r&&"application/json"===n&&"charset=utf-8"===o:{let t;if(!e.body)throw Error("Missing body");try{let a="function"==typeof e.body?await e.body():e.body;t="string"==typeof a?JSON.parse(a):a}catch(e){throw Error("Unparsable JSON body")}if(!eJ(t))throw Error("JSON body must be an object");l.operationName=t.operationName,l.query=t.query,l.variables=t.variables,l.extensions=t.extensions;break}default:return[null,{status:415,statusText:"Unsupported Media Type"}]}if(null==l.query)throw Error("Missing query");if("string"!=typeof l.query)throw Error("Invalid query");if(null!=l.variables&&("object"!=typeof l.variables||Array.isArray(l.variables)))throw Error("Invalid variables");if(null!=l.operationName&&"string"!=typeof l.operationName)throw Error("Invalid operationName");if(null!=l.extensions&&("object"!=typeof l.extensions||Array.isArray(l.extensions)))throw Error("Invalid extensions");return l}function eK(e,t,a){if(e instanceof Error&&!eX(e))return[JSON.stringify({errors:[a(e)]},eY),{status:400,statusText:"Bad Request",headers:{"content-type":"application/json; charset=utf-8"}}];let r=eX(e)?[e]:eW(e)?e:null;return r?[JSON.stringify({errors:r.map(a)},eY),Object.assign(Object.assign({},"application/json"===t?{status:200,statusText:"OK"}:{status:400,statusText:"Bad Request"}),{headers:{"content-type":"application/json"===t?"application/json; charset=utf-8":"application/graphql-response+json; charset=utf-8"}})]:[JSON.stringify("errors"in e&&e.errors?Object.assign(Object.assign({},e),{errors:e.errors.map(a)}):e,eY),{status:200,statusText:"OK",headers:{"content-type":"application/json"===t?"application/json; charset=utf-8":"application/graphql-response+json; charset=utf-8"}}]}function eH(e,t){return"function"==typeof e.headers.get?e.headers.get(t):Object(e.headers)[t]}function eW(e){return Array.isArray(e)&&e.length>0&&e.some(eX)}function eX(e){return e instanceof r.GraphQLError}function eY(e,t){return t instanceof Error&&!eX(t)?{message:t.message}:t}var e0=a(12043),e1=a(71131),e4=a(9574),e7=a(28232),e9=a(38879),e3=a(14400),e5=a(69637);let e8=async({err:e,payload:t,req:a})=>{let r=e.originalError.status||e0.h.INTERNAL_SERVER_ERROR,n=e.message;(0,e1.v)({err:e,payload:t}),t.config.debug||r!==e0.h.INTERNAL_SERVER_ERROR||(n="Something went wrong.");let o={extensions:{name:e?.originalError?.name||void 0,data:e&&e.originalError&&e.originalError.data||void 0,stack:t.config.debug?e.stack:void 0,statusCode:r},locations:e.locations,message:n,path:e.path};return await t.config.hooks.afterError?.reduce(async(t,r)=>{await t;let n=await r({context:a.context,error:e,graphqlResult:o,req:a});n&&(o=n.graphqlResult||o)},Promise.resolve()),o},e2=global._payload_graphql;e2||(e2=global._payload_graphql={graphql:null,promise:null});let e6=async e=>{if(e2.graphql)return e2.graphql;if(!e2.promise){let t=await e;e2.promise=new Promise(e=>{let a=function(e){let t={collections:e.collections.reduce((e,t)=>(e[t.slug]={config:t},e),{}),globals:{config:e.globals},Mutation:{name:"Mutation",fields:{}},Query:{name:"Query",fields:{}},types:{arrayTypes:{},blockInputTypes:{},blockTypes:{},groupTypes:{},tabTypes:{}}};if(e.localization&&(t.types.localeInputType=m(e.localization),t.types.fallbackLocaleInputType=f(e.localization)),function({config:e,graphqlResult:t}){Object.keys(t.collections).forEach(a=>{var n;let o,l;let i=t.collections[a],{config:s,config:{fields:p,graphQL:u={},versions:y}}=i;if(!u)return;let h=(0,g.EI)(i.config.slug);(o=u.singularName?(0,g.x4)(u.singularName,!0):h.singular)===(l=u.pluralName?(0,g.x4)(u.pluralName,!0):h.plural)&&(l=`all${o}`),i.graphQL={};let f=(0,N.L)(p).findIndex(e=>(0,S.Z7)(e)&&"id"===e.name)>-1,m=en(e.db.defaultIDType,s),L={},b=[...p];f||(L.id={type:new r.GraphQLNonNull(m)},b.push({name:"id",type:e.db.defaultIDType}));let Q=!!y?.drafts;i.graphQL.type=eT({name:o,baseFields:L,collectionSlug:s.slug,config:e,fields:p,forceNullable:Q,graphqlResult:t,parentName:o}),i.graphQL.paginatedType=eq(l,i.graphQL.type),i.graphQL.whereInputType=eO({name:o,fields:b,parentName:o});let w=[...p];s.auth&&(!s.auth.disableLocalStrategy||"object"==typeof s.auth.disableLocalStrategy&&s.auth.disableLocalStrategy.optionalPassword)&&w.push({name:"password",type:"text",label:"Password",required:!("object"==typeof s.auth.disableLocalStrategy&&s.auth.disableLocalStrategy.optionalPassword)});let x=w;e.db.allowIDOnCreate&&!s.flattenedFields.some(e=>"id"===e.name)&&(x=[...x,{name:"id",type:e.db.defaultIDType}]);let v=eo({name:o,config:e,fields:x,graphqlResult:t,parentIsLocalized:!1,parentName:o});v&&(i.graphQL.mutationInputType=new r.GraphQLNonNull(v));let T=eo({name:`${o}Update`,config:e,fields:w.filter(e=>!((0,S.Z7)(e)&&"id"===e.name)),forceNullable:!0,graphqlResult:t,parentIsLocalized:!1,parentName:`${o}Update`});T&&(i.graphQL.updateMutationInputType=new r.GraphQLNonNull(T));let q="object"!=typeof s.graphQL||!s.graphQL.disableQueries,G="object"!=typeof s.graphQL||!s.graphQL.disableMutations;if(q&&(t.Query.fields[o]={type:i.graphQL.type,args:{id:{type:new r.GraphQLNonNull(m)},draft:{type:r.GraphQLBoolean},...e.localization?{fallbackLocale:{type:t.types.fallbackLocaleInputType},locale:{type:t.types.localeInputType}}:{},trash:{type:r.GraphQLBoolean}},resolve:async function(e,t,a){let{req:r}=a,n=r.locale,o=r.fallbackLocale;r=(0,c.i)(r,"locale"),(r=(0,c.i)(r,"fallbackLocale")).locale=t.locale||n,r.fallbackLocale=t.fallbackLocale||o,r.query||(r.query={});let l=!(t.draft??r.query?.draft==="false")&&(r.query?.draft==="true"||void 0);"boolean"==typeof l&&(r.query.draft=String(l)),a.req=r;let s={id:t.id,collection:i,depth:0,draft:t.draft,req:(0,c.i)(r,"transactionID"),trash:t.trash};return await (0,K.$)(s)}},t.Query.fields[l]={type:eq(l,i.graphQL.type),args:{draft:{type:r.GraphQLBoolean},where:{type:i.graphQL.whereInputType},...e.localization?{fallbackLocale:{type:t.types.fallbackLocaleInputType},locale:{type:t.types.localeInputType}}:{},limit:{type:r.GraphQLInt},page:{type:r.GraphQLInt},pagination:{type:r.GraphQLBoolean},sort:{type:r.GraphQLString},trash:{type:r.GraphQLBoolean}},resolve:async function(e,t,a){let{req:r}=a,n=r.locale,o=r.fallbackLocale;(r=(0,c.i)(r,["locale","fallbackLocale","transactionID"])).locale=t.locale||n,r.fallbackLocale=t.fallbackLocale||o,r.query||(r.query={});let l=!(t.draft??r.query?.draft==="false")&&(r.query?.draft==="true"||void 0);"boolean"==typeof l&&(r.query.draft=String(l)),a.req=r;let s={collection:i,depth:0,draft:t.draft,limit:t.limit,page:t.page,pagination:t.pagination,req:r,sort:t.sort,trash:t.trash,where:t.where};return await (0,Z.L)(s)}},t.Query.fields[`count${l}`]={type:new r.GraphQLObjectType({name:`count${l}`,fields:{totalDocs:{type:r.GraphQLInt}}}),args:{draft:{type:r.GraphQLBoolean},where:{type:i.graphQL.whereInputType},...e.localization?{locale:{type:t.types.localeInputType}}:{}},resolve:async function(e,t,a){let{req:r}=a,n=r.locale,o=r.fallbackLocale;r=(0,c.i)(r,"locale"),(r=(0,c.i)(r,"fallbackLocale")).locale=t.locale||n,r.fallbackLocale=o,a.req=r;let l={collection:i,req:(0,c.i)(r,"transactionID"),where:t.where};return await (0,B.R)(l)}},t.Query.fields[`docAccess${o}`]={type:k({type:"collection",entity:s,scope:"docAccess",typeSuffix:"DocAccess"}),args:{id:{type:new r.GraphQLNonNull(m)}},resolve:async function(e,t,a){return(0,J.O)({id:t.id,collection:i,req:(0,c.i)(a.req,"transactionID")})}}),G&&(t.Mutation.fields[`create${o}`]={type:i.graphQL.type,args:{...v?{data:{type:i.graphQL.mutationInputType}}:{},draft:{type:r.GraphQLBoolean},...e.localization?{locale:{type:t.types.localeInputType}}:{}},resolve:async function(e,t,a){return t.locale&&(a.req.locale=t.locale),await (0,F.k)({collection:i,data:t.data,depth:0,draft:t.draft,req:(0,c.i)(a.req,"transactionID")})}},t.Mutation.fields[`update${o}`]={type:i.graphQL.type,args:{id:{type:new r.GraphQLNonNull(m)},autosave:{type:r.GraphQLBoolean},...T?{data:{type:i.graphQL.updateMutationInputType}}:{},draft:{type:r.GraphQLBoolean},...e.localization?{locale:{type:t.types.localeInputType}}:{},trash:{type:r.GraphQLBoolean}},resolve:async function(e,t,a){let{req:r}=a,n=r.locale,o=r.fallbackLocale;r=(0,c.i)(r,"locale"),(r=(0,c.i)(r,"fallbackLocale")).locale=t.locale||n,r.fallbackLocale=t.fallbackLocale||o,r.query||(r.query={});let l=!(t.draft??r.query?.draft==="false")&&(r.query?.draft==="true"||void 0);"boolean"==typeof l&&(r.query.draft=String(l)),a.req=r;let s={id:t.id,autosave:t.autosave,collection:i,data:t.data,depth:0,draft:t.draft,req:(0,c.i)(r,"transactionID"),trash:t.trash};return await (0,Y.Z)(s)}},t.Mutation.fields[`delete${o}`]={type:i.graphQL.type,args:{id:{type:new r.GraphQLNonNull(m)},trash:{type:r.GraphQLBoolean}},resolve:async function(e,t,a){let{req:r}=a,n=r.locale,o=r.fallbackLocale;r=(0,c.i)(r,"locale"),(r=(0,c.i)(r,"fallbackLocale")).locale=t.locale||n,r.fallbackLocale=t.fallbackLocale||o,r.query||(r.query={});let l=!(t.draft??r.query?.draft==="false")&&(r.query?.draft==="true"||void 0);"boolean"==typeof l&&(r.query.draft=String(l)),a.req=r;let s={id:t.id,collection:i,depth:0,req:(0,c.i)(r,"transactionID"),trash:t.trash};return await (0,V.l)(s)}},!0!==s.disableDuplicate&&(t.Mutation.fields[`duplicate${o}`]={type:i.graphQL.type,args:{id:{type:new r.GraphQLNonNull(m)},...v?{data:{type:i.graphQL.mutationInputType}}:{}},resolve:async function(e,t,a){let{req:r}=a,n=r.locale,o=r.fallbackLocale;return r.locale=t.locale||n,r.fallbackLocale=t.fallbackLocale||o,a.req=r,await (0,U.h)({id:t.id,collection:i,data:t.data,depth:0,draft:t.draft,req:(0,c.i)(r,"transactionID")})}})),s.versions){let a="text"===e.db.defaultIDType?r.GraphQLString:r.GraphQLInt,n=[...(0,I.c)(e,s),{name:"id",type:e.db.defaultIDType},{name:"createdAt",type:"date",label:({t:e})=>e("general:createdAt")},{name:"updatedAt",type:"date",label:({t:e})=>e("general:updatedAt")}];i.graphQL.versionType=eT({name:`${o}Version`,collectionSlug:s.slug,config:e,fields:n,forceNullable:Q,graphqlResult:t,parentName:`${o}Version`}),q&&(t.Query.fields[`version${d(o)}`]={type:i.graphQL.versionType,args:{id:{type:a},...e.localization?{fallbackLocale:{type:t.types.fallbackLocaleInputType},locale:{type:t.types.localeInputType}}:{},trash:{type:r.GraphQLBoolean}},resolve:async function(e,t,a){let{req:r}=a,n=r.locale,o=r.fallbackLocale;r=(0,c.i)(r,"locale"),(r=(0,c.i)(r,"fallbackLocale")).locale=t.locale||n,r.fallbackLocale=t.fallbackLocale||o,a.req=r;let l={id:t.id,collection:i,depth:0,req:(0,c.i)(r,"transactionID"),trash:t.trash};return await (0,H.L)(l)}},t.Query.fields[`versions${l}`]={type:eq(`versions${d(l)}`,i.graphQL.versionType),args:{where:{type:eO({name:`versions${o}`,fields:n,parentName:`versions${o}`})},...e.localization?{fallbackLocale:{type:t.types.fallbackLocaleInputType},locale:{type:t.types.localeInputType}}:{},limit:{type:r.GraphQLInt},page:{type:r.GraphQLInt},pagination:{type:r.GraphQLBoolean},sort:{type:r.GraphQLString},trash:{type:r.GraphQLBoolean}},resolve:async function(e,t,a){let{req:r}=a,n=r.locale,o=r.fallbackLocale;r=(0,c.i)(r,"locale"),(r=(0,c.i)(r,"fallbackLocale")).locale=t.locale||n,r.fallbackLocale=t.fallbackLocale||o,r.query||(r.query={});let l=!(t.draft??r.query?.draft==="false")&&(r.query?.draft==="true"||void 0);"boolean"==typeof l&&(r.query.draft=String(l)),a.req=r;let s={collection:i,depth:0,limit:t.limit,page:t.page,pagination:t.pagination,req:(0,c.i)(r,"transactionID"),sort:t.sort,trash:t.trash,where:t.where};return await (0,W.s)(s)}}),G&&(t.Mutation.fields[`restoreVersion${d(o)}`]={type:i.graphQL.type,args:{id:{type:a},draft:{type:r.GraphQLBoolean}},resolve:async function(e,t,a){let r={id:t.id,collection:i,depth:0,draft:t.draft,req:(0,c.i)(a.req,"transactionID")};return await (0,X.c)(r)}})}if(s.auth){let l=s.auth.disableLocalStrategy||s.auth.loginWithUsername&&!s.auth.loginWithUsername.allowEmailLogin&&!s.auth.loginWithUsername.requireEmail?[]:[{name:"email",type:"email",required:!0}];if(i.graphQL.JWT=eT({name:d(`${a}JWT`),config:e,fields:[...s.fields.filter(e=>(0,S.Z7)(e)&&e.saveToJWT),...l,{name:"collection",type:"text",required:!0}],graphqlResult:t,parentName:d(`${a}JWT`)}),q&&(t.Query.fields[`me${o}`]={type:new r.GraphQLObjectType({name:d(`${a}Me`),fields:{collection:{type:r.GraphQLString},exp:{type:r.GraphQLInt},strategy:{type:r.GraphQLString},token:{type:r.GraphQLString},user:{type:i.graphQL.type}}}),resolve:async function(e,t,a){let r={collection:i,currentToken:(0,C.p)(a.req),depth:0,req:(0,c.i)(a.req,"transactionID")},n=await (0,R.M)(r);return i.config.auth.removeTokenFromResponses&&delete n.token,n}},t.Query.fields[`initialized${o}`]={type:r.GraphQLBoolean,resolve:(n=i.config.slug,async function(e,t,a){let r={collection:n,req:(0,c.i)(a.req,"transactionID")};return(0,E.M)(r)})}),G&&(t.Mutation.fields[`refreshToken${o}`]={type:new r.GraphQLObjectType({name:d(`${a}Refreshed${o}`),fields:{exp:{type:r.GraphQLInt},refreshedToken:{type:r.GraphQLString},strategy:{type:r.GraphQLString},user:{type:i.graphQL.JWT}}}),resolve:async function(e,t,a){let r={collection:i,depth:0,req:(0,c.i)(a.req,"transactionID")},n=await (0,_.r)(r),o=(0,j.IS)({collectionAuthConfig:i.config.auth,cookiePrefix:a.req.payload.config.cookiePrefix,token:n.refreshedToken});return a.headers["Set-Cookie"]=o,i.config.auth.removeTokenFromResponses&&delete n.refreshedToken,n}},t.Mutation.fields[`logout${o}`]={type:r.GraphQLString,args:{allSessions:{type:r.GraphQLBoolean}},resolve:async function(e,t,a){let r={allSessions:t.allSessions,collection:i,req:(0,c.i)(a.req,"transactionID")},n=await (0,$.u)(r),o=(0,j.DN)({collectionAuthConfig:i.config.auth,config:a.req.payload.config,cookiePrefix:a.req.payload.config.cookiePrefix});return a.headers["Set-Cookie"]=o,n}},!s.auth.disableLocalStrategy)){let e={},{canLoginWithEmail:n,canLoginWithUsername:l}=(0,A.Y)(s.auth.loginWithUsername);n&&(e.email={type:new r.GraphQLNonNull(r.GraphQLString)}),l&&(e.username={type:new r.GraphQLNonNull(r.GraphQLString)}),s.auth.maxLoginAttempts>0&&(t.Mutation.fields[`unlock${o}`]={type:new r.GraphQLNonNull(r.GraphQLBoolean),args:e,resolve:async function(e,t,a){let r={collection:i,data:{email:t.email,username:t.username},req:(0,c.i)(a.req,"transactionID")};return await (0,z.k)(r)}}),t.Mutation.fields[`login${o}`]={type:new r.GraphQLObjectType({name:d(`${a}LoginResult`),fields:{exp:{type:r.GraphQLInt},token:{type:r.GraphQLString},user:{type:i.graphQL.type}}}),args:{...e,password:{type:r.GraphQLString}},resolve:async function(e,t,a){let r={collection:i,data:{email:t.email,password:t.password,username:t.username},depth:0,req:(0,c.i)(a.req,"transactionID")},n=await (0,O.k)(r),o=(0,j.IS)({collectionAuthConfig:i.config.auth,cookiePrefix:a.req.payload.config.cookiePrefix,token:n.token});return a.headers["Set-Cookie"]=o,i.config.auth.removeTokenFromResponses&&delete n.token,n}},t.Mutation.fields[`forgotPassword${o}`]={type:new r.GraphQLNonNull(r.GraphQLBoolean),args:{disableEmail:{type:r.GraphQLBoolean},expiration:{type:r.GraphQLInt},...e},resolve:async function(e,t,a){let r={collection:i,data:{email:t.email,username:t.username},disableEmail:t.disableEmail,expiration:t.expiration,req:(0,c.i)(a.req,"transactionID")};return await (0,D.g)(r),!0}},t.Mutation.fields[`resetPassword${o}`]={type:new r.GraphQLObjectType({name:d(`${a}ResetPassword`),fields:{token:{type:r.GraphQLString},user:{type:i.graphQL.type}}}),args:{password:{type:r.GraphQLString},token:{type:r.GraphQLString}},resolve:async function(e,t,a){t.locale&&(a.req.locale=t.locale),t.fallbackLocale&&(a.req.fallbackLocale=t.fallbackLocale);let r={api:"GraphQL",collection:i,data:t,depth:0,req:(0,c.i)(a.req,"transactionID")},n=await (0,M.q)(r),o=(0,j.IS)({collectionAuthConfig:i.config.auth,cookiePrefix:a.req.payload.config.cookiePrefix,token:n.token});return a.headers["Set-Cookie"]=o,i.config.auth.removeTokenFromResponses&&delete n.token,n}},t.Mutation.fields[`verifyEmail${o}`]={type:r.GraphQLBoolean,args:{token:{type:r.GraphQLString}},resolve:async function(e,t,a){t.locale&&(a.req.locale=t.locale),t.fallbackLocale&&(a.req.fallbackLocale=t.fallbackLocale);let r={api:"GraphQL",collection:i,req:(0,c.i)(a.req,"transactionID"),token:t.token};return await (0,P.p)(r)}}}}})}({config:e,graphqlResult:t}),function({config:e,graphqlResult:t}){Object.keys(t.globals.config).forEach(a=>{let n=t.globals.config[a],{fields:o,graphQL:l,versions:i}=n;if(!1===l)return;let s=l?.name?l.name:eB((0,g.x4)(n.slug,!0)),p=!!i?.drafts;t.globals.graphQL||(t.globals.graphQL={});let u=eo({name:s,config:e,fields:o,graphqlResult:t,parentIsLocalized:!1,parentName:s});t.globals.graphQL[a]={type:eT({name:s,config:e,fields:o,forceNullable:p,graphqlResult:t,parentName:s}),mutationInputType:u?new r.GraphQLNonNull(u):null};let y="object"!=typeof n.graphQL||!n.graphQL.disableQueries,h="object"!=typeof n.graphQL||!n.graphQL.disableMutations;if(y&&(t.Query.fields[s]={type:t.globals.graphQL[a].type,args:{draft:{type:r.GraphQLBoolean},...e.localization?{fallbackLocale:{type:t.types.fallbackLocaleInputType},locale:{type:t.types.localeInputType}}:{}},resolve:async function(e,t,a){t.locale&&(a.req.locale=t.locale),t.fallbackLocale&&(a.req.fallbackLocale=t.fallbackLocale);let{slug:r}=n,o={slug:r,depth:0,draft:t.draft,globalConfig:n,req:(0,c.i)(a.req,"transactionID")};return await (0,eR.j)(o)}},t.Query.fields[`docAccess${s}`]={type:k({type:"global",entity:n,scope:"docAccess",typeSuffix:"DocAccess"}),resolve:async function(e,t){return(0,eC.O)({globalConfig:n,req:(0,c.i)(t.req,"transactionID")})}}),h&&(t.Mutation.fields[`update${s}`]={type:t.globals.graphQL[a].type,args:{...u?{data:{type:t.globals.graphQL[a].mutationInputType}}:{},draft:{type:r.GraphQLBoolean},...e.localization?{locale:{type:t.types.localeInputType}}:{}},resolve:async function(e,t,a){t.locale&&(a.req.locale=t.locale),t.fallbackLocale&&(a.req.fallbackLocale=t.fallbackLocale);let{slug:r}=n,o={slug:r,data:t.data,depth:0,draft:t.draft,globalConfig:n,req:(0,c.i)(a.req,"transactionID")};return await (0,eP.L)(o)}}),n.versions){let o="number"===e.db.defaultIDType?r.GraphQLInt:r.GraphQLString,l=[...(0,e$.p)(e,n),{name:"id",type:e.db.defaultIDType},{name:"createdAt",type:"date",label:"Created At"},{name:"updatedAt",type:"date",label:"Updated At"}];t.globals.graphQL[a].versionType=eT({name:`${s}Version`,config:e,fields:l,forceNullable:p,graphqlResult:t,parentName:`${s}Version`}),y&&(t.Query.fields[`version${d(s)}`]={type:t.globals.graphQL[a].versionType,args:{id:{type:o},draft:{type:r.GraphQLBoolean},...e.localization?{fallbackLocale:{type:t.types.fallbackLocaleInputType},locale:{type:t.types.localeInputType}}:{}},resolve:async function(e,t,a){t.locale&&(a.req.locale=t.locale),t.fallbackLocale&&(a.req.fallbackLocale=t.fallbackLocale);let r={id:t.id,depth:0,draft:t.draft,globalConfig:n,req:(0,c.i)(a.req,"transactionID")};return await (0,e_.L)(r)}},t.Query.fields[`versions${s}`]={type:eq(`versions${d(s)}`,t.globals.graphQL[a].versionType),args:{where:{type:eO({name:`versions${s}`,fields:l,parentName:`versions${s}`})},...e.localization?{fallbackLocale:{type:t.types.fallbackLocaleInputType},locale:{type:t.types.localeInputType}}:{},limit:{type:r.GraphQLInt},page:{type:r.GraphQLInt},pagination:{type:r.GraphQLBoolean},sort:{type:r.GraphQLString}},resolve:async function(e,t,a){let r={depth:0,globalConfig:n,limit:t.limit,page:t.page,pagination:t.pagination,req:(0,c.i)(a.req,"transactionID"),sort:t.sort,where:t.where};return await (0,eM.s)(r)}}),h&&(t.Mutation.fields[`restoreVersion${d(s)}`]={type:t.globals.graphQL[a].type,args:{id:{type:o},draft:{type:r.GraphQLBoolean}},resolve:async function(e,t,a){let r={id:t.id,depth:0,draft:t.draft,globalConfig:n,req:(0,c.i)(a.req,"transactionID")};return await (0,ez.c)(r)}})}})}({config:e,graphqlResult:t}),t.Query.fields.Access={type:function(e){let t={canAccessAdmin:{type:new r.GraphQLNonNull(r.GraphQLBoolean)}};return Object.values(e.collections).forEach(e=>{if(!1===e.graphQL)return;let a=k({type:"collection",entity:e,typeSuffix:"Access"});t[d(e.slug)]={type:a}}),Object.values(e.globals).forEach(e=>{if(!1===e.graphQL)return;let a=k({type:"global",entity:e,typeSuffix:"Access"});t[d(e.slug)]={type:a}}),new r.GraphQLObjectType({name:"Access",fields:t})}(e),resolve:async function(t,a,r){let n={req:(0,c.i)(r.req,"transactionID")},o=await (0,u.m)(n);return{...o,...h(o.collections,e.collections),...h(o.globals,e.globals)}}},"function"==typeof e.graphQL.queries){let a=e.graphQL.queries(n,{...t,config:e});t.Query={...t.Query,fields:{...t.Query.fields,...eF(a||{})}}}if("function"==typeof e.graphQL.mutations){let a=e.graphQL.mutations(n,{...t,config:e});t.Mutation={...t.Mutation,fields:{...t.Mutation.fields,...eF(a||{})}}}let a=new r.GraphQLObjectType(t.Query),o=new r.GraphQLObjectType(t.Mutation);return{schema:new r.GraphQLSchema({mutation:o,query:a}),validationRules:t=>{var a;return[(a={estimators:[s(),p({defaultComplexity:1})],maximumComplexity:e.graphQL.maxComplexity,variables:t.variableValues},e=>new l(e,a)),...e.graphQL.disableIntrospectionInProduction?[eV]:[],..."function"==typeof e?.graphQL?.validationRules?e.graphQL.validationRules(t):[]]}}}(t);e(e2.graphql||a)})}try{e2.graphql=await e2.promise}catch(e){throw e2.promise=null,e}return e2.graphql},te=e=>async t=>{let a=t.clone(),n=await (0,e4.o)({canSetHeaders:!0,config:e,request:t});await (0,e7.z)(n),(0,e9.r)(n);let{schema:o,validationRules:l}=await e6(e),{payload:i}=n,s={},p=await (function(e,t={}){let a={Response:t.Response||Response,TextEncoder:t.TextEncoder||TextEncoder,ReadableStream:t.ReadableStream||ReadableStream},n=function(e){let{schema:t,context:a,validate:n=r.validate,validationRules:o=[],execute:l=r.execute,parse:i=r.parse,getOperationAST:s=r.getOperationAST,rootValue:p,onSubscribe:c,onOperation:u,formatError:y=e=>e,parseRequestParams:d=eZ}=e;return async function(e){let h,f,m,g=null;for(let t of(eH(e,"accept")||"*/*").replace(/\s/g,"").toLowerCase().split(",")){let[e,...a]=t.split(";"),r=(null==a?void 0:a.find(e=>e.includes("charset=")))||"charset=utf-8";if("application/graphql-response+json"===e&&"charset=utf-8"===r){g="application/graphql-response+json";break}if(("application/json"===e||"application/*"===e||"*/*"===e)&&("charset=utf-8"===r||"charset=utf8"===r)){g="application/json";break}}if(!g)return[null,{status:406,statusText:"Not Acceptable",headers:{accept:"application/graphql-response+json; charset=utf-8, application/json; charset=utf-8"}}];try{let t=await d(e);if(t||(t=await eZ(e)),eU(t))return t;h=t}catch(e){return eK(e,g,y)}let L=await (null==c?void 0:c(e,h));if(eU(L))return L;if(eJ(L)&&("data"in L||"data"in L&&null==L.data&&"errors"in L)||eW(L))return eK(L,g,y);if(L)f=L;else{let l;if(!t)throw Error("The GraphQL schema is not provided");let{operationName:s,query:p,variables:c}=h;try{l=i(p)}catch(e){return eK(e,g,y)}let u="function"==typeof a?await a(e,h):a;if(eU(u))return u;let d={operationName:s,document:l,variableValues:c,contextValue:u};if("function"==typeof t){let a=await t(e,d);if(eU(a))return a;f=Object.assign(Object.assign({},d),{schema:a})}else f=Object.assign(Object.assign({},d),{schema:t});let m=r.specifiedRules;m="function"==typeof o?await o(e,f,r.specifiedRules):[...m,...o];let L=n(f.schema,f.document,m);if(L.length)return eK(L,g,y)}try{let e=s(f.document,f.operationName);if(!e)throw null;m=e.operation}catch(e){return eK(new r.GraphQLError("Unable to detect operation AST"),g,y)}if("subscription"===m)return eK(new r.GraphQLError("Subscriptions are not supported"),g,y);if("mutation"===m&&"GET"===e.method)return[JSON.stringify({errors:[new r.GraphQLError("Cannot perform mutations over GET")]}),{status:405,statusText:"Method Not Allowed",headers:{allow:"POST"}}];if("rootValue"in f||(f.rootValue=p),!("contextValue"in f)){let t="function"==typeof a?await a(e,h):a;if(eU(t))return t;f.contextValue=t}let b=await l(f),Q=await (null==u?void 0:u(e,f,b));return eU(Q)?Q:(Q&&(b=Q),"function"==typeof Object(b)[Symbol.asyncIterator]?eK(new r.GraphQLError("Subscriptions are not supported"),g,y):eK(b,g,y))}}(e);return async function(e){try{let[t,r]=await n(function(e,t={}){return{method:e.method,url:e.url,headers:e.headers,body:()=>e.text(),raw:e,context:{Response:t.Response||Response,TextEncoder:t.TextEncoder||TextEncoder,ReadableStream:t.ReadableStream||ReadableStream}}}(e,a));return new a.Response(t,r)}catch(e){return console.error("Internal error occurred during request handling. Please check your implementation.",e),new a.Response(null,{status:500})}}})({context:{headers:s,req:n},onOperation:async(e,t,a)=>{let r="function"==typeof i.extensions?await i.extensions({args:t,req:e,result:a}):a;if(r.errors){let e=await Promise.all(a.errors.map(e=>e8({err:e,payload:i,req:n})));return{...r,errors:e}}return r},schema:o,validationRules:(e,t,a)=>a.concat(l(t))})(a),c=(0,e3.y)({headers:new Headers(p.headers),req:n});for(let e in s)c.append(e,s[e]);return new Response(p.body,{headers:n.responseHeaders?(0,e5.l)(n.responseHeaders,c):c,status:p.status})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[9572,9574,4970,6425],()=>a(31486));module.exports=r})();