"use strict";exports.id=6717,exports.ids=[6717],exports.modules={6717:(a,b,c)=>{c.d(b,{fromSSO:()=>o});var d=c(71930),e=c(92413),f=c(21905);let g="To refresh this SSO session run 'aws sso login' with the corresponding profile.",h=async(a,b={})=>{let{SSOOIDCClient:d}=await c.e(8672).then(c.bind(c,58672));return new d(Object.assign({},b.clientConfig??{},{region:a??b.clientConfig?.region,logger:b.clientConfig?.logger??b.parentClientConfig?.logger}))},i=async(a,b,d={})=>{let{CreateTokenCommand:e}=await c.e(8672).then(c.bind(c,58672));return(await h(b,d)).send(new e({clientId:a.clientId,clientSecret:a.clientSecret,refreshToken:a.refreshToken,grantType:"refresh_token"}))},j=a=>{if(a.expiration&&a.expiration.getTime()<Date.now())throw new d.Jh(`Token is expired. ${g}`,!1)},k=(a,b,c=!1)=>{if(void 0===b)throw new d.Jh(`Value not present for '${a}' in SSO Token${c?". Cannot refresh":""}. ${g}`,!1)},{writeFile:l}=c(29021).promises,m=new Date(0),n=async({ssoStartUrl:a,ssoSession:b,ssoAccountId:h,ssoRegion:n,ssoRoleName:o,ssoClient:p,clientConfig:q,parentClientConfig:r,profile:s,logger:t})=>{let u,v,w="To refresh this SSO session run aws sso login with the corresponding profile.";if(b)try{let a=await ((a={})=>async({callerClientConfig:b}={})=>{let c,f={...a,parentClientConfig:{...b,...a.parentClientConfig}};f.logger?.debug("@aws-sdk/token-providers - fromSso");let h=await (0,e.YU)(f),n=(0,e.Bz)({profile:f.profile??b?.profile}),o=h[n];if(o){if(!o.sso_session)throw new d.Jh(`Profile '${n}' is missing required property 'sso_session'.`)}else throw new d.Jh(`Profile '${n}' could not be found in shared credentials file.`,!1);let p=o.sso_session,q=(await (0,e.qw)(f))[p];if(!q)throw new d.Jh(`Sso session '${p}' could not be found in shared credentials file.`,!1);for(let a of["sso_start_url","sso_region"])if(!q[a])throw new d.Jh(`Sso session '${p}' is missing required property '${a}'.`,!1);q.sso_start_url;let r=q.sso_region;try{c=await (0,e.vf)(p)}catch(a){throw new d.Jh(`The SSO session token associated with profile=${n} was not found or is invalid. ${g}`,!1)}k("accessToken",c.accessToken),k("expiresAt",c.expiresAt);let{accessToken:s,expiresAt:t}=c,u={token:s,expiration:new Date(t)};if(u.expiration.getTime()-Date.now()>3e5)return u;if(Date.now()-m.getTime()<3e4)return j(u),u;k("clientId",c.clientId,!0),k("clientSecret",c.clientSecret,!0),k("refreshToken",c.refreshToken,!0);try{m.setTime(Date.now());let a=await i(c,r,f);k("accessToken",a.accessToken),k("expiresIn",a.expiresIn);let b=new Date(Date.now()+1e3*a.expiresIn);try{await ((a,b)=>{let c=(0,e.C9)(a);return l(c,JSON.stringify(b,null,2))})(p,{...c,accessToken:a.accessToken,expiresAt:b.toISOString(),refreshToken:a.refreshToken})}catch(a){}return{token:a.accessToken,expiration:b}}catch(a){return j(u),u}})({profile:s})();u={accessToken:a.token,expiresAt:new Date(a.expiration).toISOString()}}catch(a){throw new d.C1(a.message,{tryNextLink:!1,logger:t})}else try{u=await (0,e.vf)(a)}catch(a){throw new d.C1(`The SSO session associated with this profile is invalid. ${w}`,{tryNextLink:!1,logger:t})}if(new Date(u.expiresAt).getTime()-Date.now()<=0)throw new d.C1(`The SSO session associated with this profile has expired. ${w}`,{tryNextLink:!1,logger:t});let{accessToken:x}=u,{SSOClient:y,GetRoleCredentialsCommand:z}=await c.e(9455).then(c.bind(c,39455)),A=p||new y(Object.assign({},q??{},{logger:q?.logger??r?.logger,region:q?.region??n}));try{v=await A.send(new z({accountId:h,roleName:o,accessToken:x}))}catch(a){throw new d.C1(a,{tryNextLink:!1,logger:t})}let{roleCredentials:{accessKeyId:B,secretAccessKey:C,sessionToken:D,expiration:E,credentialScope:F,accountId:G}={}}=v;if(!B||!C||!D||!E)throw new d.C1("SSO returns an invalid temporary credential.",{tryNextLink:!1,logger:t});let H={accessKeyId:B,secretAccessKey:C,sessionToken:D,expiration:new Date(E),...F&&{credentialScope:F},...G&&{accountId:G}};return b?(0,f.g)(H,"CREDENTIALS_SSO","s"):(0,f.g)(H,"CREDENTIALS_SSO_LEGACY","u"),H},o=(a={})=>async({callerClientConfig:b}={})=>{a.logger?.debug("@aws-sdk/credential-provider-sso - fromSSO");let{ssoStartUrl:c,ssoAccountId:f,ssoRegion:g,ssoRoleName:h,ssoSession:i}=a,{ssoClient:j}=a,k=(0,e.Bz)({profile:a.profile??b?.profile});if(c||f||g||h||i)if(c&&f&&g&&h)return n({ssoStartUrl:c,ssoSession:i,ssoAccountId:f,ssoRegion:g,ssoRoleName:h,ssoClient:j,clientConfig:a.clientConfig,parentClientConfig:a.parentClientConfig,profile:k});else throw new d.C1('Incomplete configuration. The fromSSO() argument hash must include "ssoStartUrl", "ssoAccountId", "ssoRegion", "ssoRoleName"',{tryNextLink:!1,logger:a.logger});{let b=(await (0,e.YU)(a))[k];if(!b)throw new d.C1(`Profile ${k} was not found.`,{logger:a.logger});if(!(a=>a&&("string"==typeof a.sso_start_url||"string"==typeof a.sso_account_id||"string"==typeof a.sso_session||"string"==typeof a.sso_region||"string"==typeof a.sso_role_name))(b))throw new d.C1(`Profile ${k} is not configured with SSO credentials.`,{logger:a.logger});if(b?.sso_session){let f=(await (0,e.qw)(a))[b.sso_session],h=` configurations in profile ${k} and sso-session ${b.sso_session}`;if(g&&g!==f.sso_region)throw new d.C1("Conflicting SSO region"+h,{tryNextLink:!1,logger:a.logger});if(c&&c!==f.sso_start_url)throw new d.C1("Conflicting SSO start_url"+h,{tryNextLink:!1,logger:a.logger});b.sso_region=f.sso_region,b.sso_start_url=f.sso_start_url}let{sso_start_url:f,sso_account_id:h,sso_region:i,sso_role_name:l,sso_session:m}=((a,b)=>{let{sso_start_url:c,sso_account_id:e,sso_region:f,sso_role_name:g}=a;if(!c||!e||!f||!g)throw new d.C1(`Profile is configured with invalid SSO credentials. Required parameters "sso_account_id", "sso_region", "sso_role_name", "sso_start_url". Got ${Object.keys(a).join(", ")}
Reference: https://docs.aws.amazon.com/cli/latest/userguide/cli-configure-sso.html`,{tryNextLink:!1,logger:b});return a})(b,a.logger);return n({ssoStartUrl:f,ssoSession:m,ssoAccountId:h,ssoRegion:i,ssoRoleName:l,ssoClient:j,clientConfig:a.clientConfig,parentClientConfig:a.parentClientConfig,profile:k})}}}};