"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_date-fns_locale_ru_js"],{

/***/ "(app-pages-browser)/./node_modules/date-fns/isSameWeek.js":
/*!*********************************************!*\
  !*** ./node_modules/date-fns/isSameWeek.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isSameWeek: () => (/* binding */ isSameWeek)\n/* harmony export */ });\n/* harmony import */ var _lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_lib/normalizeDates.js */ \"(app-pages-browser)/./node_modules/date-fns/_lib/normalizeDates.js\");\n/* harmony import */ var _startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./startOfWeek.js */ \"(app-pages-browser)/./node_modules/date-fns/startOfWeek.js\");\n\n\n/**\n * The {@link isSameWeek} function options.\n */ /**\n * @name isSameWeek\n * @category Week Helpers\n * @summary Are the given dates in the same week (and month and year)?\n *\n * @description\n * Are the given dates in the same week (and month and year)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same week (and month and year)\n *\n * @example\n * // Are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4))\n * //=> true\n *\n * @example\n * // If week starts with Monday,\n * // are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4), {\n *   weekStartsOn: 1\n * })\n * //=> false\n *\n * @example\n * // Are 1 January 2014 and 1 January 2015 in the same week?\n * const result = isSameWeek(new Date(2014, 0, 1), new Date(2015, 0, 1))\n * //=> false\n */ function isSameWeek(laterDate, earlierDate, options) {\n    const [laterDate_, earlierDate_] = (0,_lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__.normalizeDates)(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate);\n    return +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(laterDate_, options) === +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(earlierDate_, options);\n}\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (isSameWeek);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/isSameWeek.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/ru.js":
/*!********************************************!*\
  !*** ./node_modules/date-fns/locale/ru.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   ru: () => (/* binding */ ru)\n/* harmony export */ });\n/* harmony import */ var _ru_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ru/_lib/formatDistance.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/ru/_lib/formatDistance.js\");\n/* harmony import */ var _ru_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ru/_lib/formatLong.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/ru/_lib/formatLong.js\");\n/* harmony import */ var _ru_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ru/_lib/formatRelative.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/ru/_lib/formatRelative.js\");\n/* harmony import */ var _ru_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ru/_lib/localize.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/ru/_lib/localize.js\");\n/* harmony import */ var _ru_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ru/_lib/match.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/ru/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary Russian locale.\n * @language Russian\n * @iso-639-2 rus\n * <AUTHOR> Koss [@kossnocorp](https://github.com/kossnocorp)\n * <AUTHOR> Koss [@leshakoss](https://github.com/leshakoss)\n */ const ru = {\n    code: \"ru\",\n    formatDistance: _ru_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _ru_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _ru_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _ru_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _ru_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 1 /* Monday */ ,\n        firstWeekContainsDate: 1\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ru);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/ru.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/ru/_lib/formatDistance.js":
/*!****************************************************************!*\
  !*** ./node_modules/date-fns/locale/ru/_lib/formatDistance.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\nfunction declension(scheme, count) {\n    // scheme for count=1 exists\n    if (scheme.one !== undefined && count === 1) {\n        return scheme.one;\n    }\n    const rem10 = count % 10;\n    const rem100 = count % 100;\n    // 1, 21, 31, ...\n    if (rem10 === 1 && rem100 !== 11) {\n        return scheme.singularNominative.replace(\"{{count}}\", String(count));\n    // 2, 3, 4, 22, 23, 24, 32 ...\n    } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n        return scheme.singularGenitive.replace(\"{{count}}\", String(count));\n    // 5, 6, 7, 8, 9, 10, 11, ...\n    } else {\n        return scheme.pluralGenitive.replace(\"{{count}}\", String(count));\n    }\n}\nfunction buildLocalizeTokenFn(scheme) {\n    return (count, options)=>{\n        if (options === null || options === void 0 ? void 0 : options.addSuffix) {\n            if (options.comparison && options.comparison > 0) {\n                if (scheme.future) {\n                    return declension(scheme.future, count);\n                } else {\n                    return \"через \" + declension(scheme.regular, count);\n                }\n            } else {\n                if (scheme.past) {\n                    return declension(scheme.past, count);\n                } else {\n                    return declension(scheme.regular, count) + \" назад\";\n                }\n            }\n        } else {\n            return declension(scheme.regular, count);\n        }\n    };\n}\nconst formatDistanceLocale = {\n    lessThanXSeconds: buildLocalizeTokenFn({\n        regular: {\n            one: \"меньше секунды\",\n            singularNominative: \"меньше {{count}} секунды\",\n            singularGenitive: \"меньше {{count}} секунд\",\n            pluralGenitive: \"меньше {{count}} секунд\"\n        },\n        future: {\n            one: \"меньше, чем через секунду\",\n            singularNominative: \"меньше, чем через {{count}} секунду\",\n            singularGenitive: \"меньше, чем через {{count}} секунды\",\n            pluralGenitive: \"меньше, чем через {{count}} секунд\"\n        }\n    }),\n    xSeconds: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} секунда\",\n            singularGenitive: \"{{count}} секунды\",\n            pluralGenitive: \"{{count}} секунд\"\n        },\n        past: {\n            singularNominative: \"{{count}} секунду назад\",\n            singularGenitive: \"{{count}} секунды назад\",\n            pluralGenitive: \"{{count}} секунд назад\"\n        },\n        future: {\n            singularNominative: \"через {{count}} секунду\",\n            singularGenitive: \"через {{count}} секунды\",\n            pluralGenitive: \"через {{count}} секунд\"\n        }\n    }),\n    halfAMinute: (_count, options)=>{\n        if (options === null || options === void 0 ? void 0 : options.addSuffix) {\n            if (options.comparison && options.comparison > 0) {\n                return \"через полминуты\";\n            } else {\n                return \"полминуты назад\";\n            }\n        }\n        return \"полминуты\";\n    },\n    lessThanXMinutes: buildLocalizeTokenFn({\n        regular: {\n            one: \"меньше минуты\",\n            singularNominative: \"меньше {{count}} минуты\",\n            singularGenitive: \"меньше {{count}} минут\",\n            pluralGenitive: \"меньше {{count}} минут\"\n        },\n        future: {\n            one: \"меньше, чем через минуту\",\n            singularNominative: \"меньше, чем через {{count}} минуту\",\n            singularGenitive: \"меньше, чем через {{count}} минуты\",\n            pluralGenitive: \"меньше, чем через {{count}} минут\"\n        }\n    }),\n    xMinutes: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} минута\",\n            singularGenitive: \"{{count}} минуты\",\n            pluralGenitive: \"{{count}} минут\"\n        },\n        past: {\n            singularNominative: \"{{count}} минуту назад\",\n            singularGenitive: \"{{count}} минуты назад\",\n            pluralGenitive: \"{{count}} минут назад\"\n        },\n        future: {\n            singularNominative: \"через {{count}} минуту\",\n            singularGenitive: \"через {{count}} минуты\",\n            pluralGenitive: \"через {{count}} минут\"\n        }\n    }),\n    aboutXHours: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"около {{count}} часа\",\n            singularGenitive: \"около {{count}} часов\",\n            pluralGenitive: \"около {{count}} часов\"\n        },\n        future: {\n            singularNominative: \"приблизительно через {{count}} час\",\n            singularGenitive: \"приблизительно через {{count}} часа\",\n            pluralGenitive: \"приблизительно через {{count}} часов\"\n        }\n    }),\n    xHours: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} час\",\n            singularGenitive: \"{{count}} часа\",\n            pluralGenitive: \"{{count}} часов\"\n        }\n    }),\n    xDays: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} день\",\n            singularGenitive: \"{{count}} дня\",\n            pluralGenitive: \"{{count}} дней\"\n        }\n    }),\n    aboutXWeeks: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"около {{count}} недели\",\n            singularGenitive: \"около {{count}} недель\",\n            pluralGenitive: \"около {{count}} недель\"\n        },\n        future: {\n            singularNominative: \"приблизительно через {{count}} неделю\",\n            singularGenitive: \"приблизительно через {{count}} недели\",\n            pluralGenitive: \"приблизительно через {{count}} недель\"\n        }\n    }),\n    xWeeks: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} неделя\",\n            singularGenitive: \"{{count}} недели\",\n            pluralGenitive: \"{{count}} недель\"\n        }\n    }),\n    aboutXMonths: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"около {{count}} месяца\",\n            singularGenitive: \"около {{count}} месяцев\",\n            pluralGenitive: \"около {{count}} месяцев\"\n        },\n        future: {\n            singularNominative: \"приблизительно через {{count}} месяц\",\n            singularGenitive: \"приблизительно через {{count}} месяца\",\n            pluralGenitive: \"приблизительно через {{count}} месяцев\"\n        }\n    }),\n    xMonths: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} месяц\",\n            singularGenitive: \"{{count}} месяца\",\n            pluralGenitive: \"{{count}} месяцев\"\n        }\n    }),\n    aboutXYears: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"около {{count}} года\",\n            singularGenitive: \"около {{count}} лет\",\n            pluralGenitive: \"около {{count}} лет\"\n        },\n        future: {\n            singularNominative: \"приблизительно через {{count}} год\",\n            singularGenitive: \"приблизительно через {{count}} года\",\n            pluralGenitive: \"приблизительно через {{count}} лет\"\n        }\n    }),\n    xYears: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} год\",\n            singularGenitive: \"{{count}} года\",\n            pluralGenitive: \"{{count}} лет\"\n        }\n    }),\n    overXYears: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"больше {{count}} года\",\n            singularGenitive: \"больше {{count}} лет\",\n            pluralGenitive: \"больше {{count}} лет\"\n        },\n        future: {\n            singularNominative: \"больше, чем через {{count}} год\",\n            singularGenitive: \"больше, чем через {{count}} года\",\n            pluralGenitive: \"больше, чем через {{count}} лет\"\n        }\n    }),\n    almostXYears: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"почти {{count}} год\",\n            singularGenitive: \"почти {{count}} года\",\n            pluralGenitive: \"почти {{count}} лет\"\n        },\n        future: {\n            singularNominative: \"почти через {{count}} год\",\n            singularGenitive: \"почти через {{count}} года\",\n            pluralGenitive: \"почти через {{count}} лет\"\n        }\n    })\n};\nconst formatDistance = (token, count, options)=>{\n    return formatDistanceLocale[token](count, options);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/ru/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/ru/_lib/formatLong.js":
/*!************************************************************!*\
  !*** ./node_modules/date-fns/locale/ru/_lib/formatLong.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\nconst dateFormats = {\n    full: \"EEEE, d MMMM y 'г.'\",\n    long: \"d MMMM y 'г.'\",\n    medium: \"d MMM y 'г.'\",\n    short: \"dd.MM.y\"\n};\nconst timeFormats = {\n    full: \"H:mm:ss zzzz\",\n    long: \"H:mm:ss z\",\n    medium: \"H:mm:ss\",\n    short: \"H:mm\"\n};\nconst dateTimeFormats = {\n    any: \"{{date}}, {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"full\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/ru/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/ru/_lib/formatRelative.js":
/*!****************************************************************!*\
  !*** ./node_modules/date-fns/locale/ru/_lib/formatRelative.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\n/* harmony import */ var _isSameWeek_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../isSameWeek.js */ \"(app-pages-browser)/./node_modules/date-fns/isSameWeek.js\");\n\nconst accusativeWeekdays = [\n    \"воскресенье\",\n    \"понедельник\",\n    \"вторник\",\n    \"среду\",\n    \"четверг\",\n    \"пятницу\",\n    \"субботу\"\n];\nfunction lastWeek(day) {\n    const weekday = accusativeWeekdays[day];\n    switch(day){\n        case 0:\n            return \"'в прошлое \" + weekday + \" в' p\";\n        case 1:\n        case 2:\n        case 4:\n            return \"'в прошлый \" + weekday + \" в' p\";\n        case 3:\n        case 5:\n        case 6:\n            return \"'в прошлую \" + weekday + \" в' p\";\n    }\n}\nfunction thisWeek(day) {\n    const weekday = accusativeWeekdays[day];\n    if (day === 2 /* Tue */ ) {\n        return \"'во \" + weekday + \" в' p\";\n    } else {\n        return \"'в \" + weekday + \" в' p\";\n    }\n}\nfunction nextWeek(day) {\n    const weekday = accusativeWeekdays[day];\n    switch(day){\n        case 0:\n            return \"'в следующее \" + weekday + \" в' p\";\n        case 1:\n        case 2:\n        case 4:\n            return \"'в следующий \" + weekday + \" в' p\";\n        case 3:\n        case 5:\n        case 6:\n            return \"'в следующую \" + weekday + \" в' p\";\n    }\n}\nconst formatRelativeLocale = {\n    lastWeek: (date, baseDate, options)=>{\n        const day = date.getDay();\n        if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_0__.isSameWeek)(date, baseDate, options)) {\n            return thisWeek(day);\n        } else {\n            return lastWeek(day);\n        }\n    },\n    yesterday: \"'вчера в' p\",\n    today: \"'сегодня в' p\",\n    tomorrow: \"'завтра в' p\",\n    nextWeek: (date, baseDate, options)=>{\n        const day = date.getDay();\n        if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_0__.isSameWeek)(date, baseDate, options)) {\n            return thisWeek(day);\n        } else {\n            return nextWeek(day);\n        }\n    },\n    other: \"P\"\n};\nconst formatRelative = (token, date, baseDate, options)=>{\n    const format = formatRelativeLocale[token];\n    if (typeof format === \"function\") {\n        return format(date, baseDate, options);\n    }\n    return format;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/ru/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/ru/_lib/localize.js":
/*!**********************************************************!*\
  !*** ./node_modules/date-fns/locale/ru/_lib/localize.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\nconst eraValues = {\n    narrow: [\n        \"до н.э.\",\n        \"н.э.\"\n    ],\n    abbreviated: [\n        \"до н. э.\",\n        \"н. э.\"\n    ],\n    wide: [\n        \"до нашей эры\",\n        \"нашей эры\"\n    ]\n};\nconst quarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"1-й кв.\",\n        \"2-й кв.\",\n        \"3-й кв.\",\n        \"4-й кв.\"\n    ],\n    wide: [\n        \"1-й квартал\",\n        \"2-й квартал\",\n        \"3-й квартал\",\n        \"4-й квартал\"\n    ]\n};\nconst monthValues = {\n    narrow: [\n        \"Я\",\n        \"Ф\",\n        \"М\",\n        \"А\",\n        \"М\",\n        \"И\",\n        \"И\",\n        \"А\",\n        \"С\",\n        \"О\",\n        \"Н\",\n        \"Д\"\n    ],\n    abbreviated: [\n        \"янв.\",\n        \"фев.\",\n        \"март\",\n        \"апр.\",\n        \"май\",\n        \"июнь\",\n        \"июль\",\n        \"авг.\",\n        \"сент.\",\n        \"окт.\",\n        \"нояб.\",\n        \"дек.\"\n    ],\n    wide: [\n        \"январь\",\n        \"февраль\",\n        \"март\",\n        \"апрель\",\n        \"май\",\n        \"июнь\",\n        \"июль\",\n        \"август\",\n        \"сентябрь\",\n        \"октябрь\",\n        \"ноябрь\",\n        \"декабрь\"\n    ]\n};\nconst formattingMonthValues = {\n    narrow: [\n        \"Я\",\n        \"Ф\",\n        \"М\",\n        \"А\",\n        \"М\",\n        \"И\",\n        \"И\",\n        \"А\",\n        \"С\",\n        \"О\",\n        \"Н\",\n        \"Д\"\n    ],\n    abbreviated: [\n        \"янв.\",\n        \"фев.\",\n        \"мар.\",\n        \"апр.\",\n        \"мая\",\n        \"июн.\",\n        \"июл.\",\n        \"авг.\",\n        \"сент.\",\n        \"окт.\",\n        \"нояб.\",\n        \"дек.\"\n    ],\n    wide: [\n        \"января\",\n        \"февраля\",\n        \"марта\",\n        \"апреля\",\n        \"мая\",\n        \"июня\",\n        \"июля\",\n        \"августа\",\n        \"сентября\",\n        \"октября\",\n        \"ноября\",\n        \"декабря\"\n    ]\n};\nconst dayValues = {\n    narrow: [\n        \"В\",\n        \"П\",\n        \"В\",\n        \"С\",\n        \"Ч\",\n        \"П\",\n        \"С\"\n    ],\n    short: [\n        \"вс\",\n        \"пн\",\n        \"вт\",\n        \"ср\",\n        \"чт\",\n        \"пт\",\n        \"сб\"\n    ],\n    abbreviated: [\n        \"вск\",\n        \"пнд\",\n        \"втр\",\n        \"срд\",\n        \"чтв\",\n        \"птн\",\n        \"суб\"\n    ],\n    wide: [\n        \"воскресенье\",\n        \"понедельник\",\n        \"вторник\",\n        \"среда\",\n        \"четверг\",\n        \"пятница\",\n        \"суббота\"\n    ]\n};\nconst dayPeriodValues = {\n    narrow: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"полн.\",\n        noon: \"полд.\",\n        morning: \"утро\",\n        afternoon: \"день\",\n        evening: \"веч.\",\n        night: \"ночь\"\n    },\n    abbreviated: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"полн.\",\n        noon: \"полд.\",\n        morning: \"утро\",\n        afternoon: \"день\",\n        evening: \"веч.\",\n        night: \"ночь\"\n    },\n    wide: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"полночь\",\n        noon: \"полдень\",\n        morning: \"утро\",\n        afternoon: \"день\",\n        evening: \"вечер\",\n        night: \"ночь\"\n    }\n};\nconst formattingDayPeriodValues = {\n    narrow: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"полн.\",\n        noon: \"полд.\",\n        morning: \"утра\",\n        afternoon: \"дня\",\n        evening: \"веч.\",\n        night: \"ночи\"\n    },\n    abbreviated: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"полн.\",\n        noon: \"полд.\",\n        morning: \"утра\",\n        afternoon: \"дня\",\n        evening: \"веч.\",\n        night: \"ночи\"\n    },\n    wide: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"полночь\",\n        noon: \"полдень\",\n        morning: \"утра\",\n        afternoon: \"дня\",\n        evening: \"вечера\",\n        night: \"ночи\"\n    }\n};\nconst ordinalNumber = (dirtyNumber, options)=>{\n    const number = Number(dirtyNumber);\n    const unit = options === null || options === void 0 ? void 0 : options.unit;\n    let suffix;\n    if (unit === \"date\") {\n        suffix = \"-е\";\n    } else if (unit === \"week\" || unit === \"minute\" || unit === \"second\") {\n        suffix = \"-я\";\n    } else {\n        suffix = \"-й\";\n    }\n    return number + suffix;\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        defaultWidth: \"wide\",\n        formattingValues: formattingMonthValues,\n        defaultFormattingWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"any\",\n        formattingValues: formattingDayPeriodValues,\n        defaultFormattingWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/ru/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/ru/_lib/match.js":
/*!*******************************************************!*\
  !*** ./node_modules/date-fns/locale/ru/_lib/match.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^(\\d+)(-?(е|я|й|ое|ье|ая|ья|ый|ой|ий|ый))?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^((до )?н\\.?\\s?э\\.?)/i,\n    abbreviated: /^((до )?н\\.?\\s?э\\.?)/i,\n    wide: /^(до нашей эры|нашей эры|наша эра)/i\n};\nconst parseEraPatterns = {\n    any: [\n        /^д/i,\n        /^н/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]/i,\n    abbreviated: /^[1234](-?[ыои]?й?)? кв.?/i,\n    wide: /^[1234](-?[ыои]?й?)? квартал/i\n};\nconst parseQuarterPatterns = {\n    any: [\n        /1/i,\n        /2/i,\n        /3/i,\n        /4/i\n    ]\n};\nconst matchMonthPatterns = {\n    narrow: /^[яфмаисонд]/i,\n    abbreviated: /^(янв|фев|март?|апр|ма[йя]|июн[ья]?|июл[ья]?|авг|сент?|окт|нояб?|дек)\\.?/i,\n    wide: /^(январ[ья]|феврал[ья]|марта?|апрел[ья]|ма[йя]|июн[ья]|июл[ья]|августа?|сентябр[ья]|октябр[ья]|октябр[ья]|ноябр[ья]|декабр[ья])/i\n};\nconst parseMonthPatterns = {\n    narrow: [\n        /^я/i,\n        /^ф/i,\n        /^м/i,\n        /^а/i,\n        /^м/i,\n        /^и/i,\n        /^и/i,\n        /^а/i,\n        /^с/i,\n        /^о/i,\n        /^н/i,\n        /^я/i\n    ],\n    any: [\n        /^я/i,\n        /^ф/i,\n        /^мар/i,\n        /^ап/i,\n        /^ма[йя]/i,\n        /^июн/i,\n        /^июл/i,\n        /^ав/i,\n        /^с/i,\n        /^о/i,\n        /^н/i,\n        /^д/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^[впсч]/i,\n    short: /^(вс|во|пн|по|вт|ср|чт|че|пт|пя|сб|су)\\.?/i,\n    abbreviated: /^(вск|вос|пнд|пон|втр|вто|срд|сре|чтв|чет|птн|пят|суб).?/i,\n    wide: /^(воскресень[ея]|понедельника?|вторника?|сред[аы]|четверга?|пятниц[аы]|суббот[аы])/i\n};\nconst parseDayPatterns = {\n    narrow: [\n        /^в/i,\n        /^п/i,\n        /^в/i,\n        /^с/i,\n        /^ч/i,\n        /^п/i,\n        /^с/i\n    ],\n    any: [\n        /^в[ос]/i,\n        /^п[он]/i,\n        /^в/i,\n        /^ср/i,\n        /^ч/i,\n        /^п[ят]/i,\n        /^с[уб]/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    narrow: /^([дп]п|полн\\.?|полд\\.?|утр[оа]|день|дня|веч\\.?|ноч[ьи])/i,\n    abbreviated: /^([дп]п|полн\\.?|полд\\.?|утр[оа]|день|дня|веч\\.?|ноч[ьи])/i,\n    wide: /^([дп]п|полночь|полдень|утр[оа]|день|дня|вечера?|ноч[ьи])/i\n};\nconst parseDayPeriodPatterns = {\n    any: {\n        am: /^дп/i,\n        pm: /^пп/i,\n        midnight: /^полн/i,\n        noon: /^полд/i,\n        morning: /^у/i,\n        afternoon: /^д[ен]/i,\n        evening: /^в/i,\n        night: /^н/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value, 10)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/ru/_lib/match.js\n"));

/***/ })

}]);