"use strict";exports.id=9189,exports.ids=[9189],exports.modules={99189:(a,b,c)=>{c.d(b,{fromHttp:()=>m});var d=c(21905),e=c(66426),f=c(71930),g=c(79748),h=c.n(g),i=c(31734),j=c(35639),k=c(706);async function l(a,b){let c=(0,k.c9)(a.body),d=await c.transformToString();if(200===a.statusCode){let a=JSON.parse(d);if("string"!=typeof a.AccessKeyId||"string"!=typeof a.SecretAccessKey||"string"!=typeof a.Token||"string"!=typeof a.Expiration)throw new f.C1("HTTP credential provider response not of the required format, an object matching: { AccessKeyId: string, SecretAccessKey: string, Token: string, Expiration: string(rfc3339) }",{logger:b});return{accessKeyId:a.<PERSON>,secretAccessKey:a.<PERSON>,sessionToken:a.<PERSON>,expiration:(0,j.EI)(a.Expiration)}}if(a.statusCode>=400&&a.statusCode<500){let c={};try{c=JSON.parse(d)}catch(a){}throw Object.assign(new f.C1(`Server responded with status: ${a.statusCode}`,{logger:b}),{Code:c.Code,Message:c.Message})}throw new f.C1(`Server responded with status: ${a.statusCode}`,{logger:b})}let m=(a={})=>{let b;a.logger?.debug("@aws-sdk/credential-provider-http - fromHttp");let c=a.awsContainerCredentialsRelativeUri??process.env.AWS_CONTAINER_CREDENTIALS_RELATIVE_URI,g=a.awsContainerCredentialsFullUri??process.env.AWS_CONTAINER_CREDENTIALS_FULL_URI,j=a.awsContainerAuthorizationToken??process.env.AWS_CONTAINER_AUTHORIZATION_TOKEN,k=a.awsContainerAuthorizationTokenFile??process.env.AWS_CONTAINER_AUTHORIZATION_TOKEN_FILE,m=a.logger?.constructor?.name!=="NoOpLogger"&&a.logger?a.logger.warn:console.warn;if(c&&g&&(m("@aws-sdk/credential-provider-http: you have set both awsContainerCredentialsRelativeUri and awsContainerCredentialsFullUri."),m("awsContainerCredentialsFullUri will take precedence.")),j&&k&&(m("@aws-sdk/credential-provider-http: you have set both awsContainerAuthorizationToken and awsContainerAuthorizationTokenFile."),m("awsContainerAuthorizationToken will take precedence.")),g)b=g;else if(c)b=`http://*************${c}`;else throw new f.C1(`No HTTP credential provider host provided.
Set AWS_CONTAINER_CREDENTIALS_FULL_URI or AWS_CONTAINER_CREDENTIALS_RELATIVE_URI.`,{logger:a.logger});let n=new URL(b);((a,b)=>{if("https:"!==a.protocol&&"*************"!==a.hostname&&"**************"!==a.hostname&&"[fd00:ec2::23]"!==a.hostname){if(a.hostname.includes("[")){if("[::1]"===a.hostname||"[0000:0000:0000:0000:0000:0000:0000:0001]"===a.hostname)return}else{if("localhost"===a.hostname)return;let b=a.hostname.split("."),c=a=>{let b=parseInt(a,10);return 0<=b&&b<=255};if("127"===b[0]&&c(b[1])&&c(b[2])&&c(b[3])&&4===b.length)return}throw new f.C1(`URL not accepted. It must either be HTTPS or match one of the following:
  - loopback CIDR *********/8 or [::1/128]
  - ECS container host *************
  - EKS container host ************** or [fd00:ec2::23]`,{logger:b})}})(n,a.logger);let o=new e.$c({requestTimeout:a.timeout??1e3,connectionTimeout:a.timeout??1e3});return((a,b,c)=>async()=>{for(let d=0;d<b;++d)try{return await a()}catch(a){await new Promise(a=>setTimeout(a,c))}return await a()})(async()=>{let b=function(a){return new i.Kd({protocol:a.protocol,hostname:a.hostname,port:Number(a.port),path:a.pathname,query:Array.from(a.searchParams.entries()).reduce((a,[b,c])=>(a[b]=c,a),{}),fragment:a.hash})}(n);j?b.headers.Authorization=j:k&&(b.headers.Authorization=(await h().readFile(k)).toString());try{let a=await o.handle(b);return l(a.response).then(a=>(0,d.g)(a,"CREDENTIALS_HTTP","z"))}catch(b){throw new f.C1(String(b),{logger:a.logger})}},a.maxRetries??3,a.timeout??1e3)}}};