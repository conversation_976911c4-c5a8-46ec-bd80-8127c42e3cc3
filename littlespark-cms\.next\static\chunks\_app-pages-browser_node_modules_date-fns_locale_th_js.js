"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_date-fns_locale_th_js"],{

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/th.js":
/*!********************************************!*\
  !*** ./node_modules/date-fns/locale/th.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   th: () => (/* binding */ th)\n/* harmony export */ });\n/* harmony import */ var _th_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./th/_lib/formatDistance.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/th/_lib/formatDistance.js\");\n/* harmony import */ var _th_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./th/_lib/formatLong.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/th/_lib/formatLong.js\");\n/* harmony import */ var _th_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./th/_lib/formatRelative.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/th/_lib/formatRelative.js\");\n/* harmony import */ var _th_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./th/_lib/localize.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/th/_lib/localize.js\");\n/* harmony import */ var _th_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./th/_lib/match.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/th/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary Thai locale.\n * @language Thai\n * @iso-639-2 tha\n * <AUTHOR> Hirunworawongkun [@athivvat](https://github.com/athivvat)\n * <AUTHOR> * <AUTHOR> I. [@nodtem66](https://github.com/nodtem66)\n */ const th = {\n    code: \"th\",\n    formatDistance: _th_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _th_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _th_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _th_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _th_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 0 /* Sunday */ ,\n        firstWeekContainsDate: 1\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (th);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/th.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/th/_lib/formatDistance.js":
/*!****************************************************************!*\
  !*** ./node_modules/date-fns/locale/th/_lib/formatDistance.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\nconst formatDistanceLocale = {\n    lessThanXSeconds: {\n        one: \"น้อยกว่า 1 วินาที\",\n        other: \"น้อยกว่า {{count}} วินาที\"\n    },\n    xSeconds: {\n        one: \"1 วินาที\",\n        other: \"{{count}} วินาที\"\n    },\n    halfAMinute: \"ครึ่งนาที\",\n    lessThanXMinutes: {\n        one: \"น้อยกว่า 1 นาที\",\n        other: \"น้อยกว่า {{count}} นาที\"\n    },\n    xMinutes: {\n        one: \"1 นาที\",\n        other: \"{{count}} นาที\"\n    },\n    aboutXHours: {\n        one: \"ประมาณ 1 ชั่วโมง\",\n        other: \"ประมาณ {{count}} ชั่วโมง\"\n    },\n    xHours: {\n        one: \"1 ชั่วโมง\",\n        other: \"{{count}} ชั่วโมง\"\n    },\n    xDays: {\n        one: \"1 วัน\",\n        other: \"{{count}} วัน\"\n    },\n    aboutXWeeks: {\n        one: \"ประมาณ 1 สัปดาห์\",\n        other: \"ประมาณ {{count}} สัปดาห์\"\n    },\n    xWeeks: {\n        one: \"1 สัปดาห์\",\n        other: \"{{count}} สัปดาห์\"\n    },\n    aboutXMonths: {\n        one: \"ประมาณ 1 เดือน\",\n        other: \"ประมาณ {{count}} เดือน\"\n    },\n    xMonths: {\n        one: \"1 เดือน\",\n        other: \"{{count}} เดือน\"\n    },\n    aboutXYears: {\n        one: \"ประมาณ 1 ปี\",\n        other: \"ประมาณ {{count}} ปี\"\n    },\n    xYears: {\n        one: \"1 ปี\",\n        other: \"{{count}} ปี\"\n    },\n    overXYears: {\n        one: \"มากกว่า 1 ปี\",\n        other: \"มากกว่า {{count}} ปี\"\n    },\n    almostXYears: {\n        one: \"เกือบ 1 ปี\",\n        other: \"เกือบ {{count}} ปี\"\n    }\n};\nconst formatDistance = (token, count, options)=>{\n    let result;\n    const tokenValue = formatDistanceLocale[token];\n    if (typeof tokenValue === \"string\") {\n        result = tokenValue;\n    } else if (count === 1) {\n        result = tokenValue.one;\n    } else {\n        result = tokenValue.other.replace(\"{{count}}\", String(count));\n    }\n    if (options === null || options === void 0 ? void 0 : options.addSuffix) {\n        if (options.comparison && options.comparison > 0) {\n            if (token === \"halfAMinute\") {\n                return \"ใน\" + result;\n            } else {\n                return \"ใน \" + result;\n            }\n        } else {\n            return result + \"ที่ผ่านมา\";\n        }\n    }\n    return result;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/th/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/th/_lib/formatLong.js":
/*!************************************************************!*\
  !*** ./node_modules/date-fns/locale/th/_lib/formatLong.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\nconst dateFormats = {\n    full: \"วันEEEEที่ do MMMM y\",\n    long: \"do MMMM y\",\n    medium: \"d MMM y\",\n    short: \"dd/MM/yyyy\"\n};\nconst timeFormats = {\n    full: \"H:mm:ss น. zzzz\",\n    long: \"H:mm:ss น. z\",\n    medium: \"H:mm:ss น.\",\n    short: \"H:mm น.\"\n};\nconst dateTimeFormats = {\n    full: \"{{date}} 'เวลา' {{time}}\",\n    long: \"{{date}} 'เวลา' {{time}}\",\n    medium: \"{{date}}, {{time}}\",\n    short: \"{{date}}, {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"medium\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"full\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/th/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/th/_lib/formatRelative.js":
/*!****************************************************************!*\
  !*** ./node_modules/date-fns/locale/th/_lib/formatRelative.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\nconst formatRelativeLocale = {\n    lastWeek: \"eeee'ที่แล้วเวลา' p\",\n    yesterday: \"'เมื่อวานนี้เวลา' p\",\n    today: \"'วันนี้เวลา' p\",\n    tomorrow: \"'พรุ่งนี้เวลา' p\",\n    nextWeek: \"eeee 'เวลา' p\",\n    other: \"P\"\n};\nconst formatRelative = (token, _date, _baseDate, _options)=>formatRelativeLocale[token];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9kYXRlLWZucy9sb2NhbGUvdGgvX2xpYi9mb3JtYXRSZWxhdGl2ZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsTUFBTUEsdUJBQXVCO0lBQzNCQyxVQUFVO0lBQ1ZDLFdBQVc7SUFDWEMsT0FBTztJQUNQQyxVQUFVO0lBQ1ZDLFVBQVU7SUFDVkMsT0FBTztBQUNUO0FBRU8sTUFBTUMsaUJBQWlCLENBQUNDLE9BQU9DLE9BQU9DLFdBQVdDLFdBQ3REWCxvQkFBb0IsQ0FBQ1EsTUFBTSxDQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJhdmkxXFxrYXZ5YS1naXRcXHNwYXJrLW5ld1xcbGl0dGxlc3BhcmstY21zXFxub2RlX21vZHVsZXNcXGRhdGUtZm5zXFxsb2NhbGVcXHRoXFxfbGliXFxmb3JtYXRSZWxhdGl2ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBmb3JtYXRSZWxhdGl2ZUxvY2FsZSA9IHtcbiAgbGFzdFdlZWs6IFwiZWVlZSfguJfguLXguYjguYHguKXguYnguKfguYDguKfguKXguLInIHBcIixcbiAgeWVzdGVyZGF5OiBcIifguYDguKHguLfguYjguK3guKfguLLguJnguJnguLXguYnguYDguKfguKXguLInIHBcIixcbiAgdG9kYXk6IFwiJ+C4p+C4seC4meC4meC4teC5ieC5gOC4p+C4peC4sicgcFwiLFxuICB0b21vcnJvdzogXCIn4Lie4Lij4Li44LmI4LiH4LiZ4Li14LmJ4LmA4Lin4Lil4LiyJyBwXCIsXG4gIG5leHRXZWVrOiBcImVlZWUgJ+C5gOC4p+C4peC4sicgcFwiLFxuICBvdGhlcjogXCJQXCIsXG59O1xuXG5leHBvcnQgY29uc3QgZm9ybWF0UmVsYXRpdmUgPSAodG9rZW4sIF9kYXRlLCBfYmFzZURhdGUsIF9vcHRpb25zKSA9PlxuICBmb3JtYXRSZWxhdGl2ZUxvY2FsZVt0b2tlbl07XG4iXSwibmFtZXMiOlsiZm9ybWF0UmVsYXRpdmVMb2NhbGUiLCJsYXN0V2VlayIsInllc3RlcmRheSIsInRvZGF5IiwidG9tb3Jyb3ciLCJuZXh0V2VlayIsIm90aGVyIiwiZm9ybWF0UmVsYXRpdmUiLCJ0b2tlbiIsIl9kYXRlIiwiX2Jhc2VEYXRlIiwiX29wdGlvbnMiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/th/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/th/_lib/localize.js":
/*!**********************************************************!*\
  !*** ./node_modules/date-fns/locale/th/_lib/localize.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\nconst eraValues = {\n    narrow: [\n        \"B\",\n        \"คศ\"\n    ],\n    abbreviated: [\n        \"BC\",\n        \"ค.ศ.\"\n    ],\n    wide: [\n        \"ปีก่อนคริสตกาล\",\n        \"คริสต์ศักราช\"\n    ]\n};\nconst quarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"Q1\",\n        \"Q2\",\n        \"Q3\",\n        \"Q4\"\n    ],\n    wide: [\n        \"ไตรมาสแรก\",\n        \"ไตรมาสที่สอง\",\n        \"ไตรมาสที่สาม\",\n        \"ไตรมาสที่สี่\"\n    ]\n};\nconst dayValues = {\n    narrow: [\n        \"อา.\",\n        \"จ.\",\n        \"อ.\",\n        \"พ.\",\n        \"พฤ.\",\n        \"ศ.\",\n        \"ส.\"\n    ],\n    short: [\n        \"อา.\",\n        \"จ.\",\n        \"อ.\",\n        \"พ.\",\n        \"พฤ.\",\n        \"ศ.\",\n        \"ส.\"\n    ],\n    abbreviated: [\n        \"อา.\",\n        \"จ.\",\n        \"อ.\",\n        \"พ.\",\n        \"พฤ.\",\n        \"ศ.\",\n        \"ส.\"\n    ],\n    wide: [\n        \"อาทิตย์\",\n        \"จันทร์\",\n        \"อังคาร\",\n        \"พุธ\",\n        \"พฤหัสบดี\",\n        \"ศุกร์\",\n        \"เสาร์\"\n    ]\n};\nconst monthValues = {\n    narrow: [\n        \"ม.ค.\",\n        \"ก.พ.\",\n        \"มี.ค.\",\n        \"เม.ย.\",\n        \"พ.ค.\",\n        \"มิ.ย.\",\n        \"ก.ค.\",\n        \"ส.ค.\",\n        \"ก.ย.\",\n        \"ต.ค.\",\n        \"พ.ย.\",\n        \"ธ.ค.\"\n    ],\n    abbreviated: [\n        \"ม.ค.\",\n        \"ก.พ.\",\n        \"มี.ค.\",\n        \"เม.ย.\",\n        \"พ.ค.\",\n        \"มิ.ย.\",\n        \"ก.ค.\",\n        \"ส.ค.\",\n        \"ก.ย.\",\n        \"ต.ค.\",\n        \"พ.ย.\",\n        \"ธ.ค.\"\n    ],\n    wide: [\n        \"มกราคม\",\n        \"กุมภาพันธ์\",\n        \"มีนาคม\",\n        \"เมษายน\",\n        \"พฤษภาคม\",\n        \"มิถุนายน\",\n        \"กรกฎาคม\",\n        \"สิงหาคม\",\n        \"กันยายน\",\n        \"ตุลาคม\",\n        \"พฤศจิกายน\",\n        \"ธันวาคม\"\n    ]\n};\nconst dayPeriodValues = {\n    narrow: {\n        am: \"ก่อนเที่ยง\",\n        pm: \"หลังเที่ยง\",\n        midnight: \"เที่ยงคืน\",\n        noon: \"เที่ยง\",\n        morning: \"เช้า\",\n        afternoon: \"บ่าย\",\n        evening: \"เย็น\",\n        night: \"กลางคืน\"\n    },\n    abbreviated: {\n        am: \"ก่อนเที่ยง\",\n        pm: \"หลังเที่ยง\",\n        midnight: \"เที่ยงคืน\",\n        noon: \"เที่ยง\",\n        morning: \"เช้า\",\n        afternoon: \"บ่าย\",\n        evening: \"เย็น\",\n        night: \"กลางคืน\"\n    },\n    wide: {\n        am: \"ก่อนเที่ยง\",\n        pm: \"หลังเที่ยง\",\n        midnight: \"เที่ยงคืน\",\n        noon: \"เที่ยง\",\n        morning: \"เช้า\",\n        afternoon: \"บ่าย\",\n        evening: \"เย็น\",\n        night: \"กลางคืน\"\n    }\n};\nconst formattingDayPeriodValues = {\n    narrow: {\n        am: \"ก่อนเที่ยง\",\n        pm: \"หลังเที่ยง\",\n        midnight: \"เที่ยงคืน\",\n        noon: \"เที่ยง\",\n        morning: \"ตอนเช้า\",\n        afternoon: \"ตอนกลางวัน\",\n        evening: \"ตอนเย็น\",\n        night: \"ตอนกลางคืน\"\n    },\n    abbreviated: {\n        am: \"ก่อนเที่ยง\",\n        pm: \"หลังเที่ยง\",\n        midnight: \"เที่ยงคืน\",\n        noon: \"เที่ยง\",\n        morning: \"ตอนเช้า\",\n        afternoon: \"ตอนกลางวัน\",\n        evening: \"ตอนเย็น\",\n        night: \"ตอนกลางคืน\"\n    },\n    wide: {\n        am: \"ก่อนเที่ยง\",\n        pm: \"หลังเที่ยง\",\n        midnight: \"เที่ยงคืน\",\n        noon: \"เที่ยง\",\n        morning: \"ตอนเช้า\",\n        afternoon: \"ตอนกลางวัน\",\n        evening: \"ตอนเย็น\",\n        night: \"ตอนกลางคืน\"\n    }\n};\nconst ordinalNumber = (dirtyNumber, _options)=>{\n    return String(dirtyNumber);\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        defaultWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"wide\",\n        formattingValues: formattingDayPeriodValues,\n        defaultFormattingWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/th/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/th/_lib/match.js":
/*!*******************************************************!*\
  !*** ./node_modules/date-fns/locale/th/_lib/match.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^\\d+/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^([bB]|[aA]|คศ)/i,\n    abbreviated: /^([bB]\\.?\\s?[cC]\\.?|b\\.?\\s?c\\.?\\s?e\\.?|a\\.?\\s?d\\.?|c\\.?\\s?e\\.?|ค\\.?ศ\\.?)/i,\n    wide: /^(ก่อนคริสตกาล|คริสต์ศักราช|คริสตกาล)/i\n};\nconst parseEraPatterns = {\n    any: [\n        /^[bB]/i,\n        /^(^[aA]|ค\\.?ศ\\.?|คริสตกาล|คริสต์ศักราช|)/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]/i,\n    abbreviated: /^q[1234]/i,\n    wide: /^ไตรมาส(ที่)? ?[1234]/i\n};\nconst parseQuarterPatterns = {\n    any: [\n        /(1|แรก|หนึ่ง)/i,\n        /(2|สอง)/i,\n        /(3|สาม)/i,\n        /(4|สี่)/i\n    ]\n};\nconst matchMonthPatterns = {\n    narrow: /^(ม\\.?ค\\.?|ก\\.?พ\\.?|มี\\.?ค\\.?|เม\\.?ย\\.?|พ\\.?ค\\.?|มิ\\.?ย\\.?|ก\\.?ค\\.?|ส\\.?ค\\.?|ก\\.?ย\\.?|ต\\.?ค\\.?|พ\\.?ย\\.?|ธ\\.?ค\\.?)/i,\n    abbreviated: /^(ม\\.?ค\\.?|ก\\.?พ\\.?|มี\\.?ค\\.?|เม\\.?ย\\.?|พ\\.?ค\\.?|มิ\\.?ย\\.?|ก\\.?ค\\.?|ส\\.?ค\\.?|ก\\.?ย\\.?|ต\\.?ค\\.?|พ\\.?ย\\.?|ธ\\.?ค\\.?')/i,\n    wide: /^(มกราคม|กุมภาพันธ์|มีนาคม|เมษายน|พฤษภาคม|มิถุนายน|กรกฎาคม|สิงหาคม|กันยายน|ตุลาคม|พฤศจิกายน|ธันวาคม)/i\n};\nconst parseMonthPatterns = {\n    wide: [\n        /^มก/i,\n        /^กุม/i,\n        /^มี/i,\n        /^เม/i,\n        /^พฤษ/i,\n        /^มิ/i,\n        /^กรก/i,\n        /^ส/i,\n        /^กัน/i,\n        /^ต/i,\n        /^พฤศ/i,\n        /^ธ/i\n    ],\n    any: [\n        /^ม\\.?ค\\.?/i,\n        /^ก\\.?พ\\.?/i,\n        /^มี\\.?ค\\.?/i,\n        /^เม\\.?ย\\.?/i,\n        /^พ\\.?ค\\.?/i,\n        /^มิ\\.?ย\\.?/i,\n        /^ก\\.?ค\\.?/i,\n        /^ส\\.?ค\\.?/i,\n        /^ก\\.?ย\\.?/i,\n        /^ต\\.?ค\\.?/i,\n        /^พ\\.?ย\\.?/i,\n        /^ธ\\.?ค\\.?/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^(อา\\.?|จ\\.?|อ\\.?|พฤ\\.?|พ\\.?|ศ\\.?|ส\\.?)/i,\n    short: /^(อา\\.?|จ\\.?|อ\\.?|พฤ\\.?|พ\\.?|ศ\\.?|ส\\.?)/i,\n    abbreviated: /^(อา\\.?|จ\\.?|อ\\.?|พฤ\\.?|พ\\.?|ศ\\.?|ส\\.?)/i,\n    wide: /^(อาทิตย์|จันทร์|อังคาร|พุธ|พฤหัสบดี|ศุกร์|เสาร์)/i\n};\nconst parseDayPatterns = {\n    wide: [\n        /^อา/i,\n        /^จั/i,\n        /^อั/i,\n        /^พุธ/i,\n        /^พฤ/i,\n        /^ศ/i,\n        /^เส/i\n    ],\n    any: [\n        /^อา/i,\n        /^จ/i,\n        /^อ/i,\n        /^พ(?!ฤ)/i,\n        /^พฤ/i,\n        /^ศ/i,\n        /^ส/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    any: /^(ก่อนเที่ยง|หลังเที่ยง|เที่ยงคืน|เที่ยง|(ตอน.*?)?.*(เที่ยง|เช้า|บ่าย|เย็น|กลางคืน))/i\n};\nconst parseDayPeriodPatterns = {\n    any: {\n        am: /^ก่อนเที่ยง/i,\n        pm: /^หลังเที่ยง/i,\n        midnight: /^เที่ยงคืน/i,\n        noon: /^เที่ยง/i,\n        morning: /เช้า/i,\n        afternoon: /บ่าย/i,\n        evening: /เย็น/i,\n        night: /กลางคืน/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value, 10)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"any\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/th/_lib/match.js\n"));

/***/ })

}]);