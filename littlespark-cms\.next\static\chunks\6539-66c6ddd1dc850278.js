"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6539],{11981:(e,a,t)=>{t.d(a,{ResetPreferences:()=>c});var i=t(19749),n=t(95155),l=t(92825),r=t(87677),s=t(46205),o=t(12115);let u="confirm-reset-modal",c=e=>{let a,t,c,d=(0,i.c)(9),{apiRoute:m,user:h}=e,{openModal:p}=(0,l.useModal)(),{t:f}=(0,r.d)();d[0]!==m||d[1]!==h?(a=async()=>{if(!h)return;let e=s.A({depth:0,where:{user:{id:{equals:h.id}}}},{addQueryPrefix:!0});try{let a=await fetch("".concat(m,"/payload-preferences").concat(e),{credentials:"include",headers:{"Content-Type":"application/json"},method:"DELETE"}),t=(await a.json()).message;a.ok?l.toast.success(t):l.toast.error(t)}catch(e){}},d[0]=m,d[1]=h,d[2]=a):a=d[2];let g=a;return d[3]!==p?(t=()=>p(u),d[3]=p,d[4]=t):t=d[4],d[5]!==g||d[6]!==f||d[7]!==t?(c=(0,n.jsxs)(o.Fragment,{children:[(0,n.jsx)("div",{children:(0,n.jsx)(l.Button,{buttonStyle:"secondary",onClick:t,children:f("general:resetPreferences")})}),(0,n.jsx)(l.ConfirmationModal,{body:f("general:resetPreferencesDescription"),confirmingLabel:f("general:resettingPreferences"),heading:f("general:resetPreferences"),modalSlug:u,onConfirm:g})]}),d[5]=g,d[6]=f,d[7]=t,d[8]=c):c=d[8],c}},19557:(e,a,t)=>{t.d(a,{ToggleTheme:()=>o});var i=t(19749),n=t(95155),l=t(58028),r=t(87677),s=t(92825);t(12115);let o=()=>{let e,a,t=(0,i.c)(7),{autoMode:o,setTheme:u,theme:c}=(0,l.h)(),{t:d}=(0,r.d)();t[0]!==u?(e=e=>{u(e)},t[0]=u,t[1]=e):e=t[1];let m=e;return t[2]!==o||t[3]!==m||t[4]!==d||t[5]!==c?(a=(0,n.jsx)(s.RadioGroupField,{disableModifyingForm:!0,field:{name:"theme",label:d("general:adminTheme"),options:[{label:d("general:automatic"),value:"auto"},{label:d("general:light"),value:"light"},{label:d("general:dark"),value:"dark"}]},onChange:m,path:"theme",value:o?"auto":c}),t[2]=o,t[3]=m,t[4]=d,t[5]=c,t[6]=a):a=t[6],a}},24404:(e,a,t)=>{t.d(a,{AccountClient:()=>s});var i=t(19749),n=t(92825),l=t(87677),r=t(12115);let s=()=>{let e,a,t=(0,i.c)(4),{setStepNav:s}=(0,n.useStepNav)(),{t:o}=(0,l.d)();return t[0]!==s||t[1]!==o?(e=()=>{let e=[];e.push({label:o("authentication:account"),url:"/account"}),s(e)},a=[s,o],t[0]=s,t[1]=o,t[2]=e,t[3]=a):(e=t[2],a=t[3]),r.useEffect(e,a),null}},58029:(e,a,t)=>{t.d(a,{ResetPasswordForm:()=>c});var i=t(19749),n=t(95155),l=t(87677),r=t(58028),s=t(92825),o=t(35695),u=t(6001);t(12115);let c=e=>{let a,t,c=(0,i.c)(12),{token:d}=e,m=(0,l.d)(),{config:h}=(0,r.b)(),{admin:p,routes:f,serverURL:g}=h,{routes:v,user:w}=p,{login:x}=v,{admin:j,api:b}=f,S=(0,o.useRouter)(),{fetchFullUser:F}=(0,s.useAuth)();c[0]!==j||c[1]!==F||c[2]!==S||c[3]!==x?(a=async()=>{await F()?S.push(j):S.push((0,u.Q)({adminRoute:j,path:x}))},c[0]=j,c[1]=F,c[2]=S,c[3]=x,c[4]=a):a=c[4];let y=a;return c[5]!==b||c[6]!==m||c[7]!==y||c[8]!==g||c[9]!==d||c[10]!==w?(t=(0,n.jsxs)(s.Form,{action:"".concat(g).concat(b,"/").concat(w,"/reset-password"),initialState:{"confirm-password":{initialValue:"",valid:!1,value:""},password:{initialValue:"",valid:!1,value:""},token:{initialValue:d,valid:!0,value:d}},method:"POST",onSuccess:y,children:[(0,n.jsxs)("div",{className:"inputWrap",children:[(0,n.jsx)(s.PasswordField,{field:{name:"password",label:m.t("authentication:newPassword"),required:!0},path:"password",schemaPath:"".concat(w,".password")}),(0,n.jsx)(s.ConfirmPasswordField,{}),(0,n.jsx)(s.HiddenField,{path:"token",schemaPath:"".concat(w,".token"),value:d})]}),(0,n.jsx)(s.FormSubmit,{size:"large",children:m.t("authentication:resetPassword")})]}),c[5]=b,c[6]=m,c[7]=y,c[8]=g,c[9]=d,c[10]=w,c[11]=t):t=c[11],t}},65582:(e,a,t)=>{t.d(a,{CreateFirstUserClient:()=>u});var i=t(95155),n=t(58028),l=t(92825),r=t(87677),s=t(78234),o=t(12115);let u=e=>{let{docPermissions:a,docPreferences:t,initialState:u,loginWithUsername:c,userSlug:d}=e,{config:{routes:{admin:m,api:h},serverURL:p},getEntityConfig:f}=(0,n.b)(),{getFormState:g}=(0,l.useServerFunctions)(),{t:v}=(0,r.d)(),{setUser:w}=(0,l.useAuth)(),x=o.useRef(null),j=f({collectionSlug:d}),b=o.useCallback(async e=>{let{formState:i,submitted:n}=e,l=(0,s.wN)(x),r=await g({collectionSlug:d,docPermissions:a,docPreferences:t,formState:i,operation:"create",schemaPath:d,signal:l.signal,skipValidation:!n});if(x.current=null,r&&r.state)return r.state},[d,g,a,t]);return(0,o.useEffect)(()=>{let e=x.current;return()=>{(0,s.eS)(e)}},[]),(0,i.jsxs)(l.Form,{action:"".concat(p).concat(h,"/").concat(d,"/first-register"),initialState:{...u,"confirm-password":{...u["confirm-password"],valid:u["confirm-password"].valid||!1,value:u["confirm-password"].value||""}},method:"POST",onChange:[b],onSuccess:e=>{w(e)},redirect:m,validationOperation:"create",children:[(0,i.jsx)(l.EmailAndUsernameFields,{className:"emailAndUsername",loginWithUsername:c,operation:"create",readOnly:!1,t:v}),(0,i.jsx)(l.PasswordField,{autoComplete:"off",field:{name:"password",label:v("authentication:newPassword"),required:!0},path:"password"}),(0,i.jsx)(l.ConfirmPasswordField,{}),(0,i.jsx)(l.RenderFields,{fields:j.fields,forceRender:!0,parentIndexPath:"",parentPath:"",parentSchemaPath:d,permissions:!0,readOnly:!1}),(0,i.jsx)(l.FormSubmit,{size:"large",children:v("general:create")})]})}},70685:(e,a,t)=>{t.d(a,{LoginForm:()=>h});var i=t(19749),n=t(95155),l=t(12115),r=t(58028),s=t(87677),o=t(92825),u=t(6001),c=t(48493);let d=e=>{let a=(0,i.c)(11),{type:t,required:l}=e,r=void 0===l||l,{t:u}=(0,s.d)();if("email"===t){let e;return a[0]!==r||a[1]!==u?(e=(0,n.jsx)(o.EmailField,{field:{name:"email",admin:{autoComplete:"email"},label:u("general:email"),required:r},path:"email",validate:c.Rp}),a[0]=r,a[1]=u,a[2]=e):e=a[2],e}if("username"===t){let e;return a[3]!==r||a[4]!==u?(e=(0,n.jsx)(o.TextField,{field:{name:"username",label:u("authentication:username"),required:r},path:"username",validate:c.Xh}),a[3]=r,a[4]=u,a[5]=e):e=a[5],e}if("emailOrUsername"===t){let e;if(a[6]!==r||a[7]!==u){let t;a[9]!==u?(t=(e,a)=>{let t=(0,c.Xh)(e,a),i=(0,c.Rp)(e,a);return!!i||!!t||"".concat(u("general:email"),": ").concat(i," ").concat(u("general:username"),": ").concat(t)},a[9]=u,a[10]=t):t=a[10],e=(0,n.jsx)(o.TextField,{field:{name:"username",label:u("authentication:emailOrUsername"),required:r},path:"username",validate:t}),a[6]=r,a[7]=u,a[8]=e}else e=a[8];return e}return null},m="login__form",h=e=>{let a,t,c,h,p=(0,i.c)(23),{prefillEmail:f,prefillPassword:g,prefillUsername:v,searchParams:w}=e,{config:x,getEntityConfig:j}=(0,r.b)(),{admin:b,routes:S}=x,{routes:F,user:y}=b,{forgot:P}=F,{admin:C,api:k}=S;if(p[0]!==j||p[1]!==y){let{auth:e}=j({collectionSlug:y});t=(e=>({canLoginWithEmail:!e||e.allowEmailLogin,canLoginWithUsername:!!e}))(a=e.loginWithUsername),p[0]=j,p[1]=y,p[2]=a,p[3]=t}else a=p[2],t=p[3];let{canLoginWithEmail:R,canLoginWithUsername:T}=t;p[4]!==R||p[5]!==T?(c=()=>R&&T?"emailOrUsername":T?"username":"email",p[4]=R,p[5]=T,p[6]=c):c=p[6];let[E]=l.useState(c),{t:W}=(0,s.d)(),{setUser:O}=(0,o.useAuth)(),_=null!=g?g:void 0,U=null!=g?g:void 0;if(p[7]!==C||p[8]!==k||p[9]!==P||p[10]!==E||p[11]!==a||p[12]!==f||p[13]!==v||p[14]!==(null==w?void 0:w.redirect)||p[15]!==O||p[16]!==W||p[17]!==_||p[18]!==U||p[19]!==y){let e,t={password:{initialValue:_,valid:!0,value:U}};a?t.username={initialValue:null!=v?v:void 0,valid:!0,value:null!=v?v:void 0}:t.email={initialValue:null!=f?f:void 0,valid:!0,value:null!=f?f:void 0},p[21]!==O?(e=e=>{O(e)},p[21]=O,p[22]=e):e=p[22];let i=e;h=(0,n.jsxs)(o.Form,{action:"".concat(k,"/").concat(y,"/login"),className:m,disableSuccessStatus:!0,initialState:t,method:"POST",onSuccess:i,redirect:(({allowAbsoluteUrls:e=!1,fallbackTo:a="/",redirectTo:t})=>{let i;if("string"!=typeof t)return a;try{i=decodeURIComponent(t.trim())}catch{return a}let n=i.startsWith("/")&&!i.startsWith("//")&&!i.startsWith("/%2F")&&!i.startsWith("/\\/")&&!i.startsWith("/\\\\")&&!i.startsWith("/\\")&&!i.toLowerCase().startsWith("/javascript:")&&!i.toLowerCase().startsWith("/http"),l=e&&/^https?:\/\/\S+$/i.test(i);return n||l?i:a})({fallbackTo:C,redirectTo:null==w?void 0:w.redirect}),waitForAutocomplete:!0,children:[(0,n.jsxs)("div",{className:"".concat(m,"__inputWrap"),children:[(0,n.jsx)(d,{type:E}),(0,n.jsx)(o.PasswordField,{field:{name:"password",label:W("general:password"),required:!0},path:"password"})]}),(0,n.jsx)(o.Link,{href:(0,u.Q)({adminRoute:C,path:P}),prefetch:!1,children:W("authentication:forgotPasswordQuestion")}),(0,n.jsx)(o.FormSubmit,{size:"large",children:W("authentication:login")})]}),p[7]=C,p[8]=k,p[9]=P,p[10]=E,p[11]=a,p[12]=f,p[13]=v,p[14]=null==w?void 0:w.redirect,p[15]=O,p[16]=W,p[17]=_,p[18]=U,p[19]=y,p[20]=h}else h=p[20];return h}},74289:(e,a,t)=>{t.d(a,{LogoutClient:()=>c});var i=t(19749),n=t(95155),l=t(92825),r=t(87677),s=t(35695),o=t(6001),u=t(12115);let c=e=>{let a,t,c,d,m,h=(0,i.c)(23),{adminRoute:p,inactivity:f,redirect:g}=e,{logOut:v,user:w}=(0,l.useAuth)(),{startRouteTransition:x}=(0,l.useRouteTransition)(),[j,b]=u.useState(!w),S=u.useRef(!1);h[0]!==p||h[1]!==f||h[2]!==g?(a=()=>(0,o.Q)({adminRoute:p,path:"/login".concat(f&&g&&g.length>0?"?redirect=".concat(encodeURIComponent(g)):"")}),h[0]=p,h[1]=f,h[2]=g,h[3]=a):a=h[3];let[F]=u.useState(a),{t:y}=(0,r.d)(),P=(0,s.useRouter)();h[4]!==f||h[5]!==v||h[6]!==F||h[7]!==P||h[8]!==x||h[9]!==y?(t=async()=>{let e=await v();if(b(e),!f&&e&&!S.current){l.toast.success(y("authentication:loggedOutSuccessfully")),S.current=!0,x(()=>P.push(F));return}},h[4]=f,h[5]=v,h[6]=F,h[7]=P,h[8]=x,h[9]=y,h[10]=t):t=h[10];let C=t;if(h[11]!==C||h[12]!==j||h[13]!==F||h[14]!==P||h[15]!==x?(c=()=>{j?x(()=>P.push(F)):C()},d=[C,j,F,P,x],h[11]=C,h[12]=j,h[13]=F,h[14]=P,h[15]=x,h[16]=c,h[17]=d):(c=h[16],d=h[17]),(0,u.useEffect)(c,d),j&&f){let e;return h[18]!==F||h[19]!==y?(e=(0,n.jsxs)("div",{className:"".concat("logout","__wrap"),children:[(0,n.jsx)("h2",{children:y("authentication:loggedOutInactivity")}),(0,n.jsx)(l.Button,{buttonStyle:"secondary",el:"link",size:"large",url:F,children:y("authentication:logBackIn")})]}),h[18]=F,h[19]=y,h[20]=e):e=h[20],e}return h[21]!==y?(m=(0,n.jsx)(l.LoadingOverlay,{animationDuration:"0ms",loadingText:y("authentication:loggingOut")}),h[21]=y,h[22]=m):m=h[22],m}},78229:(e,a,t)=>{t.d(a,{ForgotPasswordForm:()=>d});var i=t(19749),n=t(95155),l=t(58028),r=t(87677),s=t(92825),o=t(48493),u=t(12115);function c({description:e,heading:a}){return a?(0,n.jsxs)("div",{className:"form-header",children:[(0,n.jsx)("h1",{children:a}),!!e&&(0,n.jsx)("p",{children:e})]}):null}let d=()=>{var e;let a,t,d,m,h=(0,i.c)(17),{config:p,getEntityConfig:f}=(0,l.b)(),{admin:g,routes:v}=p,{user:w}=g,{api:x}=v,{t:j}=(0,r.d)(),[b,S]=(0,u.useState)(!1);h[0]!==f||h[1]!==w?(a=f({collectionSlug:w}),h[0]=f,h[1]=w,h[2]=a):a=h[2];let F=a,y=null==F||null==(e=F.auth)?void 0:e.loginWithUsername;h[3]!==y||h[4]!==j?(t=(e,a,t)=>{e.json().then(()=>{S(!0),a(j("general:submissionSuccessful"))}).catch(()=>{t(y?j("authentication:usernameNotValid"):j("authentication:emailNotValid"))})},h[3]=y,h[4]=j,h[5]=t):t=h[5];let P=t;if(h[6]!==x||h[7]!==p||h[8]!==P||h[9]!==b||h[10]!==y||h[11]!==j||h[12]!==w){m=Symbol.for("react.early_return_sentinel");e:{let e=y?{username:{initialValue:"",valid:!0,value:void 0}}:{email:{initialValue:"",valid:!0,value:void 0}};if(b){let e;h[15]!==j?(e=(0,n.jsx)(c,{description:j("authentication:checkYourEmailForPasswordReset"),heading:j("authentication:emailSent")}),h[15]=j,h[16]=e):e=h[16],m=e;break e}d=(0,n.jsxs)(s.Form,{action:"".concat(x,"/").concat(w,"/forgot-password"),handleResponse:P,initialState:e,method:"POST",children:[(0,n.jsx)(c,{description:y?j("authentication:forgotPasswordUsernameInstructions"):j("authentication:forgotPasswordEmailInstructions"),heading:j("authentication:forgotPassword")}),y?(0,n.jsx)(s.TextField,{field:{name:"username",label:j("authentication:username"),required:!0},path:"username",validate:e=>(0,o.Qq)(e,{name:"username",type:"text",blockData:{},data:{},event:"onChange",path:["username"],preferences:{fields:{}},req:{payload:{config:p},t:j},required:!0,siblingData:{}})}):(0,n.jsx)(s.EmailField,{field:{name:"email",admin:{autoComplete:"email"},label:j("general:email"),required:!0},path:"email",validate:e=>(0,o.Rp)(e,{name:"email",type:"email",blockData:{},data:{},event:"onChange",path:["email"],preferences:{fields:{}},req:{payload:{config:p},t:j},required:!0,siblingData:{}})}),(0,n.jsx)(s.FormSubmit,{size:"large",children:j("general:submit")})]})}h[6]=x,h[7]=p,h[8]=P,h[9]=b,h[10]=y,h[11]=j,h[12]=w,h[13]=d,h[14]=m}else d=h[13],m=h[14];return m!==Symbol.for("react.early_return_sentinel")?m:d}},87790:(e,a,t)=>{t.d(a,{LanguageSelector:()=>s});var i=t(19749),n=t(95155),l=t(87677),r=t(92825);t(12115);let s=e=>{let a,t,s=(0,i.c)(8),{languageOptions:o}=e,{i18n:u,switchLanguage:c}=(0,l.d)();if(s[0]!==c?(a=async e=>{await c(e.value)},s[0]=c,s[1]=a):a=s[1],s[2]!==u||s[3]!==o||s[4]!==a){let e;s[6]!==u?(e=e=>e.value===u.language,s[6]=u,s[7]=e):e=s[7],t=(0,n.jsx)(r.ReactSelect,{inputId:"language-select",isClearable:!1,onChange:a,options:o,value:o.find(e)}),s[2]=u,s[3]=o,s[4]=a,s[5]=t}else t=s[5];return t}},97260:(e,a,t)=>{t.d(a,{ToastAndRedirect:()=>s});var i=t(19749),n=t(92825),l=t(35695),r=t(12115);function s(e){let a,t,s=(0,i.c)(6),{message:o,redirectTo:u}=e,c=(0,l.useRouter)(),{startRouteTransition:d}=(0,n.useRouteTransition)(),m=r.useRef(!1);return s[0]!==o||s[1]!==u||s[2]!==c||s[3]!==d?(a=()=>{let e;return n.toast&&(e=setTimeout(()=>{n.toast.success(o),m.current=!0,d(()=>c.push(u))},100)),()=>{e&&clearTimeout(e)}},t=[c,u,o,d],s[0]=o,s[1]=u,s[2]=c,s[3]=d,s[4]=a,s[5]=t):(a=s[4],t=s[5]),(0,r.useEffect)(a,t),null}}}]);