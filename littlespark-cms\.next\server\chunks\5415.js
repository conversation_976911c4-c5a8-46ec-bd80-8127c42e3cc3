"use strict";exports.id=5415,exports.ids=[5415],exports.modules={72310:(e,t,i)=>{let{EMPTY_BUFFER:r}=i(93226),o=Buffer[Symbol.species];function s(e,t,i,r,o){for(let s=0;s<o;s++)i[r+s]=e[s]^t[3&s]}function n(e,t){for(let i=0;i<e.length;i++)e[i]^=t[3&i]}if(e.exports={concat:function(e,t){if(0===e.length)return r;if(1===e.length)return e[0];let i=Buffer.allocUnsafe(t),s=0;for(let t=0;t<e.length;t++){let r=e[t];i.set(r,s),s+=r.length}return s<t?new o(i.buffer,i.byteOffset,s):i},mask:s,toArrayBuffer:function(e){return e.length===e.buffer.byteLength?e.buffer:e.buffer.slice(e.byteOffset,e.byteOffset+e.length)},toBuffer:function e(t){let i;return(e.readOnly=!0,Buffer.isBuffer(t))?t:(t instanceof ArrayBuffer?i=new o(t):ArrayBuffer.isView(t)?i=new o(t.buffer,t.byteOffset,t.byteLength):(i=Buffer.from(t),e.readOnly=!1),i)},unmask:n},!process.env.WS_NO_BUFFER_UTIL)try{let t=i(39727);e.exports.mask=function(e,i,r,o,n){n<48?s(e,i,r,o,n):t.mask(e,i,r,o,n)},e.exports.unmask=function(e,i){e.length<32?n(e,i):t.unmask(e,i)}}catch(e){}},93226:e=>{let t=["nodebuffer","arraybuffer","fragments"],i="undefined"!=typeof Blob;i&&t.push("blob"),e.exports={BINARY_TYPES:t,EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",hasBlob:i,kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}},32573:(e,t,i)=>{let{kForOnEventAttribute:r,kListener:o}=i(93226),s=Symbol("kCode"),n=Symbol("kData"),a=Symbol("kError"),l=Symbol("kMessage"),c=Symbol("kReason"),h=Symbol("kTarget"),d=Symbol("kType"),f=Symbol("kWasClean");class u{constructor(e){this[h]=null,this[d]=e}get target(){return this[h]}get type(){return this[d]}}Object.defineProperty(u.prototype,"target",{enumerable:!0}),Object.defineProperty(u.prototype,"type",{enumerable:!0});class p extends u{constructor(e,t={}){super(e),this[s]=void 0===t.code?0:t.code,this[c]=void 0===t.reason?"":t.reason,this[f]=void 0!==t.wasClean&&t.wasClean}get code(){return this[s]}get reason(){return this[c]}get wasClean(){return this[f]}}Object.defineProperty(p.prototype,"code",{enumerable:!0}),Object.defineProperty(p.prototype,"reason",{enumerable:!0}),Object.defineProperty(p.prototype,"wasClean",{enumerable:!0});class m extends u{constructor(e,t={}){super(e),this[a]=void 0===t.error?null:t.error,this[l]=void 0===t.message?"":t.message}get error(){return this[a]}get message(){return this[l]}}Object.defineProperty(m.prototype,"error",{enumerable:!0}),Object.defineProperty(m.prototype,"message",{enumerable:!0});class g extends u{constructor(e,t={}){super(e),this[n]=void 0===t.data?null:t.data}get data(){return this[n]}}function _(e,t,i){"object"==typeof e&&e.handleEvent?e.handleEvent.call(e,i):e.call(t,i)}Object.defineProperty(g.prototype,"data",{enumerable:!0}),e.exports={CloseEvent:p,ErrorEvent:m,Event:u,EventTarget:{addEventListener(e,t,i={}){let s;for(let s of this.listeners(e))if(!i[r]&&s[o]===t&&!s[r])return;if("message"===e)s=function(e,i){let r=new g("message",{data:i?e:e.toString()});r[h]=this,_(t,this,r)};else if("close"===e)s=function(e,i){let r=new p("close",{code:e,reason:i.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});r[h]=this,_(t,this,r)};else if("error"===e)s=function(e){let i=new m("error",{error:e,message:e.message});i[h]=this,_(t,this,i)};else{if("open"!==e)return;s=function(){let e=new u("open");e[h]=this,_(t,this,e)}}s[r]=!!i[r],s[o]=t,i.once?this.once(e,s):this.on(e,s)},removeEventListener(e,t){for(let i of this.listeners(e))if(i[o]===t&&!i[r]){this.removeListener(e,i);break}}},MessageEvent:g}},7450:(e,t,i)=>{let{tokenChars:r}=i(88704);function o(e,t,i){void 0===e[t]?e[t]=[i]:e[t].push(i)}e.exports={format:function(e){return Object.keys(e).map(t=>{let i=e[t];return Array.isArray(i)||(i=[i]),i.map(e=>[t].concat(Object.keys(e).map(t=>{let i=e[t];return Array.isArray(i)||(i=[i]),i.map(e=>!0===e?t:`${t}=${e}`).join("; ")})).join("; ")).join(", ")}).join(", ")},parse:function(e){let t,i;let s=Object.create(null),n=Object.create(null),a=!1,l=!1,c=!1,h=-1,d=-1,f=-1,u=0;for(;u<e.length;u++)if(d=e.charCodeAt(u),void 0===t){if(-1===f&&1===r[d])-1===h&&(h=u);else if(0!==u&&(32===d||9===d))-1===f&&-1!==h&&(f=u);else if(59===d||44===d){if(-1===h)throw SyntaxError(`Unexpected character at index ${u}`);-1===f&&(f=u);let i=e.slice(h,f);44===d?(o(s,i,n),n=Object.create(null)):t=i,h=f=-1}else throw SyntaxError(`Unexpected character at index ${u}`)}else if(void 0===i){if(-1===f&&1===r[d])-1===h&&(h=u);else if(32===d||9===d)-1===f&&-1!==h&&(f=u);else if(59===d||44===d){if(-1===h)throw SyntaxError(`Unexpected character at index ${u}`);-1===f&&(f=u),o(n,e.slice(h,f),!0),44===d&&(o(s,t,n),n=Object.create(null),t=void 0),h=f=-1}else if(61===d&&-1!==h&&-1===f)i=e.slice(h,u),h=f=-1;else throw SyntaxError(`Unexpected character at index ${u}`)}else if(l){if(1!==r[d])throw SyntaxError(`Unexpected character at index ${u}`);-1===h?h=u:a||(a=!0),l=!1}else if(c){if(1===r[d])-1===h&&(h=u);else if(34===d&&-1!==h)c=!1,f=u;else if(92===d)l=!0;else throw SyntaxError(`Unexpected character at index ${u}`)}else if(34===d&&61===e.charCodeAt(u-1))c=!0;else if(-1===f&&1===r[d])-1===h&&(h=u);else if(-1!==h&&(32===d||9===d))-1===f&&(f=u);else if(59===d||44===d){if(-1===h)throw SyntaxError(`Unexpected character at index ${u}`);-1===f&&(f=u);let r=e.slice(h,f);a&&(r=r.replace(/\\/g,""),a=!1),o(n,i,r),44===d&&(o(s,t,n),n=Object.create(null),t=void 0),i=void 0,h=f=-1}else throw SyntaxError(`Unexpected character at index ${u}`);if(-1===h||c||32===d||9===d)throw SyntaxError("Unexpected end of input");-1===f&&(f=u);let p=e.slice(h,f);return void 0===t?o(s,p,n):(void 0===i?o(n,p,!0):a?o(n,i,p.replace(/\\/g,"")):o(n,i,p),o(s,t,n)),s}}},68963:e=>{let t=Symbol("kDone"),i=Symbol("kRun");class r{constructor(e){this[t]=()=>{this.pending--,this[i]()},this.concurrency=e||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[i]()}[i](){if(this.pending!==this.concurrency&&this.jobs.length){let e=this.jobs.shift();this.pending++,e(this[t])}}}e.exports=r},62911:(e,t,i)=>{let r;let o=i(74075),s=i(72310),n=i(68963),{kStatusCode:a}=i(93226),l=Buffer[Symbol.species],c=Buffer.from([0,0,255,255]),h=Symbol("permessage-deflate"),d=Symbol("total-length"),f=Symbol("callback"),u=Symbol("buffers"),p=Symbol("error");class m{constructor(e,t,i){this._maxPayload=0|i,this._options=e||{},this._threshold=void 0!==this._options.threshold?this._options.threshold:1024,this._isServer=!!t,this._deflate=null,this._inflate=null,this.params=null,r||(r=new n(void 0!==this._options.concurrencyLimit?this._options.concurrencyLimit:10))}static get extensionName(){return"permessage-deflate"}offer(){let e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:null==this._options.clientMaxWindowBits&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let e=this._deflate[f];this._deflate.close(),this._deflate=null,e&&e(Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){let t=this._options,i=e.find(e=>(!1!==t.serverNoContextTakeover||!e.server_no_context_takeover)&&(!e.server_max_window_bits||!1!==t.serverMaxWindowBits&&("number"!=typeof t.serverMaxWindowBits||!(t.serverMaxWindowBits>e.server_max_window_bits)))&&("number"!=typeof t.clientMaxWindowBits||!!e.client_max_window_bits));if(!i)throw Error("None of the extension offers can be accepted");return t.serverNoContextTakeover&&(i.server_no_context_takeover=!0),t.clientNoContextTakeover&&(i.client_no_context_takeover=!0),"number"==typeof t.serverMaxWindowBits&&(i.server_max_window_bits=t.serverMaxWindowBits),"number"==typeof t.clientMaxWindowBits?i.client_max_window_bits=t.clientMaxWindowBits:(!0===i.client_max_window_bits||!1===t.clientMaxWindowBits)&&delete i.client_max_window_bits,i}acceptAsClient(e){let t=e[0];if(!1===this._options.clientNoContextTakeover&&t.client_no_context_takeover)throw Error('Unexpected parameter "client_no_context_takeover"');if(t.client_max_window_bits){if(!1===this._options.clientMaxWindowBits||"number"==typeof this._options.clientMaxWindowBits&&t.client_max_window_bits>this._options.clientMaxWindowBits)throw Error('Unexpected or invalid parameter "client_max_window_bits"')}else"number"==typeof this._options.clientMaxWindowBits&&(t.client_max_window_bits=this._options.clientMaxWindowBits);return t}normalizeParams(e){return e.forEach(e=>{Object.keys(e).forEach(t=>{let i=e[t];if(i.length>1)throw Error(`Parameter "${t}" must have only a single value`);if(i=i[0],"client_max_window_bits"===t){if(!0!==i){let e=+i;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${i}`);i=e}else if(!this._isServer)throw TypeError(`Invalid value for parameter "${t}": ${i}`)}else if("server_max_window_bits"===t){let e=+i;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${i}`);i=e}else if("client_no_context_takeover"===t||"server_no_context_takeover"===t){if(!0!==i)throw TypeError(`Invalid value for parameter "${t}": ${i}`)}else throw Error(`Unknown parameter "${t}"`);e[t]=i})}),e}decompress(e,t,i){r.add(r=>{this._decompress(e,t,(e,t)=>{r(),i(e,t)})})}compress(e,t,i){r.add(r=>{this._compress(e,t,(e,t)=>{r(),i(e,t)})})}_decompress(e,t,i){let r=this._isServer?"client":"server";if(!this._inflate){let e=`${r}_max_window_bits`,t="number"!=typeof this.params[e]?o.Z_DEFAULT_WINDOWBITS:this.params[e];this._inflate=o.createInflateRaw({...this._options.zlibInflateOptions,windowBits:t}),this._inflate[h]=this,this._inflate[d]=0,this._inflate[u]=[],this._inflate.on("error",y),this._inflate.on("data",_)}this._inflate[f]=i,this._inflate.write(e),t&&this._inflate.write(c),this._inflate.flush(()=>{let e=this._inflate[p];if(e){this._inflate.close(),this._inflate=null,i(e);return}let o=s.concat(this._inflate[u],this._inflate[d]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[d]=0,this._inflate[u]=[],t&&this.params[`${r}_no_context_takeover`]&&this._inflate.reset()),i(null,o)})}_compress(e,t,i){let r=this._isServer?"server":"client";if(!this._deflate){let e=`${r}_max_window_bits`,t="number"!=typeof this.params[e]?o.Z_DEFAULT_WINDOWBITS:this.params[e];this._deflate=o.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:t}),this._deflate[d]=0,this._deflate[u]=[],this._deflate.on("data",g)}this._deflate[f]=i,this._deflate.write(e),this._deflate.flush(o.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let e=s.concat(this._deflate[u],this._deflate[d]);t&&(e=new l(e.buffer,e.byteOffset,e.length-4)),this._deflate[f]=null,this._deflate[d]=0,this._deflate[u]=[],t&&this.params[`${r}_no_context_takeover`]&&this._deflate.reset(),i(null,e)})}}function g(e){this[u].push(e),this[d]+=e.length}function _(e){if(this[d]+=e.length,this[h]._maxPayload<1||this[d]<=this[h]._maxPayload){this[u].push(e);return}this[p]=RangeError("Max payload size exceeded"),this[p].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[p][a]=1009,this.removeListener("data",_),this.reset()}function y(e){if(this[h]._inflate=null,this[p]){this[f](this[p]);return}e[a]=1007,this[f](e)}e.exports=m},57522:(e,t,i)=>{let{Writable:r}=i(27910),o=i(62911),{BINARY_TYPES:s,EMPTY_BUFFER:n,kStatusCode:a,kWebSocket:l}=i(93226),{concat:c,toArrayBuffer:h,unmask:d}=i(72310),{isValidStatusCode:f,isValidUTF8:u}=i(88704),p=Buffer[Symbol.species];class m extends r{constructor(e={}){super(),this._allowSynchronousEvents=void 0===e.allowSynchronousEvents||e.allowSynchronousEvents,this._binaryType=e.binaryType||s[0],this._extensions=e.extensions||{},this._isServer=!!e.isServer,this._maxPayload=0|e.maxPayload,this._skipUTF8Validation=!!e.skipUTF8Validation,this[l]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._errored=!1,this._loop=!1,this._state=0}_write(e,t,i){if(8===this._opcode&&0==this._state)return i();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(i)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){let t=this._buffers[0];return this._buffers[0]=new p(t.buffer,t.byteOffset+e,t.length-e),new p(t.buffer,t.byteOffset,e)}let t=Buffer.allocUnsafe(e);do{let i=this._buffers[0],r=t.length-e;e>=i.length?t.set(this._buffers.shift(),r):(t.set(new Uint8Array(i.buffer,i.byteOffset,e),r),this._buffers[0]=new p(i.buffer,i.byteOffset+e,i.length-e)),e-=i.length}while(e>0);return t}startLoop(e){this._loop=!0;do switch(this._state){case 0:this.getInfo(e);break;case 1:this.getPayloadLength16(e);break;case 2:this.getPayloadLength64(e);break;case 3:this.getMask();break;case 4:this.getData(e);break;case 5:case 6:this._loop=!1;return}while(this._loop);this._errored||e()}getInfo(e){if(this._bufferedBytes<2){this._loop=!1;return}let t=this.consume(2);if((48&t[0])!=0){e(this.createError(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3"));return}let i=(64&t[0])==64;if(i&&!this._extensions[o.extensionName]){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(this._fin=(128&t[0])==128,this._opcode=15&t[0],this._payloadLength=127&t[1],0===this._opcode){if(i){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(!this._fragmented){e(this.createError(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE"));return}this._opcode=this._fragmented}else if(1===this._opcode||2===this._opcode){if(this._fragmented){e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));return}this._compressed=i}else if(this._opcode>7&&this._opcode<11){if(!this._fin){e(this.createError(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN"));return}if(i){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(this._payloadLength>125||8===this._opcode&&1===this._payloadLength){e(this.createError(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH"));return}}else{e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));return}if(this._fin||this._fragmented||(this._fragmented=this._opcode),this._masked=(128&t[1])==128,this._isServer){if(!this._masked){e(this.createError(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK"));return}}else if(this._masked){e(this.createError(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK"));return}126===this._payloadLength?this._state=1:127===this._payloadLength?this._state=2:this.haveLength(e)}getPayloadLength16(e){if(this._bufferedBytes<2){this._loop=!1;return}this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength(e)}getPayloadLength64(e){if(this._bufferedBytes<8){this._loop=!1;return}let t=this.consume(8),i=t.readUInt32BE(0);if(i>2097151){e(this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH"));return}this._payloadLength=0x100000000*i+t.readUInt32BE(4),this.haveLength(e)}haveLength(e){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0)){e(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));return}this._masked?this._state=3:this._state=4}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=4}getData(e){let t=n;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}t=this.consume(this._payloadLength),this._masked&&(this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3])!=0&&d(t,this._mask)}if(this._opcode>7){this.controlMessage(t,e);return}if(this._compressed){this._state=5,this.decompress(t,e);return}t.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(t)),this.dataMessage(e)}decompress(e,t){this._extensions[o.extensionName].decompress(e,this._fin,(e,i)=>{if(e)return t(e);if(i.length){if(this._messageLength+=i.length,this._messageLength>this._maxPayload&&this._maxPayload>0){t(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));return}this._fragments.push(i)}this.dataMessage(t),0===this._state&&this.startLoop(t)})}dataMessage(e){if(!this._fin){this._state=0;return}let t=this._messageLength,i=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],2===this._opcode){let r;r="nodebuffer"===this._binaryType?c(i,t):"arraybuffer"===this._binaryType?h(c(i,t)):"blob"===this._binaryType?new Blob(i):i,this._allowSynchronousEvents?(this.emit("message",r,!0),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",r,!0),this._state=0,this.startLoop(e)}))}else{let r=c(i,t);if(!this._skipUTF8Validation&&!u(r)){e(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));return}5===this._state||this._allowSynchronousEvents?(this.emit("message",r,!1),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",r,!1),this._state=0,this.startLoop(e)}))}}controlMessage(e,t){if(8===this._opcode){if(0===e.length)this._loop=!1,this.emit("conclude",1005,n),this.end();else{let i=e.readUInt16BE(0);if(!f(i)){t(this.createError(RangeError,`invalid status code ${i}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE"));return}let r=new p(e.buffer,e.byteOffset+2,e.length-2);if(!this._skipUTF8Validation&&!u(r)){t(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));return}this._loop=!1,this.emit("conclude",i,r),this.end()}this._state=0;return}this._allowSynchronousEvents?(this.emit(9===this._opcode?"ping":"pong",e),this._state=0):(this._state=6,setImmediate(()=>{this.emit(9===this._opcode?"ping":"pong",e),this._state=0,this.startLoop(t)}))}createError(e,t,i,r,o){this._loop=!1,this._errored=!0;let s=new e(i?`Invalid WebSocket frame: ${t}`:t);return Error.captureStackTrace(s,this.createError),s.code=o,s[a]=r,s}}e.exports=m},25870:(e,t,i)=>{let r;let{Duplex:o}=i(27910),{randomFillSync:s}=i(55511),n=i(62911),{EMPTY_BUFFER:a,kWebSocket:l,NOOP:c}=i(93226),{isBlob:h,isValidStatusCode:d}=i(88704),{mask:f,toBuffer:u}=i(72310),p=Symbol("kByteLength"),m=Buffer.alloc(4),g=8192;class _{constructor(e,t,i){this._extensions=t||{},i&&(this._generateMask=i,this._maskBuffer=Buffer.alloc(4)),this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._queue=[],this._state=0,this.onerror=c,this[l]=void 0}static frame(e,t){let i,o;let n=!1,a=2,l=!1;t.mask&&(i=t.maskBuffer||m,t.generateMask?t.generateMask(i):(8192===g&&(void 0===r&&(r=Buffer.alloc(8192)),s(r,0,8192),g=0),i[0]=r[g++],i[1]=r[g++],i[2]=r[g++],i[3]=r[g++]),l=(i[0]|i[1]|i[2]|i[3])==0,a=6),"string"==typeof e?o=(!t.mask||l)&&void 0!==t[p]?t[p]:(e=Buffer.from(e)).length:(o=e.length,n=t.mask&&t.readOnly&&!l);let c=o;o>=65536?(a+=8,c=127):o>125&&(a+=2,c=126);let h=Buffer.allocUnsafe(n?o+a:a);return(h[0]=t.fin?128|t.opcode:t.opcode,t.rsv1&&(h[0]|=64),h[1]=c,126===c?h.writeUInt16BE(o,2):127===c&&(h[2]=h[3]=0,h.writeUIntBE(o,4,6)),t.mask)?(h[1]|=128,h[a-4]=i[0],h[a-3]=i[1],h[a-2]=i[2],h[a-1]=i[3],l)?[h,e]:n?(f(e,i,h,a,o),[h]):(f(e,i,e,0,o),[h,e]):[h,e]}close(e,t,i,r){let o;if(void 0===e)o=a;else if("number"==typeof e&&d(e)){if(void 0!==t&&t.length){let i=Buffer.byteLength(t);if(i>123)throw RangeError("The message must not be greater than 123 bytes");(o=Buffer.allocUnsafe(2+i)).writeUInt16BE(e,0),"string"==typeof t?o.write(t,2):o.set(t,2)}else(o=Buffer.allocUnsafe(2)).writeUInt16BE(e,0)}else throw TypeError("First argument must be a valid error code number");let s={[p]:o.length,fin:!0,generateMask:this._generateMask,mask:i,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};0!==this._state?this.enqueue([this.dispatch,o,!1,s,r]):this.sendFrame(_.frame(o,s),r)}ping(e,t,i){let r,o;if("string"==typeof e?(r=Buffer.byteLength(e),o=!1):h(e)?(r=e.size,o=!1):(r=(e=u(e)).length,o=u.readOnly),r>125)throw RangeError("The data size must not be greater than 125 bytes");let s={[p]:r,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:9,readOnly:o,rsv1:!1};h(e)?0!==this._state?this.enqueue([this.getBlobData,e,!1,s,i]):this.getBlobData(e,!1,s,i):0!==this._state?this.enqueue([this.dispatch,e,!1,s,i]):this.sendFrame(_.frame(e,s),i)}pong(e,t,i){let r,o;if("string"==typeof e?(r=Buffer.byteLength(e),o=!1):h(e)?(r=e.size,o=!1):(r=(e=u(e)).length,o=u.readOnly),r>125)throw RangeError("The data size must not be greater than 125 bytes");let s={[p]:r,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:10,readOnly:o,rsv1:!1};h(e)?0!==this._state?this.enqueue([this.getBlobData,e,!1,s,i]):this.getBlobData(e,!1,s,i):0!==this._state?this.enqueue([this.dispatch,e,!1,s,i]):this.sendFrame(_.frame(e,s),i)}send(e,t,i){let r,o;let s=this._extensions[n.extensionName],a=t.binary?2:1,l=t.compress;"string"==typeof e?(r=Buffer.byteLength(e),o=!1):h(e)?(r=e.size,o=!1):(r=(e=u(e)).length,o=u.readOnly),this._firstFragment?(this._firstFragment=!1,l&&s&&s.params[s._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(l=r>=s._threshold),this._compress=l):(l=!1,a=0),t.fin&&(this._firstFragment=!0);let c={[p]:r,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:a,readOnly:o,rsv1:l};h(e)?0!==this._state?this.enqueue([this.getBlobData,e,this._compress,c,i]):this.getBlobData(e,this._compress,c,i):0!==this._state?this.enqueue([this.dispatch,e,this._compress,c,i]):this.dispatch(e,this._compress,c,i)}getBlobData(e,t,i,r){this._bufferedBytes+=i[p],this._state=2,e.arrayBuffer().then(e=>{if(this._socket.destroyed){let e=Error("The socket was closed while the blob was being read");process.nextTick(y,this,e,r);return}this._bufferedBytes-=i[p];let o=u(e);t?this.dispatch(o,t,i,r):(this._state=0,this.sendFrame(_.frame(o,i),r),this.dequeue())}).catch(e=>{process.nextTick(w,this,e,r)})}dispatch(e,t,i,r){if(!t){this.sendFrame(_.frame(e,i),r);return}let o=this._extensions[n.extensionName];this._bufferedBytes+=i[p],this._state=1,o.compress(e,i.fin,(e,t)=>{if(this._socket.destroyed){y(this,Error("The socket was closed while data was being compressed"),r);return}this._bufferedBytes-=i[p],this._state=0,i.readOnly=!1,this.sendFrame(_.frame(t,i),r),this.dequeue()})}dequeue(){for(;0===this._state&&this._queue.length;){let e=this._queue.shift();this._bufferedBytes-=e[3][p],Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][p],this._queue.push(e)}sendFrame(e,t){2===e.length?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],t),this._socket.uncork()):this._socket.write(e[0],t)}}function y(e,t,i){"function"==typeof i&&i(t);for(let i=0;i<e._queue.length;i++){let r=e._queue[i],o=r[r.length-1];"function"==typeof o&&o(t)}}function w(e,t,i){y(e,t,i),e.onerror(t)}e.exports=_},40883:(e,t,i)=>{i(36852);let{Duplex:r}=i(27910);function o(e){e.emit("close")}function s(){!this.destroyed&&this._writableState.finished&&this.destroy()}function n(e){this.removeListener("error",n),this.destroy(),0===this.listenerCount("error")&&this.emit("error",e)}e.exports=function(e,t){let i=!0,a=new r({...t,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return e.on("message",function(t,i){let r=!i&&a._readableState.objectMode?t.toString():t;a.push(r)||e.pause()}),e.once("error",function(e){a.destroyed||(i=!1,a.destroy(e))}),e.once("close",function(){a.destroyed||a.push(null)}),a._destroy=function(t,r){if(e.readyState===e.CLOSED){r(t),process.nextTick(o,a);return}let s=!1;e.once("error",function(e){s=!0,r(e)}),e.once("close",function(){s||r(t),process.nextTick(o,a)}),i&&e.terminate()},a._final=function(t){if(e.readyState===e.CONNECTING){e.once("open",function(){a._final(t)});return}null!==e._socket&&(e._socket._writableState.finished?(t(),a._readableState.endEmitted&&a.destroy()):(e._socket.once("finish",function(){t()}),e.close()))},a._read=function(){e.isPaused&&e.resume()},a._write=function(t,i,r){if(e.readyState===e.CONNECTING){e.once("open",function(){a._write(t,i,r)});return}e.send(t,r)},a.on("end",s),a.on("error",n),a}},97669:(e,t,i)=>{let{tokenChars:r}=i(88704);e.exports={parse:function(e){let t=new Set,i=-1,o=-1,s=0;for(;s<e.length;s++){let n=e.charCodeAt(s);if(-1===o&&1===r[n])-1===i&&(i=s);else if(0!==s&&(32===n||9===n))-1===o&&-1!==i&&(o=s);else if(44===n){if(-1===i)throw SyntaxError(`Unexpected character at index ${s}`);-1===o&&(o=s);let r=e.slice(i,o);if(t.has(r))throw SyntaxError(`The "${r}" subprotocol is duplicated`);t.add(r),i=o=-1}else throw SyntaxError(`Unexpected character at index ${s}`)}if(-1===i||-1!==o)throw SyntaxError("Unexpected end of input");let n=e.slice(i,s);if(t.has(n))throw SyntaxError(`The "${n}" subprotocol is duplicated`);return t.add(n),t}}},88704:(e,t,i)=>{let{isUtf8:r}=i(79428),{hasBlob:o}=i(93226);function s(e){let t=e.length,i=0;for(;i<t;)if((128&e[i])==0)i++;else if((224&e[i])==192){if(i+1===t||(192&e[i+1])!=128||(254&e[i])==192)return!1;i+=2}else if((240&e[i])==224){if(i+2>=t||(192&e[i+1])!=128||(192&e[i+2])!=128||224===e[i]&&(224&e[i+1])==128||237===e[i]&&(224&e[i+1])==160)return!1;i+=3}else{if((248&e[i])!=240||i+3>=t||(192&e[i+1])!=128||(192&e[i+2])!=128||(192&e[i+3])!=128||240===e[i]&&(240&e[i+1])==128||244===e[i]&&e[i+1]>143||e[i]>244)return!1;i+=4}return!0}if(e.exports={isBlob:function(e){return o&&"object"==typeof e&&"function"==typeof e.arrayBuffer&&"string"==typeof e.type&&"function"==typeof e.stream&&("Blob"===e[Symbol.toStringTag]||"File"===e[Symbol.toStringTag])},isValidStatusCode:function(e){return e>=1e3&&e<=1014&&1004!==e&&1005!==e&&1006!==e||e>=3e3&&e<=4999},isValidUTF8:s,tokenChars:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0]},r)e.exports.isValidUTF8=function(e){return e.length<24?s(e):r(e)};else if(!process.env.WS_NO_UTF_8_VALIDATE)try{let t=i(47990);e.exports.isValidUTF8=function(e){return e.length<32?s(e):t(e)}}catch(e){}},4902:(e,t,i)=>{let r=i(94735),o=i(81630),{Duplex:s}=i(27910),{createHash:n}=i(55511),a=i(7450),l=i(62911),c=i(97669),h=i(36852),{GUID:d,kWebSocket:f}=i(93226),u=/^[+/0-9A-Za-z]{22}==$/;class p extends r{constructor(e,t){if(super(),null==(e={allowSynchronousEvents:!0,autoPong:!0,maxPayload:0x6400000,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:h,...e}).port&&!e.server&&!e.noServer||null!=e.port&&(e.server||e.noServer)||e.server&&e.noServer)throw TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(null!=e.port?(this._server=o.createServer((e,t)=>{let i=o.STATUS_CODES[426];t.writeHead(426,{"Content-Length":i.length,"Content-Type":"text/plain"}),t.end(i)}),this._server.listen(e.port,e.host,e.backlog,t)):e.server&&(this._server=e.server),this._server){let e=this.emit.bind(this,"connection");this._removeListeners=function(e,t){for(let i of Object.keys(t))e.on(i,t[i]);return function(){for(let i of Object.keys(t))e.removeListener(i,t[i])}}(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(t,i,r)=>{this.handleUpgrade(t,i,r,e)}})}!0===e.perMessageDeflate&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=e,this._state=0}address(){if(this.options.noServer)throw Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(2===this._state){e&&this.once("close",()=>{e(Error("The server is not running"))}),process.nextTick(m,this);return}if(e&&this.once("close",e),1!==this._state){if(this._state=1,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients&&this.clients.size?this._shouldEmitClose=!0:process.nextTick(m,this);else{let e=this._server;this._removeListeners(),this._removeListeners=this._server=null,e.close(()=>{m(this)})}}}shouldHandle(e){if(this.options.path){let t=e.url.indexOf("?");if((-1!==t?e.url.slice(0,t):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,t,i,r){t.on("error",g);let o=e.headers["sec-websocket-key"],s=e.headers.upgrade,n=+e.headers["sec-websocket-version"];if("GET"!==e.method){y(this,e,t,405,"Invalid HTTP method");return}if(void 0===s||"websocket"!==s.toLowerCase()){y(this,e,t,400,"Invalid Upgrade header");return}if(void 0===o||!u.test(o)){y(this,e,t,400,"Missing or invalid Sec-WebSocket-Key header");return}if(13!==n&&8!==n){y(this,e,t,400,"Missing or invalid Sec-WebSocket-Version header",{"Sec-WebSocket-Version":"13, 8"});return}if(!this.shouldHandle(e)){_(t,400);return}let h=e.headers["sec-websocket-protocol"],d=new Set;if(void 0!==h)try{d=c.parse(h)}catch(i){y(this,e,t,400,"Invalid Sec-WebSocket-Protocol header");return}let f=e.headers["sec-websocket-extensions"],p={};if(this.options.perMessageDeflate&&void 0!==f){let i=new l(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let e=a.parse(f);e[l.extensionName]&&(i.accept(e[l.extensionName]),p[l.extensionName]=i)}catch(i){y(this,e,t,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){let s={origin:e.headers[`${8===n?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(2===this.options.verifyClient.length){this.options.verifyClient(s,(s,n,a,l)=>{if(!s)return _(t,n||401,a,l);this.completeUpgrade(p,o,d,e,t,i,r)});return}if(!this.options.verifyClient(s))return _(t,401)}this.completeUpgrade(p,o,d,e,t,i,r)}completeUpgrade(e,t,i,r,o,s,c){if(!o.readable||!o.writable)return o.destroy();if(o[f])throw Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>0)return _(o,503);let h=n("sha1").update(t+d).digest("base64"),u=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${h}`],p=new this.options.WebSocket(null,void 0,this.options);if(i.size){let e=this.options.handleProtocols?this.options.handleProtocols(i,r):i.values().next().value;e&&(u.push(`Sec-WebSocket-Protocol: ${e}`),p._protocol=e)}if(e[l.extensionName]){let t=e[l.extensionName].params,i=a.format({[l.extensionName]:[t]});u.push(`Sec-WebSocket-Extensions: ${i}`),p._extensions=e}this.emit("headers",u,r),o.write(u.concat("\r\n").join("\r\n")),o.removeListener("error",g),p.setSocket(o,s,{allowSynchronousEvents:this.options.allowSynchronousEvents,maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(p),p.on("close",()=>{this.clients.delete(p),this._shouldEmitClose&&!this.clients.size&&process.nextTick(m,this)})),c(p,r)}}function m(e){e._state=2,e.emit("close")}function g(){this.destroy()}function _(e,t,i,r){i=i||o.STATUS_CODES[t],r={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(i),...r},e.once("finish",e.destroy),e.end(`HTTP/1.1 ${t} ${o.STATUS_CODES[t]}\r
`+Object.keys(r).map(e=>`${e}: ${r[e]}`).join("\r\n")+"\r\n\r\n"+i)}function y(e,t,i,r,o,s){if(e.listenerCount("wsClientError")){let r=Error(o);Error.captureStackTrace(r,y),e.emit("wsClientError",r,i,t)}else _(i,r,o,s)}e.exports=p},36852:(e,t,i)=>{let r=i(94735),o=i(55591),s=i(81630),n=i(91645),a=i(34631),{randomBytes:l,createHash:c}=i(55511),{Duplex:h,Readable:d}=i(27910),{URL:f}=i(79551),u=i(62911),p=i(57522),m=i(25870),{isBlob:g}=i(88704),{BINARY_TYPES:_,EMPTY_BUFFER:y,GUID:w,kForOnEventAttribute:b,kListener:v,kStatusCode:S,kWebSocket:k,NOOP:E}=i(93226),{EventTarget:{addEventListener:x,removeEventListener:T}}=i(32573),{format:L,parse:O}=i(7450),{toBuffer:C}=i(72310),P=Symbol("kAborted"),N=[8,13],M=["CONNECTING","OPEN","CLOSING","CLOSED"],I=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/;class A extends r{constructor(e,t,i){super(),this._binaryType=_[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=y,this._closeTimer=null,this._errorEmitted=!1,this._extensions={},this._paused=!1,this._protocol="",this._readyState=A.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,null!==e?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,void 0===t?t=[]:Array.isArray(t)||("object"==typeof t&&null!==t?(i=t,t=[]):t=[t]),function e(t,i,r,n){let a,h,d,p;let m={allowSynchronousEvents:!0,autoPong:!0,protocolVersion:N[1],maxPayload:0x6400000,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...n,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0};if(t._autoPong=m.autoPong,!N.includes(m.protocolVersion))throw RangeError(`Unsupported protocol version: ${m.protocolVersion} (supported versions: ${N.join(", ")})`);if(i instanceof f)a=i;else try{a=new f(i)}catch(e){throw SyntaxError(`Invalid URL: ${i}`)}"http:"===a.protocol?a.protocol="ws:":"https:"===a.protocol&&(a.protocol="wss:"),t._url=a.href;let g="wss:"===a.protocol,_="ws+unix:"===a.protocol;if("ws:"===a.protocol||g||_?_&&!a.pathname?h="The URL's pathname is empty":a.hash&&(h="The URL contains a fragment identifier"):h='The URL\'s protocol must be one of "ws:", "wss:", "http:", "https:", or "ws+unix:"',h){let e=SyntaxError(h);if(0===t._redirects)throw e;B(t,e);return}let y=g?443:80,b=l(16).toString("base64"),v=g?o.request:s.request,S=new Set;if(m.createConnection=m.createConnection||(g?R:D),m.defaultPort=m.defaultPort||y,m.port=a.port||y,m.host=a.hostname.startsWith("[")?a.hostname.slice(1,-1):a.hostname,m.headers={...m.headers,"Sec-WebSocket-Version":m.protocolVersion,"Sec-WebSocket-Key":b,Connection:"Upgrade",Upgrade:"websocket"},m.path=a.pathname+a.search,m.timeout=m.handshakeTimeout,m.perMessageDeflate&&(d=new u(!0!==m.perMessageDeflate?m.perMessageDeflate:{},!1,m.maxPayload),m.headers["Sec-WebSocket-Extensions"]=L({[u.extensionName]:d.offer()})),r.length){for(let e of r){if("string"!=typeof e||!I.test(e)||S.has(e))throw SyntaxError("An invalid or duplicated subprotocol was specified");S.add(e)}m.headers["Sec-WebSocket-Protocol"]=r.join(",")}if(m.origin&&(m.protocolVersion<13?m.headers["Sec-WebSocket-Origin"]=m.origin:m.headers.Origin=m.origin),(a.username||a.password)&&(m.auth=`${a.username}:${a.password}`),_){let e=m.path.split(":");m.socketPath=e[0],m.path=e[1]}if(m.followRedirects){if(0===t._redirects){t._originalIpc=_,t._originalSecure=g,t._originalHostOrSocketPath=_?m.socketPath:a.host;let e=n&&n.headers;if(n={...n,headers:{}},e)for(let[t,i]of Object.entries(e))n.headers[t.toLowerCase()]=i}else if(0===t.listenerCount("redirect")){let e=_?!!t._originalIpc&&m.socketPath===t._originalHostOrSocketPath:!t._originalIpc&&a.host===t._originalHostOrSocketPath;e&&(!t._originalSecure||g)||(delete m.headers.authorization,delete m.headers.cookie,e||delete m.headers.host,m.auth=void 0)}m.auth&&!n.headers.authorization&&(n.headers.authorization="Basic "+Buffer.from(m.auth).toString("base64")),p=t._req=v(m),t._redirects&&t.emit("redirect",t.url,p)}else p=t._req=v(m);m.timeout&&p.on("timeout",()=>{U(t,p,"Opening handshake has timed out")}),p.on("error",e=>{null===p||p[P]||(p=t._req=null,B(t,e))}),p.on("response",o=>{let s=o.headers.location,a=o.statusCode;if(s&&m.followRedirects&&a>=300&&a<400){let o;if(++t._redirects>m.maxRedirects){U(t,p,"Maximum redirects exceeded");return}p.abort();try{o=new f(s,i)}catch(e){B(t,SyntaxError(`Invalid URL: ${s}`));return}e(t,o,r,n)}else t.emit("unexpected-response",p,o)||U(t,p,`Unexpected server response: ${o.statusCode}`)}),p.on("upgrade",(e,i,r)=>{let o;if(t.emit("upgrade",e),t.readyState!==A.CONNECTING)return;p=t._req=null;let s=e.headers.upgrade;if(void 0===s||"websocket"!==s.toLowerCase()){U(t,i,"Invalid Upgrade header");return}let n=c("sha1").update(b+w).digest("base64");if(e.headers["sec-websocket-accept"]!==n){U(t,i,"Invalid Sec-WebSocket-Accept header");return}let a=e.headers["sec-websocket-protocol"];if(void 0!==a?S.size?S.has(a)||(o="Server sent an invalid subprotocol"):o="Server sent a subprotocol but none was requested":S.size&&(o="Server sent no subprotocol"),o){U(t,i,o);return}a&&(t._protocol=a);let l=e.headers["sec-websocket-extensions"];if(void 0!==l){let e;if(!d){U(t,i,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");return}try{e=O(l)}catch(e){U(t,i,"Invalid Sec-WebSocket-Extensions header");return}let r=Object.keys(e);if(1!==r.length||r[0]!==u.extensionName){U(t,i,"Server indicated an extension that was not requested");return}try{d.accept(e[u.extensionName])}catch(e){U(t,i,"Invalid Sec-WebSocket-Extensions header");return}t._extensions[u.extensionName]=d}t.setSocket(i,r,{allowSynchronousEvents:m.allowSynchronousEvents,generateMask:m.generateMask,maxPayload:m.maxPayload,skipUTF8Validation:m.skipUTF8Validation})}),m.finishRequest?m.finishRequest(p,t):p.end()}(this,e,t,i)):(this._autoPong=i.autoPong,this._isServer=!0)}get binaryType(){return this._binaryType}set binaryType(e){_.includes(e)&&(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,t,i){let r=new p({allowSynchronousEvents:i.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:i.maxPayload,skipUTF8Validation:i.skipUTF8Validation}),o=new m(e,this._extensions,i.generateMask);this._receiver=r,this._sender=o,this._socket=e,r[k]=this,o[k]=this,e[k]=this,r.on("conclude",$),r.on("drain",W),r.on("error",j),r.on("message",V),r.on("ping",G),r.on("pong",H),o.onerror=J,e.setTimeout&&e.setTimeout(0),e.setNoDelay&&e.setNoDelay(),t.length>0&&e.unshift(t),e.on("close",K),e.on("data",X),e.on("end",Z),e.on("error",Q),this._readyState=A.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=A.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[u.extensionName]&&this._extensions[u.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=A.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(e,t){if(this.readyState!==A.CLOSED){if(this.readyState===A.CONNECTING){U(this,this._req,"WebSocket was closed before the connection was established");return}if(this.readyState===A.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=A.CLOSING,this._sender.close(e,t,!this._isServer,e=>{!e&&(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),Y(this)}}pause(){this.readyState!==A.CONNECTING&&this.readyState!==A.CLOSED&&(this._paused=!0,this._socket.pause())}ping(e,t,i){if(this.readyState===A.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(i=e,e=t=void 0):"function"==typeof t&&(i=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==A.OPEN){q(this,e,i);return}void 0===t&&(t=!this._isServer),this._sender.ping(e||y,t,i)}pong(e,t,i){if(this.readyState===A.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(i=e,e=t=void 0):"function"==typeof t&&(i=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==A.OPEN){q(this,e,i);return}void 0===t&&(t=!this._isServer),this._sender.pong(e||y,t,i)}resume(){this.readyState!==A.CONNECTING&&this.readyState!==A.CLOSED&&(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(e,t,i){if(this.readyState===A.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof t&&(i=t,t={}),"number"==typeof e&&(e=e.toString()),this.readyState!==A.OPEN){q(this,e,i);return}let r={binary:"string"!=typeof e,mask:!this._isServer,compress:!0,fin:!0,...t};this._extensions[u.extensionName]||(r.compress=!1),this._sender.send(e||y,r,i)}terminate(){if(this.readyState!==A.CLOSED){if(this.readyState===A.CONNECTING){U(this,this._req,"WebSocket was closed before the connection was established");return}this._socket&&(this._readyState=A.CLOSING,this._socket.destroy())}}}function B(e,t){e._readyState=A.CLOSING,e._errorEmitted=!0,e.emit("error",t),e.emitClose()}function D(e){return e.path=e.socketPath,n.connect(e)}function R(e){return e.path=void 0,e.servername||""===e.servername||(e.servername=n.isIP(e.host)?"":e.host),a.connect(e)}function U(e,t,i){e._readyState=A.CLOSING;let r=Error(i);Error.captureStackTrace(r,U),t.setHeader?(t[P]=!0,t.abort(),t.socket&&!t.socket.destroyed&&t.socket.destroy(),process.nextTick(B,e,r)):(t.destroy(r),t.once("error",e.emit.bind(e,"error")),t.once("close",e.emitClose.bind(e)))}function q(e,t,i){if(t){let i=g(t)?t.size:C(t).length;e._socket?e._sender._bufferedBytes+=i:e._bufferedAmount+=i}if(i){let t=Error(`WebSocket is not open: readyState ${e.readyState} (${M[e.readyState]})`);process.nextTick(i,t)}}function $(e,t){let i=this[k];i._closeFrameReceived=!0,i._closeMessage=t,i._closeCode=e,void 0!==i._socket[k]&&(i._socket.removeListener("data",X),process.nextTick(z,i._socket),1005===e?i.close():i.close(e,t))}function W(){let e=this[k];e.isPaused||e._socket.resume()}function j(e){let t=this[k];void 0!==t._socket[k]&&(t._socket.removeListener("data",X),process.nextTick(z,t._socket),t.close(e[S])),t._errorEmitted||(t._errorEmitted=!0,t.emit("error",e))}function F(){this[k].emitClose()}function V(e,t){this[k].emit("message",e,t)}function G(e){let t=this[k];t._autoPong&&t.pong(e,!this._isServer,E),t.emit("ping",e)}function H(e){this[k].emit("pong",e)}function z(e){e.resume()}function J(e){let t=this[k];t.readyState===A.CLOSED||(t.readyState===A.OPEN&&(t._readyState=A.CLOSING,Y(t)),this._socket.end(),t._errorEmitted||(t._errorEmitted=!0,t.emit("error",e)))}function Y(e){e._closeTimer=setTimeout(e._socket.destroy.bind(e._socket),3e4)}function K(){let e;let t=this[k];this.removeListener("close",K),this.removeListener("data",X),this.removeListener("end",Z),t._readyState=A.CLOSING,this._readableState.endEmitted||t._closeFrameReceived||t._receiver._writableState.errorEmitted||null===(e=t._socket.read())||t._receiver.write(e),t._receiver.end(),this[k]=void 0,clearTimeout(t._closeTimer),t._receiver._writableState.finished||t._receiver._writableState.errorEmitted?t.emitClose():(t._receiver.on("error",F),t._receiver.on("finish",F))}function X(e){this[k]._receiver.write(e)||this.pause()}function Z(){let e=this[k];e._readyState=A.CLOSING,e._receiver.end(),this.end()}function Q(){let e=this[k];this.removeListener("error",Q),this.on("error",E),e&&(e._readyState=A.CLOSING,this.destroy())}Object.defineProperty(A,"CONNECTING",{enumerable:!0,value:M.indexOf("CONNECTING")}),Object.defineProperty(A.prototype,"CONNECTING",{enumerable:!0,value:M.indexOf("CONNECTING")}),Object.defineProperty(A,"OPEN",{enumerable:!0,value:M.indexOf("OPEN")}),Object.defineProperty(A.prototype,"OPEN",{enumerable:!0,value:M.indexOf("OPEN")}),Object.defineProperty(A,"CLOSING",{enumerable:!0,value:M.indexOf("CLOSING")}),Object.defineProperty(A.prototype,"CLOSING",{enumerable:!0,value:M.indexOf("CLOSING")}),Object.defineProperty(A,"CLOSED",{enumerable:!0,value:M.indexOf("CLOSED")}),Object.defineProperty(A.prototype,"CLOSED",{enumerable:!0,value:M.indexOf("CLOSED")}),["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(e=>{Object.defineProperty(A.prototype,e,{enumerable:!0})}),["open","error","close","message"].forEach(e=>{Object.defineProperty(A.prototype,`on${e}`,{enumerable:!0,get(){for(let t of this.listeners(e))if(t[b])return t[v];return null},set(t){for(let t of this.listeners(e))if(t[b]){this.removeListener(e,t);break}"function"==typeof t&&this.addEventListener(e,t,{[b]:!0})}})}),A.prototype.addEventListener=x,A.prototype.removeEventListener=T,e.exports=A},45415:(e,t,i)=>{i.d(t,{nm0:()=>tT});var r=i(79646),o=i(55511),s=i(73136),n=i(33873);i(40883),i(57522),i(25870),i(36852),i(4902);var a=i(10230),l=i(76398),c=i(55882);async function h(e,t){let{collection:i,data:r,disableEmail:o,expiration:s}=t,n=e.collections[i];if(!n)throw new a.L(`The collection with slug ${String(i)} can't be found. Forgot Password Operation.`);return(0,c.g)({collection:n,data:r,disableEmail:o,expiration:s,req:await (0,l.M)(t,e)})}var d=i(87472);async function f(e,t){let{collection:i,data:r,depth:o,overrideAccess:s=!0,showHiddenFields:n}=t,c=e.collections[i];if(!c)throw new a.L(`The collection with slug ${String(i)} can't be found. Login Operation.`);let h={collection:c,data:r,depth:o,overrideAccess:s,req:await (0,l.M)(t,e),showHiddenFields:n},f=await (0,d.k)(h);return c.config.auth.removeTokenFromResponses&&delete f.token,f}var u=i(15792);async function p(e,t){let{collection:i,data:r,overrideAccess:o}=t,s=e.collections[i];if(!s)throw new a.L(`The collection with slug ${String(i)} can't be found. Reset Password Operation.`);let n=await (0,u.q)({collection:s,data:r,overrideAccess:o,req:await (0,l.M)(t,e)});return s.config.auth.removeTokenFromResponses&&delete n.token,n}var m=i(25180);async function g(e,t){let{collection:i,data:r,overrideAccess:o=!0}=t,s=e.collections[i];if(!s)throw new a.L(`The collection with slug ${String(i)} can't be found. Unlock Operation.`);return(0,m.k)({collection:s,data:r,overrideAccess:o,req:await (0,l.M)(t,e)})}var _=i(75337);async function y(e,t){let{collection:i,token:r}=t,o=e.collections[i];if(!o)throw new a.L(`The collection with slug ${String(i)} can't be found. Verify Email Operation.`);return(0,_.p)({collection:o,req:await (0,l.M)(t,e),token:r})}var w=i(50125);async function b(e,t){let{collection:i,disableErrors:r,overrideAccess:o=!0,where:s}=t,n=e.collections[i];if(!n)throw new a.L(`The collection with slug ${String(i)} can't be found. Count Operation.`);return(0,w.R)({collection:n,disableErrors:r,overrideAccess:o,req:await (0,l.M)(t,e),where:s})}var v=i(30815),S=i(62841),k=i(6158);async function E(e,t){let{collection:i,data:r,depth:o,disableTransaction:s,disableVerificationEmail:n,draft:c,duplicateFromID:h,file:d,filePath:f,overrideAccess:u=!0,overwriteExistingFiles:p=!1,populate:m,select:g,showHiddenFields:_}=t,y=e.collections[i];if(!y)throw new a.L(`The collection with slug ${String(i)} can't be found. Create Operation.`);let w=await (0,l.M)(t,e);return w.file=d??await (0,S.d)(f),(0,k.k)({collection:y,data:(0,v.Zt)(r),depth:o,disableTransaction:s,disableVerificationEmail:n,draft:c,duplicateFromID:h,overrideAccess:u,overwriteExistingFiles:p,populate:m,req:w,select:g,showHiddenFields:_})}var x=i(65630),T=i(50386);async function L(e,t){let{id:i,collection:r,depth:o,disableTransaction:s,overrideAccess:n=!0,overrideLock:c,populate:h,select:d,showHiddenFields:f,trash:u=!1,where:p}=t,m=e.collections[r];if(!m)throw new a.L(`The collection with slug ${String(r)} can't be found. Delete Operation.`);let g={id:i,collection:m,depth:o,disableTransaction:s,overrideAccess:n,overrideLock:c,populate:h,req:await (0,l.M)(t,e),select:d,showHiddenFields:f,trash:u,where:p};return t.id?(0,T.l)(g):(0,x.N)(g)}var O=i(77039);async function C(e,t){let{id:i,collection:r,data:o,depth:s,disableTransaction:n,draft:c,overrideAccess:h=!0,populate:d,select:f,showHiddenFields:u}=t,p=e.collections[r];if(!p)throw new a.L(`The collection with slug ${String(r)} can't be found. Duplicate Operation.`);if(!0===p.config.disableDuplicate)throw new a.L(`The collection with slug ${String(r)} cannot be duplicated.`,400);let m=await (0,l.M)(t,e);return(0,O.h)({id:i,collection:p,data:o,depth:s,disableTransaction:n,draft:c,overrideAccess:h,populate:d,req:m,select:f,showHiddenFields:u})}var P=i(82390);async function N(e,t){let{collection:i,currentDepth:r,depth:o,disableErrors:s,draft:n=!1,includeLockStatus:c,joins:h,limit:d,overrideAccess:f=!0,page:u,pagination:p=!0,populate:m,select:g,showHiddenFields:_,sort:y,trash:w=!1,where:b}=t,v=e.collections[i];if(!v)throw new a.L(`The collection with slug ${String(i)} can't be found. Find Operation.`);return(0,P.L)({collection:v,currentDepth:r,depth:o,disableErrors:s,draft:n,includeLockStatus:c,joins:h,limit:d,overrideAccess:f,page:u,pagination:p,populate:m,req:await (0,l.M)(t,e),select:g,showHiddenFields:_,sort:y,trash:w,where:b})}var M=i(5834);async function I(e,t){let{id:i,collection:r,currentDepth:o,depth:s,disableErrors:n=!1,draft:c=!1,includeLockStatus:h,joins:d,overrideAccess:f=!0,populate:u,select:p,showHiddenFields:m,trash:g=!1}=t,_=e.collections[r];if(!_)throw new a.L(`The collection with slug ${String(r)} can't be found. Find By ID Operation.`);return(0,M.$)({id:i,collection:_,currentDepth:o,depth:s,disableErrors:n,draft:c,includeLockStatus:h,joins:d,overrideAccess:f,populate:u,req:await (0,l.M)(t,e),select:p,showHiddenFields:m,trash:g})}var A=i(12043),B=i(36470),D=i(11533),R=i(7114),U=i(1456),q=i(65239),$=i(94148),W=i(61925),j=i(83231),F=i(73880);let V=async e=>{let t=e;try{let e;if(t.collection.config.hooks?.beforeOperation?.length)for(let e of t.collection.config.hooks.beforeOperation)t=await e({args:t,collection:t.collection.config,context:t.req.context,operation:"readDistinct",req:t.req})||t;let{collection:{config:i},disableErrors:r,overrideAccess:o,populate:s,showHiddenFields:n=!1,where:l}=t,c=t.req,{locale:h,payload:d}=c;if(!o&&(e=await (0,B.m)({disableErrors:r,req:c},i.access.read),!1===e))return{hasNextPage:!1,hasPrevPage:!1,limit:t.limit||0,nextPage:null,page:1,pagingCounter:1,prevPage:null,totalDocs:0,totalPages:0,values:[]};let f=(0,D.m)(l,e);(0,U.D)({fields:i.flattenedFields,payload:d,where:f}),await (0,R.d)({collectionConfig:i,overrideAccess:o,req:c,where:l??{}});let u=(0,W.p)({fields:i.flattenedFields,path:t.field});if(!u)throw new a.L(`Field ${t.field} was not found in the collection ${i.slug}`,A.A.BAD_REQUEST);if(u.field.hidden&&!n||u.field.access?.read&&!await u.field.access.read({req:c}))throw new q.c(c.t);let p=await d.db.findDistinct({collection:i.slug,field:t.field,limit:t.limit,locale:h,page:t.page,req:c,sort:t.sort,where:f});if(("relationship"===u.field.type||"upload"===u.field.type)&&t.depth){let e=[];for(let i of p.values)e.push((0,$.x)({currentDepth:0,depth:t.depth,draft:!1,fallbackLocale:c.fallbackLocale||null,field:u.field,locale:c.locale||null,overrideAccess:t.overrideAccess??!0,parentIsLocalized:!1,populate:s,req:c,showHiddenFields:!1,siblingDoc:i}));await Promise.all(e)}return p=await (0,F.E)({args:t,collection:i,operation:"findDistinct",result:p})}catch(e){throw await (0,j.J)(t.req),e}};async function G(e,t){let{collection:i,depth:r=0,disableErrors:o,field:s,limit:n,overrideAccess:c=!0,page:h,populate:d,showHiddenFields:f,sort:u,where:p}=t,m=e.collections[i];if(!m)throw new a.L(`The collection with slug ${String(i)} can't be found. Find Operation.`);return V({collection:m,depth:r,disableErrors:o,field:s,limit:n,overrideAccess:c,page:h,populate:d,req:await (0,l.M)(t,e),showHiddenFields:f,sort:u,where:p})}var H=i(75832);async function z(e,t){let{id:i,collection:r,depth:o,disableErrors:s=!1,overrideAccess:n=!0,populate:c,select:h,showHiddenFields:d,trash:f=!1}=t,u=e.collections[r];if(!u)throw new a.L(`The collection with slug ${String(r)} can't be found. Find Version By ID Operation.`);return(0,H.L)({id:i,collection:u,depth:o,disableErrors:s,overrideAccess:n,populate:c,req:await (0,l.M)(t,e),select:h,showHiddenFields:d,trash:f})}var J=i(71907);async function Y(e,t){let{collection:i,depth:r,limit:o,overrideAccess:s=!0,page:n,populate:c,select:h,showHiddenFields:d,sort:f,trash:u=!1,where:p}=t,m=e.collections[i];if(!m)throw new a.L(`The collection with slug ${String(i)} can't be found. Find Versions Operation.`);return(0,J.s)({collection:m,depth:r,limit:o,overrideAccess:s,page:n,populate:c,req:await (0,l.M)(t,e),select:h,showHiddenFields:d,sort:f,trash:u,where:p})}var K=i(89851);async function X(e,t){let{id:i,collection:r,depth:o,overrideAccess:s=!0,populate:n,select:c,showHiddenFields:h}=t,d=e.collections[r];if(!d)throw new a.L(`The collection with slug ${String(r)} can't be found. Restore Version Operation.`);let f={id:i,collection:d,depth:o,overrideAccess:s,payload:e,populate:n,req:await (0,l.M)(t,e),select:c,showHiddenFields:h};return(0,K.c)(f)}var Z=i(8084),Q=i(3252);async function ee(e,t){let{id:i,autosave:r,collection:o,data:s,depth:n,disableTransaction:c,draft:h,file:d,filePath:f,limit:u,overrideAccess:p=!0,overrideLock:m,overwriteExistingFiles:g=!1,populate:_,publishSpecificLocale:y,select:w,showHiddenFields:b,sort:v,trash:k=!1,where:E}=t,x=e.collections[o];if(!x)throw new a.L(`The collection with slug ${String(o)} can't be found. Update Operation.`);let T=await (0,l.M)(t,e);T.file=d??await (0,S.d)(f);let L={id:i,autosave:r,collection:x,data:s,depth:n,disableTransaction:c,draft:h,limit:u,overrideAccess:p,overrideLock:m,overwriteExistingFiles:g,payload:e,populate:_,publishSpecificLocale:y,req:T,select:w,showHiddenFields:b,sort:v,trash:k,where:E};return t.id?(0,Q.Z)(L):(0,Z.L)(L)}var et=i(54098);let ei=async e=>{try{let t;let{disableErrors:i,global:r,overrideAccess:o,where:s}=e,n=e.req,{payload:a}=n;if(!o&&(t=await (0,B.m)({disableErrors:i,req:n},r.access.readVersions),!1===t))return{totalDocs:0};let l=(0,D.m)(s,t),c=(0,et.p)(a.config,r,!0);return await (0,R.d)({globalConfig:r,overrideAccess:o,req:n,versionFields:c,where:s}),await a.db.countGlobalVersions({global:r.slug,req:n,where:l})}catch(t){throw await (0,j.J)(e.req),t}};async function er(e,t){let{disableErrors:i,global:r,overrideAccess:o=!0,where:s}=t,n=e.globals.config.find(({slug:e})=>e===r);if(!n)throw new a.L(`The global with slug ${String(r)} can't be found. Count Global Versions Operation.`);return ei({disableErrors:i,global:n,overrideAccess:o,req:await (0,l.M)(t,e),where:s})}var eo=i(8795);async function es(e,t){let{slug:i,depth:r,draft:o=!1,includeLockStatus:s,overrideAccess:n=!0,populate:c,select:h,showHiddenFields:d}=t,f=e.globals.config.find(e=>e.slug===i);if(!f)throw new a.L(`The global with slug ${String(i)} can't be found.`);return(0,eo.j)({slug:i,depth:r,draft:o,globalConfig:f,includeLockStatus:s,overrideAccess:n,populate:c,req:await (0,l.M)(t,e),select:h,showHiddenFields:d})}var en=i(81491);async function ea(e,t){let{id:i,slug:r,depth:o,disableErrors:s=!1,overrideAccess:n=!0,populate:c,select:h,showHiddenFields:d}=t,f=e.globals.config.find(e=>e.slug===r);if(!f)throw new a.L(`The global with slug ${String(r)} can't be found.`);return(0,en.L)({id:i,depth:o,disableErrors:s,globalConfig:f,overrideAccess:n,populate:c,req:await (0,l.M)(t,e),select:h,showHiddenFields:d})}var el=i(19422);async function ec(e,t){let{slug:i,depth:r,limit:o,overrideAccess:s=!0,page:n,populate:c,select:h,showHiddenFields:d,sort:f,where:u}=t,p=e.globals.config.find(e=>e.slug===i);if(!p)throw new a.L(`The global with slug ${String(i)} can't be found.`);return(0,el.s)({depth:r,globalConfig:p,limit:o,overrideAccess:s,page:n,populate:c,req:await (0,l.M)(t,e),select:h,showHiddenFields:d,sort:f,where:u})}var eh=i(34334);async function ed(e,t){let{id:i,slug:r,depth:o,overrideAccess:s=!0,populate:n,showHiddenFields:c}=t,h=e.globals.config.find(e=>e.slug===r);if(!h)throw new a.L(`The global with slug ${String(r)} can't be found.`);return(0,eh.c)({id:i,depth:o,globalConfig:h,overrideAccess:s,populate:n,req:await (0,l.M)(t,e),showHiddenFields:c})}var ef=i(54803);async function eu(e,t){let{slug:i,data:r,depth:o,draft:s,overrideAccess:n=!0,overrideLock:c,populate:h,publishSpecificLocale:d,select:f,showHiddenFields:u}=t,p=e.globals.config.find(e=>e.slug===i);if(!p)throw new a.L(`The global with slug ${String(i)} can't be found.`);return(0,ef.L)({slug:i,data:(0,v.Zt)(r),depth:o,draft:s,globalConfig:p,overrideAccess:n,overrideLock:c,populate:h,publishSpecificLocale:d,req:await (0,l.M)(t,e),select:f,showHiddenFields:u})}var ep=i(92714);let em="aes-256-ctr";function eg(e){let t=o.randomBytes(16),i=this.secret,r=o.createCipheriv(em,i,t),s=Buffer.concat([r.update(e),r.final()]),n=t.toString("hex"),a=s.toString("hex");return`${n}${a}`}function e_(e){let t=e.slice(0,32),i=e.slice(32),r=this.secret,s=o.createDecipheriv(em,r,Buffer.from(t,"hex"));return Buffer.concat([s.update(Buffer.from(i,"hex")),s.final()]).toString()}let ey=({err:e,payload:t})=>{let i="error";if(e&&"object"==typeof e&&"name"in e&&"string"==typeof e.name&&void 0!==t.config.loggingLevels[e.name]&&(i=t.config.loggingLevels[e.name]),i){let r={};"info"===i?r.msg="object"==typeof e&&"message"in e?e.message:"Error":r.err=e,t.logger[i](r)}},ew=(e,t)=>{let i=new Headers(t);return e.forEach((e,t)=>{i.append(t,e)}),i},eb=async e=>{let t={user:null};if(!e.payload.authStrategies?.length)return t;for(let i of e.payload.authStrategies){e.strategyName=i.name,e.isGraphQL=!!e.isGraphQL,e.canSetHeaders=!!e.canSetHeaders;try{let r=await i.authenticate(e);r.responseHeaders&&(r.responseHeaders=ew(t.responseHeaders||new Headers,r.responseHeaders||new Headers)),t=r}catch(t){ey({err:t,payload:e.payload})}if(t.user)break}return t};var ev=i(53907);let eS=async e=>{let{canSetHeaders:t,headers:i}=e,r=e.req,{payload:o}=r;try{let{responseHeaders:e,user:s}=await eb({canSetHeaders:t,headers:i,payload:o});return r.user=s,r.responseHeaders=e,{permissions:await (0,ev.R)({req:r}),responseHeaders:e,user:s}}catch(e){throw await (0,j.J)(r),e}},ek=async(e,t)=>{let{headers:i,req:r}=t;return await eS({canSetHeaders:!!t.canSetHeaders,headers:i,req:await (0,l.M)({req:r},e)})},eE=e=>async({headers:t,payload:i})=>{let r=t.get("Authorization");if(r?.startsWith(`${e.slug} API-Key `)){let t=r.replace(`${e.slug} API-Key `,""),s=[{apiKeyIndex:{equals:o.createHmac("sha1",i.secret).update(t).digest("hex")}},{apiKeyIndex:{equals:o.createHmac("sha256",i.secret).update(t).digest("hex")}}];try{let t={};e.auth?.verify?t.and=[{or:s},{_verified:{not_equals:!1}}]:t.or=s;let r=await i.find({collection:e.slug,depth:e.auth.depth,limit:1,overrideAccess:!0,pagination:!1,where:t});if(r.docs&&r.docs.length>0){let t=r.docs[0];return t.collection=e.slug,t._strategy="api-key",{user:t}}}catch(e){}}return{user:null}};var ex=i(32521),eT=i(77598),eL=i(57975),eO=i(37424),eC=i(94531),eP=i(82122),eN=i(8940);let eM=(0,eL.promisify)(eT.verify),eI=async(e,t,i,r)=>{let o=(0,eN.A)(e,t,"verify");if(e.startsWith("HS")){let t=await (0,eP.A)(e,o,r);try{return eT.timingSafeEqual(i,t)}catch{return!1}}let s=(0,eO.A)(e),n=(0,eC.A)(e,o);try{return await eM(s,r,n,i)}catch{return!1}};var eA=i(34624),eB=i(52532),eD=i(26587),eR=i(64140),eU=i(36721),eq=i(26573);let e$=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)};var eW=i(16763);let ej=e=>e.d?(0,eT.createPrivateKey)({format:"jwk",key:e}):(0,eT.createPublicKey)({format:"jwk",key:e});async function eF(e,t){if(!(0,eR.A)(e))throw TypeError("JWK must be an object");switch(t||=e.alg,e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');return(0,ex.D4)(e.k);case"RSA":if(void 0!==e.oth)throw new eA.T0('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return ej({...e,alg:t});default:throw new eA.T0('Unsupported "kty" (Key Type) Parameter value')}}async function eV(e,t,i){let r,o;if(!(0,eR.A)(e))throw new eA.Ye("Flattened JWS must be an object");if(void 0===e.protected&&void 0===e.header)throw new eA.Ye('Flattened JWS must have either of the "protected" or "header" members');if(void 0!==e.protected&&"string"!=typeof e.protected)throw new eA.Ye("JWS Protected Header incorrect type");if(void 0===e.payload)throw new eA.Ye("JWS Payload missing");if("string"!=typeof e.signature)throw new eA.Ye("JWS Signature missing or incorrect type");if(void 0!==e.header&&!(0,eR.A)(e.header))throw new eA.Ye("JWS Unprotected Header incorrect type");let s={};if(e.protected)try{let t=(0,ex.D4)(e.protected);s=JSON.parse(eB.D0.decode(t))}catch{throw new eA.Ye("JWS Protected Header is invalid")}if(!(0,eD.A)(s,e.header))throw new eA.Ye("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let n={...s,...e.header},a=(0,eq.A)(eA.Ye,new Map([["b64",!0]]),i?.crit,s,n),l=!0;if(a.has("b64")&&"boolean"!=typeof(l=s.b64))throw new eA.Ye('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:c}=n;if("string"!=typeof c||!c)throw new eA.Ye('JWS "alg" (Algorithm) Header Parameter missing or invalid');let h=i&&e$("algorithms",i.algorithms);if(h&&!h.has(c))throw new eA.Rb('"alg" (Algorithm) Header Parameter value not allowed');if(l){if("string"!=typeof e.payload)throw new eA.Ye("JWS Payload must be a string")}else if("string"!=typeof e.payload&&!(e.payload instanceof Uint8Array))throw new eA.Ye("JWS Payload must be a string or an Uint8Array instance");let d=!1;"function"==typeof t?(t=await t(s,e),d=!0,(0,eU.I)(c,t,"verify"),(0,eW.ll)(t)&&(t=await eF(t,c))):(0,eU.I)(c,t,"verify");let f=(0,eB.xW)(eB.Rd.encode(e.protected??""),eB.Rd.encode("."),"string"==typeof e.payload?eB.Rd.encode(e.payload):e.payload);try{r=(0,ex.D4)(e.signature)}catch{throw new eA.Ye("Failed to base64url decode the signature")}if(!await eI(c,t,r,f))throw new eA.h2;if(l)try{o=(0,ex.D4)(e.payload)}catch{throw new eA.Ye("Failed to base64url decode the payload")}else o="string"==typeof e.payload?eB.Rd.encode(e.payload):e.payload;let u={payload:o};return(void 0!==e.protected&&(u.protectedHeader=s),void 0!==e.header&&(u.unprotectedHeader=e.header),d)?{...u,key:t}:u}async function eG(e,t,i){if(e instanceof Uint8Array&&(e=eB.D0.decode(e)),"string"!=typeof e)throw new eA.Ye("Compact JWS must be a string or Uint8Array");let{0:r,1:o,2:s,length:n}=e.split(".");if(3!==n)throw new eA.Ye("Invalid Compact JWS");let a=await eV({payload:o,protected:r,signature:s},t,i),l={payload:a.payload,protectedHeader:a.protectedHeader};return"function"==typeof t?{...l,key:a.key}:l}var eH=i(14731),ez=i(15050);let eJ=e=>e.toLowerCase().replace(/^application\//,""),eY=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e))),eK=(e,t,i={})=>{let r,o;try{r=JSON.parse(eB.D0.decode(t))}catch{}if(!(0,eR.A)(r))throw new eA.Dp("JWT Claims Set must be a top-level JSON object");let{typ:s}=i;if(s&&("string"!=typeof e.typ||eJ(e.typ)!==eJ(s)))throw new eA.ie('unexpected "typ" JWT header value',r,"typ","check_failed");let{requiredClaims:n=[],issuer:a,subject:l,audience:c,maxTokenAge:h}=i,d=[...n];for(let e of(void 0!==h&&d.push("iat"),void 0!==c&&d.push("aud"),void 0!==l&&d.push("sub"),void 0!==a&&d.push("iss"),new Set(d.reverse())))if(!(e in r))throw new eA.ie(`missing required "${e}" claim`,r,e,"missing");if(a&&!(Array.isArray(a)?a:[a]).includes(r.iss))throw new eA.ie('unexpected "iss" claim value',r,"iss","check_failed");if(l&&r.sub!==l)throw new eA.ie('unexpected "sub" claim value',r,"sub","check_failed");if(c&&!eY(r.aud,"string"==typeof c?[c]:c))throw new eA.ie('unexpected "aud" claim value',r,"aud","check_failed");switch(typeof i.clockTolerance){case"string":o=(0,ez.A)(i.clockTolerance);break;case"number":o=i.clockTolerance;break;case"undefined":o=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:f}=i,u=(0,eH.A)(f||new Date);if((void 0!==r.iat||h)&&"number"!=typeof r.iat)throw new eA.ie('"iat" claim must be a number',r,"iat","invalid");if(void 0!==r.nbf){if("number"!=typeof r.nbf)throw new eA.ie('"nbf" claim must be a number',r,"nbf","invalid");if(r.nbf>u+o)throw new eA.ie('"nbf" claim timestamp check failed',r,"nbf","check_failed")}if(void 0!==r.exp){if("number"!=typeof r.exp)throw new eA.ie('"exp" claim must be a number',r,"exp","invalid");if(r.exp<=u-o)throw new eA.n('"exp" claim timestamp check failed',r,"exp","check_failed")}if(h){let e=u-r.iat;if(e-o>("number"==typeof h?h:(0,ez.A)(h)))throw new eA.n('"iat" claim timestamp check failed (too far in the past)',r,"iat","check_failed");if(e<0-o)throw new eA.ie('"iat" claim timestamp check failed (it should be in the past)',r,"iat","check_failed")}return r};async function eX(e,t,i){let r=await eG(e,t,i);if(r.protectedHeader.crit?.includes("b64")&&!1===r.protectedHeader.b64)throw new eA.Dp("JWTs MUST NOT use unencoded payload");let o={payload:eK(r.protectedHeader,r.payload,i),protectedHeader:r.protectedHeader};return"function"==typeof t?{...o,key:r.key}:o}var eZ=i(20065);async function eQ({isGraphQL:e,payload:t,strategyName:i="local-jwt"}){if("object"!=typeof t?.config?.admin?.autoLogin||t.config.admin?.autoLogin.prefillOnly||!t?.config?.admin?.autoLogin||!t.config.admin?.autoLogin.email&&!t.config.admin?.autoLogin.username)return{user:null};let r=t.collections[t.config.admin.user],o={or:[]};t.config.admin?.autoLogin.email?o.or?.push({email:{equals:t.config.admin?.autoLogin.email}}):t.config.admin?.autoLogin.username&&o.or?.push({username:{equals:t.config.admin?.autoLogin.username}});let s=(await t.find({collection:r.config.slug,depth:e?0:r.config.auth.depth,limit:1,pagination:!1,where:o})).docs[0];return s?(s.collection=r.config.slug,s._strategy=i,{user:s}):{user:null}}let e0=async({headers:e,isGraphQL:t=!1,payload:i,strategyName:r="local-jwt"})=>{try{let o=(0,eZ.p)({headers:e,payload:i});if(!o){if("true"!==e.get("DisableAutologin"))return await eQ({isGraphQL:t,payload:i,strategyName:r});return{user:null}}let s=new TextEncoder().encode(i.secret),{payload:n}=await eX(o,s),a=i.collections[n.collection],l=await i.findByID({id:n.id,collection:n.collection,depth:t?0:a.config.auth.depth});if(l&&(!a.config.auth.verify||l._verified)){if(a.config.auth.useSessions){if(!(l.sessions||[]).find(({id:e})=>e===n.sid)||!n.sid)return{user:null};l._sid=n.sid}return l.collection=a.config.slug,l._strategy=r,{user:l}}if("true"!==e.get("DisableAutologin"))return await eQ({isGraphQL:t,payload:i,strategyName:r});return{user:null}}catch(o){if("true"!==e.get("DisableAutologin"))return await eQ({isGraphQL:t,payload:i,strategyName:r});return{user:null}}};var e1=i(79748),e2=i(1708),e4=i(54046);function e8({importMap:e,importMapToBaseDirPath:t,imports:i,payloadComponent:r}){if(!r)return null;let{exportName:s,path:a}=function(e){if(!e)return null;let t="string"==typeof e?e:e.path,i="",r="default";return t?.includes("#")?[i,r]=t.split("#"):i=t,"object"==typeof e&&e.exportName&&(r=e.exportName),{exportName:r,path:i}}(r);if(e[a+"#"+s])return null;let l=s+"_"+o.createHash("md5").update(a).digest("hex");if(e[a+"#"+s]=l,!(a.startsWith(".")||a.startsWith("/")))return i[l]={path:a,specifier:s},{path:a,specifier:s};{let e=function(e,t){let i=e.replace(/\\/g,"/"),r=t.replace(/\\/g,"/");if(i.startsWith("./")){let e=r.startsWith("./")?r.substring(2):r;return`${i}${e}`}return n.posix.join(i,r)}(t,a);return i[l]={path:e,specifier:s},{path:e,specifier:s}}}async function e5(e){try{return await e1.access(e),!0}catch{return!1}}async function e3({adminRoute:e="/admin",importMapFile:t,rootDir:i}){let r;if(t?.length){if(!await e5(t))try{await e1.writeFile(t,"",{flag:"wx"})}catch(e){throw Error(`Could not find the import map file at ${t}${e instanceof Error&&e?.message?`: ${e.message}`:""}`)}r=t}else{let t=n.resolve(i,`app/(payload)${e}/`),o=n.resolve(i,`src/app/(payload)${e}/`);if(t&&await e5(t))r=n.resolve(t,"importMap.js"),await e5(r)||await e1.writeFile(r,"",{flag:"wx"});else if(o&&await e5(o))r=n.resolve(o,"importMap.js"),await e5(r)||await e1.writeFile(r,"",{flag:"wx"});else throw Error(`Could not find Payload import map folder. Looked in ${t} and ${o}`)}return r}async function e6(e,t){let i=t?.log??!0;i&&console.log("Generating import map");let r={},o={},s=e2.env.ROOT_DIR??e2.cwd(),a=e.admin.importMap.baseDir??e2.cwd(),l=await e3({adminRoute:e.routes.admin,importMapFile:e?.admin?.importMap?.importMapFile,rootDir:s}),c=function({baseDir:e,importMapPath:t}){let i=n.dirname(t),r=n.relative(i,e).replace(/\\/g,"/");return r?r.startsWith(".")||r.startsWith("/")||(r=`./${r}`):r="./",r.endsWith("/")||(r+="/"),r}({baseDir:a,importMapPath:l});(function({addToImportMap:e,baseDir:t,config:i,importMap:r,imports:o}){if(function({addToImportMap:e,baseDir:t,collections:i,config:r,importMap:o,imports:s}){for(let n of i){if((0,e4.J)({addToImportMap:e,baseDir:t,config:r,fields:n.fields,importMap:o,imports:s}),e(n.admin?.components?.afterList),e(n.admin?.components?.listMenuItems),e(n.admin?.components?.afterListTable),e(n.admin?.components?.beforeList),e(n.admin?.components?.beforeListTable),e(n.admin?.components?.Description),e(n.admin?.components?.edit?.beforeDocumentControls),e(n.admin?.components?.edit?.editMenuItems),e(n.admin?.components?.edit?.PreviewButton),e(n.admin?.components?.edit?.PublishButton),e(n.admin?.components?.edit?.SaveButton),e(n.admin?.components?.edit?.SaveDraftButton),e(n.admin?.components?.edit?.Upload),n.upload?.admin?.components?.controls&&e(n.upload?.admin?.components?.controls),n.admin?.components?.views?.edit)for(let t of Object.values(n.admin?.components?.views?.edit))"Component"in t&&e(t?.Component),"actions"in t&&e(t?.actions),"tab"in t&&(e(t?.tab?.Component),e(t?.tab?.Pill));e(n.admin?.components?.views?.list?.Component),e(n.admin?.components?.views?.list?.actions)}}({addToImportMap:e,baseDir:t,collections:i.collections,config:i,importMap:r,imports:o}),function({addToImportMap:e,baseDir:t,config:i,globals:r,importMap:o,imports:s}){for(let n of r)if((0,e4.J)({addToImportMap:e,baseDir:t,config:i,fields:n.fields,importMap:o,imports:s}),e(n.admin?.components?.elements?.Description),e(n.admin?.components?.elements?.PreviewButton),e(n.admin?.components?.elements?.PublishButton),e(n.admin?.components?.elements?.SaveButton),e(n.admin?.components?.elements?.SaveDraftButton),n.admin?.components?.views?.edit)for(let t of Object.values(n.admin?.components?.views?.edit))"Component"in t&&e(t?.Component),"actions"in t&&e(t?.actions),"tab"in t&&(e(t?.tab?.Component),e(t?.tab?.Pill))}({addToImportMap:e,baseDir:t,config:i,globals:i.globals,importMap:r,imports:o}),i?.blocks){let s=Object.values(i.blocks);s?.length&&(0,e4.J)({addToImportMap:e,baseDir:t,config:i,fields:s,importMap:r,imports:o})}if("object"==typeof i.admin?.avatar&&e(i.admin?.avatar?.Component),e(i.admin?.components?.Nav),e(i.admin?.components?.header),e(i.admin?.components?.logout?.Button),e(i.admin?.components?.graphics?.Icon),e(i.admin?.components?.graphics?.Logo),e(i.admin?.components?.actions),e(i.admin?.components?.afterDashboard),e(i.admin?.components?.afterLogin),e(i.admin?.components?.afterNavLinks),e(i.admin?.components?.beforeDashboard),e(i.admin?.components?.beforeLogin),e(i.admin?.components?.beforeNavLinks),e(i.admin?.components?.providers),i.admin?.components?.views&&Object.keys(i.admin?.components?.views)?.length)for(let t in i.admin?.components?.views){let r=i.admin?.components?.views[t];e(r?.Component)}if(i?.admin?.importMap?.generators?.length)for(let s of i.admin.importMap.generators)s({addToImportMap:e,baseDir:t,config:i,importMap:r,imports:o});if(i?.admin?.dependencies)for(let t of Object.values(i.admin.dependencies))e(t.path)})({addToImportMap:e=>{if(e){if("object"!=typeof e&&"string"!=typeof e)throw console.error(e),Error("addToImportMap > Payload component must be an object or a string");if(Array.isArray(e))for(let t of e)e8({importMap:r,importMapToBaseDirPath:c,imports:o,payloadComponent:t});else e8({importMap:r,importMapToBaseDirPath:c,imports:o,payloadComponent:e})}},baseDir:e.admin.importMap.baseDir,config:e,importMap:r,imports:o}),await e9({componentMap:r,force:t?.force,importMap:o,importMapFilePath:l,log:i})}async function e9({componentMap:e,force:t,importMap:i,importMapFilePath:r,log:o}){let s=[];for(let[e,{path:t,specifier:r}]of Object.entries(i))s.push(`import { ${r} as ${e} } from '${t}'`);let n=[];for(let[t,i]of Object.entries(e))n.push(`  "${t}": ${i}`);let a=`${s.join("\n")}

export const importMap = {
${n.join(",\n")}
}
`;if(!t){let e=await e1.readFile(r,"utf-8");if(e?.trim()===a?.trim()){o&&console.log("No new imports found, skipping writing import map");return}}o&&console.log("Writing import map to",r),await e1.writeFile(r,a)}var e7=i(64429);let te=async e=>{let t=e;try{let e,i;if(t.collection.config.hooks.beforeOperation?.length)for(let e of t.collection.config.hooks.beforeOperation)t=await e({args:t,collection:t.collection.config,context:t.req.context,operation:"countVersions",req:t.req})||t;let{collection:{config:r},disableErrors:o,overrideAccess:s,req:n,where:a}=t,{payload:l}=n;if(!s&&(e=await (0,B.m)({disableErrors:o,req:n},r.access.readVersions),!1===e))return{totalDocs:0};let c=(0,D.m)(a,e),h=(0,e7.c)(l.config,r,!0);return(0,U.D)({fields:h,payload:l,where:c}),await (0,R.d)({collectionConfig:r,overrideAccess:s,req:n,versionFields:h,where:a}),i=await l.db.countVersions({collection:r.slug,req:n,where:c}),i=await (0,F.E)({args:t,collection:r,operation:"countVersions",result:i})}catch(e){throw await (0,j.J)(t.req),e}};async function tt(e,t){let{collection:i,disableErrors:r,overrideAccess:o=!0,where:s}=t,n=e.collections[i];if(!n)throw new a.L(`The collection with slug ${String(i)} can't be found. Count Versions Operation.`);return te({collection:n,disableErrors:r,overrideAccess:o,req:await (0,l.M)(t,e),where:s})}let ti={defaultFromAddress:"<EMAIL>",defaultFromName:"Payload"},tr=e=>{let t;return"string"==typeof e.to?t=e.to:Array.isArray(e.to)?t=e.to.map(e=>"string"==typeof e?e:e.address?e.address:"").join(", "):e.to?.address&&(t=e.to.address),t},to=({payload:e})=>({name:"console",defaultFromAddress:ti.defaultFromAddress,defaultFromName:ti.defaultFromName,sendEmail:async t=>{let i=tr(t),r=`Email attempted without being configured. To: '${i}', Subject: '${t.subject}'`;return e.logger.info({msg:r}),Promise.resolve()}});var ts=i(51022),tn=i(80531),ta=i(10509),tl=i(93984),tc=i(2326);let th=e=>({handleSchedules:async t=>{let i=t?.req??await (0,l.M)({},e);return await (0,ta.L6)({queue:t?.queue,req:i})},queue:async t=>{let i;if(t.queue)i=t.queue;else if(t.workflow){let r=e.config.jobs?.workflows?.find(({slug:e})=>e===t.workflow);r?.queue&&(i=r.queue)}let r={input:t.input};return(i&&(r.queue=i),t.waitUntil&&(r.waitUntil=t.waitUntil?.toISOString()),t.workflow&&(r.workflowSlug=t.workflow),t.task&&(r.taskSlug=t.task),t.meta&&(r.meta=t.meta),e?.config?.jobs?.depth||e?.config?.jobs?.runHooks)?await e.create({collection:tn.SJ,data:r,depth:e.config.jobs.depth??0,req:t.req}):(0,tn.Sf)({config:e.config,doc:await e.db.create({collection:tn.SJ,data:r,req:t.req})})},run:async t=>{let i=t?.req??await (0,l.M)({},e);return await (0,tl.A)({allQueues:t?.allQueues,limit:t?.limit,overrideAccess:t?.overrideAccess!==!1,processingOrder:t?.processingOrder,queue:t?.queue,req:i,sequential:t?.sequential,silent:t?.silent,where:t?.where})},runByID:async t=>{let i=t.req??await (0,l.M)({},e);return await (0,tl.A)({id:t.id,overrideAccess:!1!==t.overrideAccess,req:i,silent:t.silent})},cancel:async t=>{let i=t.req??await (0,l.M)({},e),r=[t.where,{completedAt:{exists:!1}},{hasError:{not_equals:!0}}];t.queue&&r.push({queue:{equals:t.queue}}),await (0,tc.Y)({data:{completedAt:null,error:{cancelled:!0},hasError:!0,processing:!1,waitUntil:null},depth:0,disableTransaction:!0,req:i,returning:!1,where:{and:r}})},cancelByID:async t=>{let i=t.req??await (0,l.M)({},e);await (0,tc.l)({id:t.id,data:{completedAt:null,error:{cancelled:!0},hasError:!0,processing:!1,waitUntil:null},depth:0,disableTransaction:!0,req:i,returning:!1})}});var td=i(66253),tf=i(74552),tu=i(14007);let tp={colorize:!0,ignore:"pid,hostname",translateTime:"SYS:HH:MM:ss"},tm=(0,tu.build)({...tp,destination:1,sync:!0}),tg=(0,tu.build)(tp),t_=(e="payload",t)=>{if(!t)return(0,tf.pino)(tg);if("sync"===t)return(0,tf.pino)(tm);if(!("options"in t))return t;{let{destination:i,options:r}=t;return r.name||(r.name=e),r.enabled||(r.enabled="true"!==process.env.DISABLE_LOGGING),(0,tf.pino)(r,i)}};var ty=i(58906);let tw=e=>{(0,ty.BI)({event:{type:"server-init"},payload:e})};var tb=i(91444);let tv=(0,s.fileURLToPath)("file:///C:/Users/<USER>/kavya-git/spark-new/littlespark-cms/node_modules/payload/dist/index.js"),tS=n.dirname(tv);class tk{auth=async e=>ek(this,e);authStrategies;blocks={};collections={};config;count=async e=>b(this,e);countGlobalVersions=async e=>er(this,e);countVersions=async e=>tt(this,e);create=async e=>E(this,e);crons=[];db;decrypt=e_;destroy=async()=>{if(this.crons.length){let e=this.crons.splice(0,this.crons.length);await Promise.all(e.map(e=>e.stop()))}this.db?.destroy&&"function"==typeof this.db.destroy&&await this.db.destroy()};duplicate=async e=>C(this,e);email;encrypt=eg;extensions;find=async e=>N(this,e);findByID=async e=>I(this,e);findDistinct=async e=>G(this,e);findGlobal=async e=>es(this,e);findGlobalVersionByID=async e=>ea(this,e);findGlobalVersions=async e=>ec(this,e);findVersionByID=async e=>z(this,e);findVersions=async e=>Y(this,e);forgotPassword=async e=>h(this,e);getAdminURL=()=>`${this.config.serverURL}${this.config.routes.admin}`;getAPIURL=()=>`${this.config.serverURL}${this.config.routes.api}`;globals;importMap;jobs=th(this);logger;login=async e=>f(this,e);resetPassword=async e=>p(this,e);restoreGlobalVersion=async e=>ed(this,e);restoreVersion=async e=>X(this,e);schema;secret;sendEmail;types;unlock=async e=>g(this,e);updateGlobal=async e=>eu(this,e);validationRules;verifyEmail=async e=>y(this,e);versions={};async bin({args:e,cwd:t,log:i}){return new Promise((o,s)=>{let a=(0,r.spawn)("node",[n.resolve(tS,"../bin.js"),...e],{cwd:t,stdio:i||void 0===i?"inherit":"ignore"});a.on("exit",e=>{o({code:e})}),a.on("error",e=>{s(e)})})}delete(e){return L(this,e)}async init(e){if(this.importMap=e.importMap,!e?.config)throw Error("Error: the payload config is required to initialize payload.");if(this.config=await e.config,this.logger=t_("payload",this.config.logger),!this.config.secret)throw Error("Error: missing secret key. A secret key is needed to secure Payload.");for(let e of(this.secret=o.createHash("sha256").update(this.config.secret).digest("hex").slice(0,32),this.globals={config:this.config.globals},this.config.collections)){let t;let i=({field:e})=>!!["array","blocks","group"].includes(e.type)||"tab"===e.type&&"name"in e||((0,ts.Z7)(e)&&"id"===e.name?(t=e.type,!0):void 0);(0,tb.m)({callback:i,config:this.config,fields:e.fields,parentIsLocalized:!1}),this.collections[e.slug]={config:e,customIDType:t}}if(this.blocks=this.config.blocks.reduce((e,t)=>(e[t.slug]=t,e),{}),this.db=this.config.db.init({payload:this}),this.db.payload=this,this.db?.init&&await this.db.init(),!e.disableDBConnect&&this.db.connect&&await this.db.connect(),this.config.email instanceof Promise){let e=await this.config.email;this.email=e({payload:this})}else this.config.email?this.email=this.config.email({payload:this}):("phase-production-build"!==process.env.NEXT_PHASE&&this.logger.warn("No email adapter provided. Email will be written to console. More info at https://payloadcms.com/docs/email/overview."),this.email=to({payload:this}));if(!this.config.sharp&&this.config.collections.some(e=>e.upload.imageSizes||e.upload.formatOptions)&&this.logger.warn("Image resizing is enabled for one or more collections, but sharp not installed. Please install 'sharp' and pass into the config."),process.env.VERCEL){let e=this.config.collections.filter(e=>e.upload&&void 0===e.upload.adapter);if(e.length){let t=e.map(e=>e.slug).join(", ");this.logger.warn(`Collections with uploads enabled require a storage adapter when deploying to Vercel. Collection(s) without storage adapters: ${t}. See https://payloadcms.com/docs/upload/storage-adapters for more info.`)}}this.sendEmail=this.email.sendEmail,tw(this);let t=!1;this.authStrategies=this.config.collections.reduce((e,i)=>(!i?.auth||(i.auth.strategies.length>0&&e.push(...i.auth.strategies),i.auth?.useAPIKey&&e.push({name:`${i.slug}-api-key`,authenticate:eE(i)}),i.auth.disableLocalStrategy||t||(t=!0)),e),[]),t&&this.authStrategies.push({name:"local-jwt",authenticate:e0});try{e.disableOnInit||("function"==typeof e.onInit&&await e.onInit(this),"function"==typeof this.config.onInit&&await this.config.onInit(this))}catch(e){throw this.logger.error({err:e},"Error running onInit function"),e}if(this.config.jobs.enabled&&this.config.jobs.autoRun&&!("phase-production-build"===process.env.NEXT_PHASE||"build"===process.env.npm_lifecycle_event)&&e.cron){let e="function"==typeof this.config.jobs.autoRun?await this.config.jobs.autoRun(this):this.config.jobs.autoRun;await Promise.all(e.map(e=>{let t=new ep.l4(e.cron??"* * * * *",async()=>{if(td.sO.shouldAutoSchedule&&!e.disableScheduling&&this.config.jobs.scheduling&&await this.jobs.handleSchedules({queue:e.queue}),td.sO.shouldAutoRun){if("function"==typeof this.config.jobs.shouldAutoRun&&!await this.config.jobs.shouldAutoRun(this)){t.stop();return}await this.jobs.run({limit:e.limit??10,queue:e.queue,silent:e.silent})}});this.crons.push(t)}))}return this}update(e){return ee(this,e)}}new tk;let tE=global._payload;tE||(tE=global._payload={payload:null,promise:null,reload:!1,ws:null});let tx=async(e,t,i)=>{"function"==typeof t.db.destroy&&await t.db.destroy(),t.config=e,t.collections=e.collections.reduce((e,i)=>(e[i.slug]={config:i,customIDType:t.collections[i.slug]?.customIDType},e),{}),t.blocks=e.blocks.reduce((e,t)=>(e[t.slug]=t,e),{}),t.globals={config:e.globals},!1!==e.typescript.autoGenerate&&t.bin({args:["generate:types"],log:!1}),!0!==i&&e.admin?.importMap?.autoGenerate!==!1&&await e6(e,{log:!0}),await t.db.init?.(),t.db.connect&&await t.db.connect({hotReload:!0}),global._payload_clientConfigs={},global._payload_schemaMap=null,global._payload_clientSchemaMap=null,global._payload_doNotCacheClientConfig=!0,global._payload_doNotCacheSchemaMap=!0,global._payload_doNotCacheClientSchemaMap=!0},tT=async e=>{if(!e?.config)throw Error("Error: the payload config is required for getPayload to work.");if(tE.payload){if(!0===tE.reload){let t;tE.reload=new Promise(e=>t=e);let i=await e.config;await tx(i,tE.payload,!e.importMap),t()}return tE.reload instanceof Promise&&await tE.reload,e?.importMap&&(tE.payload.importMap=e.importMap),tE.payload}tE.promise||(tE.promise=new tk().init(e));try{tE.payload=await tE.promise,tE.ws}catch(e){throw tE.promise=null,e.payloadInitError=!0,e}return e?.importMap&&(tE.payload.importMap=e.importMap),tE.payload}}};