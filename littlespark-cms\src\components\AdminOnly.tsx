import React, { useState, useEffect, useCallback } from 'react';

interface AdminOnlyProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

interface User {
  id: string;
  email: string;
  role: string;
  isActive: boolean;
}

/**
 * Component that only renders its children if the current user is an admin
 * Provides role-based access control for UI elements
 */
  export function AdminOnly({ children, fallback }: AdminOnlyProps) {
  const [isAdmin, setIsAdmin] = useState<boolean | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  const checkUserPermissions = useCallback(async () => {
    try {
      // Get auth token from localStorage or cookies
      const token = getAuthToken();
      if (!token) {
        setIsAdmin(false);
        setLoading(false);
        return;
      }

      // Verify user permissions with the server
      const response = await fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bear<PERSON> ${token}`,
        },
      });

      if (response.ok) {
        const userData = await response.json();
        setUser(userData.user);
        
        // Check if user has admin role
        const hasPermission = userData.user?.role === 'admin' && userData.user?.isActive;
        setIsAdmin(hasPermission);
      } else {
        setIsAdmin(false);
      }
    } catch (error) {
      console.error('Error checking user permissions:', error);
      setIsAdmin(false);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    checkUserPermissions();
  }, [checkUserPermissions]);

  const getAuthToken = (): string | null => {
    // Try localStorage first (common for Payload CMS)
    const token = localStorage.getItem('payload-token');
    if (token) return token;

    // Try cookies as fallback
    const cookies = document.cookie.split(';');
    for (const cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === 'payload-token') {
        return value;
      }
    }

    return null;
  };

  // Show loading state
  if (loading) {
    return (
      <div style={{ 
        padding: '20px', 
        textAlign: 'center', 
        color: '#666',
        fontSize: '14px'
      }}>
        🔐 Checking permissions...
      </div>
    );
  }

  // Show fallback if user is not admin
  if (!isAdmin) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div style={{ 
        padding: '20px', 
        backgroundColor: '#fef2f2', 
        border: '1px solid #fecaca', 
        borderRadius: '8px',
        textAlign: 'center'
      }}>
        <div style={{ fontSize: '48px', marginBottom: '10px' }}>🔒</div>
        <h3 style={{ color: '#dc2626', marginBottom: '8px', fontSize: '18px' }}>
          Admin Access Required
        </h3>
        <p style={{ color: '#991b1b', fontSize: '14px', margin: '0' }}>
          This feature is only available to CMS administrators.
        </p>
        {user && (
          <p style={{ color: '#7f1d1d', fontSize: '12px', marginTop: '8px' }}>
            Current user: {user.email} (Role: {user.role})
          </p>
        )}
      </div>
    );
  }

  // Render children if user is admin
  return <>{children}</>;
}

/**
 * Hook to check if current user has admin permissions
 */
export function useIsAdmin() {
  const [isAdmin, setIsAdmin] = useState<boolean | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkAdminStatus();
  }, []);

  const checkAdminStatus = async () => {
    try {
      const token = localStorage.getItem('payload-token');
      if (!token) {
        setIsAdmin(false);
        setLoading(false);
        return;
      }

      const response = await fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setIsAdmin(data.user?.role === 'admin' && data.user?.isActive);
      } else {
        setIsAdmin(false);
      }
    } catch (error) {
      console.error('Error checking admin status:', error);
      setIsAdmin(false);
    } finally {
      setLoading(false);
    }
  };

  return { isAdmin, loading };
}

/**
 * Simple wrapper component for admin-only content
 */
export function AdminOnlyWrapper({ children }: { children: React.ReactNode }) {
  return (
    <AdminOnly
      fallback={
        <div style={{ 
          padding: '40px', 
          textAlign: 'center',
          backgroundColor: '#f9fafb',
          border: '2px dashed #d1d5db',
          borderRadius: '8px'
        }}>
          <div style={{ fontSize: '64px', marginBottom: '16px' }}>🛡️</div>
          <h2 style={{ color: '#374151', marginBottom: '8px' }}>
            Administrator Access Only
          </h2>
          <p style={{ color: '#6b7280', fontSize: '16px' }}>
            This section is restricted to CMS administrators.
          </p>
        </div>
      }
    >
      {children}
    </AdminOnly>
  );
}
