'use client';

import React, { useEffect, useState } from 'react';

export default function SyncPage() {
  const [mounted, setMounted] = useState(false);
  const [stats, setStats] = useState({
    cmsChallenge: 0,
    mainAppCmsChallenge: 0
  });

  useEffect(() => {
    setMounted(true);
    fetchStats();
  }, []);

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/sync/challenges');
      const data = await response.json();
      if (data.success && data.stats) {
        setStats({
          cmsChallenge: data.stats.cmsChallenge || 0,
          mainAppCmsChallenge: data.stats.mainAppCmsChallenge || 0
        });
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const addTestData = async () => {
    try {
      const response = await fetch('/api/test-data', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      const data = await response.json();
      alert(`Test data result: ${data.stats?.created || 0} challenges created, ${data.stats?.skipped || 0} skipped`);
      await fetchStats();
    } catch (error) {
      alert('Error: ' + error.message);
    }
  };

  const syncChallenges = async () => {
    try {
      const response = await fetch('/api/sync/challenges', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });
      const data = await response.json();
      alert(`Sync result: ${data.stats?.synced || 0} challenges synced successfully`);
      await fetchStats();
    } catch (error) {
      alert('Error: ' + error.message);
    }
  };

  if (!mounted) {
    return (
      <div style={{ padding: '20px', textAlign: 'center' }}>
        <div>Loading sync panel...</div>
      </div>
    );
  }
  return (
    <div className="sync-page">
      <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      <div style={{ marginBottom: '30px' }}>
        <h1 style={{ fontSize: '28px', fontWeight: 'bold', marginBottom: '10px' }}>
          🔄 Sync Management
        </h1>
        <p style={{ color: '#666', fontSize: '16px' }}>
          Synchronize content and users between CMS and main application
        </p>
        <div style={{ 
          display: 'inline-block',
          backgroundColor: '#f0f9ff',
          color: '#0369a1',
          padding: '4px 12px',
          borderRadius: '6px',
          fontSize: '14px',
          fontWeight: '500',
          marginTop: '10px'
        }}>
          🛡️ Admin Only Feature
        </div>
      </div>

      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '20px', marginBottom: '30px' }}>
        {/* Challenge Sync Card */}
        <div style={{ 
          border: '1px solid #e5e7eb', 
          borderRadius: '8px', 
          padding: '20px',
          backgroundColor: '#fff'
        }}>
          <h2 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '10px' }}>
            📝 Challenge Sync
          </h2>
          <p style={{ color: '#666', marginBottom: '20px', fontSize: '14px' }}>
            Sync published challenges from CMS to main application
          </p>
          
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' }}>
            <div style={{ 
              backgroundColor: '#eff6ff', 
              padding: '15px', 
              borderRadius: '6px',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#2563eb' }}>
                <span id="cms-challenges-count">{stats.cmsChallenge}</span>
              </div>
              <div style={{ fontSize: '12px', color: '#1e40af', fontWeight: '500' }}>
                CMS Challenges
              </div>
            </div>
            <div style={{
              backgroundColor: '#f0fdf4',
              padding: '15px',
              borderRadius: '6px',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#16a34a' }}>
                <span id="synced-challenges-count">{stats.mainAppCmsChallenge}</span>
              </div>
              <div style={{ fontSize: '12px', color: '#15803d', fontWeight: '500' }}>
                Synced to Main App
              </div>
            </div>
          </div>

          <button
            id="sync-challenges-btn"
            onClick={syncChallenges}
            style={{
              width: '100%',
              backgroundColor: '#2563eb',
              color: 'white',
              border: 'none',
              padding: '12px 20px',
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              cursor: 'pointer',
              marginBottom: '10px'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#1d4ed8'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#2563eb'}
          >
            🔄 Sync Challenges to Main App
          </button>

          <div id="challenge-sync-result" style={{ fontSize: '12px', color: '#666' }}></div>
        </div>

        {/* User Sync Card */}
        <div style={{ 
          border: '1px solid #e5e7eb', 
          borderRadius: '8px', 
          padding: '20px',
          backgroundColor: '#fff'
        }}>
          <h2 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '10px' }}>
            👥 User Sync
          </h2>
          <p style={{ color: '#666', marginBottom: '20px', fontSize: '14px' }}>
            Sync CMS users to main application for content access
          </p>
          
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' }}>
            <div style={{ 
              backgroundColor: '#faf5ff', 
              padding: '15px', 
              borderRadius: '6px',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#9333ea' }}>
                <span id="cms-users-count">-</span>
              </div>
              <div style={{ fontSize: '12px', color: '#7c3aed', fontWeight: '500' }}>
                CMS Users
              </div>
            </div>
            <div style={{ 
              backgroundColor: '#fff7ed', 
              padding: '15px', 
              borderRadius: '6px',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#ea580c' }}>
                <span id="synced-users-count">-</span>
              </div>
              <div style={{ fontSize: '12px', color: '#c2410c', fontWeight: '500' }}>
                Synced to Main App
              </div>
            </div>
          </div>

          <button 
            id="sync-users-btn"
            style={{
              width: '100%',
              backgroundColor: '#9333ea',
              color: 'white',
              border: 'none',
              padding: '12px 20px',
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              cursor: 'pointer',
              marginBottom: '10px'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#7c3aed'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#9333ea'}
          >
            🔄 Sync Users to Main App
          </button>

          <div id="user-sync-result" style={{ fontSize: '12px', color: '#666' }}></div>
        </div>

        {/* Reverse Sync Card - User-created challenges from Main App */}
        <div style={{
          border: '1px solid #e5e7eb',
          borderRadius: '8px',
          padding: '20px',
          backgroundColor: '#fff'
        }}>
          <h2 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '10px' }}>
            ⬅️ Import User Challenges
          </h2>
          <p style={{ color: '#666', marginBottom: '20px', fontSize: '14px' }}>
            Import user-created challenges from main app to CMS for review
          </p>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '20px' }}>
            <div style={{
              backgroundColor: '#f0f9ff',
              padding: '15px',
              borderRadius: '6px',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#0369a1' }}>
                <span id="available-user-challenges-count">-</span>
              </div>
              <div style={{ fontSize: '12px', color: '#0c4a6e', fontWeight: '500' }}>
                Available to Import
              </div>
            </div>
            <div style={{
              backgroundColor: '#f0fdf4',
              padding: '15px',
              borderRadius: '6px',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#16a34a' }}>
                <span id="imported-challenges-count">-</span>
              </div>
              <div style={{ fontSize: '12px', color: '#15803d', fontWeight: '500' }}>
                Already Imported
              </div>
            </div>
          </div>

          <button
            id="import-challenges-btn"
            style={{
              width: '100%',
              backgroundColor: '#0369a1',
              color: 'white',
              border: 'none',
              padding: '12px 20px',
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              cursor: 'pointer',
              marginBottom: '10px'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#0c4a6e'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#0369a1'}
          >
            ⬅️ Import User Challenges from Main App
          </button>

          <div id="import-sync-result" style={{ fontSize: '12px', color: '#666' }}></div>
        </div>
      </div>

      {/* Action Buttons */}
      <div style={{ textAlign: 'center', marginBottom: '30px' }}>
        <div style={{ display: 'flex', gap: '15px', justifyContent: 'center', flexWrap: 'wrap' }}>
          <button
            id="refresh-stats-btn"
            onClick={fetchStats}
            style={{
              backgroundColor: '#f3f4f6',
              color: '#374151',
              border: '1px solid #d1d5db',
              padding: '10px 20px',
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              cursor: 'pointer'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#e5e7eb'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#f3f4f6'}
          >
            🔄 Refresh Statistics
          </button>

          <button
            id="add-test-data-btn"
            onClick={addTestData}
            style={{
              backgroundColor: '#10b981',
              color: 'white',
              border: 'none',
              padding: '10px 20px',
              borderRadius: '6px',
              fontSize: '14px',
              fontWeight: '500',
              cursor: 'pointer'
            }}
            onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#059669'}
            onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#10b981'}
          >
            🧪 Add Sample Challenges
          </button>
        </div>
      </div>

      {/* Error Display */}
      <div 
        id="error-display" 
        style={{ 
          display: 'none',
          backgroundColor: '#fef2f2',
          border: '1px solid #fecaca',
          color: '#dc2626',
          padding: '15px',
          borderRadius: '6px',
          marginBottom: '20px'
        }}
      >
        <strong>Error:</strong> <span id="error-message"></span>
      </div>

      {/* Info Panel */}
      <div style={{ 
        backgroundColor: '#fffbeb', 
        border: '1px solid #fed7aa', 
        borderRadius: '8px', 
        padding: '20px' 
      }}>
        <h3 style={{ fontSize: '16px', fontWeight: '600', marginBottom: '10px', color: '#92400e' }}>
          ℹ️ Sync Information
        </h3>
        <ul style={{ fontSize: '14px', color: '#92400e', lineHeight: '1.6' }}>
          <li><strong>Challenge Sync:</strong> Publishes CMS challenges to the main application for users to access</li>
          <li><strong>User Sync:</strong> Creates accounts in the main application for CMS content creators and educators</li>
          <li><strong>Admin Only:</strong> Only CMS administrators can perform sync operations</li>
          <li><strong>Safe Operation:</strong> Sync operations are idempotent and won't create duplicates</li>
        </ul>
      </div>

        {/* Load sync functionality script */}
        <script src="/admin/sync/sync.js" defer></script>

        {/* Inline sync functionality for immediate loading */}
        <script dangerouslySetInnerHTML={{
          __html: `
            console.log('🔄 Sync page loaded');

            // Fetch and update stats
            window.fetchStats = async function() {
              try {
                console.log('Fetching stats...');
                const response = await fetch('/api/sync/challenges');
                const data = await response.json();
                console.log('Stats data:', data);

                if (data.success && data.stats) {
                  document.getElementById('cms-challenges-count').textContent = data.stats.cmsChallenge || '0';
                  document.getElementById('synced-challenges-count').textContent = data.stats.mainAppCmsChallenge || '0';
                }
              } catch (error) {
                console.error('Error fetching stats:', error);
              }
            };

            // Simple test data function
            window.addTestDataSimple = async function() {
              try {
                console.log('Adding test data...');
                const response = await fetch('/api/test-data', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' }
                });
                const data = await response.json();
                console.log('Test data result:', data);
                alert('Test data result: ' + JSON.stringify(data, null, 2));

                // Refresh stats
                await window.fetchStats();
              } catch (error) {
                console.error('Error:', error);
                alert('Error: ' + error.message);
              }
            };

            // Simple sync function
            window.syncChallengesSimple = async function() {
              try {
                console.log('Syncing challenges...');
                const response = await fetch('/api/sync/challenges', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' }
                });
                const data = await response.json();
                console.log('Sync result:', data);
                alert('Sync result: ' + JSON.stringify(data, null, 2));

                // Refresh stats
                await window.fetchStats();
              } catch (error) {
                console.error('Error:', error);
                alert('Error: ' + error.message);
              }
            };

            // Add click handlers when DOM is ready
            document.addEventListener('DOMContentLoaded', function() {
              const addTestBtn = document.getElementById('add-test-data-btn');
              const syncBtn = document.getElementById('sync-challenges-btn');
              const refreshBtn = document.getElementById('refresh-stats-btn');

              if (addTestBtn) {
                addTestBtn.onclick = window.addTestDataSimple;
                console.log('✅ Added test data button handler');
              }

              if (syncBtn) {
                syncBtn.onclick = window.syncChallengesSimple;
                console.log('✅ Added sync button handler');
              }

              if (refreshBtn) {
                refreshBtn.onclick = window.fetchStats;
                console.log('✅ Added refresh stats button handler');
              }

              // Load initial stats
              window.fetchStats();
            });
          `
        }} />

        {/* Add floating sync button to other admin pages */}
        <script dangerouslySetInnerHTML={{
          __html: `
            // Add floating sync button to CMS admin panel
            (function() {
              function addSyncButtonToOtherPages() {
                // Only add on non-sync pages
                if (window.location.pathname.includes('/admin/sync')) return;

                // Check if button already exists
                if (document.getElementById('floating-sync-btn')) return;

                const syncButton = document.createElement('div');
                syncButton.id = 'floating-sync-btn';
                syncButton.style.cssText = \`
                  position: fixed;
                  bottom: 20px;
                  right: 20px;
                  z-index: 9999;
                  background: linear-gradient(135deg, #0070f3, #0056b3);
                  color: white;
                  width: 60px;
                  height: 60px;
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  cursor: pointer;
                  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                  transition: all 0.3s ease;
                  font-size: 24px;
                  border: 2px solid white;
                \`;

                syncButton.innerHTML = '🔄';
                syncButton.title = 'Open Sync Management Panel';

                syncButton.addEventListener('mouseenter', function() {
                  this.style.transform = 'scale(1.1)';
                });

                syncButton.addEventListener('mouseleave', function() {
                  this.style.transform = 'scale(1)';
                });

                syncButton.addEventListener('click', function() {
                  window.location.href = '/admin/sync';
                });

                document.body.appendChild(syncButton);
              }

              // Try to add button to parent window (if in iframe)
              try {
                if (window.parent && window.parent !== window) {
                  window.parent.postMessage({type: 'ADD_SYNC_BUTTON'}, '*');
                }
              } catch(e) {}

              // Add to current window
              setTimeout(addSyncButtonToOtherPages, 1000);
            })();
          `
        }} />
      </div>
    </div>
  );
}
