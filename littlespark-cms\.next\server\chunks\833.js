"use strict";exports.id=833,exports.ids=[833],exports.modules={1039:(a,b,c)=>{c.d(b,{k:()=>d});function d(a){return (b={})=>{let c=b.width?String(b.width):a.defaultWidth;return a.formats[c]||a.formats[a.defaultWidth]}}},28153:(a,b,c)=>{function d(a){return(b,c={})=>{let d,e=c.width,f=e&&a.matchPatterns[e]||a.matchPatterns[a.defaultMatchWidth],g=b.match(f);if(!g)return null;let h=g[0],i=e&&a.parsePatterns[e]||a.parsePatterns[a.defaultParseWidth],j=Array.isArray(i)?function(a,b){for(let c=0;c<a.length;c++)if(b(a[c]))return c}(i,a=>a.test(h)):function(a,b){for(let c in a)if(Object.prototype.hasOwnProperty.call(a,c)&&b(a[c]))return c}(i,a=>a.test(h));return d=a.valueCallback?a.valueCallback(j):j,{value:d=c.valueCallback?c.valueCallback(d):d,rest:b.slice(h.length)}}}c.d(b,{A:()=>d})},55991:(a,b,c)=>{c.d(b,{K:()=>d});function d(a){return(b,c={})=>{let d=b.match(a.matchPattern);if(!d)return null;let e=d[0],f=b.match(a.parsePattern);if(!f)return null;let g=a.valueCallback?a.valueCallback(f[0]):f[0];return{value:g=c.valueCallback?c.valueCallback(g):g,rest:b.slice(e.length)}}}},70833:(a,b,c)=>{c.r(b),c.d(b,{default:()=>l,ro:()=>k});let d={lessThanXSeconds:{one:"mai puțin de o secundă",other:"mai puțin de {{count}} secunde"},xSeconds:{one:"1 secundă",other:"{{count}} secunde"},halfAMinute:"jumătate de minut",lessThanXMinutes:{one:"mai puțin de un minut",other:"mai puțin de {{count}} minute"},xMinutes:{one:"1 minut",other:"{{count}} minute"},aboutXHours:{one:"circa 1 oră",other:"circa {{count}} ore"},xHours:{one:"1 oră",other:"{{count}} ore"},xDays:{one:"1 zi",other:"{{count}} zile"},aboutXWeeks:{one:"circa o săptăm\xe2nă",other:"circa {{count}} săptăm\xe2ni"},xWeeks:{one:"1 săptăm\xe2nă",other:"{{count}} săptăm\xe2ni"},aboutXMonths:{one:"circa 1 lună",other:"circa {{count}} luni"},xMonths:{one:"1 lună",other:"{{count}} luni"},aboutXYears:{one:"circa 1 an",other:"circa {{count}} ani"},xYears:{one:"1 an",other:"{{count}} ani"},overXYears:{one:"peste 1 an",other:"peste {{count}} ani"},almostXYears:{one:"aproape 1 an",other:"aproape {{count}} ani"}};var e=c(1039);let f={date:(0,e.k)({formats:{full:"EEEE, d MMMM yyyy",long:"d MMMM yyyy",medium:"d MMM yyyy",short:"dd.MM.yyyy"},defaultWidth:"full"}),time:(0,e.k)({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},defaultWidth:"full"}),dateTime:(0,e.k)({formats:{full:"{{date}} 'la' {{time}}",long:"{{date}} 'la' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},g={lastWeek:"eeee 'trecută la' p",yesterday:"'ieri la' p",today:"'astăzi la' p",tomorrow:"'m\xe2ine la' p",nextWeek:"eeee 'viitoare la' p",other:"P"};var h=c(80429);let i={ordinalNumber:(a,b)=>String(a),era:(0,h.o)({values:{narrow:["\xce","D"],abbreviated:["\xce.d.C.","D.C."],wide:["\xcenainte de Cristos","După Cristos"]},defaultWidth:"wide"}),quarter:(0,h.o)({values:{narrow:["1","2","3","4"],abbreviated:["T1","T2","T3","T4"],wide:["primul trimestru","al doilea trimestru","al treilea trimestru","al patrulea trimestru"]},defaultWidth:"wide",argumentCallback:a=>a-1}),month:(0,h.o)({values:{narrow:["I","F","M","A","M","I","I","A","S","O","N","D"],abbreviated:["ian","feb","mar","apr","mai","iun","iul","aug","sep","oct","noi","dec"],wide:["ianuarie","februarie","martie","aprilie","mai","iunie","iulie","august","septembrie","octombrie","noiembrie","decembrie"]},defaultWidth:"wide"}),day:(0,h.o)({values:{narrow:["d","l","m","m","j","v","s"],short:["du","lu","ma","mi","jo","vi","s\xe2"],abbreviated:["dum","lun","mar","mie","joi","vin","s\xe2m"],wide:["duminică","luni","marți","miercuri","joi","vineri","s\xe2mbătă"]},defaultWidth:"wide"}),dayPeriod:(0,h.o)({values:{narrow:{am:"a",pm:"p",midnight:"mn",noon:"ami",morning:"dim",afternoon:"da",evening:"s",night:"n"},abbreviated:{am:"AM",pm:"PM",midnight:"miezul nopții",noon:"amiază",morning:"dimineață",afternoon:"după-amiază",evening:"seară",night:"noapte"},wide:{am:"a.m.",pm:"p.m.",midnight:"miezul nopții",noon:"amiază",morning:"dimineață",afternoon:"după-amiază",evening:"seară",night:"noapte"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mn",noon:"amiază",morning:"dimineață",afternoon:"după-amiază",evening:"seară",night:"noapte"},abbreviated:{am:"AM",pm:"PM",midnight:"miezul nopții",noon:"amiază",morning:"dimineață",afternoon:"după-amiază",evening:"seară",night:"noapte"},wide:{am:"a.m.",pm:"p.m.",midnight:"miezul nopții",noon:"amiază",morning:"dimineață",afternoon:"după-amiază",evening:"seară",night:"noapte"}},defaultFormattingWidth:"wide"})};var j=c(28153);let k={code:"ro",formatDistance:(a,b,c)=>{let e,f=d[a];if(e="string"==typeof f?f:1===b?f.one:f.other.replace("{{count}}",String(b)),c?.addSuffix)if(c.comparison&&c.comparison>0)return"\xeen "+e;else return e+" \xeen urmă";return e},formatLong:f,formatRelative:(a,b,c,d)=>g[a],localize:i,match:{ordinalNumber:(0,c(55991).K)({matchPattern:/^(\d+)?/i,parsePattern:/\d+/i,valueCallback:a=>parseInt(a,10)}),era:(0,j.A)({matchPatterns:{narrow:/^(Î|D)/i,abbreviated:/^(Î\.?\s?d\.?\s?C\.?|Î\.?\s?e\.?\s?n\.?|D\.?\s?C\.?|e\.?\s?n\.?)/i,wide:/^(Înainte de Cristos|Înaintea erei noastre|După Cristos|Era noastră)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^ÎC/i,/^DC/i],wide:[/^(Înainte de Cristos|Înaintea erei noastre)/i,/^(După Cristos|Era noastră)/i]},defaultParseWidth:"any"}),quarter:(0,j.A)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^T[1234]/i,wide:/^trimestrul [1234]/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:a=>a+1}),month:(0,j.A)({matchPatterns:{narrow:/^[ifmaasond]/i,abbreviated:/^(ian|feb|mar|apr|mai|iun|iul|aug|sep|oct|noi|dec)/i,wide:/^(ianuarie|februarie|martie|aprilie|mai|iunie|iulie|august|septembrie|octombrie|noiembrie|decembrie)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^i/i,/^f/i,/^m/i,/^a/i,/^m/i,/^i/i,/^i/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ia/i,/^f/i,/^mar/i,/^ap/i,/^mai/i,/^iun/i,/^iul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:(0,j.A)({matchPatterns:{narrow:/^[dlmjvs]/i,short:/^(d|l|ma|mi|j|v|s)/i,abbreviated:/^(dum|lun|mar|mie|jo|vi|sâ)/i,wide:/^(duminica|luni|marţi|miercuri|joi|vineri|sâmbătă)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^j/i,/^v/i,/^s/i],any:[/^d/i,/^l/i,/^ma/i,/^mi/i,/^j/i,/^v/i,/^s/i]},defaultParseWidth:"any"}),dayPeriod:(0,j.A)({matchPatterns:{narrow:/^(a|p|mn|a|(dimineaţa|după-amiaza|seara|noaptea))/i,any:/^([ap]\.?\s?m\.?|miezul nopții|amiaza|(dimineaţa|după-amiaza|seara|noaptea))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mn/i,noon:/amiaza/i,morning:/dimineaţa/i,afternoon:/după-amiaza/i,evening:/seara/i,night:/noaptea/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:1}},l=k},80429:(a,b,c)=>{c.d(b,{o:()=>d});function d(a){return(b,c)=>{let d;if("formatting"===(c?.context?String(c.context):"standalone")&&a.formattingValues){let b=a.defaultFormattingWidth||a.defaultWidth,e=c?.width?String(c.width):b;d=a.formattingValues[e]||a.formattingValues[b]}else{let b=a.defaultWidth,e=c?.width?String(c.width):a.defaultWidth;d=a.values[e]||a.values[b]}return d[a.argumentCallback?a.argumentCallback(b):b]}}}};