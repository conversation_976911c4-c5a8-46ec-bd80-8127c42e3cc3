// API functions to fetch content from Payload CMS via our API routes
const API_BASE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';
const CMS_BASE_URL = process.env.CMS_BASE_URL || 'http://localhost:3001';
const CMS_ENABLED = process.env.NEXT_PUBLIC_CMS_ENABLED === 'true';

export interface CMSChallenge {
  id: string;
  title: string;
  slug: string;
  description: string;
  prompt: string; // Added for Supabase compatibility
  category: 'art' | 'story' | 'music' | 'coding' | 'video' | 'game';
  ageGroup: string[];
  difficulty: 'easy' | 'medium' | 'hard';
  estimatedTime: number;
  instructions: string;
  learningObjectives: Array<{ objective: string }>;
  materials?: Array<{ material: string; optional: boolean }>;
  media?: Array<{
    file: { url: string; alt?: string };
    caption?: string;
    type: 'tutorial' | 'example' | 'reference' | 'step';
  }>;
  subscriptionTier: 'free' | 'premium';
  featured: boolean;
  is_active?: boolean; // Added for Supabase compatibility
  status: 'draft' | 'review' | 'published' | 'archived';
  publishedAt?: string;
}

export interface CMSStoryTemplate {
  id: string;
  title: string;
  slug: string;
  description: string;
  genre: string[];
  ageGroup: string[];
  storyPrompt: string;
  characterOptions: Array<{
    name: string;
    description: string;
    image?: { url: string; alt?: string };
  }>;
  settingOptions: Array<{
    name: string;
    description: string;
    image?: { url: string; alt?: string };
  }>;
  plotPoints: Array<{
    title: string;
    description: string;
    order: number;
    optional: boolean;
  }>;
  estimatedLength?: {
    minWords: number;
    maxWords: number;
  };
  subscriptionTier: 'free' | 'premium';
  featured: boolean;
  status: 'draft' | 'review' | 'published' | 'archived';
}

export interface CMSEducationalResource {
  id: string;
  title: string;
  slug: string;
  description: string;
  type: 'tutorial' | 'guide' | 'reference' | 'video' | 'interactive' | 'worksheet' | 'tips';
  subject: string[];
  ageGroup: string[];
  content: string;
  media?: Array<{
    file: { url: string; alt?: string };
    caption?: string;
    type: 'main' | 'supporting' | 'example';
  }>;
  downloadableFiles?: Array<{
    file: { url: string; filename: string };
    title: string;
    description?: string;
  }>;
  prerequisites?: string[];
  learningOutcomes: Array<{ outcome: string }>;
  estimatedReadTime?: number;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  subscriptionTier: 'free' | 'premium';
  featured: boolean;
  tags?: string[];
  publishedAt?: string;
}

// Fetch published challenges from CMS
export async function getChallenges(filters?: {
  category?: string;
  ageGroup?: string;
  difficulty?: string;
  featured?: boolean;
}): Promise<CMSChallenge[]> {
  if (!CMS_ENABLED) {
    console.log('CMS disabled, returning empty array');
    return [];
  }

  try {
    const params = new URLSearchParams();

    // Add filters
    if (filters?.category) {
      params.append('category', filters.category);
    }
    if (filters?.ageGroup) {
      params.append('ageGroup', filters.ageGroup);
    }
    if (filters?.difficulty) {
      params.append('difficulty', filters.difficulty);
    }
    if (filters?.featured) {
      params.append('featured', 'true');
    }

    const response = await fetch(`${API_BASE_URL}/api/cms/challenges?${params}`, {
      next: { revalidate: 300 }, // Cache for 5 minutes
    });

    if (!response.ok) {
      throw new Error('Failed to fetch challenges');
    }

    const data = await response.json();
    return data.challenges || [];
  } catch (error) {
    console.error('Error fetching challenges:', error);
    return [];
  }
}

// Fetch single challenge by slug
export async function getChallenge(slug: string): Promise<CMSChallenge | null> {
  if (!CMS_ENABLED) {
    return null;
  }

  try {
    const response = await fetch(`${API_BASE_URL}/api/cms/challenges/${slug}`, {
      next: { revalidate: 300 },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch challenge');
    }

    const data = await response.json();
    return data.challenge || null;
  } catch (error) {
    console.error('Error fetching challenge:', error);
    return null;
  }
}

// Fetch featured challenges for homepage
export async function getFeaturedChallenges(): Promise<CMSChallenge[]> {
  return getChallenges({ featured: true });
}

// Fetch story templates
export async function getStoryTemplates(filters?: {
  genre?: string;
  ageGroup?: string;
}): Promise<CMSStoryTemplate[]> {
  if (!CMS_ENABLED) {
    return [];
  }

  try {
    const params = new URLSearchParams();

    if (filters?.genre) {
      params.append('genre', filters.genre);
    }
    if (filters?.ageGroup) {
      params.append('ageGroup', filters.ageGroup);
    }

    const response = await fetch(`${API_BASE_URL}/api/cms/story-templates?${params}`, {
      next: { revalidate: 300 },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch story templates');
    }

    const data = await response.json();
    return data.storyTemplates || [];
  } catch (error) {
    console.error('Error fetching story templates:', error);
    return [];
  }
}

// Fetch single story template
export async function getStoryTemplate(slug: string): Promise<CMSStoryTemplate | null> {
  try {
    const response = await fetch(
      `${CMS_BASE_URL}/api/story-templates?where[slug][equals]=${slug}&where[status][equals]=published`
    );
    
    if (!response.ok) {
      throw new Error('Failed to fetch story template');
    }
    
    const data = await response.json();
    return data.docs?.[0] || null;
  } catch (error) {
    console.error('Error fetching story template:', error);
    return null;
  }
}

// Add missing educational resources function
export async function getEducationalResources(filters?: {
  type?: string;
  subject?: string;
  ageGroup?: string;
  difficulty?: string;
}): Promise<CMSEducationalResource[]> {
  if (!CMS_ENABLED) {
    return [];
  }

  try {
    const params = new URLSearchParams();

    if (filters?.type) {
      params.append('type', filters.type);
    }
    if (filters?.subject) {
      params.append('subject', filters.subject);
    }
    if (filters?.ageGroup) {
      params.append('ageGroup', filters.ageGroup);
    }
    if (filters?.difficulty) {
      params.append('difficulty', filters.difficulty);
    }

    const response = await fetch(`${API_BASE_URL}/api/cms/educational-resources?${params}`, {
      next: { revalidate: 300 },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch educational resources');
    }

    const data = await response.json();
    return data.educationalResources || [];
  } catch (error) {
    console.error('Error fetching educational resources:', error);
    return [];
  }
}

// Helper function to get media URL
export function getMediaUrl(mediaPath: string): string {
  if (mediaPath.startsWith('http')) {
    return mediaPath;
  }
  return `${process.env.CMS_BASE_URL || 'http://localhost:3001'}${mediaPath}`;
}

// Cache management for better performance
const cache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export async function getCachedChallenges(filters?: {
  category?: string;
  difficulty?: string;
  ageGroup?: string;
  featured?: boolean;
}): Promise<CMSChallenge[]> {
  const cacheKey = JSON.stringify(filters || {});
  const cached = cache.get(cacheKey);
  
  if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
    return cached.data;
  }
  
  const data = await getChallenges(filters);
  cache.set(cacheKey, { data, timestamp: Date.now() });
  
  return data;
}
