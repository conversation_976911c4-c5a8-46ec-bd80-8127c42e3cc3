import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET /api/challenges/debug - Debug challenges in database (no auth for debugging)
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 [DEBUG] Checking challenges in database');

    // Count all challenges
    const totalCount = await prisma.challenge.count();
    
    // Count CMS challenges
    const cmsCount = await prisma.challenge.count({
      where: {
        created_by: 'cms'
      }
    });

    // Get all challenges
    const allChallenges = await prisma.challenge.findMany({
      orderBy: { created_at: 'desc' },
      take: 20
    });

    // Get CMS challenges specifically
    const cmsChallenges = await prisma.challenge.findMany({
      where: {
        created_by: 'cms'
      },
      orderBy: { created_at: 'desc' }
    });

    console.log(`📊 [DEBUG] Total challenges: ${totalCount}`);
    console.log(`📊 [DEBUG] CMS challenges: ${cmsCount}`);

    return NextResponse.json({
      success: true,
      stats: {
        total: totalCount,
        cms: cmsCount,
        other: totalCount - cmsCount
      },
      allChallenges: allChallenges.map(c => ({
        id: c.id,
        title: c.title,
        created_by: c.created_by,
        cms_id: c.cms_id,
        created_at: c.created_at
      })),
      cmsChallenges: cmsChallenges.map(c => ({
        id: c.id,
        title: c.title,
        cms_id: c.cms_id,
        created_at: c.created_at
      }))
    });

  } catch (error) {
    console.error('❌ [DEBUG] Error checking challenges:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to check challenges',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
