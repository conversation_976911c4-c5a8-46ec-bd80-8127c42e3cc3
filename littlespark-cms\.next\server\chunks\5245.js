"use strict";exports.id=5245,exports.ids=[5245],exports.modules={95245:(a,b,c)=>{c.r(b),c.d(b,{fromProcess:()=>j});var d=c(92413),e=c(71930),f=c(79646),g=c(28354),h=c(21905);let i=async(a,b,c)=>{let d=b[a];if(b[a]){let i=d.credential_process;if(void 0!==i){let d=(0,g.promisify)(f.exec);try{let c,{stdout:e}=await d(i);try{c=JSON.parse(e.trim())}catch{throw Error(`Profile ${a} credential_process returned invalid JSON.`)}return((a,b,c)=>{if(1!==b.Version)throw Error(`Profile ${a} credential_process did not return Version 1.`);if(void 0===b.AccessKeyId||void 0===b.SecretAccess<PERSON>ey)throw Error(`Profile ${a} credential_process returned invalid credentials.`);if(b.Expiration){let c=new Date;if(new Date(b.Expiration)<c)throw Error(`Profile ${a} credential_process returned expired credentials.`)}let d=b.AccountId;!d&&c?.[a]?.aws_account_id&&(d=c[a].aws_account_id);let e={accessKeyId:b.AccessKeyId,secretAccessKey:b.SecretAccessKey,...b.SessionToken&&{sessionToken:b.SessionToken},...b.Expiration&&{expiration:new Date(b.Expiration)},...b.CredentialScope&&{credentialScope:b.CredentialScope},...d&&{accountId:d}};return(0,h.g)(e,"CREDENTIALS_PROCESS","w"),e})(a,c,b)}catch(a){throw new e.C1(a.message,{logger:c})}}throw new e.C1(`Profile ${a} did not contain credential_process.`,{logger:c})}throw new e.C1(`Profile ${a} could not be found in shared credentials file.`,{logger:c})},j=(a={})=>async({callerClientConfig:b}={})=>{a.logger?.debug("@aws-sdk/credential-provider-process - fromProcess");let c=await (0,d.YU)(a);return i((0,d.Bz)({profile:a.profile??b?.profile}),c,a.logger)}}};