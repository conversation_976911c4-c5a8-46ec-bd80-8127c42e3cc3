import { NextRequest, NextResponse } from 'next/server';
import { getPayload } from 'payload';
import config from '@payload-config';
import { checkAdminAccess, logAdminAction } from '@/middleware/adminAuth';

const MAIN_APP_BASE_URL = process.env.MAIN_APP_BASE_URL || 'http://localhost:3000';

// POST /api/sync/from-main-app - Sync user-created challenges from main app to CMS
export async function POST(request: NextRequest) {
  try {
    // Check admin access
    const authCheck = await checkAdminAccess(request);
    if (!authCheck.isAdmin) {
      return NextResponse.json(
        { success: false, error: authCheck.error },
        { status: 403 }
      );
    }

    console.log('🔄 [CMS-SYNC] Starting Main App to CMS challenge sync');

    // Log admin action for audit trail
    logAdminAction(authCheck.user, 'IMPORT_CHALLENGES_FROM_MAIN_APP', {
      timestamp: new Date().toISOString()
    });

    const payload = await getPayload({ config });

    // Fetch user-created challenges from main application
    const response = await fetch(`${MAIN_APP_BASE_URL}/api/challenges/user-created`, {
      headers: {
        'Authorization': `Bearer ${process.env.CMS_SYNC_TOKEN}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Main app API error: ${response.status}`);
    }

    const data = await response.json();
    const userChallenges = data.challenges || [];
    
    console.log(`📊 [CMS-SYNC] Found ${userChallenges.length} user-created challenges in main app`);

    let syncedCount = 0;
    let skippedCount = 0;
    let errorCount = 0;

    // Sync each challenge to CMS
    for (const challenge of userChallenges) {
      try {
        // Check if challenge already exists in CMS
        const existingChallenge = await payload.find({
          collection: 'challenges',
          where: {
            or: [
              {
                'mainAppId': {
                  equals: challenge.id
                }
              },
              {
                title: {
                  equals: challenge.title
                }
              }
            ]
          },
          limit: 1
        });

        if (existingChallenge.docs.length > 0) {
          console.log(`⏭️ [CMS-SYNC] Skipping existing challenge: ${challenge.title}`);
          skippedCount++;
          continue;
        }

        // Convert main app challenge to CMS format
        const cmsChallenge = {
          title: challenge.title,
          description: challenge.description,
          category: challenge.type, // Map type to category
          difficulty: challenge.difficulty,
          prompt: challenge.prompt,
          instructions: challenge.prompt,
          ageGroup: 'all', // Default age group
          status: 'review', // User-created challenges need review
          mainAppId: challenge.id, // Store reference to main app challenge
          createdBy: authCheck.user.id, // Set current admin as creator for CMS
          isUserGenerated: true, // Flag to indicate this came from user
        };

        // Create challenge in CMS
        const newChallenge = await payload.create({
          collection: 'challenges',
          data: cmsChallenge
        });

        console.log(`✅ [CMS-SYNC] Synced challenge: ${challenge.title}`);
        syncedCount++;

      } catch (error) {
        console.error(`❌ [CMS-SYNC] Error syncing challenge ${challenge.title}:`, error);
        errorCount++;
      }
    }

    console.log(`🎉 [CMS-SYNC] Sync complete: ${syncedCount} synced, ${skippedCount} skipped, ${errorCount} errors`);

    return NextResponse.json({
      success: true,
      message: 'User-created challenges synced from main application',
      stats: {
        total: userChallenges.length,
        synced: syncedCount,
        skipped: skippedCount,
        errors: errorCount
      }
    });

  } catch (error) {
    console.error('❌ [CMS-SYNC] Error syncing challenges from main app:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to sync challenges from main application',
        details: error.message
      },
      { status: 500 }
    );
  }
}

// GET /api/sync/from-main-app - Check what user-created challenges are available for sync
export async function GET(request: NextRequest) {
  try {
    // Check admin access
    const authCheck = await checkAdminAccess(request);
    if (!authCheck.isAdmin) {
      return NextResponse.json(
        { success: false, error: authCheck.error },
        { status: 403 }
      );
    }

    // Get user-created challenges from main app
    let mainAppChallenges = [];
    try {
      const response = await fetch(`${MAIN_APP_BASE_URL}/api/challenges/user-created`, {
        headers: {
          'Authorization': `Bearer ${process.env.CMS_SYNC_TOKEN}`,
        },
      });
      
      if (response.ok) {
        const data = await response.json();
        mainAppChallenges = data.challenges || [];
      }
    } catch (error) {
      console.warn('Could not fetch main app challenges:', error);
    }

    const payload = await getPayload({ config });

    // Count how many are already synced
    const syncedChallenges = await payload.find({
      collection: 'challenges',
      where: {
        isUserGenerated: {
          equals: true
        }
      },
      limit: 1000
    });

    return NextResponse.json({
      success: true,
      stats: {
        availableForSync: mainAppChallenges.length,
        alreadySynced: syncedChallenges.totalDocs,
        pendingSync: Math.max(0, mainAppChallenges.length - syncedChallenges.totalDocs)
      },
      challenges: mainAppChallenges.slice(0, 10) // Return first 10 for preview
    });

  } catch (error) {
    console.error('Error checking sync status:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to check sync status',
        details: error.message
      },
      { status: 500 }
    );
  }
}
