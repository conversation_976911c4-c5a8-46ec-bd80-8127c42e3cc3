"use strict";exports.id=5816,exports.ids=[5816],exports.modules={22232:(a,b,c)=>{c.d(b,{CG:()=>f,Y2:()=>e,cJ:()=>g});var d=c(70525);let e=(a,b)=>(0,d.w)(a,b).then(a=>{if(a.length)try{return JSON.parse(a)}catch(b){throw b?.name==="SyntaxError"&&Object.defineProperty(b,"$responseBodyText",{value:a}),b}return{}}),f=async(a,b)=>{let c=await e(a,b);return c.message=c.message??c.Message,c},g=(a,b)=>{let c=(a,b)=>Object.keys(a).find(a=>a.toLowerCase()===b.toLowerCase()),d=a=>{let b=a;return"number"==typeof b&&(b=b.toString()),b.indexOf(",")>=0&&(b=b.split(",")[0]),b.indexOf(":")>=0&&(b=b.split(":")[0]),b.indexOf("#")>=0&&(b=b.split("#")[1]),b},e=c(a.headers,"x-amzn-errortype");if(void 0!==e)return d(a.headers[e]);if(b&&"object"==typeof b){let a=c(b,"code");if(a&&void 0!==b[a])return d(b[a]);if(void 0!==b.__type)return d(b.__type)}}},65816:(a,b,c)=>{c.d(b,{CognitoIdentityClient:()=>V.D,GetCredentialsForIdentityCommand:()=>T,GetIdCommand:()=>U});var d=c(59727),e=c(8839),f=c(35639),g=c(44794);class h extends f.TJ{constructor(a){super(a),Object.setPrototypeOf(this,h.prototype)}}class i extends h{name="InternalErrorException";$fault="server";constructor(a){super({name:"InternalErrorException",$fault:"server",...a}),Object.setPrototypeOf(this,i.prototype)}}class j extends h{name="InvalidParameterException";$fault="client";constructor(a){super({name:"InvalidParameterException",$fault:"client",...a}),Object.setPrototypeOf(this,j.prototype)}}class k extends h{name="LimitExceededException";$fault="client";constructor(a){super({name:"LimitExceededException",$fault:"client",...a}),Object.setPrototypeOf(this,k.prototype)}}class l extends h{name="NotAuthorizedException";$fault="client";constructor(a){super({name:"NotAuthorizedException",$fault:"client",...a}),Object.setPrototypeOf(this,l.prototype)}}class m extends h{name="ResourceConflictException";$fault="client";constructor(a){super({name:"ResourceConflictException",$fault:"client",...a}),Object.setPrototypeOf(this,m.prototype)}}class n extends h{name="TooManyRequestsException";$fault="client";constructor(a){super({name:"TooManyRequestsException",$fault:"client",...a}),Object.setPrototypeOf(this,n.prototype)}}class o extends h{name="ResourceNotFoundException";$fault="client";constructor(a){super({name:"ResourceNotFoundException",$fault:"client",...a}),Object.setPrototypeOf(this,o.prototype)}}class p extends h{name="ExternalServiceException";$fault="client";constructor(a){super({name:"ExternalServiceException",$fault:"client",...a}),Object.setPrototypeOf(this,p.prototype)}}class q extends h{name="InvalidIdentityPoolConfigurationException";$fault="client";constructor(a){super({name:"InvalidIdentityPoolConfigurationException",$fault:"client",...a}),Object.setPrototypeOf(this,q.prototype)}}class r extends h{name="DeveloperUserAlreadyRegisteredException";$fault="client";constructor(a){super({name:"DeveloperUserAlreadyRegisteredException",$fault:"client",...a}),Object.setPrototypeOf(this,r.prototype)}}class s extends h{name="ConcurrentModificationException";$fault="client";constructor(a){super({name:"ConcurrentModificationException",$fault:"client",...a}),Object.setPrototypeOf(this,s.prototype)}}let t=a=>({...a,...a.Logins&&{Logins:f.$H}}),u=a=>({...a,...a.Credentials&&{Credentials:(a=>({...a,...a.SecretKey&&{SecretKey:f.$H}}))(a.Credentials)}}),v=a=>({...a,...a.Logins&&{Logins:f.$H}});var w=c(22232),x=c(31734);let y=async(a,b)=>R(b,S("GetCredentialsForIdentity"),"/",void 0,JSON.stringify((0,f.Ss)(a))),z=async(a,b)=>R(b,S("GetId"),"/",void 0,JSON.stringify((0,f.Ss)(a))),A=async(a,b)=>{if(a.statusCode>=300)return C(a,b);let c=await (0,w.Y2)(a.body,b),d={};return d=O(c,b),{$metadata:P(a),...d}},B=async(a,b)=>{if(a.statusCode>=300)return C(a,b);let c=await (0,w.Y2)(a.body,b),d={};return d=(0,f.Ss)(c),{$metadata:P(a),...d}},C=async(a,b)=>{let c={...a,body:await (0,w.CG)(a.body,b)},d=(0,w.cJ)(a,c.body);switch(d){case"InternalErrorException":case"com.amazonaws.cognitoidentity#InternalErrorException":throw await G(c,b);case"InvalidParameterException":case"com.amazonaws.cognitoidentity#InvalidParameterException":throw await I(c,b);case"LimitExceededException":case"com.amazonaws.cognitoidentity#LimitExceededException":throw await J(c,b);case"NotAuthorizedException":case"com.amazonaws.cognitoidentity#NotAuthorizedException":throw await K(c,b);case"ResourceConflictException":case"com.amazonaws.cognitoidentity#ResourceConflictException":throw await L(c,b);case"TooManyRequestsException":case"com.amazonaws.cognitoidentity#TooManyRequestsException":throw await N(c,b);case"ResourceNotFoundException":case"com.amazonaws.cognitoidentity#ResourceNotFoundException":throw await M(c,b);case"ExternalServiceException":case"com.amazonaws.cognitoidentity#ExternalServiceException":throw await F(c,b);case"InvalidIdentityPoolConfigurationException":case"com.amazonaws.cognitoidentity#InvalidIdentityPoolConfigurationException":throw await H(c,b);case"DeveloperUserAlreadyRegisteredException":case"com.amazonaws.cognitoidentity#DeveloperUserAlreadyRegisteredException":throw await E(c,b);case"ConcurrentModificationException":case"com.amazonaws.cognitoidentity#ConcurrentModificationException":throw await D(c,b);default:return Q({output:a,parsedBody:c.body,errorCode:d})}},D=async(a,b)=>{let c=a.body,d=(0,f.Ss)(c),e=new s({$metadata:P(a),...d});return(0,f.Mw)(e,c)},E=async(a,b)=>{let c=a.body,d=(0,f.Ss)(c),e=new r({$metadata:P(a),...d});return(0,f.Mw)(e,c)},F=async(a,b)=>{let c=a.body,d=(0,f.Ss)(c),e=new p({$metadata:P(a),...d});return(0,f.Mw)(e,c)},G=async(a,b)=>{let c=a.body,d=(0,f.Ss)(c),e=new i({$metadata:P(a),...d});return(0,f.Mw)(e,c)},H=async(a,b)=>{let c=a.body,d=(0,f.Ss)(c),e=new q({$metadata:P(a),...d});return(0,f.Mw)(e,c)},I=async(a,b)=>{let c=a.body,d=(0,f.Ss)(c),e=new j({$metadata:P(a),...d});return(0,f.Mw)(e,c)},J=async(a,b)=>{let c=a.body,d=(0,f.Ss)(c),e=new k({$metadata:P(a),...d});return(0,f.Mw)(e,c)},K=async(a,b)=>{let c=a.body,d=(0,f.Ss)(c),e=new l({$metadata:P(a),...d});return(0,f.Mw)(e,c)},L=async(a,b)=>{let c=a.body,d=(0,f.Ss)(c),e=new m({$metadata:P(a),...d});return(0,f.Mw)(e,c)},M=async(a,b)=>{let c=a.body,d=(0,f.Ss)(c),e=new o({$metadata:P(a),...d});return(0,f.Mw)(e,c)},N=async(a,b)=>{let c=a.body,d=(0,f.Ss)(c),e=new n({$metadata:P(a),...d});return(0,f.Mw)(e,c)},O=(a,b)=>(0,f.s)(a,{Credentials:a=>(0,f.s)(a,{AccessKeyId:f.lK,Expiration:a=>(0,f.Y0)((0,f.l3)((0,f.r$)(a))),SecretKey:f.lK,SessionToken:f.lK}),IdentityId:f.lK}),P=a=>({httpStatusCode:a.statusCode,requestId:a.headers["x-amzn-requestid"]??a.headers["x-amzn-request-id"]??a.headers["x-amz-request-id"],extendedRequestId:a.headers["x-amz-id-2"],cfId:a.headers["x-amz-cf-id"]}),Q=(0,f.jr)(h),R=async(a,b,c,d,e)=>{let{hostname:f,protocol:g="https",port:h,path:i}=await a.endpoint(),j={protocol:g,hostname:f,port:h,method:"POST",path:i.endsWith("/")?i.slice(0,-1)+c:i+c,headers:b};return void 0!==d&&(j.hostname=d),void 0!==e&&(j.body=e),new x.Kd(j)};function S(a){return{"content-type":"application/x-amz-json-1.1","x-amz-target":`AWSCognitoIdentityService.${a}`}}class T extends f.uB.classBuilder().ep(g.S).m(function(a,b,c,f){return[(0,e.TM)(c,this.serialize,this.deserialize),(0,d.rD)(c,a.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","GetCredentialsForIdentity",{}).n("CognitoIdentityClient","GetCredentialsForIdentityCommand").f(t,u).ser(y).de(A).build(){}class U extends f.uB.classBuilder().ep(g.S).m(function(a,b,c,f){return[(0,e.TM)(c,this.serialize,this.deserialize),(0,d.rD)(c,a.getEndpointParameterInstructions())]}).s("AWSCognitoIdentityService","GetId",{}).n("CognitoIdentityClient","GetIdCommand").f(v,void 0).ser(z).de(B).build(){}var V=c(24540)},70525:(a,b,c)=>{c.d(b,{w:()=>e});var d=c(35639);let e=(a,b)=>(0,d.Px)(a,b).then(a=>b.utf8Encoder(a))}};