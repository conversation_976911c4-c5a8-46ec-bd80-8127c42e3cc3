"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";

import AnnouncementBanner from "@/components/shared/AnnouncementBanner";
import SubscriptionHeader from "@/components/subscription/SubscriptionHeader";
import { SubscriptionPlans } from "@/components/subscription/SubscriptionPlans";

import WhyChooseLittleSpark from "@/components/subscription/WhyChooseLittleSpark";
import { getSubscriptionPlans } from "@/components/subscription/planData";
import { SubscriptionPlan } from "@/components/subscription/SubscriptionPlans";
import { Button } from "@/components/ui/button";
import { Accordion } from "@/components/ui/Accordion";
import { ChevronDown } from "lucide-react";
import { fredoka } from "@/lib/fonts";

import { useSubscriptionStatus } from '@/hooks/useSubscriptionStatus';
import { toast } from 'sonner';

// Simple FAQ Item component matching home page style
interface FAQItemProps {
    question: string;
    answer: string;
    index: number;
}

const FAQItem: React.FC<FAQItemProps> = ({ question, answer, index }) => {
    const [isOpen, setIsOpen] = useState(false);

    return (
        <div
            className="border-b border-gray-200 rounded-lg overflow-hidden animate-fadeInUp"
            style={{ animationDelay: `${0.1 + index * 0.05}s` }}
        >
            <button
                className="w-full px-4 sm:px-6 py-3 sm:py-4 text-left bg-white transition-colors duration-200 
                   flex items-center justify-between group"
                onClick={() => setIsOpen(!isOpen)}
            >
                <h3
                    className={`text-sm sm:text-md font-bold text-gray-800 hover:underline ${fredoka.className} pr-4`}
                >
                    {question}
                </h3>
                <ChevronDown
                    className={`h-5 w-5 text-gray-500 transition-transform duration-200 flex-shrink-0 
                     ${
                         isOpen ? "rotate-180" : ""
                     } group-hover:text-littlespark-primary`}
                />
            </button>

            <div
                className={`overflow-hidden transition-all duration-300 ease-in-out ${
                    isOpen ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
                }`}
            >
                <div className="px-4 sm:px-6 pb-4 sm:pb-5 pt-1 sm:pt-2">
                    <p className="text-gray-700 text-xs sm:text-sm font-light leading-relaxed">
                        {answer}
                    </p>
                </div>
            </div>
        </div>
    );
};

// Simple FAQ Section component matching home page style
const PricingFAQ = () => {
    const faqs = [
        {
            question: "How do I cancel my subscription?",
            answer: "You can cancel your subscription at any time from your account settings. Once canceled, your subscription will remain active until the end of your current billing period.",
        },
        {
            question: "What happens when my subscription ends?",
            answer: "When your subscription ends, you'll lose access to all premium features and any content created with your Little Spark account. We recommend downloading any projects you want to keep before your subscription expires.",
        },
        {
            question: "Can I switch between subscription plans?",
            answer: "Yes, you can change your subscription plan at any time from your account settings. If you upgrade, the change will take effect immediately and you'll be charged the prorated difference.",
        },
        {
            question: "How do gift subscriptions work?",
            answer: "Gift subscriptions allow you to purchase a Little Spark subscription for someone else. The recipient will receive an email with instructions on how to activate their gift subscription. You can schedule the delivery for any date you choose.",
        },
    ];

    return (
        <section className="py-12 sm:py-16 lg:py-20 bg-white">
            <div className="container mx-auto px-4">
                <div className="text-center mb-10 sm:mb-12 max-w-3xl mx-auto">
                    <h2
                        className={`text-2xl sm:text-3xl md:text-4xl font-bold mb-4 sm:mb-5 text-gray-800 ${fredoka.className}`}
                    >
                        Frequently Asked Questions
                    </h2>
                    <p className="text-gray-600 text-base sm:text-lg">
                        Get answers to common questions about our pricing and
                        subscriptions
                    </p>
                </div>

                <div className="max-w-3xl mx-auto">
                    <Accordion type="single" collapsible className="w-full">
                        {faqs.map((faq, index) => (
                            <FAQItem
                                key={index}
                                question={faq.question}
                                answer={faq.answer}
                                index={index}
                            />
                        ))}
                    </Accordion>
                </div>
            </div>
        </section>
    );
};

const PricingPage = () => {
    const plans = getSubscriptionPlans();
    const router = useRouter();
    const searchParams = useSearchParams();
    const { accessResult } = useSubscriptionStatus();
    const [showTrialExpiredMessage, setShowTrialExpiredMessage] = useState(false);
    const [showTrialUsedMessage, setShowTrialUsedMessage] = useState(false);

    // Check for trial expiration and trial used messages
    useEffect(() => {
        const reason = searchParams.get('reason');
        const trialExpired = searchParams.get('trial_expired');

        if (reason === 'trial_expired' || trialExpired === 'true') {
            setShowTrialExpiredMessage(true);
            toast.error('Your trial has expired. Please subscribe to continue using Little Spark.');
        } else if (reason === 'subscription_required') {
            toast.info('A subscription is required to access this feature.');
        }
    }, [searchParams]);

    // Check if user has active subscription (but don't redirect)
    const hasActiveSubscription = accessResult?.hasAccess &&
        ['active', 'trialing', 'cancel_at_period_end'].includes(accessResult?.subscriptionStatus?.subscription_status || '');

    // Check if user has already used their trial
    useEffect(() => {
        if (accessResult?.subscriptionStatus?.trial_used && !accessResult.hasAccess) {
            setShowTrialUsedMessage(true);
        }
    }, [accessResult]);

    const handleSelectPlan = (plan: SubscriptionPlan) => {
        console.log("Selected plan:", plan);
        if (hasActiveSubscription) {
            // For existing subscribers, redirect to upgrade flow
            router.push(`/checkout?plan=${plan.planId}&upgrade=1`);
        } else {
            // For new subscribers, normal checkout
            router.push(`/checkout?plan=${plan.planId}`);
        }
    };

    // Get current plan details
    const getCurrentPlanDetails = () => {
        if (!accessResult?.subscriptionStatus?.plan_id) return null;
        const plans = getSubscriptionPlans();
        return plans.find(p => p.planId === accessResult.subscriptionStatus?.plan_id);
    };

    const currentPlan = getCurrentPlanDetails();



    return (
        <div className="min-h-screen bg-white">
            <div className="container mx-auto py-8 sm:py-12 px-4">
                <AnnouncementBanner />

                {showTrialExpiredMessage && (
                    <div className="mb-8 p-4 bg-red-50 border border-red-200 rounded-lg">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <div className="ml-3">
                                <h3 className="text-sm font-medium text-red-800">
                                    Trial Expired
                                </h3>
                                <div className="mt-2 text-sm text-red-700">
                                    <p>Your free trial has ended. Choose a plan below to continue creating with Little Spark!</p>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {showTrialUsedMessage && (
                    <div className="mb-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <div className="ml-3">
                                <h3 className="text-sm font-medium text-blue-800">
                                    Trial Already Used
                                </h3>
                                <div className="mt-2 text-sm text-blue-700">
                                    <p>You have already used your free trial. Purchase a subscription below to continue creating with Little Spark!</p>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Current Subscription Display */}
                {hasActiveSubscription && currentPlan && (
                    <div className="mb-8 p-6 bg-green-50 border border-green-200 rounded-lg">
                        <div className="flex items-center justify-between">
                            <div>
                                <h3 className="text-lg font-semibold text-green-800 mb-2">
                                    Your Current Subscription
                                </h3>
                                <p className="text-green-700">
                                    <span className="font-medium">{currentPlan.name} Plan</span> - {currentPlan.price}{currentPlan.priceSubtext}
                                </p>
                                <p className="text-sm text-green-600 mt-1">
                                    Status: {accessResult?.subscriptionStatus?.subscription_status === 'cancel_at_period_end'
                                        ? 'Cancelling at period end'
                                        : 'Active'}
                                </p>
                            </div>
                            <Button
                                onClick={() => router.push('/dashboard')}
                                className="bg-green-600 hover:bg-green-700 text-white"
                            >
                                Manage Subscription
                            </Button>
                        </div>
                    </div>
                )}

                <SubscriptionHeader />
                <div className="mb-12 sm:mb-16">
                    <SubscriptionPlans
                        plans={plans}
                        onSelectPlan={handleSelectPlan}
                        trialUsed={accessResult?.subscriptionStatus?.trial_used || false}
                        hasActiveSubscription={hasActiveSubscription}
                        currentPlanId={accessResult?.subscriptionStatus?.plan_id || undefined}
                    />
                </div>

                <WhyChooseLittleSpark />
            </div>

            <PricingFAQ />
        </div>
    );
};

export default PricingPage;