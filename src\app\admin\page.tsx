'use client';

import { useAuth } from '@/hooks/useAuth';
import { CMSAdminPanel } from '@/components/admin/CMSAdminPanel';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export default function AdminPage() {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/signin?redirect=/admin');
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading admin panel...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container max-w-7xl mx-auto py-8 px-4">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">Admin Dashboard</h1>
          <p className="text-gray-600">
            Welcome back, {user.email}. Manage your Little Spark platform content.
          </p>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">🔄 Sync Management</h3>
            <p className="text-gray-600 text-sm mb-4">
              Sync content between CMS and main application
            </p>
            <button
              onClick={() => router.push('/admin/sync')}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 transition-colors"
            >
              Open Sync Panel
            </button>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">📊 Analytics</h3>
            <p className="text-gray-600 text-sm mb-4">
              View platform usage and statistics
            </p>
            <button
              disabled
              className="w-full bg-gray-300 text-gray-500 py-2 px-4 rounded-md cursor-not-allowed"
            >
              Coming Soon
            </button>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-800 mb-2">⚙️ Settings</h3>
            <p className="text-gray-600 text-sm mb-4">
              Configure platform settings
            </p>
            <button
              disabled
              className="w-full bg-gray-300 text-gray-500 py-2 px-4 rounded-md cursor-not-allowed"
            >
              Coming Soon
            </button>
          </div>
        </div>

        {/* CMS Admin Panel */}
        <CMSAdminPanel />
      </div>
    </div>
  );
}
