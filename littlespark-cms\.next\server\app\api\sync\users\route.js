(()=>{var e={};e.id=1098,e.ids=[1098],e.modules={91043:e=>{"use strict";e.exports=require("@aws-sdk/client-s3")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74552:e=>{"use strict";e.exports=require("pino")},14007:e=>{"use strict";e.exports=require("pino-pretty")},82015:e=>{"use strict";e.exports=require("react")},8732:e=>{"use strict";e.exports=require("react/jsx-runtime")},79428:e=>{"use strict";e.exports=require("buffer")},79646:e=>{"use strict";e.exports=require("child_process")},55511:e=>{"use strict";e.exports=require("crypto")},14985:e=>{"use strict";e.exports=require("dns")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},73496:e=>{"use strict";e.exports=require("http2")},55591:e=>{"use strict";e.exports=require("https")},8086:e=>{"use strict";e.exports=require("module")},91645:e=>{"use strict";e.exports=require("net")},21820:e=>{"use strict";e.exports=require("os")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},11997:e=>{"use strict";e.exports=require("punycode")},4984:e=>{"use strict";e.exports=require("readline")},27910:e=>{"use strict";e.exports=require("stream")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},28855:e=>{"use strict";e.exports=import("@libsql/client")},34589:e=>{"use strict";e.exports=require("node:assert")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},4573:e=>{"use strict";e.exports=require("node:buffer")},37540:e=>{"use strict";e.exports=require("node:console")},77598:e=>{"use strict";e.exports=require("node:crypto")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},40610:e=>{"use strict";e.exports=require("node:dns")},78474:e=>{"use strict";e.exports=require("node:events")},73024:e=>{"use strict";e.exports=require("node:fs")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},37067:e=>{"use strict";e.exports=require("node:http")},32467:e=>{"use strict";e.exports=require("node:http2")},98995:e=>{"use strict";e.exports=require("node:module")},77030:e=>{"use strict";e.exports=require("node:net")},48161:e=>{"use strict";e.exports=require("node:os")},76760:e=>{"use strict";e.exports=require("node:path")},643:e=>{"use strict";e.exports=require("node:perf_hooks")},1708:e=>{"use strict";e.exports=require("node:process")},41792:e=>{"use strict";e.exports=require("node:querystring")},80099:e=>{"use strict";e.exports=require("node:sqlite")},57075:e=>{"use strict";e.exports=require("node:stream")},37830:e=>{"use strict";e.exports=require("node:stream/web")},41692:e=>{"use strict";e.exports=require("node:tls")},73136:e=>{"use strict";e.exports=require("node:url")},57975:e=>{"use strict";e.exports=require("node:util")},73429:e=>{"use strict";e.exports=require("node:util/types")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},38522:e=>{"use strict";e.exports=require("node:zlib")},39727:()=>{},47990:()=>{},13166:(e,s,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.r(s),r.d(s,{patchFetch:()=>a,routeModule:()=>p,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var o=r(42706),i=r(28203),u=r(45994),n=r(55507),c=e([n]);n=(c.then?(await c)():c)[0];let p=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/sync/users/route",pathname:"/api/sync/users",filename:"route",bundlePath:"app/api/sync/users/route"},resolvedPagePath:"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\sync\\users\\route.ts",nextConfigOutput:"standalone",userland:n}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:x}=p;function a(){return(0,u.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}t()}catch(e){t(e)}})},96487:()=>{},78335:()=>{},55507:(e,s,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.r(s),r.d(s,{GET:()=>p,POST:()=>a});var o=r(39187),i=r(45415),u=r(17750),n=e([u]);async function c(e){return{isAdmin:!0,error:null,user:"system"}}u=(n.then?(await n)():n)[0];let l=process.env.MAIN_APP_BASE_URL||"http://localhost:3000";async function a(e){try{var s,r;let t=await c(e);if(!t.isAdmin)return o.NextResponse.json({success:!1,error:t.error},{status:403});console.log("\uD83D\uDD04 [CMS-SYNC] Starting CMS to Main App user sync"),s=t.user,r={timestamp:new Date().toISOString()},console.log(`[ADMIN-ACTION] User: ${s}, Action: SYNC_USERS_TO_MAIN_APP:`,r);let n=await (0,i.nm0)({config:u.A}),a=(await n.find({collection:"users",where:{and:[{isActive:{not_equals:!1}},{role:{not_equals:"admin"}}]},limit:1e3})).docs;console.log(`📊 [CMS-SYNC] Found ${a.length} active non-admin users in CMS`);let p=0,d=0;for(let e of a)try{let s={email:e.email,full_name:`${e.firstName} ${e.lastName}`,role:e.role,bio:e.bio,specialties:e.specialties?.map(e=>e.specialty).filter(Boolean)||[],cms_user_id:e.id,is_cms_user:!0},r=await fetch(`${l}/api/users/sync-from-cms`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${process.env.CMS_SYNC_TOKEN}`},body:JSON.stringify(s)});r.ok?(console.log(`✅ [CMS-SYNC] Synced user: ${e.email}`),p++):(console.error(`❌ [CMS-SYNC] Failed to sync user ${e.email}:`,await r.text()),d++)}catch(s){console.error(`❌ [CMS-SYNC] Error syncing user ${e.email}:`,s),d++}return console.log(`🎉 [CMS-SYNC] User sync complete: ${p} synced, 0 skipped, ${d} errors`),o.NextResponse.json({success:!0,message:"CMS users synced to main application",stats:{total:a.length,synced:p,skipped:0,errors:d}})}catch(e){return console.error("❌ [CMS-SYNC] Error syncing CMS users:",e),o.NextResponse.json({success:!1,error:"Failed to sync CMS users to main application",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function p(e){try{let e=await (0,i.nm0)({config:u.A}),s=await e.count({collection:"users",where:{and:[{isActive:{not_equals:!1}},{role:{not_equals:"admin"}}]}}),r={cmsUsers:0,totalUsers:0};try{let e=await fetch(`${l}/api/sync/users`,{headers:{Authorization:`Bearer ${process.env.CMS_SYNC_TOKEN}`}});if(e.ok){let s=await e.json();s.success&&s.stats&&(r={cmsUsers:s.stats.cmsUsers||0,totalUsers:s.stats.totalUsers||0})}}catch(e){console.warn("Could not fetch main app user sync status:",e)}return o.NextResponse.json({success:!0,stats:{cmsUsers:s.totalDocs,mainAppCmsUsers:r.cmsUsers,mainAppTotalUsers:r.totalUsers,lastSync:"Not implemented yet"}})}catch(e){return console.error("Error checking user sync status:",e),o.NextResponse.json({success:!1,error:"Failed to check user sync status",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}t()}catch(e){t(e)}})}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[5994,2259,5415,5452,7750],()=>r(13166));module.exports=t})();