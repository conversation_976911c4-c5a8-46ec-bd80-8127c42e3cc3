import { NextRequest, NextResponse } from 'next/server';
import { getPayload } from 'payload';
import config from '@payload-config';

// POST /api/test-data - Add sample challenges for testing
export async function POST(request: NextRequest) {
  try {
    console.log('🧪 [CMS-TEST] Adding sample challenges for testing');

    const payload = await getPayload({ config });

    // Sample challenges data
    const sampleChallenges = [
      {
        title: 'Create a Digital Art Masterpiece',
        description: 'Design a stunning digital artwork using any digital art tool',
        category: 'art',
        difficulty: 'intermediate',
        prompt: 'Create a digital artwork that represents your vision of the future. Use vibrant colors and imaginative elements.',
        instructions: 'Use any digital art software like Procreate, Photoshop, or free alternatives like GIMP. Focus on composition, color harmony, and storytelling.',
        ageGroup: 'teens',
        status: 'published',
        isActive: true,
        syncStatus: 'not_synced'
      },
      {
        title: 'Write a Short Story Adventure',
        description: 'Craft an engaging short story with compelling characters',
        category: 'story',
        difficulty: 'beginner',
        prompt: 'Write a 500-word adventure story about a character who discovers a hidden door in their house.',
        instructions: 'Include dialogue, descriptive language, and a clear beginning, middle, and end. Make your characters relatable and your plot engaging.',
        ageGroup: 'all',
        status: 'published',
        isActive: true,
        syncStatus: 'not_synced'
      },
      {
        title: 'Compose a Musical Melody',
        description: 'Create an original musical composition',
        category: 'music',
        difficulty: 'advanced',
        prompt: 'Compose a 2-minute instrumental piece that tells a story without words.',
        instructions: 'Use any music creation software or traditional instruments. Focus on melody, rhythm, and emotional expression.',
        ageGroup: 'teens',
        status: 'published',
        isActive: true,
        syncStatus: 'not_synced'
      },
      {
        title: 'Build a Simple Game',
        description: 'Create a fun and interactive game',
        category: 'coding',
        difficulty: 'intermediate',
        prompt: 'Build a simple puzzle or arcade-style game that others can play and enjoy.',
        instructions: 'Use any programming language or game development tool. Focus on gameplay mechanics and user experience.',
        ageGroup: 'teens',
        status: 'published',
        isActive: true,
        syncStatus: 'not_synced'
      },
      {
        title: 'Create a Stop-Motion Video',
        description: 'Produce a creative stop-motion animation',
        category: 'video',
        difficulty: 'intermediate',
        prompt: 'Create a 30-second stop-motion video that tells a simple story or demonstrates a concept.',
        instructions: 'Use everyday objects, clay, or drawings. Take photos for each frame and compile them into a video.',
        ageGroup: 'all',
        status: 'published',
        isActive: true,
        syncStatus: 'not_synced'
      }
    ];

    let createdCount = 0;
    let skippedCount = 0;

    for (const challengeData of sampleChallenges) {
      try {
        // Check if challenge already exists
        const existingChallenge = await payload.find({
          collection: 'challenges',
          where: {
            title: {
              equals: challengeData.title
            }
          },
          limit: 1
        });

        if (existingChallenge.docs.length > 0) {
          console.log(`⏭️ [CMS-TEST] Skipping existing challenge: ${challengeData.title}`);
          skippedCount++;
          continue;
        }

        // Create new challenge
        const newChallenge = await payload.create({
          collection: 'challenges',
          data: challengeData
        });

        console.log(`✅ [CMS-TEST] Created challenge: ${challengeData.title}`);
        createdCount++;

      } catch (error) {
        console.error(`❌ [CMS-TEST] Error creating challenge ${challengeData.title}:`, error);
      }
    }

    console.log(`🎉 [CMS-TEST] Test data creation complete: ${createdCount} created, ${skippedCount} skipped`);

    return NextResponse.json({
      success: true,
      message: 'Sample challenges created successfully',
      stats: {
        total: sampleChallenges.length,
        created: createdCount,
        skipped: skippedCount
      }
    });

  } catch (error) {
    console.error('❌ [CMS-TEST] Error creating test data:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create test data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// GET /api/test-data - Get test data status
export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config });

    // Get all challenges
    const challenges = await payload.find({
      collection: 'challenges',
      limit: 100
    });

    return NextResponse.json({
      success: true,
      stats: {
        totalChallenges: challenges.totalDocs,
        publishedChallenges: challenges.docs.filter(c => c.status === 'published').length,
        draftChallenges: challenges.docs.filter(c => c.status === 'draft').length,
      },
      challenges: challenges.docs.map(c => ({
        id: c.id,
        title: c.title,
        category: c.category,
        difficulty: c.difficulty,
        status: c.status,
        syncStatus: c.syncStatus || 'not_synced'
      }))
    });

  } catch (error) {
    console.error('❌ [CMS-TEST] Error getting test data:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to get test data',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
