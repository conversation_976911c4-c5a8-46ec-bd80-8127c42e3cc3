'use client'

import React from 'react'

const SyncButton: React.FC = () => {
  const handleSyncClick = () => {
    // Redirect to sync page
    window.location.href = '/admin/sync'
  }

  return (
    <li style={{ listStyle: 'none' }}>
      <button
        onClick={handleSyncClick}
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          width: '100%',
          padding: '8px 16px',
          backgroundColor: '#0070f3',
          color: 'white',
          border: 'none',
          borderRadius: '6px',
          cursor: 'pointer',
          fontSize: '14px',
          fontWeight: '500',
          textDecoration: 'none',
          transition: 'background-color 0.2s ease'
        }}
        onMouseOver={(e) => e.currentTarget.style.backgroundColor = '#0056b3'}
        onMouseOut={(e) => e.currentTarget.style.backgroundColor = '#0070f3'}
      >
        <span>🔄</span>
        <span>Sync to Main App</span>
      </button>
    </li>
  )
}

export default SyncButton
