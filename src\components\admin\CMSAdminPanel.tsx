'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { ExternalLink, Settings, Users, FileText, Image, BookOpen, GraduationCap } from 'lucide-react';

const CMS_ADMIN_URL = process.env.NEXT_PUBLIC_CMS_ADMIN_URL || 'http://localhost:3001/admin';

interface CMSStats {
  challenges: number;
  storyTemplates: number;
  educationalResources: number;
  media: number;
}

export function CMSAdminPanel() {
  const { user } = useAuth();
  const [stats, setStats] = useState<CMSStats>({
    challenges: 0,
    storyTemplates: 0,
    educationalResources: 0,
    media: 0,
  });
  const [loading, setLoading] = useState(true);

  // Check if user has admin access (you can customize this logic)
  const isAdmin = user?.email === '<EMAIL>' || user?.email?.includes('admin');

  useEffect(() => {
    async function loadStats() {
      try {
        // Load basic stats from your API routes
        const [challengesRes, storiesRes, resourcesRes] = await Promise.all([
          fetch('/api/cms/challenges'),
          fetch('/api/cms/story-templates'),
          fetch('/api/cms/educational-resources'),
        ]);

        const [challengesData, storiesData, resourcesData] = await Promise.all([
          challengesRes.json(),
          storiesRes.json(),
          resourcesRes.json(),
        ]);

        setStats({
          challenges: challengesData.total || challengesData.challenges?.length || 0,
          storyTemplates: storiesData.total || storiesData.storyTemplates?.length || 0,
          educationalResources: resourcesData.total || resourcesData.educationalResources?.length || 0,
          media: 0, // You can add media count if needed
        });
      } catch (error) {
        console.error('Error loading CMS stats:', error);
      } finally {
        setLoading(false);
      }
    }

    if (isAdmin) {
      loadStats();
    } else {
      setLoading(false);
    }
  }, [isAdmin]);

  if (!isAdmin) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center">
          <div className="text-4xl mb-4">🔒</div>
          <h3 className="text-lg font-semibold text-gray-800 mb-2">Admin Access Required</h3>
          <p className="text-gray-600">You don't have permission to access the CMS admin panel.</p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4"></div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const quickActions = [
    {
      title: 'Create Challenge',
      description: 'Add a new creative challenge',
      icon: <FileText className="w-6 h-6" />,
      color: 'bg-pink-500',
      url: `${CMS_ADMIN_URL}/collections/challenges/create`,
    },
    {
      title: 'Create Story Template',
      description: 'Add a new story template',
      icon: <BookOpen className="w-6 h-6" />,
      color: 'bg-blue-500',
      url: `${CMS_ADMIN_URL}/collections/story-templates/create`,
    },
    {
      title: 'Create Educational Resource',
      description: 'Add learning content',
      icon: <GraduationCap className="w-6 h-6" />,
      color: 'bg-green-500',
      url: `${CMS_ADMIN_URL}/collections/educational-resources/create`,
    },
    {
      title: 'Upload Media',
      description: 'Add images and videos',
      icon: <Image className="w-6 h-6" />,
      color: 'bg-purple-500',
      url: `${CMS_ADMIN_URL}/collections/media/create`,
    },
    {
      title: 'Manage Users',
      description: 'CMS user management',
      icon: <Users className="w-6 h-6" />,
      color: 'bg-indigo-500',
      url: `${CMS_ADMIN_URL}/admin/users`,
    },
    {
      title: 'CMS Settings',
      description: 'Configure CMS settings',
      icon: <Settings className="w-6 h-6" />,
      color: 'bg-gray-500',
      url: `${CMS_ADMIN_URL}/admin/settings`,
    },
    {
      title: 'Sync Management',
      description: 'Sync content between CMS and main app',
      icon: <div className="w-6 h-6 flex items-center justify-center text-white">🔄</div>,
      color: 'bg-emerald-500',
      url: '/admin/sync', // Internal sync page
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold text-gray-800 mb-2">Content Management System</h2>
            <p className="text-gray-600">Manage challenges, stories, and educational content</p>
          </div>
          <a
            href={CMS_ADMIN_URL}
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            Open CMS Admin
            <ExternalLink className="w-4 h-4" />
          </a>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-md p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-pink-100 rounded-lg flex items-center justify-center">
              <FileText className="w-5 h-5 text-pink-600" />
            </div>
            <div>
              <p className="text-2xl font-bold text-gray-800">{stats.challenges}</p>
              <p className="text-sm text-gray-600">Challenges</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <BookOpen className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-2xl font-bold text-gray-800">{stats.storyTemplates}</p>
              <p className="text-sm text-gray-600">Story Templates</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <GraduationCap className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-2xl font-bold text-gray-800">{stats.educationalResources}</p>
              <p className="text-sm text-gray-600">Resources</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <Image className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <p className="text-2xl font-bold text-gray-800">{stats.media}</p>
              <p className="text-sm text-gray-600">Media Files</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {quickActions.map((action, index) => (
            <a
              key={index}
              href={action.url}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-3 p-4 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-md transition-all"
            >
              <div className={`w-10 h-10 ${action.color} rounded-lg flex items-center justify-center text-white`}>
                {action.icon}
              </div>
              <div className="flex-1">
                <h4 className="font-medium text-gray-800">{action.title}</h4>
                <p className="text-sm text-gray-600">{action.description}</p>
              </div>
              <ExternalLink className="w-4 h-4 text-gray-400" />
            </a>
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">CMS Status</h3>
        <div className="space-y-3">
          <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span className="text-sm font-medium text-green-800">CMS Connection</span>
            </div>
            <span className="text-sm text-green-600">Active</span>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-sm font-medium text-blue-800">Content Sync</span>
            </div>
            <span className="text-sm text-blue-600">Real-time</span>
          </div>
          
          <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
              <span className="text-sm font-medium text-purple-800">Media Storage</span>
            </div>
            <span className="text-sm text-purple-600">SQLite Local</span>
          </div>
        </div>
      </div>
    </div>
  );
}
