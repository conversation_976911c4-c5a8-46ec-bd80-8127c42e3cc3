"use strict";exports.id=6199,exports.ids=[6199],exports.modules={16199:(e,n,i)=>{i.r(n),i.d(n,{fromTokenFile:()=>d,fromWebToken:()=>s});var o=i(6884),r=i(53438),t=i(29021);let s=e=>async n=>{e.logger?.debug("@aws-sdk/credential-provider-web-identity - fromWebToken");let{roleArn:o,roleSessionName:r,webIdentityToken:t,providerId:s,policyArns:l,policy:d,durationSeconds:a}=e,{roleAssumerWithWebIdentity:g}=e;if(!g){let{getDefaultRoleAssumerWithWebIdentity:o}=await i.e(5452).then(i.bind(i,45452));g=o({...e.clientConfig,credentialProviderLogger:e.logger,parentClientConfig:{...n?.callerClientConfig,...e.parentClientConfig}},e.clientPlugins)}return g({RoleArn:o,RoleSessionName:r??`aws-sdk-js-session-${Date.now()}`,WebIdentityToken:t,ProviderId:s,PolicyArns:l,Policy:d,DurationSeconds:a})},l="AWS_WEB_IDENTITY_TOKEN_FILE",d=(e={})=>async()=>{e.logger?.debug("@aws-sdk/credential-provider-web-identity - fromTokenFile");let n=e?.webIdentityTokenFile??process.env[l],i=e?.roleArn??process.env.AWS_ROLE_ARN,d=e?.roleSessionName??process.env.AWS_ROLE_SESSION_NAME;if(!n||!i)throw new r.C1("Web identity configuration not specified",{logger:e.logger});let a=await s({...e,webIdentityToken:(0,t.readFileSync)(n,{encoding:"ascii"}),roleArn:i,roleSessionName:d})();return n===process.env[l]&&(0,o.g)(a,"CREDENTIALS_ENV_VARS_STS_WEB_ID_TOKEN","h"),a}}};