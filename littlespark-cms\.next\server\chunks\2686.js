"use strict";exports.id=2686,exports.ids=[2686],exports.modules={32686:(a,b,c)=>{let d;c.d(b,{getDefaultRoleAssumer:()=>cH,getDefaultRoleAssumerWithWebIdentity:()=>cI});var e=c(36576),f=c(43208),g=c(22218),h=c(59883),i=c(6284),j=c(7821),k=c(60043),l=c(18154),m=c(68270),n=c(59727),o=c(71508),p=c(35639),q=c(78577),r=c(90378);let s=async(a,b,c)=>({operation:(0,r.u)(b).operation,region:await (0,r.t)(a.region)()||(()=>{throw Error("expected `region` to be configured for `aws.auth#sigv4`")})()}),t=a=>{let b=[];if("AssumeRoleWithWebIdentity"===a.operation)b.push({schemeId:"smithy.api#noAuth"});else b.push({schemeId:"aws.auth#sigv4",signingProperties:{name:"sts",region:a.region},propertiesExtractor:(a,b)=>({signingProperties:{config:a,context:b}})});return b},u={UseGlobalEndpoint:{type:"builtInParams",name:"useGlobalEndpoint"},UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}};var v=c(43279),w=c(95941),x=c(30438),y=c(75089),z=c(54285),A=c(4553),B=c(5334),C=c(1226),D=c(66426),E=c(11396),F=c(75843),G=c(68378),H=c(139),I=c(37979),J=c(92171),K=c(90599);let L="required",M="type",N="argv",O="booleanEquals",P="stringEquals",Q="sigv4",R="us-east-1",S="endpoint",T="https://sts.{Region}.{PartitionResult#dnsSuffix}",U="tree",V="error",W="getAttr",X={[L]:!1,[M]:"String"},Y={[L]:!0,default:!1,[M]:"Boolean"},Z={ref:"Endpoint"},$={fn:"isSet",[N]:[{ref:"Region"}]},_={ref:"Region"},aa={fn:"aws.partition",[N]:[_],assign:"PartitionResult"},ab={ref:"UseFIPS"},ac={ref:"UseDualStack"},ad={url:"https://sts.amazonaws.com",properties:{authSchemes:[{name:Q,signingName:"sts",signingRegion:R}]},headers:{}},ae={},af={conditions:[{fn:P,[N]:[_,"aws-global"]}],[S]:ad,[M]:S},ag={fn:O,[N]:[ab,!0]},ah={fn:O,[N]:[ac,!0]},ai={fn:W,[N]:[{ref:"PartitionResult"},"supportsFIPS"]},aj={ref:"PartitionResult"},ak={fn:O,[N]:[!0,{fn:W,[N]:[aj,"supportsDualStack"]}]},al=[{fn:"isSet",[N]:[Z]}],am=[ag],an=[ah],ao={version:"1.0",parameters:{Region:X,UseDualStack:Y,UseFIPS:Y,Endpoint:X,UseGlobalEndpoint:Y},rules:[{conditions:[{fn:O,[N]:[{ref:"UseGlobalEndpoint"},!0]},{fn:"not",[N]:al},$,aa,{fn:O,[N]:[ab,!1]},{fn:O,[N]:[ac,!1]}],rules:[{conditions:[{fn:P,[N]:[_,"ap-northeast-1"]}],endpoint:ad,[M]:S},{conditions:[{fn:P,[N]:[_,"ap-south-1"]}],endpoint:ad,[M]:S},{conditions:[{fn:P,[N]:[_,"ap-southeast-1"]}],endpoint:ad,[M]:S},{conditions:[{fn:P,[N]:[_,"ap-southeast-2"]}],endpoint:ad,[M]:S},af,{conditions:[{fn:P,[N]:[_,"ca-central-1"]}],endpoint:ad,[M]:S},{conditions:[{fn:P,[N]:[_,"eu-central-1"]}],endpoint:ad,[M]:S},{conditions:[{fn:P,[N]:[_,"eu-north-1"]}],endpoint:ad,[M]:S},{conditions:[{fn:P,[N]:[_,"eu-west-1"]}],endpoint:ad,[M]:S},{conditions:[{fn:P,[N]:[_,"eu-west-2"]}],endpoint:ad,[M]:S},{conditions:[{fn:P,[N]:[_,"eu-west-3"]}],endpoint:ad,[M]:S},{conditions:[{fn:P,[N]:[_,"sa-east-1"]}],endpoint:ad,[M]:S},{conditions:[{fn:P,[N]:[_,R]}],endpoint:ad,[M]:S},{conditions:[{fn:P,[N]:[_,"us-east-2"]}],endpoint:ad,[M]:S},{conditions:[{fn:P,[N]:[_,"us-west-1"]}],endpoint:ad,[M]:S},{conditions:[{fn:P,[N]:[_,"us-west-2"]}],endpoint:ad,[M]:S},{endpoint:{url:T,properties:{authSchemes:[{name:Q,signingName:"sts",signingRegion:"{Region}"}]},headers:ae},[M]:S}],[M]:U},{conditions:al,rules:[{conditions:am,error:"Invalid Configuration: FIPS and custom endpoint are not supported",[M]:V},{conditions:an,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",[M]:V},{endpoint:{url:Z,properties:ae,headers:ae},[M]:S}],[M]:U},{conditions:[$],rules:[{conditions:[aa],rules:[{conditions:[ag,ah],rules:[{conditions:[{fn:O,[N]:[!0,ai]},ak],rules:[{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:ae,headers:ae},[M]:S}],[M]:U},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",[M]:V}],[M]:U},{conditions:am,rules:[{conditions:[{fn:O,[N]:[ai,!0]}],rules:[{conditions:[{fn:P,[N]:[{fn:W,[N]:[aj,"name"]},"aws-us-gov"]}],endpoint:{url:"https://sts.{Region}.amazonaws.com",properties:ae,headers:ae},[M]:S},{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dnsSuffix}",properties:ae,headers:ae},[M]:S}],[M]:U},{error:"FIPS is enabled but this partition does not support FIPS",[M]:V}],[M]:U},{conditions:an,rules:[{conditions:[ak],rules:[{endpoint:{url:"https://sts.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:ae,headers:ae},[M]:S}],[M]:U},{error:"DualStack is enabled but this partition does not support DualStack",[M]:V}],[M]:U},af,{endpoint:{url:T,properties:ae,headers:ae},[M]:S}],[M]:U}],[M]:U},{error:"Invalid Configuration: Missing Region",[M]:V}]},ap=new K.kS({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS","UseGlobalEndpoint"]}),aq=(a,b={})=>ap.get(a,()=>(0,K.sO)(ao,{endpointParams:a,logger:b.logger}));K.mw.aws=J.UF;var ar=c(52262),as=c(41352),at=c(31734);class au extends p.Kj{config;constructor(...[a]){let b=(a=>{(0,p.I9)(process.version);let b=(0,ar.I)(a),c=()=>b().then(p.lT),d=(a=>({apiVersion:"2011-06-15",base64Decoder:a?.base64Decoder??H.E,base64Encoder:a?.base64Encoder??H.n,disableHostPrefix:a?.disableHostPrefix??!1,endpointProvider:a?.endpointProvider??aq,extensions:a?.extensions??[],httpAuthSchemeProvider:a?.httpAuthSchemeProvider??t,httpAuthSchemes:a?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:a=>a.getIdentityProvider("aws.auth#sigv4"),signer:new y.f2},{schemeId:"smithy.api#noAuth",identityProvider:a=>a.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new A.m}],logger:a?.logger??new p.N4,serviceId:a?.serviceId??"STS",urlParser:a?.urlParser??G.D,utf8Decoder:a?.utf8Decoder??I.ar,utf8Encoder:a?.utf8Encoder??I.Pq}))(a);(0,w.I)(process.version);let e={profile:a?.profile,logger:d.logger};return{...d,...a,runtime:"node",defaultsMode:b,authSchemePreference:a?.authSchemePreference??(0,C.Z)(x.$,e),bodyLengthChecker:a?.bodyLengthChecker??E.n,defaultUserAgentProvider:a?.defaultUserAgentProvider??(0,z.pf)({serviceId:d.serviceId,clientVersion:v.rE}),httpAuthSchemes:a?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:b=>b.getIdentityProvider("aws.auth#sigv4")||(async b=>await a.credentialDefaultProvider(b?.__config||{})()),signer:new y.f2},{schemeId:"smithy.api#noAuth",identityProvider:a=>a.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new A.m}],maxAttempts:a?.maxAttempts??(0,C.Z)(o.qs,a),region:a?.region??(0,C.Z)(i.GG,{...i.zH,...e}),requestHandler:D.$c.create(a?.requestHandler??c),retryMode:a?.retryMode??(0,C.Z)({...o.kN,default:async()=>(await c()).retryMode||F.L0},a),sha256:a?.sha256??B.V.bind(null,"sha256"),streamCollector:a?.streamCollector??D.kv,useDualstackEndpoint:a?.useDualstackEndpoint??(0,C.Z)(i.e$,e),useFipsEndpoint:a?.useFipsEndpoint??(0,C.Z)(i.Ko,e),userAgentAppId:a?.userAgentAppId??(0,C.Z)(z.hV,e)}})(a||{});super(b),this.initConfig=b;let c=(a=>Object.assign(a,{useDualstackEndpoint:a.useDualstackEndpoint??!1,useFipsEndpoint:a.useFipsEndpoint??!1,useGlobalEndpoint:a.useGlobalEndpoint??!1,defaultSigningName:"sts"}))(b),d=(0,h.Dc)(c),u=(0,o.$z)(d),J=(0,i.TD)(u),K=(0,e.OV)(J),L=((a,b)=>{let c=Object.assign((0,as.Rq)(a),(0,p.xA)(a),(0,at.eS)(a),(a=>{let b=a.httpAuthSchemes,c=a.httpAuthSchemeProvider,d=a.credentials;return{setHttpAuthScheme(a){let c=b.findIndex(b=>b.schemeId===a.schemeId);-1===c?b.push(a):b.splice(c,1,a)},httpAuthSchemes:()=>b,setHttpAuthSchemeProvider(a){c=a},httpAuthSchemeProvider:()=>c,setCredentials(a){d=a},credentials:()=>d}})(a));return b.forEach(a=>a.configure(c)),Object.assign(a,(0,as.$3)(c),(0,p.uv)(c),(0,at.jt)(c),(a=>({httpAuthSchemes:a.httpAuthSchemes(),httpAuthSchemeProvider:a.httpAuthSchemeProvider(),credentials:a.credentials()}))(c))})((a=>{let b=Object.assign(a,{stsClientCtor:au});return Object.assign((0,q.h)(b),{authSchemePreference:(0,r.t)(a.authSchemePreference??[])})})((0,n.Co)(K)),a?.extensions||[]);this.config=L,this.middlewareStack.use((0,h.sM)(this.config)),this.middlewareStack.use((0,o.ey)(this.config)),this.middlewareStack.use((0,m.vK)(this.config)),this.middlewareStack.use((0,e.TC)(this.config)),this.middlewareStack.use((0,f.Y7)(this.config)),this.middlewareStack.use((0,g.n4)(this.config)),this.middlewareStack.use((0,j.w)(this.config,{httpAuthSchemeParametersProvider:s,identityProviderConfigProvider:async a=>new k.h({"aws.auth#sigv4":a.credentials})})),this.middlewareStack.use((0,l.l)(this.config))}destroy(){super.destroy()}}var av=c(8839);class aw extends p.TJ{constructor(a){super(a),Object.setPrototypeOf(this,aw.prototype)}}let ax=a=>({...a,...a.SecretAccessKey&&{SecretAccessKey:p.$H}}),ay=a=>({...a,...a.Credentials&&{Credentials:ax(a.Credentials)}});class az extends aw{name="ExpiredTokenException";$fault="client";constructor(a){super({name:"ExpiredTokenException",$fault:"client",...a}),Object.setPrototypeOf(this,az.prototype)}}class aA extends aw{name="MalformedPolicyDocumentException";$fault="client";constructor(a){super({name:"MalformedPolicyDocumentException",$fault:"client",...a}),Object.setPrototypeOf(this,aA.prototype)}}class aB extends aw{name="PackedPolicyTooLargeException";$fault="client";constructor(a){super({name:"PackedPolicyTooLargeException",$fault:"client",...a}),Object.setPrototypeOf(this,aB.prototype)}}class aC extends aw{name="RegionDisabledException";$fault="client";constructor(a){super({name:"RegionDisabledException",$fault:"client",...a}),Object.setPrototypeOf(this,aC.prototype)}}class aD extends aw{name="IDPRejectedClaimException";$fault="client";constructor(a){super({name:"IDPRejectedClaimException",$fault:"client",...a}),Object.setPrototypeOf(this,aD.prototype)}}class aE extends aw{name="InvalidIdentityTokenException";$fault="client";constructor(a){super({name:"InvalidIdentityTokenException",$fault:"client",...a}),Object.setPrototypeOf(this,aE.prototype)}}let aF=a=>({...a,...a.WebIdentityToken&&{WebIdentityToken:p.$H}}),aG=a=>({...a,...a.Credentials&&{Credentials:ax(a.Credentials)}});class aH extends aw{name="IDPCommunicationErrorException";$fault="client";constructor(a){super({name:"IDPCommunicationErrorException",$fault:"client",...a}),Object.setPrototypeOf(this,aH.prototype)}}let aI={preserveOrder:!1,attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,removeNSPrefix:!1,allowBooleanAttributes:!1,parseTagValue:!0,parseAttributeValue:!1,trimValues:!0,cdataPropName:!1,numberParseOptions:{hex:!0,leadingZeros:!0,eNotation:!0},tagValueProcessor:function(a,b){return b},attributeValueProcessor:function(a,b){return b},stopNodes:[],alwaysCreateTextNode:!1,isArray:()=>!1,commentPropName:!1,unpairedTags:[],processEntities:!0,htmlEntities:!1,ignoreDeclaration:!1,ignorePiTags:!1,transformTagName:!1,transformAttributeName:!1,updateTag:function(a,b,c){return a},captureMetaData:!1},aJ=":A-Za-z_\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD",aK=RegExp("^"+("["+aJ+"]["+aJ)+"\\-.\\d\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$");function aL(a,b){let c=[],d=b.exec(a);for(;d;){let e=[];e.startIndex=b.lastIndex-d[0].length;let f=d.length;for(let a=0;a<f;a++)e.push(d[a]);c.push(e),d=b.exec(a)}return c}let aM=function(a){return null!=aK.exec(a)};d="function"!=typeof Symbol?"@@xmlMetadata":Symbol("XML Node Metadata");class aN{constructor(a){this.tagname=a,this.child=[],this[":@"]={}}add(a,b){"__proto__"===a&&(a="#__proto__"),this.child.push({[a]:b})}addChild(a,b){"__proto__"===a.tagname&&(a.tagname="#__proto__"),a[":@"]&&Object.keys(a[":@"]).length>0?this.child.push({[a.tagname]:a.child,":@":a[":@"]}):this.child.push({[a.tagname]:a.child}),void 0!==b&&(this.child[this.child.length-1][d]={startIndex:b})}static getMetaDataSymbol(){return d}}let aO=(a,b)=>{for(;b<a.length&&/\s/.test(a[b]);)b++;return b};function aP(a,b,c){let d="",e=a[b];if('"'!==e&&"'"!==e)throw Error(`Expected quoted string, found "${e}"`);for(b++;b<a.length&&a[b]!==e;)d+=a[b],b++;if(a[b]!==e)throw Error(`Unterminated ${c} value`);return[++b,d]}function aQ(a,b,c){for(let d=0;d<b.length;d++)if(b[d]!==a[c+d+1])return!1;return!0}function aR(a){if(aM(a))return a;throw Error(`Invalid entity name ${a}`)}let aS=/^[-+]?0x[a-fA-F0-9]+$/,aT=/^([\-\+])?(0*)([0-9]*(\.[0-9]*)?)$/,aU={hex:!0,leadingZeros:!0,decimalPoint:".",eNotation:!0},aV=/^([-+])?(0*)(\d*(\.\d*)?[eE][-\+]?\d+)$/;class aW{constructor(a){this.options=a,this.currentNode=null,this.tagsNodeStack=[],this.docTypeEntities={},this.lastEntities={apos:{regex:/&(apos|#39|#x27);/g,val:"'"},gt:{regex:/&(gt|#62|#x3E);/g,val:">"},lt:{regex:/&(lt|#60|#x3C);/g,val:"<"},quot:{regex:/&(quot|#34|#x22);/g,val:'"'}},this.ampEntity={regex:/&(amp|#38|#x26);/g,val:"&"},this.htmlEntities={space:{regex:/&(nbsp|#160);/g,val:" "},cent:{regex:/&(cent|#162);/g,val:"\xa2"},pound:{regex:/&(pound|#163);/g,val:"\xa3"},yen:{regex:/&(yen|#165);/g,val:"\xa5"},euro:{regex:/&(euro|#8364);/g,val:"€"},copyright:{regex:/&(copy|#169);/g,val:"\xa9"},reg:{regex:/&(reg|#174);/g,val:"\xae"},inr:{regex:/&(inr|#8377);/g,val:"₹"},num_dec:{regex:/&#([0-9]{1,7});/g,val:(a,b)=>String.fromCodePoint(Number.parseInt(b,10))},num_hex:{regex:/&#x([0-9a-fA-F]{1,6});/g,val:(a,b)=>String.fromCodePoint(Number.parseInt(b,16))}},this.addExternalEntities=aX,this.parseXml=a0,this.parseTextData=aY,this.resolveNameSpace=aZ,this.buildAttributesMap=a_,this.isItStopNode=a4,this.replaceEntitiesValue=a2,this.readStopNodeData=a7,this.saveTextToParentTag=a3,this.addChild=a1,this.ignoreAttributesFn=function(a){return"function"==typeof a?a:Array.isArray(a)?b=>{for(let c of a)if("string"==typeof c&&b===c||c instanceof RegExp&&c.test(b))return!0}:()=>!1}(this.options.ignoreAttributes)}}function aX(a){let b=Object.keys(a);for(let c=0;c<b.length;c++){let d=b[c];this.lastEntities[d]={regex:RegExp("&"+d+";","g"),val:a[d]}}}function aY(a,b,c,d,e,f,g){if(void 0!==a&&(this.options.trimValues&&!d&&(a=a.trim()),a.length>0)){g||(a=this.replaceEntitiesValue(a));let d=this.options.tagValueProcessor(b,a,c,e,f);return null==d?a:typeof d!=typeof a||d!==a?d:this.options.trimValues||a.trim()===a?a8(a,this.options.parseTagValue,this.options.numberParseOptions):a}}function aZ(a){if(this.options.removeNSPrefix){let b=a.split(":"),c="/"===a.charAt(0)?"/":"";if("xmlns"===b[0])return"";2===b.length&&(a=c+b[1])}return a}let a$=RegExp("([^\\s=]+)\\s*(=\\s*(['\"])([\\s\\S]*?)\\3)?","gm");function a_(a,b,c){if(!0!==this.options.ignoreAttributes&&"string"==typeof a){let c=aL(a,a$),d=c.length,e={};for(let a=0;a<d;a++){let d=this.resolveNameSpace(c[a][1]);if(this.ignoreAttributesFn(d,b))continue;let f=c[a][4],g=this.options.attributeNamePrefix+d;if(d.length)if(this.options.transformAttributeName&&(g=this.options.transformAttributeName(g)),"__proto__"===g&&(g="#__proto__"),void 0!==f){this.options.trimValues&&(f=f.trim()),f=this.replaceEntitiesValue(f);let a=this.options.attributeValueProcessor(d,f,b);null==a?e[g]=f:typeof a!=typeof f||a!==f?e[g]=a:e[g]=a8(f,this.options.parseAttributeValue,this.options.numberParseOptions)}else this.options.allowBooleanAttributes&&(e[g]=!0)}if(Object.keys(e).length){if(this.options.attributesGroupName){let a={};return a[this.options.attributesGroupName]=e,a}return e}}}let a0=function(a){a=a.replace(/\r\n?/g,"\n");let b=new aN("!xml"),c=b,d="",e="";for(let f=0;f<a.length;f++)if("<"===a[f])if("/"===a[f+1]){let b=a5(a,">",f,"Closing Tag is not closed."),g=a.substring(f+2,b).trim();if(this.options.removeNSPrefix){let a=g.indexOf(":");-1!==a&&(g=g.substr(a+1))}this.options.transformTagName&&(g=this.options.transformTagName(g)),c&&(d=this.saveTextToParentTag(d,c,e));let h=e.substring(e.lastIndexOf(".")+1);if(g&&-1!==this.options.unpairedTags.indexOf(g))throw Error(`Unpaired tag can not be used as closing tag: </${g}>`);let i=0;h&&-1!==this.options.unpairedTags.indexOf(h)?(i=e.lastIndexOf(".",e.lastIndexOf(".")-1),this.tagsNodeStack.pop()):i=e.lastIndexOf("."),e=e.substring(0,i),c=this.tagsNodeStack.pop(),d="",f=b}else if("?"===a[f+1]){let b=a6(a,f,!1,"?>");if(!b)throw Error("Pi Tag is not closed.");if(d=this.saveTextToParentTag(d,c,e),this.options.ignoreDeclaration&&"?xml"===b.tagName||this.options.ignorePiTags);else{let a=new aN(b.tagName);a.add(this.options.textNodeName,""),b.tagName!==b.tagExp&&b.attrExpPresent&&(a[":@"]=this.buildAttributesMap(b.tagExp,e,b.tagName)),this.addChild(c,a,e,f)}f=b.closeIndex+1}else if("!--"===a.substr(f+1,3)){let b=a5(a,"--\x3e",f+4,"Comment is not closed.");if(this.options.commentPropName){let g=a.substring(f+4,b-2);d=this.saveTextToParentTag(d,c,e),c.add(this.options.commentPropName,[{[this.options.textNodeName]:g}])}f=b}else if("!D"===a.substr(f+1,2)){let b=function(a,b){let c={};if("O"===a[b+3]&&"C"===a[b+4]&&"T"===a[b+5]&&"Y"===a[b+6]&&"P"===a[b+7]&&"E"===a[b+8]){b+=9;let d=1,e=!1,f=!1;for(;b<a.length;b++)if("<"!==a[b]||f)if(">"===a[b]){if(f?"-"===a[b-1]&&"-"===a[b-2]&&(f=!1,d--):d--,0===d)break}else"["===a[b]?e=!0:a[b];else{if(e&&aQ(a,"!ENTITY",b)){let d,e;b+=7,[d,e,b]=function(a,b){b=aO(a,b);let c="";for(;b<a.length&&!/\s/.test(a[b])&&'"'!==a[b]&&"'"!==a[b];)c+=a[b],b++;if(aR(c),b=aO(a,b),"SYSTEM"===a.substring(b,b+6).toUpperCase())throw Error("External entities are not supported");if("%"===a[b])throw Error("Parameter entities are not supported");let d="";return[b,d]=aP(a,b,"entity"),[c,d,--b]}(a,b+1),-1===e.indexOf("&")&&(c[d]={regx:RegExp(`&${d};`,"g"),val:e})}else if(e&&aQ(a,"!ELEMENT",b)){let{index:c}=function(a,b){b=aO(a,b);let c="";for(;b<a.length&&!/\s/.test(a[b]);)c+=a[b],b++;if(!aR(c))throw Error(`Invalid element name: "${c}"`);b=aO(a,b);let d="";if("E"===a[b]&&aQ(a,"MPTY",b))b+=4;else if("A"===a[b]&&aQ(a,"NY",b))b+=2;else if("("===a[b]){for(b++;b<a.length&&")"!==a[b];)d+=a[b],b++;if(")"!==a[b])throw Error("Unterminated content model")}else throw Error(`Invalid Element Expression, found "${a[b]}"`);return{elementName:c,contentModel:d.trim(),index:b}}(a,(b+=8)+1);b=c}else if(e&&aQ(a,"!ATTLIST",b))b+=8;else if(e&&aQ(a,"!NOTATION",b)){let{index:c}=function(a,b){b=aO(a,b);let c="";for(;b<a.length&&!/\s/.test(a[b]);)c+=a[b],b++;aR(c),b=aO(a,b);let d=a.substring(b,b+6).toUpperCase();if("SYSTEM"!==d&&"PUBLIC"!==d)throw Error(`Expected SYSTEM or PUBLIC, found "${d}"`);b+=d.length,b=aO(a,b);let e=null,f=null;if("PUBLIC"===d)[b,e]=aP(a,b,"publicIdentifier"),b=aO(a,b),('"'===a[b]||"'"===a[b])&&([b,f]=aP(a,b,"systemIdentifier"));else if("SYSTEM"===d&&([b,f]=aP(a,b,"systemIdentifier"),!f))throw Error("Missing mandatory system identifier for SYSTEM notation");return{notationName:c,publicIdentifier:e,systemIdentifier:f,index:--b}}(a,(b+=9)+1);b=c}else if(aQ(a,"!--",b))f=!0;else throw Error("Invalid DOCTYPE");d++}if(0!==d)throw Error("Unclosed DOCTYPE")}else throw Error("Invalid Tag instead of DOCTYPE");return{entities:c,i:b}}(a,f);this.docTypeEntities=b.entities,f=b.i}else if("!["===a.substr(f+1,2)){let b=a5(a,"]]>",f,"CDATA is not closed.")-2,g=a.substring(f+9,b);d=this.saveTextToParentTag(d,c,e);let h=this.parseTextData(g,c.tagname,e,!0,!1,!0,!0);void 0==h&&(h=""),this.options.cdataPropName?c.add(this.options.cdataPropName,[{[this.options.textNodeName]:g}]):c.add(this.options.textNodeName,h),f=b+2}else{let g=a6(a,f,this.options.removeNSPrefix),h=g.tagName,i=g.rawTagName,j=g.tagExp,k=g.attrExpPresent,l=g.closeIndex;this.options.transformTagName&&(h=this.options.transformTagName(h)),c&&d&&"!xml"!==c.tagname&&(d=this.saveTextToParentTag(d,c,e,!1));let m=c;m&&-1!==this.options.unpairedTags.indexOf(m.tagname)&&(c=this.tagsNodeStack.pop(),e=e.substring(0,e.lastIndexOf("."))),h!==b.tagname&&(e+=e?"."+h:h);let n=f;if(this.isItStopNode(this.options.stopNodes,e,h)){let b="";if(j.length>0&&j.lastIndexOf("/")===j.length-1)"/"===h[h.length-1]?(h=h.substr(0,h.length-1),e=e.substr(0,e.length-1),j=h):j=j.substr(0,j.length-1),f=g.closeIndex;else if(-1!==this.options.unpairedTags.indexOf(h))f=g.closeIndex;else{let c=this.readStopNodeData(a,i,l+1);if(!c)throw Error(`Unexpected end of ${i}`);f=c.i,b=c.tagContent}let d=new aN(h);h!==j&&k&&(d[":@"]=this.buildAttributesMap(j,e,h)),b&&(b=this.parseTextData(b,h,e,!0,k,!0,!0)),e=e.substr(0,e.lastIndexOf(".")),d.add(this.options.textNodeName,b),this.addChild(c,d,e,n)}else{if(j.length>0&&j.lastIndexOf("/")===j.length-1){"/"===h[h.length-1]?(h=h.substr(0,h.length-1),e=e.substr(0,e.length-1),j=h):j=j.substr(0,j.length-1),this.options.transformTagName&&(h=this.options.transformTagName(h));let a=new aN(h);h!==j&&k&&(a[":@"]=this.buildAttributesMap(j,e,h)),this.addChild(c,a,e,n),e=e.substr(0,e.lastIndexOf("."))}else{let a=new aN(h);this.tagsNodeStack.push(c),h!==j&&k&&(a[":@"]=this.buildAttributesMap(j,e,h)),this.addChild(c,a,e,n),c=a}d="",f=l}}else d+=a[f];return b.child};function a1(a,b,c,d){this.options.captureMetaData||(d=void 0);let e=this.options.updateTag(b.tagname,c,b[":@"]);!1===e||("string"==typeof e&&(b.tagname=e),a.addChild(b,d))}let a2=function(a){if(this.options.processEntities){for(let b in this.docTypeEntities){let c=this.docTypeEntities[b];a=a.replace(c.regx,c.val)}for(let b in this.lastEntities){let c=this.lastEntities[b];a=a.replace(c.regex,c.val)}if(this.options.htmlEntities)for(let b in this.htmlEntities){let c=this.htmlEntities[b];a=a.replace(c.regex,c.val)}a=a.replace(this.ampEntity.regex,this.ampEntity.val)}return a};function a3(a,b,c,d){return a&&(void 0===d&&(d=0===b.child.length),void 0!==(a=this.parseTextData(a,b.tagname,c,!1,!!b[":@"]&&0!==Object.keys(b[":@"]).length,d))&&""!==a&&b.add(this.options.textNodeName,a),a=""),a}function a4(a,b,c){let d="*."+c;for(let c in a){let e=a[c];if(d===e||b===e)return!0}return!1}function a5(a,b,c,d){let e=a.indexOf(b,c);if(-1!==e)return e+b.length-1;throw Error(d)}function a6(a,b,c,d=">"){let e=function(a,b,c=">"){let d,e="";for(let f=b;f<a.length;f++){let b=a[f];if(d)b===d&&(d="");else if('"'===b||"'"===b)d=b;else if(b===c[0]){if(!c[1])return{data:e,index:f};else if(a[f+1]===c[1])return{data:e,index:f}}else"	"===b&&(b=" ");e+=b}}(a,b+1,d);if(!e)return;let f=e.data,g=e.index,h=f.search(/\s/),i=f,j=!0;-1!==h&&(i=f.substring(0,h),f=f.substring(h+1).trimStart());let k=i;if(c){let a=i.indexOf(":");-1!==a&&(j=(i=i.substr(a+1))!==e.data.substr(a+1))}return{tagName:i,tagExp:f,closeIndex:g,attrExpPresent:j,rawTagName:k}}function a7(a,b,c){let d=c,e=1;for(;c<a.length;c++)if("<"===a[c])if("/"===a[c+1]){let f=a5(a,">",c,`${b} is not closed`);if(a.substring(c+2,f).trim()===b&&0==--e)return{tagContent:a.substring(d,c),i:f};c=f}else if("?"===a[c+1])c=a5(a,"?>",c+1,"StopNode is not closed.");else if("!--"===a.substr(c+1,3))c=a5(a,"--\x3e",c+3,"StopNode is not closed.");else if("!["===a.substr(c+1,2))c=a5(a,"]]>",c,"StopNode is not closed.")-2;else{let d=a6(a,c,">");d&&((d&&d.tagName)===b&&"/"!==d.tagExp[d.tagExp.length-1]&&e++,c=d.closeIndex)}}function a8(a,b,c){if(b&&"string"==typeof a){let b=a.trim();return"true"===b||"false"!==b&&function(a,b={}){if(b=Object.assign({},aU,b),!a||"string"!=typeof a)return a;let c=a.trim();if(void 0!==b.skipLike&&b.skipLike.test(c))return a;{if("0"===a)return 0;if(b.hex&&aS.test(c)){var d,e=c,f=16;if(parseInt)return parseInt(e,16);if(Number.parseInt)return Number.parseInt(e,f);if(window&&window.parseInt)return window.parseInt(e,f);throw Error("parseInt, Number.parseInt, window.parseInt are not supported")}if(-1!==c.search(/.+[eE].+/))return function(a,b,c){if(!c.eNotation)return a;let d=b.match(aV);if(!d)return a;{let e=d[1]||"",f=-1===d[3].indexOf("e")?"E":"e",g=d[2],h=e?a[g.length+1]===f:a[g.length]===f;return g.length>1&&h?a:1===g.length&&(d[3].startsWith(`.${f}`)||d[3][0]===f)?Number(b):c.leadingZeros&&!h?Number(b=(d[1]||"")+d[3]):a}}(a,c,b);let g=aT.exec(c);if(!g)return a;{let e=g[1]||"",f=g[2],h=((d=g[3])&&-1!==d.indexOf(".")&&("."===(d=d.replace(/0+$/,""))?d="0":"."===d[0]?d="0"+d:"."===d[d.length-1]&&(d=d.substring(0,d.length-1))),d),i=e?"."===a[f.length+1]:"."===a[f.length];if(!b.leadingZeros&&(f.length>1||1===f.length&&!i))return a;{let d=Number(c),g=String(d);if(0===d)return d;if(-1!==g.search(/[eE]/))if(b.eNotation)return d;else return a;if(-1!==c.indexOf("."))if("0"===g)return d;else if(g===h)return d;else if(g===`${e}${h}`)return d;else return a;let i=f?h:c;return f?i===g||e+i===g?d:a:i===g||i===e+g?d:a}}}}(a,c)}return void 0!==a?a:""}let a9=aN.getMetaDataSymbol(),ba={allowBooleanAttributes:!1,unpairedTags:[]};function bb(a){return" "===a||"	"===a||"\n"===a||"\r"===a}function bc(a,b){let c=b;for(;b<a.length;b++)if("?"==a[b]||" "==a[b]){let d=a.substr(c,b-c);if(b>5&&"xml"===d)return bg("InvalidXml","XML declaration allowed only at the start of the document.",bh(a,b));if("?"!=a[b]||">"!=a[b+1])continue;b++;break}return b}function bd(a,b){if(a.length>b+5&&"-"===a[b+1]&&"-"===a[b+2]){for(b+=3;b<a.length;b++)if("-"===a[b]&&"-"===a[b+1]&&">"===a[b+2]){b+=2;break}}else if(a.length>b+8&&"D"===a[b+1]&&"O"===a[b+2]&&"C"===a[b+3]&&"T"===a[b+4]&&"Y"===a[b+5]&&"P"===a[b+6]&&"E"===a[b+7]){let c=1;for(b+=8;b<a.length;b++)if("<"===a[b])c++;else if(">"===a[b]&&0==--c)break}else if(a.length>b+9&&"["===a[b+1]&&"C"===a[b+2]&&"D"===a[b+3]&&"A"===a[b+4]&&"T"===a[b+5]&&"A"===a[b+6]&&"["===a[b+7]){for(b+=8;b<a.length;b++)if("]"===a[b]&&"]"===a[b+1]&&">"===a[b+2]){b+=2;break}}return b}let be=RegExp("(\\s*)([^\\s=]+)(\\s*=)?(\\s*(['\"])(([\\s\\S])*?)\\5)?","g");function bf(a,b){let c=aL(a,be),d={};for(let a=0;a<c.length;a++){if(0===c[a][1].length)return bg("InvalidAttr","Attribute '"+c[a][2]+"' has no space in starting.",bi(c[a]));if(void 0!==c[a][3]&&void 0===c[a][4])return bg("InvalidAttr","Attribute '"+c[a][2]+"' is without value.",bi(c[a]));if(void 0===c[a][3]&&!b.allowBooleanAttributes)return bg("InvalidAttr","boolean attribute '"+c[a][2]+"' is not allowed.",bi(c[a]));let e=c[a][2];if(!aM(e))return bg("InvalidAttr","Attribute '"+e+"' is an invalid name.",bi(c[a]));if(d.hasOwnProperty(e))return bg("InvalidAttr","Attribute '"+e+"' is repeated.",bi(c[a]));d[e]=1}return!0}function bg(a,b,c){return{err:{code:a,msg:b,line:c.line||c,col:c.col}}}function bh(a,b){let c=a.substring(0,b).split(/\r?\n/);return{line:c.length,col:c[c.length-1].length+1}}function bi(a){return a.startIndex+a[1].length}class bj{constructor(a){this.externalEntities={},this.options=Object.assign({},aI,a)}parse(a,b){if("string"==typeof a);else if(a.toString)a=a.toString();else throw Error("XML data is accepted in String or Bytes[] form.");if(b){!0===b&&(b={});let c=function(a,b){b=Object.assign({},ba,b);let c=[],d=!1,e=!1;"\uFEFF"===a[0]&&(a=a.substr(1));for(let f=0;f<a.length;f++)if("<"===a[f]&&"?"===a[f+1]){if(f+=2,(f=bc(a,f)).err)return f}else if("<"===a[f]){let g=f;if("!"===a[++f]){f=bd(a,f);continue}{let h=!1;"/"===a[f]&&(h=!0,f++);let i="";for(;f<a.length&&">"!==a[f]&&" "!==a[f]&&"	"!==a[f]&&"\n"!==a[f]&&"\r"!==a[f];f++)i+=a[f];if("/"===(i=i.trim())[i.length-1]&&(i=i.substring(0,i.length-1),f--),!aM(i))return bg("InvalidTag",0===i.trim().length?"Invalid space after '<'.":"Tag '"+i+"' is an invalid name.",bh(a,f));let j=function(a,b){let c="",d="",e=!1;for(;b<a.length;b++){if('"'===a[b]||"'"===a[b])""===d?d=a[b]:d!==a[b]||(d="");else if(">"===a[b]&&""===d){e=!0;break}c+=a[b]}return""===d&&{value:c,index:b,tagClosed:e}}(a,f);if(!1===j)return bg("InvalidAttr","Attributes for '"+i+"' have open quote.",bh(a,f));let k=j.value;if(f=j.index,"/"===k[k.length-1]){let c=f-k.length,e=bf(k=k.substring(0,k.length-1),b);if(!0!==e)return bg(e.err.code,e.err.msg,bh(a,c+e.err.line));d=!0}else if(h)if(!j.tagClosed)return bg("InvalidTag","Closing tag '"+i+"' doesn't have proper closing.",bh(a,f));else{if(k.trim().length>0)return bg("InvalidTag","Closing tag '"+i+"' can't have attributes or invalid starting.",bh(a,g));if(0===c.length)return bg("InvalidTag","Closing tag '"+i+"' has not been opened.",bh(a,g));let b=c.pop();if(i!==b.tagName){let c=bh(a,b.tagStartPos);return bg("InvalidTag","Expected closing tag '"+b.tagName+"' (opened in line "+c.line+", col "+c.col+") instead of closing tag '"+i+"'.",bh(a,g))}0==c.length&&(e=!0)}else{let h=bf(k,b);if(!0!==h)return bg(h.err.code,h.err.msg,bh(a,f-k.length+h.err.line));if(!0===e)return bg("InvalidXml","Multiple possible root nodes found.",bh(a,f));-1!==b.unpairedTags.indexOf(i)||c.push({tagName:i,tagStartPos:g}),d=!0}for(f++;f<a.length;f++)if("<"===a[f])if("!"===a[f+1]){f=bd(a,++f);continue}else if("?"===a[f+1]){if((f=bc(a,++f)).err)return f}else break;else if("&"===a[f]){let b=function(a,b){if(";"===a[++b])return -1;if("#"===a[b]){var c=++b;let d=/\d/;for("x"===a[c]&&(c++,d=/[\da-fA-F]/);c<a.length;c++){if(";"===a[c])return c;if(!a[c].match(d))break}return -1}let d=0;for(;b<a.length;b++,d++)if(!a[b].match(/\w/)||!(d<20)){if(";"===a[b])break;return -1}return b}(a,f);if(-1==b)return bg("InvalidChar","char '&' is not expected.",bh(a,f));f=b}else if(!0===e&&!bb(a[f]))return bg("InvalidXml","Extra text at the end",bh(a,f));"<"===a[f]&&f--}}else{if(bb(a[f]))continue;return bg("InvalidChar","char '"+a[f]+"' is not expected.",bh(a,f))}return d?1==c.length?bg("InvalidTag","Unclosed tag '"+c[0].tagName+"'.",bh(a,c[0].tagStartPos)):!(c.length>0)||bg("InvalidXml","Invalid '"+JSON.stringify(c.map(a=>a.tagName),null,4).replace(/\r?\n/g,"")+"' found.",{line:1,col:1}):bg("InvalidXml","Start tag expected.",1)}(a,b);if(!0!==c)throw Error(`${c.err.msg}:${c.err.line}:${c.err.col}`)}let c=new aW(this.options);c.addExternalEntities(this.externalEntities);let d=c.parseXml(a);return this.options.preserveOrder||void 0===d?d:function a(b,c,d){let e,f={};for(let g=0;g<b.length;g++){let h=b[g],i=function(a){let b=Object.keys(a);for(let a=0;a<b.length;a++){let c=b[a];if(":@"!==c)return c}}(h),j="";if(j=void 0===d?i:d+"."+i,i===c.textNodeName)void 0===e?e=h[i]:e+=""+h[i];else if(void 0===i)continue;else if(h[i]){let b=a(h[i],c,j),d=function(a,b){let{textNodeName:c}=b,d=Object.keys(a).length;return 0===d||1===d&&(!!a[c]||"boolean"==typeof a[c]||0===a[c])}(b,c);void 0!==h[a9]&&(b[a9]=h[a9]),h[":@"]?function(a,b,c,d){if(b){let e=Object.keys(b),f=e.length;for(let g=0;g<f;g++){let f=e[g];d.isArray(f,c+"."+f,!0,!0)?a[f]=[b[f]]:a[f]=b[f]}}}(b,h[":@"],j,c):1!==Object.keys(b).length||void 0===b[c.textNodeName]||c.alwaysCreateTextNode?0===Object.keys(b).length&&(c.alwaysCreateTextNode?b[c.textNodeName]="":b=""):b=b[c.textNodeName],void 0!==f[i]&&f.hasOwnProperty(i)?(Array.isArray(f[i])||(f[i]=[f[i]]),f[i].push(b)):c.isArray(i,j,d)?f[i]=[b]:f[i]=b}}return"string"==typeof e?e.length>0&&(f[c.textNodeName]=e):void 0!==e&&(f[c.textNodeName]=e),f}(d,this.options)}addEntity(a,b){if(-1!==b.indexOf("&"))throw Error("Entity value can't have '&'");if(-1!==a.indexOf("&")||-1!==a.indexOf(";"))throw Error("An entity must be set without '&' and ';'. Eg. use '#xD' for '&#xD;'");if("&"===b)throw Error("An entity with value '&' is not permitted");this.externalEntities[a]=b}static getMetaDataSymbol(){return aN.getMetaDataSymbol()}}var bk=c(70525);let bl=(a,b)=>(0,bk.w)(a,b).then(a=>{if(a.length){let b,c=new bj({attributeNamePrefix:"",htmlEntities:!0,ignoreAttributes:!1,ignoreDeclaration:!0,parseTagValue:!1,trimValues:!1,tagValueProcessor:(a,b)=>""===b.trim()&&b.includes("\n")?"":void 0});c.addEntity("#xD","\r"),c.addEntity("#10","\n");try{b=c.parse(a,!0)}catch(b){throw b&&"object"==typeof b&&Object.defineProperty(b,"$responseBodyText",{value:a}),b}let d="#text",e=Object.keys(b)[0],f=b[e];return f[d]&&(f[e]=f[d],delete f[d]),(0,p.rm)(f)}return{}}),bm=async(a,b)=>{let c=await bl(a,b);return c.Error&&(c.Error.message=c.Error.message??c.Error.Message),c},bn=async(a,b)=>{let c;return c=cw({...bz(a,b),[bY]:b$,[cr]:bX}),bV(b,bW,"/",void 0,c)},bo=async(a,b)=>{let c;return c=cw({...bA(a,b),[bY]:b1,[cr]:bX}),bV(b,bW,"/",void 0,c)},bp=async(a,b)=>{if(a.statusCode>=300)return br(a,b);let c=await bl(a.body,b),d={};return d=bJ(c.AssumeRoleResult,b),{$metadata:bT(a),...d}},bq=async(a,b)=>{if(a.statusCode>=300)return br(a,b);let c=await bl(a.body,b),d={};return d=bK(c.AssumeRoleWithWebIdentityResult,b),{$metadata:bT(a),...d}},br=async(a,b)=>{let c={...a,body:await bm(a.body,b)},d=cx(a,c.body);switch(d){case"ExpiredTokenException":case"com.amazonaws.sts#ExpiredTokenException":throw await bs(c,b);case"MalformedPolicyDocument":case"com.amazonaws.sts#MalformedPolicyDocumentException":throw await bw(c,b);case"PackedPolicyTooLarge":case"com.amazonaws.sts#PackedPolicyTooLargeException":throw await bx(c,b);case"RegionDisabledException":case"com.amazonaws.sts#RegionDisabledException":throw await by(c,b);case"IDPCommunicationError":case"com.amazonaws.sts#IDPCommunicationErrorException":throw await bt(c,b);case"IDPRejectedClaim":case"com.amazonaws.sts#IDPRejectedClaimException":throw await bu(c,b);case"InvalidIdentityToken":case"com.amazonaws.sts#InvalidIdentityTokenException":throw await bv(c,b);default:return bU({output:a,parsedBody:c.body.Error,errorCode:d})}},bs=async(a,b)=>{let c=a.body,d=bM(c.Error,b),e=new az({$metadata:bT(a),...d});return(0,p.Mw)(e,c)},bt=async(a,b)=>{let c=a.body,d=bN(c.Error,b),e=new aH({$metadata:bT(a),...d});return(0,p.Mw)(e,c)},bu=async(a,b)=>{let c=a.body,d=bO(c.Error,b),e=new aD({$metadata:bT(a),...d});return(0,p.Mw)(e,c)},bv=async(a,b)=>{let c=a.body,d=bP(c.Error,b),e=new aE({$metadata:bT(a),...d});return(0,p.Mw)(e,c)},bw=async(a,b)=>{let c=a.body,d=bQ(c.Error,b),e=new aA({$metadata:bT(a),...d});return(0,p.Mw)(e,c)},bx=async(a,b)=>{let c=a.body,d=bR(c.Error,b),e=new aB({$metadata:bT(a),...d});return(0,p.Mw)(e,c)},by=async(a,b)=>{let c=a.body,d=bS(c.Error,b),e=new aC({$metadata:bT(a),...d});return(0,p.Mw)(e,c)},bz=(a,b)=>{let c={};if(null!=a[ch]&&(c[ch]=a[ch]),null!=a[ci]&&(c[ci]=a[ci]),null!=a[cb]){let d=bB(a[cb],b);a[cb]?.length===0&&(c.PolicyArns=[]),Object.entries(d).forEach(([a,b])=>{c[`PolicyArns.${a}`]=b})}if(null!=a[ca]&&(c[ca]=a[ca]),null!=a[b6]&&(c[b6]=a[b6]),null!=a[co]){let d=bH(a[co],b);a[co]?.length===0&&(c.Tags=[]),Object.entries(d).forEach(([a,b])=>{c[`Tags.${a}`]=b})}if(null!=a[cq]){let d=bG(a[cq],b);a[cq]?.length===0&&(c.TransitiveTagKeys=[]),Object.entries(d).forEach(([a,b])=>{c[`TransitiveTagKeys.${a}`]=b})}if(null!=a[b8]&&(c[b8]=a[b8]),null!=a[cm]&&(c[cm]=a[cm]),null!=a[cp]&&(c[cp]=a[cp]),null!=a[cl]&&(c[cl]=a[cl]),null!=a[cd]){let d=bE(a[cd],b);a[cd]?.length===0&&(c.ProvidedContexts=[]),Object.entries(d).forEach(([a,b])=>{c[`ProvidedContexts.${a}`]=b})}return c},bA=(a,b)=>{let c={};if(null!=a[ch]&&(c[ch]=a[ch]),null!=a[ci]&&(c[ci]=a[ci]),null!=a[ct]&&(c[ct]=a[ct]),null!=a[ce]&&(c[ce]=a[ce]),null!=a[cb]){let d=bB(a[cb],b);a[cb]?.length===0&&(c.PolicyArns=[]),Object.entries(d).forEach(([a,b])=>{c[`PolicyArns.${a}`]=b})}return null!=a[ca]&&(c[ca]=a[ca]),null!=a[b6]&&(c[b6]=a[b6]),c},bB=(a,b)=>{let c={},d=1;for(let e of a)null!==e&&(Object.entries(bC(e,b)).forEach(([a,b])=>{c[`member.${d}.${a}`]=b}),d++);return c},bC=(a,b)=>{let c={};return null!=a[cu]&&(c[cu]=a[cu]),c},bD=(a,b)=>{let c={};return null!=a[cc]&&(c[cc]=a[cc]),null!=a[b5]&&(c[b5]=a[b5]),c},bE=(a,b)=>{let c={},d=1;for(let e of a)null!==e&&(Object.entries(bD(e,b)).forEach(([a,b])=>{c[`member.${d}.${a}`]=b}),d++);return c},bF=(a,b)=>{let c={};return null!=a[b9]&&(c[b9]=a[b9]),null!=a[cs]&&(c[cs]=a[cs]),c},bG=(a,b)=>{let c={},d=1;for(let b of a)null!==b&&(c[`member.${d}`]=b,d++);return c},bH=(a,b)=>{let c={},d=1;for(let e of a)null!==e&&(Object.entries(bF(e,b)).forEach(([a,b])=>{c[`member.${d}.${a}`]=b}),d++);return c},bI=(a,b)=>{let c={};return null!=a[b_]&&(c[b_]=(0,p.lK)(a[b_])),null!=a[b2]&&(c[b2]=(0,p.lK)(a[b2])),c},bJ=(a,b)=>{let c={};return null!=a[b4]&&(c[b4]=bL(a[b4],b)),null!=a[b0]&&(c[b0]=bI(a[b0],b)),null!=a[cf]&&(c[cf]=(0,p.xW)(a[cf])),null!=a[cl]&&(c[cl]=(0,p.lK)(a[cl])),c},bK=(a,b)=>{let c={};return null!=a[b4]&&(c[b4]=bL(a[b4],b)),null!=a[ck]&&(c[ck]=(0,p.lK)(a[ck])),null!=a[b0]&&(c[b0]=bI(a[b0],b)),null!=a[cf]&&(c[cf]=(0,p.xW)(a[cf])),null!=a[cg]&&(c[cg]=(0,p.lK)(a[cg])),null!=a[b3]&&(c[b3]=(0,p.lK)(a[b3])),null!=a[cl]&&(c[cl]=(0,p.lK)(a[cl])),c},bL=(a,b)=>{let c={};return null!=a[bZ]&&(c[bZ]=(0,p.lK)(a[bZ])),null!=a[cj]&&(c[cj]=(0,p.lK)(a[cj])),null!=a[cn]&&(c[cn]=(0,p.lK)(a[cn])),null!=a[b7]&&(c[b7]=(0,p.Y0)((0,p.t_)(a[b7]))),c},bM=(a,b)=>{let c={};return null!=a[cv]&&(c[cv]=(0,p.lK)(a[cv])),c},bN=(a,b)=>{let c={};return null!=a[cv]&&(c[cv]=(0,p.lK)(a[cv])),c},bO=(a,b)=>{let c={};return null!=a[cv]&&(c[cv]=(0,p.lK)(a[cv])),c},bP=(a,b)=>{let c={};return null!=a[cv]&&(c[cv]=(0,p.lK)(a[cv])),c},bQ=(a,b)=>{let c={};return null!=a[cv]&&(c[cv]=(0,p.lK)(a[cv])),c},bR=(a,b)=>{let c={};return null!=a[cv]&&(c[cv]=(0,p.lK)(a[cv])),c},bS=(a,b)=>{let c={};return null!=a[cv]&&(c[cv]=(0,p.lK)(a[cv])),c},bT=a=>({httpStatusCode:a.statusCode,requestId:a.headers["x-amzn-requestid"]??a.headers["x-amzn-request-id"]??a.headers["x-amz-request-id"],extendedRequestId:a.headers["x-amz-id-2"],cfId:a.headers["x-amz-cf-id"]}),bU=(0,p.jr)(aw),bV=async(a,b,c,d,e)=>{let{hostname:f,protocol:g="https",port:h,path:i}=await a.endpoint(),j={protocol:g,hostname:f,port:h,method:"POST",path:i.endsWith("/")?i.slice(0,-1)+c:i+c,headers:b};return void 0!==d&&(j.hostname=d),void 0!==e&&(j.body=e),new at.Kd(j)},bW={"content-type":"application/x-www-form-urlencoded"},bX="2011-06-15",bY="Action",bZ="AccessKeyId",b$="AssumeRole",b_="AssumedRoleId",b0="AssumedRoleUser",b1="AssumeRoleWithWebIdentity",b2="Arn",b3="Audience",b4="Credentials",b5="ContextAssertion",b6="DurationSeconds",b7="Expiration",b8="ExternalId",b9="Key",ca="Policy",cb="PolicyArns",cc="ProviderArn",cd="ProvidedContexts",ce="ProviderId",cf="PackedPolicySize",cg="Provider",ch="RoleArn",ci="RoleSessionName",cj="SecretAccessKey",ck="SubjectFromWebIdentityToken",cl="SourceIdentity",cm="SerialNumber",cn="SessionToken",co="Tags",cp="TokenCode",cq="TransitiveTagKeys",cr="Version",cs="Value",ct="WebIdentityToken",cu="arn",cv="message",cw=a=>Object.entries(a).map(([a,b])=>(0,p.$6)(a)+"="+(0,p.$6)(b)).join("&"),cx=(a,b)=>b.Error?.Code!==void 0?b.Error.Code:404==a.statusCode?"NotFound":void 0;class cy extends p.uB.classBuilder().ep(u).m(function(a,b,c,d){return[(0,av.TM)(c,this.serialize,this.deserialize),(0,n.rD)(c,a.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRole",{}).n("STSClient","AssumeRoleCommand").f(void 0,ay).ser(bn).de(bp).build(){}class cz extends p.uB.classBuilder().ep(u).m(function(a,b,c,d){return[(0,av.TM)(c,this.serialize,this.deserialize),(0,n.rD)(c,a.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRoleWithWebIdentity",{}).n("STSClient","AssumeRoleWithWebIdentityCommand").f(aF,aG).ser(bo).de(bq).build(){}class cA extends au{}(0,p.J1)({AssumeRoleCommand:cy,AssumeRoleWithWebIdentityCommand:cz},cA);var cB=c(21905);let cC="us-east-1",cD=a=>{if("string"==typeof a?.Arn){let b=a.Arn.split(":");if(b.length>4&&""!==b[4])return b[4]}},cE=async(a,b,c)=>{let d="function"==typeof a?await a():a,e="function"==typeof b?await b():b;return c?.debug?.("@aws-sdk/client-sts::resolveRegion","accepting first of:",`${d} (provider)`,`${e} (parent client)`,`${cC} (STS default)`),d??e??cC},cF=a=>a?.metadata?.handlerProtocol==="h2",cG=(a,b)=>b?class extends a{constructor(a){for(let c of(super(a),b))this.middlewareStack.use(c)}}:a,cH=(a={},b)=>((a,b)=>{let c,d;return async(e,f)=>{if(d=e,!c){let{logger:e=a?.parentClientConfig?.logger,region:f,requestHandler:g=a?.parentClientConfig?.requestHandler,credentialProviderLogger:h}=a,i=await cE(f,a?.parentClientConfig?.region,h),j=!cF(g);c=new b({profile:a?.parentClientConfig?.profile,credentialDefaultProvider:()=>async()=>d,region:i,requestHandler:j?g:void 0,logger:e})}let{Credentials:g,AssumedRoleUser:h}=await c.send(new cy(f));if(!g||!g.AccessKeyId||!g.SecretAccessKey)throw Error(`Invalid response from STS.assumeRole call with role ${f.RoleArn}`);let i=cD(h),j={accessKeyId:g.AccessKeyId,secretAccessKey:g.SecretAccessKey,sessionToken:g.SessionToken,expiration:g.Expiration,...g.CredentialScope&&{credentialScope:g.CredentialScope},...i&&{accountId:i}};return(0,cB.g)(j,"CREDENTIALS_STS_ASSUME_ROLE","i"),j}})(a,cG(au,b)),cI=(a={},b)=>((a,b)=>{let c;return async d=>{if(!c){let{logger:d=a?.parentClientConfig?.logger,region:e,requestHandler:f=a?.parentClientConfig?.requestHandler,credentialProviderLogger:g}=a,h=await cE(e,a?.parentClientConfig?.region,g),i=!cF(f);c=new b({profile:a?.parentClientConfig?.profile,region:h,requestHandler:i?f:void 0,logger:d})}let{Credentials:e,AssumedRoleUser:f}=await c.send(new cz(d));if(!e||!e.AccessKeyId||!e.SecretAccessKey)throw Error(`Invalid response from STS.assumeRoleWithWebIdentity call with role ${d.RoleArn}`);let g=cD(f),h={accessKeyId:e.AccessKeyId,secretAccessKey:e.SecretAccessKey,sessionToken:e.SessionToken,expiration:e.Expiration,...e.CredentialScope&&{credentialScope:e.CredentialScope},...g&&{accountId:g}};return g&&(0,cB.g)(h,"RESOLVED_ACCOUNT_ID","T"),(0,cB.g)(h,"CREDENTIALS_STS_ASSUME_ROLE_WEB_ID","k"),h}})(a,cG(au,b))},43279:a=>{a.exports={rE:"3.855.0"}},70525:(a,b,c)=>{c.d(b,{w:()=>e});var d=c(35639);let e=(a,b)=>(0,d.Px)(a,b).then(a=>b.utf8Encoder(a))}};