// Sync Management JavaScript
(function() {
  'use strict';

  // State management
  let isLoading = false;
  let stats = null;

  // DOM elements
  const elements = {
    cmsChallengersCount: document.getElementById('cms-challenges-count'),
    syncedChallengesCount: document.getElementById('synced-challenges-count'),
    cmsUsersCount: document.getElementById('cms-users-count'),
    syncedUsersCount: document.getElementById('synced-users-count'),
    availableUserChallengesCount: document.getElementById('available-user-challenges-count'),
    importedChallengesCount: document.getElementById('imported-challenges-count'),
    syncChallengesBtn: document.getElementById('sync-challenges-btn'),
    syncUsersBtn: document.getElementById('sync-users-btn'),
    importChallengesBtn: document.getElementById('import-challenges-btn'),
    refreshStatsBtn: document.getElementById('refresh-stats-btn'),
    challengeSyncResult: document.getElementById('challenge-sync-result'),
    userSyncResult: document.getElementById('user-sync-result'),
    importSyncResult: document.getElementById('import-sync-result'),
    errorDisplay: document.getElementById('error-display'),
    errorMessage: document.getElementById('error-message')
  };

  // Utility functions
  function showError(message) {
    elements.errorMessage.textContent = message;
    elements.errorDisplay.style.display = 'block';
    setTimeout(() => {
      elements.errorDisplay.style.display = 'none';
    }, 5000);
  }

  function showSuccess(message) {
    // Create a temporary success message
    const successDiv = document.createElement('div');
    successDiv.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background-color: #10b981;
      color: white;
      padding: 15px 20px;
      border-radius: 6px;
      font-weight: 500;
      z-index: 1000;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    `;
    successDiv.textContent = message;
    document.body.appendChild(successDiv);
    
    setTimeout(() => {
      document.body.removeChild(successDiv);
    }, 3000);
  }

  function setLoading(loading) {
    isLoading = loading;
    const buttons = [elements.syncChallengesBtn, elements.syncUsersBtn, elements.importChallengesBtn, elements.refreshStatsBtn];

    buttons.forEach(btn => {
      if (btn) {
        btn.disabled = loading;
        btn.style.opacity = loading ? '0.6' : '1';
        btn.style.cursor = loading ? 'not-allowed' : 'pointer';
      }
    });

    if (loading) {
      if (elements.syncChallengesBtn) {
        elements.syncChallengesBtn.textContent = '🔄 Syncing Challenges...';
      }
      if (elements.syncUsersBtn) {
        elements.syncUsersBtn.textContent = '🔄 Syncing Users...';
      }
      if (elements.importChallengesBtn) {
        elements.importChallengesBtn.textContent = '🔄 Importing Challenges...';
      }
    } else {
      if (elements.syncChallengesBtn) {
        elements.syncChallengesBtn.textContent = '🔄 Sync Challenges to Main App';
      }
      if (elements.syncUsersBtn) {
        elements.syncUsersBtn.textContent = '🔄 Sync Users to Main App';
      }
      if (elements.importChallengesBtn) {
        elements.importChallengesBtn.textContent = '⬅️ Import User Challenges from Main App';
      }
    }
  }

  // Get auth token from localStorage or cookies
  function getAuthToken() {
    // Try localStorage first (common for Payload CMS)
    let token = localStorage.getItem('payload-token');
    if (token) return token;

    // Try cookies as fallback
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === 'payload-token') {
        return value;
      }
    }

    return null;
  }

  // Fetch sync statistics
  async function fetchStats() {
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('Authentication token not found. Please log in again.');
      }

      const [challengeResponse, userResponse, importResponse] = await Promise.all([
        fetch('/api/sync/challenges', {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }),
        fetch('/api/sync/users', {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }),
        fetch('/api/sync/from-main-app', {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }),
      ]);

      if (!challengeResponse.ok || !userResponse.ok || !importResponse.ok) {
        throw new Error('Failed to fetch sync statistics');
      }

      const challengeData = await challengeResponse.json();
      const userData = await userResponse.json();
      const importData = await importResponse.json();

      if (challengeData.success && userData.success && importData.success) {
        stats = {
          challenges: challengeData.stats,
          users: userData.stats,
          import: importData.stats,
        };
        updateStatsDisplay();
      } else {
        throw new Error('Invalid response from sync API');
      }
    } catch (error) {
      console.error('Error fetching sync stats:', error);
      showError(error.message);
    }
  }

  // Update stats display
  function updateStatsDisplay() {
    if (!stats) return;

    if (elements.cmsChallengersCount) {
      elements.cmsChallengersCount.textContent = stats.challenges.cmsChallenge || '0';
    }
    if (elements.syncedChallengesCount) {
      elements.syncedChallengesCount.textContent = stats.challenges.mainAppCmsChallenge || '0';
    }
    if (elements.cmsUsersCount) {
      elements.cmsUsersCount.textContent = stats.users.cmsUsers || '0';
    }
    if (elements.syncedUsersCount) {
      elements.syncedUsersCount.textContent = stats.users.mainAppCmsUsers || '0';
    }
    if (elements.availableUserChallengesCount) {
      elements.availableUserChallengesCount.textContent = stats.import.availableForSync || '0';
    }
    if (elements.importedChallengesCount) {
      elements.importedChallengesCount.textContent = stats.import.alreadySynced || '0';
    }
  }

  // Sync challenges
  async function syncChallenges() {
    if (isLoading) return;
    
    setLoading(true);
    
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('Authentication token not found. Please log in again.');
      }

      const response = await fetch('/api/sync/challenges', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      const data = await response.json();

      if (data.success) {
        showSuccess(`Challenge sync completed! ${data.stats.synced} challenges synced successfully.`);
        
        if (elements.challengeSyncResult) {
          elements.challengeSyncResult.innerHTML = `
            <strong>Last Sync:</strong> 
            Total: ${data.stats.total}, 
            Synced: <span style="color: #16a34a;">${data.stats.synced}</span>
            ${data.stats.errors > 0 ? `, Errors: <span style="color: #dc2626;">${data.stats.errors}</span>` : ''}
          `;
        }
        
        // Refresh stats
        await fetchStats();
      } else {
        throw new Error(data.error || 'Challenge sync failed');
      }
    } catch (error) {
      console.error('Error syncing challenges:', error);
      showError(error.message);
    } finally {
      setLoading(false);
    }
  }

  // Sync users
  async function syncUsers() {
    if (isLoading) return;
    
    setLoading(true);
    
    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('Authentication token not found. Please log in again.');
      }

      const response = await fetch('/api/sync/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      const data = await response.json();

      if (data.success) {
        showSuccess(`User sync completed! ${data.stats.synced} users synced successfully.`);
        
        if (elements.userSyncResult) {
          elements.userSyncResult.innerHTML = `
            <strong>Last Sync:</strong> 
            Total: ${data.stats.total}, 
            Synced: <span style="color: #16a34a;">${data.stats.synced}</span>
            ${data.stats.errors > 0 ? `, Errors: <span style="color: #dc2626;">${data.stats.errors}</span>` : ''}
          `;
        }
        
        // Refresh stats
        await fetchStats();
      } else {
        throw new Error(data.error || 'User sync failed');
      }
    } catch (error) {
      console.error('Error syncing users:', error);
      showError(error.message);
    } finally {
      setLoading(false);
    }
  }

  // Import user-created challenges from main app
  async function importChallenges() {
    if (isLoading) return;

    setLoading(true);

    try {
      const token = getAuthToken();
      if (!token) {
        throw new Error('Authentication token not found. Please log in again.');
      }

      const response = await fetch('/api/sync/from-main-app', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      const data = await response.json();

      if (data.success) {
        showSuccess(`Import completed! ${data.stats.synced} user challenges imported successfully.`);

        if (elements.importSyncResult) {
          elements.importSyncResult.innerHTML = `
            <strong>Last Import:</strong>
            Total: ${data.stats.total},
            Imported: <span style="color: #16a34a;">${data.stats.synced}</span>,
            Skipped: <span style="color: #f59e0b;">${data.stats.skipped}</span>
            ${data.stats.errors > 0 ? `, Errors: <span style="color: #dc2626;">${data.stats.errors}</span>` : ''}
          `;
        }

        // Refresh stats
        await fetchStats();
      } else {
        throw new Error(data.error || 'Challenge import failed');
      }
    } catch (error) {
      console.error('Error importing challenges:', error);
      showError(error.message);
    } finally {
      setLoading(false);
    }
  }

  // Event listeners
  function setupEventListeners() {
    if (elements.syncChallengesBtn) {
      elements.syncChallengesBtn.addEventListener('click', syncChallenges);
    }

    if (elements.syncUsersBtn) {
      elements.syncUsersBtn.addEventListener('click', syncUsers);
    }

    if (elements.importChallengesBtn) {
      elements.importChallengesBtn.addEventListener('click', importChallenges);
    }

    if (elements.refreshStatsBtn) {
      elements.refreshStatsBtn.addEventListener('click', fetchStats);
    }
  }

  // Initialize
  function init() {
    console.log('Initializing Sync Management...');
    setupEventListeners();
    fetchStats();
  }

  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }

})();
