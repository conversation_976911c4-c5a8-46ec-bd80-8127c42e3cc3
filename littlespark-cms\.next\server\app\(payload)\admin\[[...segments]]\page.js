(()=>{var a={};a.id=1687,a.ids=[1687],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},643:a=>{"use strict";a.exports=require("node:perf_hooks")},1708:a=>{"use strict";a.exports=require("node:process")},2989:(a,b,c)=>{"use strict";c.d(b,{ResetPreferences:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call ResetPreferences() from the server but ResetPreferences is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\node_modules\\@payloadcms\\next\\dist\\views\\Account\\ResetPreferences\\index.js","ResetPreferences")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4268:(a,b,c)=>{"use strict";c.d(b,{ToastAndRedirect:()=>h});var d=c(83951),e=c(54845),f=c(16189),g=c(43210);function h(a){let b,c,h=(0,d.c)(6),{message:i,redirectTo:j}=a,k=(0,f.useRouter)(),{startRouteTransition:l}=(0,e.useRouteTransition)(),m=g.useRef(!1);return h[0]!==i||h[1]!==j||h[2]!==k||h[3]!==l?(b=()=>{let a;return e.toast&&(a=setTimeout(()=>{e.toast.success(i),m.current=!0,l(()=>k.push(j))},100)),()=>{a&&clearTimeout(a)}},c=[k,j,i,l],h[0]=i,h[1]=j,h[2]=k,h[3]=l,h[4]=b,h[5]=c):(b=h[4],c=h[5]),null}},4573:a=>{"use strict";a.exports=require("node:buffer")},4984:a=>{"use strict";a.exports=require("readline")},8086:a=>{"use strict";a.exports=require("module")},8377:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{default:()=>k,generateMetadata:()=>j});var e=c(81329),f=c(16074),g=c(40119),h=c(63068),i=a([e]);e=(i.then?(await i)():i)[0];let j=({params:a,searchParams:b})=>(0,f._)({config:e.A,params:a,searchParams:b}),k=({params:a,searchParams:b})=>(0,g.S)({config:e.A,params:a,searchParams:b,importMap:h.m});d()}catch(a){d(a)}})},9288:a=>{"use strict";a.exports=require("sharp")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11906:(a,b,c)=>{"use strict";c.d(b,{ToastAndRedirect:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call ToastAndRedirect() from the server but ToastAndRedirect is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\node_modules\\@payloadcms\\next\\dist\\views\\Verify\\index.client.js","ToastAndRedirect")},11997:a=>{"use strict";a.exports=require("punycode")},12149:(a,b,c)=>{"use strict";c.d(b,{ToggleTheme:()=>i});var d=c(83951),e=c(60687),f=c(54836),g=c(85957),h=c(54845);c(43210);let i=()=>{let a,b,c=(0,d.c)(7),{autoMode:i,setTheme:j,theme:k}=(0,f.h)(),{t:l}=(0,g.d)();c[0]!==j?(a=a=>{j(a)},c[0]=j,c[1]=a):a=c[1];let m=a;return c[2]!==i||c[3]!==m||c[4]!==l||c[5]!==k?(b=(0,e.jsx)(h.RadioGroupField,{disableModifyingForm:!0,field:{name:"theme",label:l("general:adminTheme"),options:[{label:l("general:automatic"),value:"auto"},{label:l("general:light"),value:"light"},{label:l("general:dark"),value:"dark"}]},onChange:m,path:"theme",value:i?"auto":k}),c[2]=i,c[3]=m,c[4]=l,c[5]=k,c[6]=b):b=c[6],b}},14007:a=>{"use strict";a.exports=require("pino-pretty")},14253:(a,b,c)=>{"use strict";c.d(b,{LoginForm:()=>n});var d=c(83951),e=c(60687),f=c(43210),g=c(54836),h=c(85957),i=c(54845),j=c(68081),k=c(28749);let l=a=>{let b=(0,d.c)(11),{type:c,required:f}=a,g=void 0===f||f,{t:j}=(0,h.d)();if("email"===c){let a;return b[0]!==g||b[1]!==j?(a=(0,e.jsx)(i.EmailField,{field:{name:"email",admin:{autoComplete:"email"},label:j("general:email"),required:g},path:"email",validate:k.Rp}),b[0]=g,b[1]=j,b[2]=a):a=b[2],a}if("username"===c){let a;return b[3]!==g||b[4]!==j?(a=(0,e.jsx)(i.TextField,{field:{name:"username",label:j("authentication:username"),required:g},path:"username",validate:k.Xh}),b[3]=g,b[4]=j,b[5]=a):a=b[5],a}if("emailOrUsername"===c){let a;if(b[6]!==g||b[7]!==j){let c;b[9]!==j?(c=(a,b)=>{let c=(0,k.Xh)(a,b),d=(0,k.Rp)(a,b);return!!d||!!c||`${j("general:email")}: ${d} ${j("general:username")}: ${c}`},b[9]=j,b[10]=c):c=b[10],a=(0,e.jsx)(i.TextField,{field:{name:"username",label:j("authentication:emailOrUsername"),required:g},path:"username",validate:c}),b[6]=g,b[7]=j,b[8]=a}else a=b[8];return a}return null},m="login__form",n=a=>{let b,c,k,n,o=(0,d.c)(23),{prefillEmail:p,prefillPassword:q,prefillUsername:r,searchParams:s}=a,{config:t,getEntityConfig:u}=(0,g.b)(),{admin:v,routes:w}=t,{routes:x,user:y}=v,{forgot:z}=x,{admin:A,api:B}=w;if(o[0]!==u||o[1]!==y){let{auth:a}=u({collectionSlug:y});c=(a=>({canLoginWithEmail:!a||a.allowEmailLogin,canLoginWithUsername:!!a}))(b=a.loginWithUsername),o[0]=u,o[1]=y,o[2]=b,o[3]=c}else b=o[2],c=o[3];let{canLoginWithEmail:C,canLoginWithUsername:D}=c;o[4]!==C||o[5]!==D?(k=()=>C&&D?"emailOrUsername":D?"username":"email",o[4]=C,o[5]=D,o[6]=k):k=o[6];let[E]=f.useState(k),{t:F}=(0,h.d)(),{setUser:G}=(0,i.useAuth)(),H=q??void 0,I=q??void 0;if(o[7]!==A||o[8]!==B||o[9]!==z||o[10]!==E||o[11]!==b||o[12]!==p||o[13]!==r||o[14]!==s?.redirect||o[15]!==G||o[16]!==F||o[17]!==H||o[18]!==I||o[19]!==y){let a,c={password:{initialValue:H,valid:!0,value:I}};b?c.username={initialValue:r??void 0,valid:!0,value:r??void 0}:c.email={initialValue:p??void 0,valid:!0,value:p??void 0},o[21]!==G?(a=a=>{G(a)},o[21]=G,o[22]=a):a=o[22];let d=a;n=(0,e.jsxs)(i.Form,{action:`${B}/${y}/login`,className:m,disableSuccessStatus:!0,initialState:c,method:"POST",onSuccess:d,redirect:(({allowAbsoluteUrls:a=!1,fallbackTo:b="/",redirectTo:c})=>{let d;if("string"!=typeof c)return b;try{d=decodeURIComponent(c.trim())}catch{return b}let e=d.startsWith("/")&&!d.startsWith("//")&&!d.startsWith("/%2F")&&!d.startsWith("/\\/")&&!d.startsWith("/\\\\")&&!d.startsWith("/\\")&&!d.toLowerCase().startsWith("/javascript:")&&!d.toLowerCase().startsWith("/http"),f=a&&/^https?:\/\/\S+$/i.test(d);return e||f?d:b})({fallbackTo:A,redirectTo:s?.redirect}),waitForAutocomplete:!0,children:[(0,e.jsxs)("div",{className:`${m}__inputWrap`,children:[(0,e.jsx)(l,{type:E}),(0,e.jsx)(i.PasswordField,{field:{name:"password",label:F("general:password"),required:!0},path:"password"})]}),(0,e.jsx)(i.Link,{href:(0,j.Q)({adminRoute:A,path:z}),prefetch:!1,children:F("authentication:forgotPasswordQuestion")}),(0,e.jsx)(i.FormSubmit,{size:"large",children:F("authentication:login")})]}),o[7]=A,o[8]=B,o[9]=z,o[10]=E,o[11]=b,o[12]=p,o[13]=r,o[14]=s?.redirect,o[15]=G,o[16]=F,o[17]=H,o[18]=I,o[19]=y,o[20]=n}else n=o[20];return n}},16074:(a,b,c)=>{"use strict";c.d(b,{_:()=>M});var d=c(3439),e=c(44999),f=c(12431);let g=async({config:a})=>await (0,d.L)({config:a.i18n,context:"client",language:(0,f.Y)({config:a,cookies:await (0,e.UL)(),headers:await (0,e.b3)()})});var h=c(99203),i=c(12563),j=c(73768),k=c(86565);let l={description:"Payload is a headless CMS and application framework built with TypeScript, Node.js, and React.",siteName:"Payload App",title:"Payload App"},m=async a=>{let{defaultOGImageType:b,serverURL:c,titleSuffix:d,...e}=a,f=e.icons||[{type:"image/png",rel:"icon",sizes:"32x32",url:"object"==typeof h.default?h.default?.src:h.default},{type:"image/png",media:"(prefers-color-scheme: dark)",rel:"icon",sizes:"32x32",url:"object"==typeof i.default?i.default?.src:i.default}],g=[e.title,d].filter(Boolean).join(" "),m=`${"string"==typeof e.openGraph?.title?e.openGraph.title:e.title} ${d}`,n={...l||{},..."dynamic"===b?{images:[{alt:m,height:630,url:`/api/og${k.A({description:e.openGraph?.description||l.description,title:m},{addQueryPrefix:!0})}`,width:1200}]}:{},..."static"===b?{images:[{alt:m,height:480,url:"object"==typeof j.default?j.default?.src:j.default,width:640}]}:{},title:m,...e.openGraph||{}};return Promise.resolve({...e,icons:f,metadataBase:new URL(c||process.env.PAYLOAD_PUBLIC_SERVER_URL||`http://localhost:${process.env.PORT||3e3}`),openGraph:n,title:g})},n=async({config:a,i18n:{t:b}})=>m({description:`${b("authentication:accountOfCurrentUser")}`,keywords:`${b("authentication:account")}`,serverURL:a.serverURL,title:b("authentication:account"),...a.admin.meta||{}}),o=async a=>{let{config:b,i18n:c}=a,d=c.t("folder:browseByFolder");return m({...b.admin.meta||{},description:"",keywords:"",serverURL:b.serverURL,title:d})};var p=c(46849);let q=async a=>{let{collectionConfig:b,config:c,i18n:d}=a,e="";return b&&(e=(0,p.s)(b.labels.singular,d)),e=`${e?`${e} `:e}${d.t("folder:folders")}`,m({...c.admin.meta||{},description:"",keywords:"",serverURL:c.serverURL,title:e,...b?.admin?.meta||{}})},r=async a=>{let{collectionConfig:b,config:c,i18n:d}=a,e="";return b&&(e=(0,p.s)(b.labels.plural,d)),e=`${e?`${e} `:e}${d.t("general:trash")}`,m({...c.admin.meta||{},description:"",keywords:"",serverURL:c.serverURL,title:e,...b?.admin?.meta||{}})},s=async({config:a,i18n:{t:b}})=>m({description:b("authentication:createFirstUser"),keywords:b("general:create"),serverURL:a.serverURL,title:b("authentication:createFirstUser"),...a.admin.meta||{}}),t=async({config:a,i18n:{t:b}})=>m({serverURL:a.serverURL,title:b("general:dashboard"),...a.admin.meta,openGraph:{title:b("general:dashboard"),...a.admin.meta?.openGraph||{}}}),u=async({collectionConfig:a,config:b,globalConfig:c,i18n:d})=>{let e=a?(0,p.s)(a.labels.singular,d):c?(0,p.s)(c.label,d):"";return Promise.resolve(m({...b.admin.meta||{},description:`API - ${e}`,keywords:"API",serverURL:b.serverURL,title:`API - ${e}`,...a?{...a?.admin.meta||{},...a?.admin?.components?.views?.edit?.api?.meta||{}}:{},...c?{...c?.admin.meta||{},...c?.admin?.components?.views?.edit?.api?.meta||{}}:{}}))},v=async({collectionConfig:a,config:b,globalConfig:c,i18n:d,isEditing:e,isReadOnly:f=!1,view:g="default"})=>{let{t:h}=d,i=a?(0,p.s)(a.labels.singular,d):c?(0,p.s)(c.label,d):"",j=h(f?"general:viewing":e?"general:editing":"general:creating"),k={...b.admin.meta||{},description:`${j} - ${i}`,keywords:`${i}, Payload, CMS`,title:`${j} - ${i}`},l={title:`${h("general:edit")} - ${i}`,...b.admin.meta.openGraph||{},...a?{...a?.admin.meta?.openGraph||{},...a?.admin?.components?.views?.edit?.[g]?.meta?.openGraph||{}}:{},...c?{...c?.admin.meta?.openGraph||{},...c?.admin?.components?.views?.edit?.[g]?.meta?.openGraph||{}}:{}};return m({...k,openGraph:l,...a?{...a?.admin.meta||{},...a?.admin?.components?.views?.edit?.[g]?.meta||{}}:{},...c?{...c?.admin.meta||{},...c?.admin?.components?.views?.edit?.[g]?.meta||{}}:{},serverURL:b.serverURL})},w=async({config:a,i18n:b})=>m({description:b.t("general:pageNotFound"),keywords:`404 ${b.t("general:notFound")}`,serverURL:a.serverURL,title:b.t("general:notFound")});var x=c(93033);let y=async({collectionConfig:a,config:b,globalConfig:c,i18n:d})=>{let{t:e}=d,f={...b.admin.meta||{}},g={},h=g?.createdAt?(0,x.Yq)({date:g.createdAt,i18n:d,pattern:b?.admin?.dateFormat}):"";if(a){let c=a?.admin?.useAsTitle||"id",i=(0,p.s)(a.labels.singular,d),j=g?.[c];f={...b.admin.meta||{},description:e("version:viewingVersion",{documentTitle:j,entityLabel:i}),title:`${e("version:version")}${h?` - ${h}`:""}${j?` - ${j}`:""} - ${i}`,...a?.admin?.meta||{},...a?.admin?.components?.views?.edit?.version?.meta||{}}}if(c){let a=(0,p.s)(c.label,d);f={...b.admin.meta||{},description:e("version:viewingVersionGlobal",{entityLabel:a}),title:`${e("version:version")}${h?` - ${h}`:""}${a}`,...c?.admin?.meta||{},...c?.admin?.components?.views?.edit?.version?.meta||{}}}return m({...f,serverURL:b.serverURL})},z=async({collectionConfig:a,config:b,globalConfig:c,i18n:d})=>{let{t:e}=d,f=a?(0,p.s)(a.labels.singular,d):c?(0,p.s)(c.label,d):"",g={...b.admin.meta||{}},h={};if(a){let c=a?.admin?.useAsTitle||"id",d=h?.[c];g={...b.admin.meta||{},description:e("version:viewingVersions",{documentTitle:h?.[c],entitySlug:a.slug}),title:`${e("version:versions")}${d?` - ${d}`:""} - ${f}`,...a?.admin.meta||{},...a?.admin?.components?.views?.edit?.versions?.meta||{}}}return c&&(g={...b.admin.meta||{},description:e("version:viewingVersionsGlobal",{entitySlug:c.slug}),title:`${e("version:versions")} - ${f}`,...c?.admin.meta||{},...c?.admin?.components?.views?.edit?.versions?.meta||{}}),m({...g,serverURL:b.serverURL})};var A=c(14915);let B=async({collectionConfig:a,config:b,globalConfig:c,params:d})=>{let{segments:e}=d,f=null,[h]=e,i="collections"===h,j="globals"===h,k=j||!!(i&&e?.length>2&&"create"!==e[2]);if(i){if(3===d.segments.length&&(f=v),4===e.length&&"trash"===e[2]&&(f=a=>v({...a,isReadOnly:!0})),4===d.segments.length)switch(d.segments[3]){case"api":f=u;break;case"versions":f=z}if(5===d.segments.length&&"versions"===d.segments[3]&&(f=y),5===e.length&&"trash"===e[2])switch(e[4]){case"api":f=u;break;case"versions":f=z}6===e.length&&"trash"===e[2]&&"versions"===e[4]&&(f=y)}if(j){if(d.segments?.length===2&&(f=v),d.segments?.length===3)switch(d.segments[2]){case"api":f=u;break;case"versions":f=z}d.segments?.length===4&&"versions"===d.segments[2]&&(f=y)}let l=await g({config:b});if("function"==typeof f)return f({collectionConfig:a,config:b,globalConfig:c,i18n:l,isEditing:k});{let{viewKey:d}=(0,A.$)({collectionConfig:a,config:b,docPermissions:{create:!0,delete:!0,fields:!0,read:!0,readVersions:!0,update:!0},globalConfig:c,routeSegments:"string"==typeof e?[e]:e});if(d&&(a?.admin?.components?.views?.edit?.[d]||c?.admin?.components?.views?.edit?.[d]))return v({collectionConfig:a,config:b,globalConfig:c,i18n:l,isEditing:k,view:d})}return w({config:b,i18n:l})},C=async a=>B(a),D=async({config:a,i18n:{t:b}})=>m({description:b("authentication:forgotPassword"),keywords:b("authentication:forgotPassword"),title:b("authentication:forgotPassword"),...a.admin.meta||{},serverURL:a.serverURL}),E=async a=>{let{collectionConfig:b,config:c,i18n:d}=a,e="";return b&&(e=(0,p.s)(b.labels.plural,d)),m({...c.admin.meta||{},description:"",keywords:"",serverURL:c.serverURL,title:e,...b?.admin?.meta||{}})},F=async({config:a,i18n:{t:b}})=>m({description:`${b("authentication:login")}`,keywords:`${b("authentication:login")}`,serverURL:a.serverURL,title:b("authentication:login"),...a.admin.meta||{}}),G=async({config:a,i18n:{t:b}})=>m({description:b("authentication:resetPassword"),keywords:b("authentication:resetPassword"),serverURL:a.serverURL,title:b("authentication:resetPassword"),...a.admin.meta||{}}),H=async({config:a,i18n:{t:b}})=>m({description:b("error:unauthorized"),keywords:b("error:unauthorized"),serverURL:a.serverURL,title:b("error:unauthorized"),...a.admin.meta||{}}),I=async({config:a,i18n:{t:b}})=>m({description:b("authentication:verifyUser"),keywords:b("authentication:verify"),serverURL:a.serverURL,title:b("authentication:verify"),...a.admin.meta||{}}),J=async a=>{let{config:b,viewConfig:c}=a;return c?m({description:"Payload",keywords:"Payload",serverURL:b.serverURL,title:"Payload",...b.admin.meta||{},...c.meta||{},openGraph:{title:"Payload",...b.admin.meta?.openGraph||{},...c.meta?.openGraph||{}}}):null};var K=c(54820);let L={"create-first-user":s,folders:o,forgot:D,login:F,logout:H,"logout-inactivity":H,unauthorized:H},M=async({config:a,params:b})=>{let c,d=await a,e=await b,f=d.collections.reduce((a,{slug:b,folders:c})=>c?[...a,b]:a,[]),h=Array.isArray(e.segments)?e.segments:[],i=`/${h.join("/")}`,[j,k,l]=h,m="globals"===j,p="collections"===j,s=await g({config:d}),u=p&&h.length>1&&d?.collections?.find(a=>a.slug===k),v=m&&h.length>1&&d?.globals?.find(a=>a.slug===k);switch(h.length){case 0:c=await t({config:d,i18n:s});break;case 1:f.length&&`/${j}`===d.admin.routes.browseByFolder?c=await L.folders({config:d,i18n:s}):"account"===j?c=await n({config:d,i18n:s}):L[j]&&(c=await L[j]({config:d,i18n:s}));break;case 2:`/${j}`===d.admin.routes.reset?c=await G({config:d,i18n:s}):f.length&&`/${j}`===d.admin.routes.browseByFolder?c=await o({config:d,i18n:s}):p?c=await E({collectionConfig:u,config:d,i18n:s}):m&&(c=await C({config:d,globalConfig:v,i18n:s,params:e}));break;default:"verify"===k?c=await I({config:d,i18n:s}):p?"trash"===l&&3===h.length&&u?c=await r({collectionConfig:u,config:d,i18n:s,params:e}):d.folders&&l===d.folders.slug?f.includes(u.slug)&&(c=await q({collectionConfig:u,config:d,i18n:s,params:e})):c=await C({collectionConfig:u,config:d,i18n:s,params:e}):m&&(c=await C({config:d,globalConfig:v,i18n:s,params:e}))}if(!c){let{viewConfig:a,viewKey:b}=(0,K.f)({config:d,currentRoute:i});c=b?await J({config:d,i18n:s,viewConfig:a}):await w({config:d,i18n:s})}return c}},16141:a=>{"use strict";a.exports=require("node:zlib")},16698:a=>{"use strict";a.exports=require("node:async_hooks")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:a=>{"use strict";a.exports=require("process")},21820:a=>{"use strict";a.exports=require("os")},22939:(a,b,c)=>{"use strict";c.d(b,{LoginForm:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call LoginForm() from the server but LoginForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\node_modules\\@payloadcms\\next\\dist\\views\\Login\\LoginForm\\index.js","LoginForm")},24295:(a,b,c)=>{"use strict";c.d(b,{ResetPreferences:()=>k});var d=c(83951),e=c(60687),f=c(54845),g=c(85957),h=c(71955),i=c(43210);let j="confirm-reset-modal",k=a=>{let b,c,k,l=(0,d.c)(9),{apiRoute:m,user:n}=a,{openModal:o}=(0,f.useModal)(),{t:p}=(0,g.d)();l[0]!==m||l[1]!==n?(b=async()=>{if(!n)return;let a=h.A({depth:0,where:{user:{id:{equals:n.id}}}},{addQueryPrefix:!0});try{let b=await fetch(`${m}/payload-preferences${a}`,{credentials:"include",headers:{"Content-Type":"application/json"},method:"DELETE"}),c=(await b.json()).message;b.ok?f.toast.success(c):f.toast.error(c)}catch(a){}},l[0]=m,l[1]=n,l[2]=b):b=l[2];let q=b;return l[3]!==o?(c=()=>o(j),l[3]=o,l[4]=c):c=l[4],l[5]!==q||l[6]!==p||l[7]!==c?(k=(0,e.jsxs)(i.Fragment,{children:[(0,e.jsx)("div",{children:(0,e.jsx)(f.Button,{buttonStyle:"secondary",onClick:c,children:p("general:resetPreferences")})}),(0,e.jsx)(f.ConfirmationModal,{body:p("general:resetPreferencesDescription"),confirmingLabel:p("general:resettingPreferences"),heading:p("general:resetPreferences"),modalSlug:j,onConfirm:q})]}),l[5]=q,l[6]=p,l[7]=c,l[8]=k):k=l[8],k}},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},27910:a=>{"use strict";a.exports=require("stream")},28114:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{default:()=>k,generateMetadata:()=>j});var e=c(81329),f=c(16074),g=c(97653),h=c(63068),i=a([e]);e=(i.then?(await i)():i)[0];let j=({params:a,searchParams:b})=>(0,f._)({config:e.A,params:a,searchParams:b}),k=({params:a,searchParams:b})=>(0,g.jT)({config:e.A,params:a,searchParams:b,importMap:h.m});d()}catch(a){d(a)}})},28354:a=>{"use strict";a.exports=require("util")},28855:a=>{"use strict";a.exports=import("@libsql/client")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29799:(a,b,c)=>{"use strict";c.d(b,{LogoutClient:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call LogoutClient() from the server but LogoutClient is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\node_modules\\@payloadcms\\next\\dist\\views\\Logout\\LogoutClient.js","LogoutClient")},31672:(a,b,c)=>{Promise.resolve().then(c.bind(c,30743)),Promise.resolve().then(c.bind(c,5081)),Promise.resolve().then(c.bind(c,88062)),Promise.resolve().then(c.bind(c,93725)),Promise.resolve().then(c.bind(c,23943)),Promise.resolve().then(c.bind(c,59627)),Promise.resolve().then(c.bind(c,84295)),Promise.resolve().then(c.bind(c,29910)),Promise.resolve().then(c.bind(c,90356)),Promise.resolve().then(c.bind(c,2989)),Promise.resolve().then(c.bind(c,60260)),Promise.resolve().then(c.bind(c,52763)),Promise.resolve().then(c.bind(c,5261)),Promise.resolve().then(c.bind(c,60462)),Promise.resolve().then(c.bind(c,93642)),Promise.resolve().then(c.bind(c,57881)),Promise.resolve().then(c.bind(c,22939)),Promise.resolve().then(c.bind(c,29799)),Promise.resolve().then(c.bind(c,95384)),Promise.resolve().then(c.bind(c,37991)),Promise.resolve().then(c.bind(c,11906)),Promise.resolve().then(c.bind(c,8048)),Promise.resolve().then(c.bind(c,3795)),Promise.resolve().then(c.bind(c,28273)),Promise.resolve().then(c.bind(c,76820)),Promise.resolve().then(c.bind(c,75699)),Promise.resolve().then(c.bind(c,28867)),Promise.resolve().then(c.bind(c,35757)),Promise.resolve().then(c.bind(c,99349)),Promise.resolve().then(c.bind(c,22798)),Promise.resolve().then(c.bind(c,52231)),Promise.resolve().then(c.bind(c,83929)),Promise.resolve().then(c.bind(c,44473)),Promise.resolve().then(c.bind(c,91460)),Promise.resolve().then(c.bind(c,88403)),Promise.resolve().then(c.bind(c,1799)),Promise.resolve().then(c.bind(c,87614)),Promise.resolve().then(c.bind(c,99203)),Promise.resolve().then(c.bind(c,12563)),Promise.resolve().then(c.bind(c,2127)),Promise.resolve().then(c.bind(c,73768)),Promise.resolve().then(c.bind(c,96394)),Promise.resolve().then(c.bind(c,96709)),Promise.resolve().then(c.bind(c,99313)),Promise.resolve().then(c.bind(c,31171))},32467:a=>{"use strict";a.exports=require("node:http2")},33873:a=>{"use strict";a.exports=require("path")},34589:a=>{"use strict";a.exports=require("node:assert")},34631:a=>{"use strict";a.exports=require("tls")},37067:a=>{"use strict";a.exports=require("node:http")},37366:a=>{"use strict";a.exports=require("dns")},37540:a=>{"use strict";a.exports=require("node:console")},37830:a=>{"use strict";a.exports=require("node:stream/web")},37991:(a,b,c)=>{"use strict";c.d(b,{ResetPasswordForm:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call ResetPasswordForm() from the server but ResetPasswordForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\node_modules\\@payloadcms\\next\\dist\\views\\ResetPassword\\ResetPasswordForm\\index.js","ResetPasswordForm")},40119:(a,b,c)=>{"use strict";c.d(b,{S:()=>an});var d=c(37413),e=c(36658),f=c(91423),g=c(39916),h=c(26685),i=c(539),j=c(61120),k=c(62467);let l="template-minimal",m=a=>{let{children:b,className:c,style:e={},width:f="normal"}=a,g=[c,l,`${l}--width-${f}`].filter(Boolean).join(" ");return(0,d.jsx)("section",{className:g,style:e,children:(0,d.jsx)("div",{className:`${l}__wrap`,children:b})})};var n=c(51368),o=c(54820),p=c(99313),q=c(38939),r=c(65486),s=c(12487),t=c(57486),u=c(20964),v=c(3985),w=c(6254),x=c(93642),y=c(90356),z=c(2989),A=c(52763),B=c(60260);let C="payload-settings",D=a=>{let{className:b,i18n:c,languageOptions:e,payload:f,theme:g,user:h}=a,i=f.config.routes.api;return(0,d.jsxs)("div",{className:[C,b].filter(Boolean).join(" "),children:[(0,d.jsx)("h3",{children:c.t("general:payloadSettings")}),(0,d.jsxs)("div",{className:`${C}__language`,children:[(0,d.jsx)(p.FieldLabel,{htmlFor:"language-select",label:c.t("general:language")}),(0,d.jsx)(B.LanguageSelector,{languageOptions:e})]}),"all"===g&&(0,d.jsx)(A.ToggleTheme,{}),(0,d.jsx)(z.ResetPreferences,{apiRoute:i,user:h})]})};async function E({initPageResult:a,params:b,searchParams:c}){let{languageOptions:f,locale:h,permissions:i,req:j,req:{i18n:k,payload:l,payload:{config:m},user:n}}=a,{admin:{theme:o,user:z},routes:{api:A},serverURL:B}=m,C=l?.collections?.[z]?.config;if(C&&n?.id){let g=await (0,t.U)({id:n.id,collectionSlug:C.slug,locale:h,payload:l,req:j,user:n});if(!g)throw Error("not-found");let E=await (0,s.B)({id:n.id,collectionSlug:C.slug,payload:l,user:n}),{docPermissions:F,hasPublishPermission:G,hasSavePermission:H}=await (0,u.q)({id:n.id,collectionConfig:C,data:g,req:j}),{state:I}=await (0,q.s)({id:n.id,collectionSlug:C.slug,data:g,docPermissions:F,docPreferences:E,locale:h?.code,operation:"update",renderAllFields:!0,req:j,schemaPath:C.slug,skipValidation:!0}),{currentEditor:J,isLocked:K,lastUpdateTime:L}=await (0,v.p)({id:n.id,collectionConfig:C,isEditing:!0,req:j}),{hasPublishedDoc:M,mostRecentVersionIsAutosaved:N,unpublishedVersionCount:O,versionCount:P}=await (0,w.c)({id:n.id,collectionConfig:C,doc:g,docPermissions:F,locale:h?.code,payload:l,user:n});return(0,d.jsx)(p.DocumentInfoProvider,{AfterFields:(0,d.jsx)(D,{i18n:k,languageOptions:f,payload:l,theme:o,user:n}),apiURL:`${B}${A}/${z}${n?.id?`/${n.id}`:""}`,collectionSlug:z,currentEditor:J,docPermissions:F,hasPublishedDoc:M,hasPublishPermission:G,hasSavePermission:H,id:n?.id,initialData:g,initialState:I,isEditing:!0,isLocked:K,lastUpdateTime:L,mostRecentVersionIsAutosaved:N,unpublishedVersionCount:O,versionCount:P,children:(0,d.jsxs)(p.EditDepthProvider,{children:[(0,d.jsx)(r.j,{collectionConfig:C,hideTabs:!0,i18n:k,payload:l,permissions:i}),(0,d.jsx)(p.HydrateAuthProvider,{permissions:i}),(0,e.f)({Component:m.admin?.components?.views?.account?.Component,Fallback:x.EditView,importMap:l.importMap,serverProps:{doc:g,hasPublishedDoc:M,i18n:k,initPageResult:a,locale:h,params:b,payload:l,permissions:i,routeSegments:[],searchParams:c,user:n}}),(0,d.jsx)(y.AccountClient,{})]})})}return(0,g.notFound)()}var F=c(91197),G=c(21008);let H=async a=>{let{browseByFolderSlugs:b=[],disableBulkDelete:c,disableBulkEdit:f,enableRowSelections:h,folderID:j,initPageResult:k,isInDrawer:l,params:m,query:n,searchParams:o}=a,{locale:q,permissions:r,req:{i18n:s,payload:t,payload:{config:u},query:v,user:w},visibleEntities:x}=k;if(!1===u.folders||!1===u.folders.browseByFolder)throw Error("not-found");let y=u.folders.slug,z=b.filter(a=>r?.collections?.[a]?.read&&x.collections.includes(a)),A=n||(v?{...v,relationTo:"string"==typeof v?.relationTo?JSON.parse(v.relationTo):void 0}:{}),B=[];B=j&&Array.isArray(A?.relationTo)?A.relationTo.filter(a=>z.includes(a)||a===y):j?[...z,y]:[y];let{routes:{admin:C}}=u,D=await (0,F.y)({key:"browse-by-folder",req:k.req,value:{sort:A?.sort}}),E=D?.sort||"name",H=D?.viewPreference||"grid",{breadcrumbs:I,documents:J,folderAssignedCollections:K,FolderResultsComponent:L,subfolders:M}=await (0,G.x)({browseByFolder:!0,collectionsToDisplay:B,displayAs:H,folderAssignedCollections:B.filter(a=>a!==y)||[],folderID:j,req:k.req,sort:E}),N=I[I.length-1]?.id;!l&&(N&&j&&j!==N||j&&!N)&&(0,g.redirect)((0,i.Q)({adminRoute:C,path:u.admin.routes.browseByFolder,serverURL:u.serverURL}));let O=j&&Array.isArray(K)&&K.length?z.filter(a=>K.includes(a)):z,P=B.filter(a=>a===y?r?.collections?.[y]?.read:!K||K.includes(a)),Q=(N?[y,...O]:[y]).filter(a=>a===y?r?.collections?.[y]?.create:r?.collections?.[a]?.create&&x.collections.includes(a));return{View:(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(p.HydrateAuthProvider,{permissions:r}),(0,e.f)({clientProps:{activeCollectionFolderSlugs:P,allCollectionFolderSlugs:O,allowCreateCollectionSlugs:Q,baseFolderPath:"/browse-by-folder",breadcrumbs:I,disableBulkDelete:c,disableBulkEdit:f,documents:J,enableRowSelections:h,folderAssignedCollections:K,folderFieldName:u.folders.fieldName,folderID:N||null,FolderResultsComponent:L,sort:E,subfolders:M,viewPreference:H},Fallback:p.DefaultBrowseByFolderView,importMap:t.importMap,serverProps:{documents:J,i18n:s,locale:q,params:m,payload:t,permissions:r,searchParams:o,subfolders:M,user:w}})]})}},I=async a=>{try{let{View:b}=await H(a);return b}catch(a){if(a?.message==="NEXT_REDIRECT")throw a;"not-found"===a.message?(0,g.notFound)():console.error(a)}},J=async a=>{let{disableBulkDelete:b,disableBulkEdit:c,enableRowSelections:f,folderID:h,initPageResult:j,isInDrawer:k,overrideEntityVisibility:l,params:m,query:n,searchParams:o}=a,{collectionConfig:q,collectionConfig:{slug:r},locale:s,permissions:t,req:{i18n:u,payload:v,payload:{config:w},query:x,user:y},visibleEntities:z}=j;if(!w.folders||!t?.collections?.[r]?.read||!t?.collections?.[w.folders.slug].read)throw Error("not-found");if(q){if(!z.collections.includes(r)&&!l||!w.folders)throw Error("not-found");let a=n||x,A=await (0,F.y)({key:`${r}-collection-folder`,req:j.req,value:{sort:a?.sort}}),B=A?.sort||"name",C=A?.viewPreference||"grid",{routes:{admin:D}}=w,{breadcrumbs:E,documents:H,folderAssignedCollections:I,FolderResultsComponent:J,subfolders:K}=await (0,G.x)({browseByFolder:!1,collectionsToDisplay:[w.folders.slug,r],displayAs:C,folderAssignedCollections:[r],folderID:h,req:j.req,sort:B}),L=E[E.length-1]?.id;!k&&(L&&h&&h!==L||h&&!L)&&(0,g.redirect)((0,i.Q)({adminRoute:D,path:`/collections/${r}/${w.folders.slug}`,serverURL:w.serverURL}));let M=a?.search;return{View:(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(p.HydrateAuthProvider,{permissions:t}),(0,e.f)({clientProps:{allCollectionFolderSlugs:[w.folders.slug,r],allowCreateCollectionSlugs:[t?.collections?.[w.folders.slug]?.create?w.folders.slug:null,t?.collections?.[r]?.create?r:null].filter(Boolean),baseFolderPath:`/collections/${r}/${w.folders.slug}`,breadcrumbs:E,collectionSlug:r,disableBulkDelete:b,disableBulkEdit:c,documents:H,enableRowSelections:f,folderAssignedCollections:I,folderFieldName:w.folders.fieldName,folderID:L||null,FolderResultsComponent:J,search:M,sort:B,subfolders:K,viewPreference:C},Fallback:p.DefaultCollectionFolderView,importMap:v.importMap,serverProps:{collectionConfig:q,documents:H,i18n:u,locale:s,params:m,payload:v,permissions:t,searchParams:o,subfolders:K,user:y}})]})}}throw Error("not-found")},K=async a=>{try{let{View:b}=await J(a);return b}catch(a){if(a?.message==="NEXT_REDIRECT")throw a;"not-found"===a.message?(0,g.notFound)():console.error(a)}};var L=c(62902);let M=async a=>{try{let{List:b}=await (0,L.c)({...a,enableRowSelections:!0,query:{...a.query||{},trash:!0},viewType:"trash"});return b}catch(a){"not-found"===a.message&&(0,g.notFound)(),console.error(a)}};var N=c(60462);async function O({initPageResult:a}){let{locale:b,req:c,req:{payload:{collections:e,config:{admin:{user:f}}}}}=a,g=e?.[f]?.config,{auth:h}=g,i=h.loginWithUsername,j=await (0,t.U)({collectionSlug:g.slug,locale:b,payload:c.payload,req:c,user:c.user}),k=await (0,s.B)({collectionSlug:g.slug,payload:c.payload,user:c.user}),{docPermissions:l}=await (0,u.q)({collectionConfig:g,data:j,req:c}),{state:m}=await (0,q.s)({collectionSlug:g.slug,data:j,docPermissions:l,docPreferences:k,locale:b?.code,operation:"create",renderAllFields:!0,req:c,schemaPath:g.slug,skipValidation:!0});return(0,d.jsxs)("div",{className:"create-first-user",children:[(0,d.jsx)("h1",{children:c.t("general:welcome")}),(0,d.jsx)("p",{children:c.t("authentication:beginCreateFirstUser")}),(0,d.jsx)(N.CreateFirstUserClient,{docPermissions:l,docPreferences:k,initialState:m,loginWithUsername:i,userSlug:f})]})}var P=c(93033),Q=c(46849);let R="dashboard";function S(a){let{globalData:b,i18n:c,i18n:{t:f},locale:g,navGroups:h,params:k,payload:{config:{admin:{components:{afterDashboard:l,beforeDashboard:m}},routes:{admin:n}}},payload:o,permissions:q,searchParams:r,user:s}=a;return(0,d.jsx)("div",{className:R,children:(0,d.jsxs)(p.Gutter,{className:`${R}__wrap`,children:[m&&(0,e.f)({Component:m,importMap:o.importMap,serverProps:{i18n:c,locale:g,params:k,payload:o,permissions:q,searchParams:r,user:s}}),(0,d.jsx)(j.Fragment,{children:h&&h?.length!==0?h.map(({entities:a,label:e},g)=>(0,d.jsxs)("div",{className:`${R}__group`,children:[(0,d.jsx)("h2",{className:`${R}__label`,children:e}),(0,d.jsx)("ul",{className:`${R}__card-list`,children:a.map(({slug:a,type:e,label:g},h)=>{let j,k,l,m,o=null,r=null;if(e===P.ck.collection&&(j=f("general:showAllLabel",{label:(0,Q.s)(g,c)}),l=(0,i.Q)({adminRoute:n,path:`/collections/${a}`}),k=(0,i.Q)({adminRoute:n,path:`/collections/${a}/create`}),m=q?.collections?.[a]?.create),e===P.ck.global){(0,Q.s)(g,c),j=f("general:editLabel",{label:(0,Q.s)(g,c)}),l=(0,i.Q)({adminRoute:n,path:`/globals/${a}`});let d=b.find(b=>b.slug===a);if(d){o=d.data._isLocked,r=d.data._userEditing;let a=d?.lockDuration,b=new Date(d.data?._lastEditedAt).getTime();new Date().getTime()>b+1e3*a&&(o=!1,r=null)}}return(0,d.jsx)("li",{children:(0,d.jsx)(p.Card,{actions:o&&s?.id!==r?.id?(0,d.jsx)(p.Locked,{className:`${R}__locked`,user:r}):m&&e===P.ck.collection?(0,d.jsx)(p.Button,{"aria-label":f("general:createNewLabel",{label:g}),buttonStyle:"icon-label",el:"link",icon:"plus",iconStyle:"with-border",round:!0,to:k}):void 0,buttonAriaLabel:j,href:l,id:`card-${a}`,title:(0,Q.s)(g,c),titleAs:"h3"})},h)})})]},g)):(0,d.jsx)("p",{children:"no nav groups...."})}),l&&(0,e.f)({Component:l,importMap:o.importMap,serverProps:{i18n:c,locale:g,params:k,payload:o,permissions:q,searchParams:r,user:s}})]})})}async function T({initPageResult:a,params:b,searchParams:c}){let{locale:f,permissions:g,req:{i18n:h,payload:{config:i},payload:k,user:l},req:m,visibleEntities:n}=a,o=i.collections.filter(a=>g?.collections?.[a.slug]?.read&&n.collections.includes(a.slug)),q=i.globals.filter(a=>g?.globals?.[a.slug]?.read&&n.globals.includes(a.slug)),r=[];if(i.globals.length>0){let a=await k.find({collection:"payload-locked-documents",depth:1,overrideAccess:!1,pagination:!1,req:m,where:{globalSlug:{exists:!0}}});r=i.globals.map(b=>{let c="object"==typeof b.lockDocuments?b.lockDocuments.duration:300,d=a.docs.find(a=>a.globalSlug===b.slug);return{slug:b.slug,data:{_isLocked:!!d,_lastEditedAt:d?.updatedAt??null,_userEditing:d?.user?.value??null},lockDuration:c}})}let s=(0,P.d5)([...o.map(a=>({type:P.ck.collection,entity:a}))??[],...q.map(a=>({type:P.ck.global,entity:a}))??[]],g,h);return(0,d.jsxs)(j.Fragment,{children:[(0,d.jsx)(p.HydrateAuthProvider,{permissions:g}),(0,d.jsx)(p.SetStepNav,{nav:[]}),(0,e.f)({clientProps:{locale:f},Component:i.admin?.components?.views?.dashboard?.Component,Fallback:S,importMap:k.importMap,serverProps:{globalData:r,i18n:h,locale:f,navGroups:s,params:b,payload:k,permissions:g,searchParams:c,user:l,visibleEntities:n}})]})}var U=c(98931),V=c(32470),W=c(57881);let X=a=>{let{i18n:b,locale:c,params:d,payload:f,permissions:g,searchParams:h,user:i}=a,{admin:{components:{graphics:{Logo:j}={Logo:void 0}}={}}={}}=f.config;return(0,e.f)({Component:j,Fallback:P.YY,importMap:f.importMap,serverProps:{i18n:b,locale:c,params:d,payload:f,permissions:g,searchParams:h,user:i}})};var Y=c(22939);let Z="login";var $=c(29799);let _=({inactivity:a,initPageResult:b,searchParams:c})=>{let{req:{payload:{config:{routes:{admin:e}}}}}=b;return(0,d.jsx)("div",{className:"logout",children:(0,d.jsx)($.LogoutClient,{adminRoute:e,inactivity:a,redirect:c.redirect})})};var aa=c(37991);let ab="reset-password";function ac({initPageResult:a,params:b}){let{req:c}=a,{segments:[e,f]}=b,{i18n:g,payload:{config:h},user:j}=c,{admin:{routes:{account:k,login:l}},routes:{admin:m}}=h;return j?(0,d.jsxs)("div",{className:`${ab}__wrap`,children:[(0,d.jsx)(V.M,{description:(0,d.jsx)(P.wD,{elements:{0:({children:a})=>(0,d.jsx)(p.Link,{href:(0,i.Q)({adminRoute:m,path:k}),prefetch:!1,children:a})},i18nKey:"authentication:loggedInChangePassword",t:g.t}),heading:g.t("authentication:alreadyLoggedIn")}),(0,d.jsx)(p.Button,{buttonStyle:"secondary",el:"link",size:"large",to:m,children:g.t("general:backToDashboard")})]}):(0,d.jsxs)("div",{className:`${ab}__wrap`,children:[(0,d.jsx)(V.M,{heading:g.t("authentication:resetPassword")}),(0,d.jsx)(aa.ResetPasswordForm,{token:f}),(0,d.jsx)(p.Link,{href:(0,i.Q)({adminRoute:m,path:l}),prefetch:!1,children:g.t("authentication:backToLogin")})]})}var ad=c(71388),ae=c(11906);let af="verify";async function ag({initPageResult:a,params:b,searchParams:c}){let e,[f,g,h]=b.segments,{locale:k,permissions:l,req:m}=a,{i18n:n,payload:{config:o},payload:p,user:q}=m,{routes:{admin:r}}=o,s=!1;try{await m.payload.verifyEmail({collection:f,token:h}),s=!0,e=m.t("authentication:emailVerified")}catch(a){e=m.t("authentication:unableToVerify")}return s?(0,d.jsx)(ae.ToastAndRedirect,{message:m.t("authentication:emailVerified"),redirectTo:(0,i.Q)({adminRoute:r,path:"/login"})}):(0,d.jsxs)(j.Fragment,{children:[(0,d.jsx)("div",{className:`${af}__brand`,children:(0,d.jsx)(X,{i18n:n,locale:k,params:b,payload:p,permissions:l,searchParams:c,user:q})}),(0,d.jsx)("h2",{children:e})]})}function ah({editConfig:a,viewKey:b}){if(a&&b in a&&"actions"in a[b])return a[b].actions}function ai({collectionOrGlobal:a,serverProps:b,viewKeyArg:c}){if(a?.admin?.components?.views?.edit){let d=c||"default";"root"in a.admin.components.views.edit&&(d="root");let e=ah({editConfig:a.admin?.components?.views?.edit,viewKey:d});e&&(b.viewActions=b.viewActions.concat(e))}}function aj(a){let[b,c]=a;if(c){if("versions"===b)return{documentSubViewType:"version",viewType:"version"}}else if("versions"===b)return{documentSubViewType:"versions",viewType:"document"};else if("api"===b)return{documentSubViewType:"api",viewType:"document"};return{documentSubViewType:"default",viewType:"document"}}var ak=c(55107);let al={account:"account",folders:"folders",forgot:"forgot-password",login:Z,reset:ab,verify:af},am={account:E,browseByFolder:I,createFirstUser:O,forgot:function({initPageResult:a}){let{req:{i18n:b,payload:{config:c},user:e}}=a,{admin:{routes:{account:f,login:g}},routes:{admin:h}}=c;return e?(0,d.jsxs)(j.Fragment,{children:[(0,d.jsx)(V.M,{description:(0,d.jsx)(P.wD,{elements:{0:({children:a})=>(0,d.jsx)(p.Link,{href:(0,i.Q)({adminRoute:h,path:f}),prefetch:!1,children:a})},i18nKey:"authentication:loggedInChangePassword",t:b.t}),heading:b.t("authentication:alreadyLoggedIn")}),(0,d.jsx)(p.Button,{buttonStyle:"secondary",el:"link",size:"large",to:h,children:b.t("general:backToDashboard")})]}):(0,d.jsxs)(j.Fragment,{children:[(0,d.jsx)(W.ForgotPasswordForm,{}),(0,d.jsx)(p.Link,{href:(0,i.Q)({adminRoute:h,path:g}),prefetch:!1,children:b.t("authentication:backToLogin")})]})},inactivity:function(a){return(0,d.jsx)(_,{inactivity:!0,...a})},login:function({initPageResult:a,params:b,searchParams:c}){let{locale:f,permissions:h,req:i}=a,{i18n:k,payload:{config:l},payload:m,user:n}=i,{admin:{components:{afterLogin:o,beforeLogin:p}={},user:q},routes:{admin:r}}=l,s=(({allowAbsoluteUrls:a=!1,fallbackTo:b="/",redirectTo:c})=>{let d;if("string"!=typeof c)return b;try{d=decodeURIComponent(c.trim())}catch{return b}let e=d.startsWith("/")&&!d.startsWith("//")&&!d.startsWith("/%2F")&&!d.startsWith("/\\/")&&!d.startsWith("/\\\\")&&!d.startsWith("/\\")&&!d.toLowerCase().startsWith("/javascript:")&&!d.toLowerCase().startsWith("/http"),f=a&&/^https?:\/\/\S+$/i.test(d);return e||f?d:b})({fallbackTo:r,redirectTo:c.redirect});n&&(0,g.redirect)(s);let t=m?.collections?.[q]?.config,u="object"==typeof l.admin?.autoLogin&&l.admin?.autoLogin.prefillOnly,v=u&&"object"==typeof l.admin?.autoLogin?l.admin?.autoLogin.username:void 0,w=u&&"object"==typeof l.admin?.autoLogin?l.admin?.autoLogin.email:void 0,x=u&&"object"==typeof l.admin?.autoLogin?l.admin?.autoLogin.password:void 0;return(0,d.jsxs)(j.Fragment,{children:[(0,d.jsx)("div",{className:`${Z}__brand`,children:(0,d.jsx)(X,{i18n:k,locale:f,params:b,payload:m,permissions:h,searchParams:c,user:n})}),(0,e.f)({Component:p,importMap:m.importMap,serverProps:{i18n:k,locale:f,params:b,payload:m,permissions:h,searchParams:c,user:n}}),!t?.auth?.disableLocalStrategy&&(0,d.jsx)(Y.LoginForm,{prefillEmail:w,prefillPassword:x,prefillUsername:v,searchParams:c}),(0,e.f)({Component:o,importMap:m.importMap,serverProps:{i18n:k,locale:f,params:b,payload:m,permissions:h,searchParams:c,user:n}})]})},logout:_,unauthorized:ad.K},an=async({config:a,importMap:b,params:c,searchParams:l})=>{let p=await a,{admin:{routes:{createFirstUser:q},user:r},routes:{admin:s}}=p,t=await c,u=(0,i.Q)({adminRoute:s,path:Array.isArray(t.segments)?`/${t.segments.join("/")}`:null}),v=Array.isArray(t.segments)?t.segments:[],w=await l;if(1===v.length&&"collections"===v[0]){let{viewKey:a}=(0,o.f)({config:p,currentRoute:"/collections"});a||(0,g.redirect)(s)}if(1===v.length&&"globals"===v[0]){let{viewKey:a}=(0,o.f)({config:p,currentRoute:"/globals"});a||(0,g.redirect)(s)}let{browseByFolderSlugs:x,DefaultView:y,documentSubViewType:z,folderID:A,initPageOptions:B,serverProps:C,templateClassName:D,templateType:E,viewType:F}=(({adminRoute:a,config:b,currentRoute:c,importMap:d,searchParams:e,segments:f})=>{let g,h,j,k,l,m,n,p=null,q={config:b,importMap:d,route:c,routeParams:{},searchParams:e},[r,s,t,u,v,w]=f,x="globals"===r,y="collections"===r,z=b.folders&&b.folders.browseByFolder,A=z&&b.collections.reduce((a,{slug:b,folders:c})=>c&&c.browseByFolder?[...a,b]:a,[])||[],B={viewActions:b?.admin?.components?.actions||[]};switch(y&&(B.collectionConfig=g=b.collections.find(({slug:a})=>a===s)),x&&(B.globalConfig=h=b.globals.find(({slug:a})=>a===s)),f.length){case 0:c===a&&(p={Component:T},j="dashboard",k="default",m="dashboard");break;case 1:{let d;if(b.admin.routes){let e=Object.entries(b.admin.routes).find(([,b])=>(0,ak.W)({currentRoute:c,exact:!0,path:(0,i.Q)({adminRoute:a,path:b})}));e&&(d=e[0])}am[d]&&(p={Component:am[d]},j=al[d],k="minimal","account"===d&&(k="default",m="account"),z&&"browseByFolder"===d&&(k="default",m="folders"));break}case 2:`/${r}`===b.admin.routes.reset?(p={Component:ac},j=al[s],k="minimal",m="reset"):z&&`/${r}`===b.admin.routes.browseByFolder?(q.routeParams.folderID=n,p={Component:am.browseByFolder},j=al.folders,k="default",m="folders",n=s):y&&g?(q.routeParams.collection=g.slug,p={Component:L.u},j=`${s}-list`,k="default",m="list",B.viewActions=B.viewActions.concat(g.admin.components?.views?.list?.actions)):x&&h&&(q.routeParams.global=h.slug,p={Component:U.yo},j="global-edit",k="default",m="document",B.viewActions=B.viewActions.concat(ah({editConfig:h.admin?.components?.views?.edit,viewKey:"default"})));break;default:if("verify"===s)q.routeParams.collection=r,p={Component:ag},j="verify",k="minimal",m="verify";else if(y&&g)if(q.routeParams.collection=g.slug,"trash"===t&&"string"==typeof u){q.routeParams.id=u,q.routeParams.versionID=w,p={Component:U.yo},j="collection-default-edit",k="default";let a=aj([v,w]);m=a.viewType,ai({collectionOrGlobal:g,serverProps:B,viewKeyArg:l=a.documentSubViewType})}else if("trash"===t)p={Component:M},j=`${s}-trash`,k="default",m="trash",B.viewActions=B.viewActions.concat(g.admin.components?.views?.list?.actions??[]);else if(b.folders&&t===b.folders.slug&&g.folders)q.routeParams.folderCollection=t,q.routeParams.folderID=u,p={Component:K},j="collection-folders",k="default",m="collection-folders",n=u;else{q.routeParams.id=t,q.routeParams.versionID=v,p={Component:U.yo},j="collection-default-edit",k="default";let a=aj([u,v]);m=a.viewType,ai({collectionOrGlobal:g,serverProps:B,viewKeyArg:l=a.documentSubViewType})}else if(x&&h){q.routeParams.global=h.slug,q.routeParams.versionID=u,p={Component:U.yo},j="global-edit",k="default";let a=aj([t,u]);m=a.viewType,ai({collectionOrGlobal:h,serverProps:B,viewKeyArg:l=a.documentSubViewType})}}return p||(p=(0,o.f)({config:b,currentRoute:c})?.view),B.viewActions.reverse(),{browseByFolderSlugs:A,DefaultView:p,documentSubViewType:l,folderID:n,initPageOptions:q,serverProps:B,templateClassName:j,templateType:k,viewType:m}})({adminRoute:s,config:p,currentRoute:u,importMap:b,searchParams:w,segments:v}),G=await (0,n.o)(B),H=G.req.user||await G?.req.payload.db.findOne({collection:r,req:G?.req})?.then(a=>!!a);if(y?.Component||y?.payloadComponent||(G?.req?.user&&(0,g.notFound)(),H&&(0,g.redirect)(s)),"string"==typeof G?.redirectTo&&(0,g.redirect)(G.redirectTo),G){let a=(0,i.Q)({adminRoute:s,path:q}),b=p.collections.find(({slug:a})=>a===r),c=b?.auth?.disableLocalStrategy;c&&u===a&&(0,g.redirect)(s),H||u===a||c||(0,g.redirect)(a),H&&u===a&&(0,g.redirect)(s)}y?.Component||y?.payloadComponent||H||(0,g.redirect)(s);let I=(0,f.i)({config:p,i18n:G?.req.i18n,importMap:b}),J=G?.req.payload,N=J.config.folders?(0,h.M)({id:A,collectionSlug:J.config.folders.slug,payload:J}):void 0,O=(0,e.f)({clientProps:{browseByFolderSlugs:x,clientConfig:I,documentSubViewType:z,viewType:F},Component:y.payloadComponent,Fallback:y.Component,importMap:b,serverProps:{...C,clientConfig:I,docID:G?.docID,folderID:N,i18n:G?.req.i18n,importMap:b,initPageResult:G,params:t,payload:G?.req.payload,searchParams:w}});return(0,d.jsxs)(j.Fragment,{children:[!E&&(0,d.jsx)(j.Fragment,{children:O}),"minimal"===E&&(0,d.jsx)(m,{className:D,children:O}),"default"===E&&(0,d.jsx)(k.g,{collectionSlug:G?.collectionConfig?.slug,docID:G?.docID,documentSubViewType:z,globalSlug:G?.globalConfig?.slug,i18n:G?.req.i18n,locale:G?.locale,params:t,payload:G?.req.payload,permissions:G?.permissions,req:G?.req,searchParams:w,user:G?.req.user,viewActions:C.viewActions,viewType:F,visibleEntities:{collections:G?.visibleEntities?.collections,globals:G?.visibleEntities?.globals},children:O})]})}},40610:a=>{"use strict";a.exports=require("node:dns")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},41692:a=>{"use strict";a.exports=require("node:tls")},41792:a=>{"use strict";a.exports=require("node:querystring")},43591:(a,b,c)=>{"use strict";c.d(b,{ForgotPasswordForm:()=>l});var d=c(83951),e=c(60687),f=c(54836),g=c(85957),h=c(54845),i=c(28749),j=c(43210);function k({description:a,heading:b}){return b?(0,e.jsxs)("div",{className:"form-header",children:[(0,e.jsx)("h1",{children:b}),!!a&&(0,e.jsx)("p",{children:a})]}):null}let l=()=>{let a,b,c,l,m=(0,d.c)(17),{config:n,getEntityConfig:o}=(0,f.b)(),{admin:p,routes:q}=n,{user:r}=p,{api:s}=q,{t}=(0,g.d)(),[u,v]=(0,j.useState)(!1);m[0]!==o||m[1]!==r?(a=o({collectionSlug:r}),m[0]=o,m[1]=r,m[2]=a):a=m[2];let w=a,x=w?.auth?.loginWithUsername;m[3]!==x||m[4]!==t?(b=(a,b,c)=>{a.json().then(()=>{v(!0),b(t("general:submissionSuccessful"))}).catch(()=>{c(x?t("authentication:usernameNotValid"):t("authentication:emailNotValid"))})},m[3]=x,m[4]=t,m[5]=b):b=m[5];let y=b;if(m[6]!==s||m[7]!==n||m[8]!==y||m[9]!==u||m[10]!==x||m[11]!==t||m[12]!==r){l=Symbol.for("react.early_return_sentinel");a:{let a=x?{username:{initialValue:"",valid:!0,value:void 0}}:{email:{initialValue:"",valid:!0,value:void 0}};if(u){let a;m[15]!==t?(a=(0,e.jsx)(k,{description:t("authentication:checkYourEmailForPasswordReset"),heading:t("authentication:emailSent")}),m[15]=t,m[16]=a):a=m[16],l=a;break a}c=(0,e.jsxs)(h.Form,{action:`${s}/${r}/forgot-password`,handleResponse:y,initialState:a,method:"POST",children:[(0,e.jsx)(k,{description:x?t("authentication:forgotPasswordUsernameInstructions"):t("authentication:forgotPasswordEmailInstructions"),heading:t("authentication:forgotPassword")}),x?(0,e.jsx)(h.TextField,{field:{name:"username",label:t("authentication:username"),required:!0},path:"username",validate:a=>(0,i.Qq)(a,{name:"username",type:"text",blockData:{},data:{},event:"onChange",path:["username"],preferences:{fields:{}},req:{payload:{config:n},t},required:!0,siblingData:{}})}):(0,e.jsx)(h.EmailField,{field:{name:"email",admin:{autoComplete:"email"},label:t("general:email"),required:!0},path:"email",validate:a=>(0,i.Rp)(a,{name:"email",type:"email",blockData:{},data:{},event:"onChange",path:["email"],preferences:{fields:{}},req:{payload:{config:n},t},required:!0,siblingData:{}})}),(0,e.jsx)(h.FormSubmit,{size:"large",children:t("general:submit")})]})}m[6]=s,m[7]=n,m[8]=y,m[9]=u,m[10]=x,m[11]=t,m[12]=r,m[13]=c,m[14]=l}else c=m[13],l=m[14];return l!==Symbol.for("react.early_return_sentinel")?l:c}},48161:a=>{"use strict";a.exports=require("node:os")},51455:a=>{"use strict";a.exports=require("node:fs/promises")},52763:(a,b,c)=>{"use strict";c.d(b,{ToggleTheme:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call ToggleTheme() from the server but ToggleTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\node_modules\\@payloadcms\\next\\dist\\views\\Account\\ToggleTheme\\index.js","ToggleTheme")},53053:a=>{"use strict";a.exports=require("node:diagnostics_channel")},54820:(a,b,c)=>{"use strict";c.d(b,{f:()=>e});var d=c(55107);let e=({config:a,currentRoute:b})=>{let c,{admin:{components:{views:e}},routes:{admin:f}}=a,g="/"===f?b:b.replace(f,""),h=e&&"object"==typeof e&&Object.entries(e).find(([a,b])=>{let e=(0,d.W)({currentRoute:g,exact:b.exact,path:b.path,sensitive:b.sensitive,strict:b.strict});return e&&(c=a),e})?.[1]||void 0;return h?{view:{payloadComponent:h.Component},viewConfig:h,viewKey:c}:{view:{Component:null},viewConfig:null,viewKey:null}}},54953:(a,b,c)=>{"use strict";c.d(b,{ResetPasswordForm:()=>k});var d=c(83951),e=c(60687),f=c(85957),g=c(54836),h=c(54845),i=c(16189),j=c(68081);c(43210);let k=a=>{let b,c,k=(0,d.c)(12),{token:l}=a,m=(0,f.d)(),{config:n}=(0,g.b)(),{admin:o,routes:p,serverURL:q}=n,{routes:r,user:s}=o,{login:t}=r,{admin:u,api:v}=p,w=(0,i.useRouter)(),{fetchFullUser:x}=(0,h.useAuth)();k[0]!==u||k[1]!==x||k[2]!==w||k[3]!==t?(b=async()=>{await x()?w.push(u):w.push((0,j.Q)({adminRoute:u,path:t}))},k[0]=u,k[1]=x,k[2]=w,k[3]=t,k[4]=b):b=k[4];let y=b;return k[5]!==v||k[6]!==m||k[7]!==y||k[8]!==q||k[9]!==l||k[10]!==s?(c=(0,e.jsxs)(h.Form,{action:`${q}${v}/${s}/reset-password`,initialState:{"confirm-password":{initialValue:"",valid:!1,value:""},password:{initialValue:"",valid:!1,value:""},token:{initialValue:l,valid:!0,value:l}},method:"POST",onSuccess:y,children:[(0,e.jsxs)("div",{className:"inputWrap",children:[(0,e.jsx)(h.PasswordField,{field:{name:"password",label:m.t("authentication:newPassword"),required:!0},path:"password",schemaPath:`${s}.password`}),(0,e.jsx)(h.ConfirmPasswordField,{}),(0,e.jsx)(h.HiddenField,{path:"token",schemaPath:`${s}.token`,value:l})]}),(0,e.jsx)(h.FormSubmit,{size:"large",children:m.t("authentication:resetPassword")})]}),k[5]=v,k[6]=m,k[7]=y,k[8]=q,k[9]=l,k[10]=s,k[11]=c):c=k[11],c}},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},56757:(a,b,c)=>{"use strict";c.d(b,{LogoutClient:()=>k});var d=c(83951),e=c(60687),f=c(54845),g=c(85957),h=c(16189),i=c(68081),j=c(43210);let k=a=>{let b,c,k,l,m,n=(0,d.c)(23),{adminRoute:o,inactivity:p,redirect:q}=a,{logOut:r,user:s}=(0,f.useAuth)(),{startRouteTransition:t}=(0,f.useRouteTransition)(),[u,v]=j.useState(!s),w=j.useRef(!1);n[0]!==o||n[1]!==p||n[2]!==q?(b=()=>(0,i.Q)({adminRoute:o,path:`/login${p&&q&&q.length>0?`?redirect=${encodeURIComponent(q)}`:""}`}),n[0]=o,n[1]=p,n[2]=q,n[3]=b):b=n[3];let[x]=j.useState(b),{t:y}=(0,g.d)(),z=(0,h.useRouter)();n[4]!==p||n[5]!==r||n[6]!==x||n[7]!==z||n[8]!==t||n[9]!==y?(c=async()=>{let a=await r();if(v(a),!p&&a&&!w.current){f.toast.success(y("authentication:loggedOutSuccessfully")),w.current=!0,t(()=>z.push(x));return}},n[4]=p,n[5]=r,n[6]=x,n[7]=z,n[8]=t,n[9]=y,n[10]=c):c=n[10];let A=c;if(n[11]!==A||n[12]!==u||n[13]!==x||n[14]!==z||n[15]!==t?(k=()=>{u?t(()=>z.push(x)):A()},l=[A,u,x,z,t],n[11]=A,n[12]=u,n[13]=x,n[14]=z,n[15]=t,n[16]=k,n[17]=l):(k=n[16],l=n[17]),(0,j.useEffect)(k,l),u&&p){let a;return n[18]!==x||n[19]!==y?(a=(0,e.jsxs)("div",{className:"logout__wrap",children:[(0,e.jsx)("h2",{children:y("authentication:loggedOutInactivity")}),(0,e.jsx)(f.Button,{buttonStyle:"secondary",el:"link",size:"large",url:x,children:y("authentication:logBackIn")})]}),n[18]=x,n[19]=y,n[20]=a):a=n[20],a}return n[21]!==y?(m=(0,e.jsx)(f.LoadingOverlay,{animationDuration:"0ms",loadingText:y("authentication:loggingOut")}),n[21]=y,n[22]=m):m=n[22],m}},57075:a=>{"use strict";a.exports=require("node:stream")},57881:(a,b,c)=>{"use strict";c.d(b,{ForgotPasswordForm:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call ForgotPasswordForm() from the server but ForgotPasswordForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\node_modules\\@payloadcms\\next\\dist\\views\\ForgotPassword\\ForgotPasswordForm\\index.js","ForgotPasswordForm")},57975:a=>{"use strict";a.exports=require("node:util")},59020:(a,b,c)=>{"use strict";c.d(b,{CreateFirstUserClient:()=>j});var d=c(60687),e=c(54836),f=c(54845),g=c(85957),h=c(51376),i=c(43210);let j=({docPermissions:a,docPreferences:b,initialState:c,loginWithUsername:j,userSlug:k})=>{let{config:{routes:{admin:l,api:m},serverURL:n},getEntityConfig:o}=(0,e.b)(),{getFormState:p}=(0,f.useServerFunctions)(),{t:q}=(0,g.d)(),{setUser:r}=(0,f.useAuth)(),s=i.useRef(null),t=o({collectionSlug:k}),u=i.useCallback(async({formState:c,submitted:d})=>{let e=(0,h.wN)(s),f=await p({collectionSlug:k,docPermissions:a,docPreferences:b,formState:c,operation:"create",schemaPath:k,signal:e.signal,skipValidation:!d});if(s.current=null,f&&f.state)return f.state},[k,p,a,b]);return(0,i.useEffect)(()=>{let a=s.current;return()=>{(0,h.eS)(a)}},[]),(0,d.jsxs)(f.Form,{action:`${n}${m}/${k}/first-register`,initialState:{...c,"confirm-password":{...c["confirm-password"],valid:c["confirm-password"].valid||!1,value:c["confirm-password"].value||""}},method:"POST",onChange:[u],onSuccess:a=>{r(a)},redirect:l,validationOperation:"create",children:[(0,d.jsx)(f.EmailAndUsernameFields,{className:"emailAndUsername",loginWithUsername:j,operation:"create",readOnly:!1,t:q}),(0,d.jsx)(f.PasswordField,{autoComplete:"off",field:{name:"password",label:q("authentication:newPassword"),required:!0},path:"password"}),(0,d.jsx)(f.ConfirmPasswordField,{}),(0,d.jsx)(f.RenderFields,{fields:t.fields,forceRender:!0,parentIndexPath:"",parentPath:"",parentSchemaPath:k,permissions:!0,readOnly:!1}),(0,d.jsx)(f.FormSubmit,{size:"large",children:q("general:create")})]})}},60182:(a,b,c)=>{"use strict";c.d(b,{AccountClient:()=>h});var d=c(83951),e=c(54845),f=c(85957),g=c(43210);let h=()=>{let a,b,c=(0,d.c)(4),{setStepNav:h}=(0,e.useStepNav)(),{t:i}=(0,f.d)();return c[0]!==h||c[1]!==i?(a=()=>{let a=[];a.push({label:i("authentication:account"),url:"/account"}),h(a)},b=[h,i],c[0]=h,c[1]=i,c[2]=a,c[3]=b):(a=c[2],b=c[3]),g.useEffect(a,b),null}},60260:(a,b,c)=>{"use strict";c.d(b,{LanguageSelector:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call LanguageSelector() from the server but LanguageSelector is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\node_modules\\@payloadcms\\next\\dist\\views\\Account\\Settings\\LanguageSelector.js","LanguageSelector")},60462:(a,b,c)=>{"use strict";c.d(b,{CreateFirstUserClient:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call CreateFirstUserClient() from the server but CreateFirstUserClient is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\node_modules\\@payloadcms\\next\\dist\\views\\CreateFirstUser\\index.client.js","CreateFirstUserClient")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66520:(a,b,c)=>{Promise.resolve().then(c.bind(c,76745)),Promise.resolve().then(c.bind(c,59095)),Promise.resolve().then(c.bind(c,99124)),Promise.resolve().then(c.bind(c,92391)),Promise.resolve().then(c.bind(c,21233)),Promise.resolve().then(c.bind(c,75553)),Promise.resolve().then(c.bind(c,65637)),Promise.resolve().then(c.bind(c,50660)),Promise.resolve().then(c.bind(c,60182)),Promise.resolve().then(c.bind(c,24295)),Promise.resolve().then(c.bind(c,67474)),Promise.resolve().then(c.bind(c,12149)),Promise.resolve().then(c.bind(c,80003)),Promise.resolve().then(c.bind(c,59020)),Promise.resolve().then(c.bind(c,67e3)),Promise.resolve().then(c.bind(c,43591)),Promise.resolve().then(c.bind(c,14253)),Promise.resolve().then(c.bind(c,56757)),Promise.resolve().then(c.bind(c,50354)),Promise.resolve().then(c.bind(c,54953)),Promise.resolve().then(c.bind(c,4268)),Promise.resolve().then(c.bind(c,67906)),Promise.resolve().then(c.bind(c,84141)),Promise.resolve().then(c.bind(c,61791)),Promise.resolve().then(c.bind(c,41986)),Promise.resolve().then(c.bind(c,96961)),Promise.resolve().then(c.bind(c,50905)),Promise.resolve().then(c.bind(c,26483)),Promise.resolve().then(c.bind(c,22483)),Promise.resolve().then(c.bind(c,35564)),Promise.resolve().then(c.bind(c,6885)),Promise.resolve().then(c.bind(c,70347)),Promise.resolve().then(c.bind(c,25925)),Promise.resolve().then(c.bind(c,44614)),Promise.resolve().then(c.bind(c,65673)),Promise.resolve().then(c.bind(c,40369)),Promise.resolve().then(c.bind(c,8300)),Promise.resolve().then(c.bind(c,13275)),Promise.resolve().then(c.bind(c,72811)),Promise.resolve().then(c.bind(c,61959)),Promise.resolve().then(c.bind(c,37320)),Promise.resolve().then(c.bind(c,69456)),Promise.resolve().then(c.bind(c,92183)),Promise.resolve().then(c.bind(c,54845)),Promise.resolve().then(c.bind(c,55629))},67474:(a,b,c)=>{"use strict";c.d(b,{LanguageSelector:()=>h});var d=c(83951),e=c(60687),f=c(85957),g=c(54845);c(43210);let h=a=>{let b,c,h=(0,d.c)(8),{languageOptions:i}=a,{i18n:j,switchLanguage:k}=(0,f.d)();if(h[0]!==k?(b=async a=>{await k(a.value)},h[0]=k,h[1]=b):b=h[1],h[2]!==j||h[3]!==i||h[4]!==b){let a;h[6]!==j?(a=a=>a.value===j.language,h[6]=j,h[7]=a):a=h[7],c=(0,e.jsx)(g.ReactSelect,{inputId:"language-select",isClearable:!1,onChange:b,options:i,value:i.find(a)}),h[2]=j,h[3]=i,h[4]=b,h[5]=c}else c=h[5];return c}},73024:a=>{"use strict";a.exports=require("node:fs")},73136:a=>{"use strict";a.exports=require("node:url")},73429:a=>{"use strict";a.exports=require("node:util/types")},73496:a=>{"use strict";a.exports=require("http2")},74075:a=>{"use strict";a.exports=require("zlib")},74552:a=>{"use strict";a.exports=require("pino")},75919:a=>{"use strict";a.exports=require("node:worker_threads")},76760:a=>{"use strict";a.exports=require("node:path")},77030:a=>{"use strict";a.exports=require("node:net")},77598:a=>{"use strict";a.exports=require("node:crypto")},78474:a=>{"use strict";a.exports=require("node:events")},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},79748:a=>{"use strict";a.exports=require("fs/promises")},80099:a=>{"use strict";a.exports=require("node:sqlite")},81630:a=>{"use strict";a.exports=require("http")},84297:a=>{"use strict";a.exports=require("async_hooks")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86834:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["(payload)",{children:["admin",{children:["[[...segments]]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,8377)),"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\[[...segments]]\\page.tsx"]}]},{"not-found":[()=>Promise.resolve().then(c.bind(c,28114)),"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\[[...segments]]\\not-found.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,22441)),"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]},{"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"]}]}.children,H=["C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\[[...segments]]\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/(payload)/admin/[[...segments]]/page",pathname:"/admin/[[...segments]]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/(payload)/admin/[[...segments]]/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},90356:(a,b,c)=>{"use strict";c.d(b,{AccountClient:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call AccountClient() from the server but AccountClient is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\node_modules\\@payloadcms\\next\\dist\\views\\Account\\index.client.js","AccountClient")},91043:a=>{"use strict";a.exports=require("@aws-sdk/client-s3")},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},98995:a=>{"use strict";a.exports=require("node:module")}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[1103,9598,1769,9436,5590,9533,6622,750],()=>b(b.s=86834));module.exports=c})();