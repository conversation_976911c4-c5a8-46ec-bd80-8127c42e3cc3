(()=>{var e={};e.id=1687,e.ids=[1687],e.modules={91043:e=>{"use strict";e.exports=require("@aws-sdk/client-s3")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},82015:e=>{"use strict";e.exports=require("react")},22326:e=>{"use strict";e.exports=require("react-dom")},8732:e=>{"use strict";e.exports=require("react/jsx-runtime")},79428:e=>{"use strict";e.exports=require("buffer")},79646:e=>{"use strict";e.exports=require("child_process")},55511:e=>{"use strict";e.exports=require("crypto")},14985:e=>{"use strict";e.exports=require("dns")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},73496:e=>{"use strict";e.exports=require("http2")},55591:e=>{"use strict";e.exports=require("https")},8086:e=>{"use strict";e.exports=require("module")},91645:e=>{"use strict";e.exports=require("net")},21820:e=>{"use strict";e.exports=require("os")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},11997:e=>{"use strict";e.exports=require("punycode")},4984:e=>{"use strict";e.exports=require("readline")},27910:e=>{"use strict";e.exports=require("stream")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},28855:e=>{"use strict";e.exports=import("@libsql/client")},84739:e=>{"use strict";e.exports=import("@payloadcms/next/layouts")},90398:e=>{"use strict";e.exports=import("@payloadcms/next/views")},34589:e=>{"use strict";e.exports=require("node:assert")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},4573:e=>{"use strict";e.exports=require("node:buffer")},37540:e=>{"use strict";e.exports=require("node:console")},77598:e=>{"use strict";e.exports=require("node:crypto")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},40610:e=>{"use strict";e.exports=require("node:dns")},78474:e=>{"use strict";e.exports=require("node:events")},73024:e=>{"use strict";e.exports=require("node:fs")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},37067:e=>{"use strict";e.exports=require("node:http")},32467:e=>{"use strict";e.exports=require("node:http2")},98995:e=>{"use strict";e.exports=require("node:module")},77030:e=>{"use strict";e.exports=require("node:net")},48161:e=>{"use strict";e.exports=require("node:os")},76760:e=>{"use strict";e.exports=require("node:path")},643:e=>{"use strict";e.exports=require("node:perf_hooks")},1708:e=>{"use strict";e.exports=require("node:process")},41792:e=>{"use strict";e.exports=require("node:querystring")},80099:e=>{"use strict";e.exports=require("node:sqlite")},57075:e=>{"use strict";e.exports=require("node:stream")},37830:e=>{"use strict";e.exports=require("node:stream/web")},41692:e=>{"use strict";e.exports=require("node:tls")},73136:e=>{"use strict";e.exports=require("node:url")},57975:e=>{"use strict";e.exports=require("node:util")},73429:e=>{"use strict";e.exports=require("node:util/types")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},38522:e=>{"use strict";e.exports=require("node:zlib")},58102:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>l,routeModule:()=>d,tree:()=>c});var s=t(70260),n=t(28203),i=t(25155),o=t.n(i),a=t(67292),u={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>a[e]);t.d(r,u);let c=["",{children:["(payload)",{children:["admin",{children:["[[...segments]]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,88564)),"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\[[...segments]]\\page.tsx"]}]},{"not-found":[()=>Promise.resolve().then(t.bind(t,28461)),"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\[[...segments]]\\not-found.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,19136)),"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"]}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,19937,23)),"next/dist/client/components/not-found-error"]}],l=["C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\[[...segments]]\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},d=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(payload)/admin/[[...segments]]/page",pathname:"/admin/[[...segments]]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},80312:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{"40fb2ba5fdcf9f0094269b16676dc4cb1768812611":()=>n.$$RSC_SERVER_ACTION_0});var n=t(19136),i=e([n]);n=(i.then?(await i)():i)[0],s()}catch(e){s(e)}})},36932:(e,r,t)=>{Promise.resolve().then(t.bind(t,81764))},90084:(e,r,t)=>{Promise.resolve().then(t.bind(t,94768))},85821:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,13219,23)),Promise.resolve().then(t.t.bind(t,34863,23)),Promise.resolve().then(t.t.bind(t,25155,23)),Promise.resolve().then(t.t.bind(t,9350,23)),Promise.resolve().then(t.t.bind(t,96313,23)),Promise.resolve().then(t.t.bind(t,48530,23)),Promise.resolve().then(t.t.bind(t,88921,23))},13437:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,66959,23)),Promise.resolve().then(t.t.bind(t,33875,23)),Promise.resolve().then(t.t.bind(t,88903,23)),Promise.resolve().then(t.t.bind(t,84178,23)),Promise.resolve().then(t.t.bind(t,86013,23)),Promise.resolve().then(t.t.bind(t,87190,23)),Promise.resolve().then(t.t.bind(t,61365,23))},69874:(e,r,t)=>{Promise.resolve().then(t.bind(t,81764))},33082:(e,r,t)=>{Promise.resolve().then(t.bind(t,94768))},94768:(e,r,t)=>{"use strict";t.d(r,{default:()=>n});var s=t(8732);t(82015);let n=()=>(0,s.jsx)("li",{style:{listStyle:"none"},children:(0,s.jsxs)("button",{onClick:()=>{window.location.href="/admin/sync"},style:{display:"flex",alignItems:"center",gap:"8px",width:"100%",padding:"8px 16px",backgroundColor:"#0070f3",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"14px",fontWeight:"500",textDecoration:"none",transition:"background-color 0.2s ease"},onMouseOver:e=>e.currentTarget.style.backgroundColor="#0056b3",onMouseOut:e=>e.currentTarget.style.backgroundColor="#0070f3",children:[(0,s.jsx)("span",{children:"\uD83D\uDD04"}),(0,s.jsx)("span",{children:"Sync to Main App"})]})})},88977:(e,r,t)=>{"use strict";Object.defineProperty(r,"A",{enumerable:!0,get:function(){return s.registerServerReference}});let s=t(46760)},54175:(e,r,t)=>{"use strict";let s;Object.defineProperty(r,"__esModule",{value:!0}),function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{arrayBufferToString:function(){return o},decrypt:function(){return c},encrypt:function(){return u},getActionEncryptionKey:function(){return g},getClientReferenceManifestForRsc:function(){return f},getServerModuleMap:function(){return d},setReferenceManifestsSingleton:function(){return p},stringToUint8Array:function(){return a}});let n=t(39212),i=t(29294);function o(e){let r=new Uint8Array(e),t=r.byteLength;if(t<65535)return String.fromCharCode.apply(null,r);let s="";for(let e=0;e<t;e++)s+=String.fromCharCode(r[e]);return s}function a(e){let r=e.length,t=new Uint8Array(r);for(let s=0;s<r;s++)t[s]=e.charCodeAt(s);return t}function u(e,r,t){return crypto.subtle.encrypt({name:"AES-GCM",iv:r},e,t)}function c(e,r,t){return crypto.subtle.decrypt({name:"AES-GCM",iv:r},e,t)}let l=Symbol.for("next.server.action-manifests");function p({page:e,clientReferenceManifest:r,serverActionsManifest:t,serverModuleMap:s}){var n;let i=null==(n=globalThis[l])?void 0:n.clientReferenceManifestsPerPage;globalThis[l]={clientReferenceManifestsPerPage:{...i,[m(e)]:r},serverActionsManifest:t,serverModuleMap:s}}function d(){let e=globalThis[l];if(!e)throw new n.InvariantError("Missing manifest for Server Actions.");return e.serverModuleMap}function f(){let e=globalThis[l];if(!e)throw new n.InvariantError("Missing manifest for Server Actions.");let{clientReferenceManifestsPerPage:r}=e,t=i.workAsyncStorage.getStore();if(!t)return function(e){let r=Object.values(e),t={clientModules:{},edgeRscModuleMapping:{},rscModuleMapping:{}};for(let e of r)t.clientModules={...t.clientModules,...e.clientModules},t.edgeRscModuleMapping={...t.edgeRscModuleMapping,...e.edgeRscModuleMapping},t.rscModuleMapping={...t.rscModuleMapping,...e.rscModuleMapping};return t}(r);let s=m(t.page),o=r[s];if(!o)throw new n.InvariantError(`Missing Client Reference Manifest for ${s}.`);return o}async function g(){if(s)return s;let e=globalThis[l];if(!e)throw new n.InvariantError("Missing manifest for Server Actions.");let r=process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY||e.serverActionsManifest.encryptionKey;if(void 0===r)throw new n.InvariantError("Missing encryption key for Server Actions");return s=await crypto.subtle.importKey("raw",a(atob(r)),"AES-GCM",!0,["encrypt","decrypt"])}function m(e){return e.replace(/\/(page|route)$/,"")}},98063:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{decryptActionBoundArgs:function(){return d},encryptActionBoundArgs:function(){return p}}),t(27315);let s=t(46760),n=t(8534),i=t(57212),o=t(54175),a=new TextEncoder,u=new TextDecoder;async function c(e,r){let t=await (0,o.getActionEncryptionKey)();if(void 0===t)throw Error("Missing encryption key for Server Action. This is a bug in Next.js");let s=atob(r),n=s.slice(0,16),i=s.slice(16),a=u.decode(await (0,o.decrypt)(t,(0,o.stringToUint8Array)(n),(0,o.stringToUint8Array)(i)));if(!a.startsWith(e))throw Error("Invalid Server Action payload: failed to decrypt.");return a.slice(e.length)}async function l(e,r){let t=await (0,o.getActionEncryptionKey)();if(void 0===t)throw Error("Missing encryption key for Server Action. This is a bug in Next.js");let s=new Uint8Array(16);crypto.getRandomValues(s);let n=(0,o.arrayBufferToString)(s.buffer),i=await (0,o.encrypt)(t,s,a.encode(e+r));return btoa(n+(0,o.arrayBufferToString)(i))}async function p(e,r){let{clientModules:t}=(0,o.getClientReferenceManifestForRsc)(),n=await (0,i.streamToString)((0,s.renderToReadableStream)(r,t));return await l(e,n)}async function d(e,r){let{edgeRscModuleMapping:t,rscModuleMapping:s}=(0,o.getClientReferenceManifestForRsc)(),i=await c(e,await r);return await (0,n.createFromReadableStream)(new ReadableStream({start(e){e.enqueue(a.encode(i)),e.close()}}),{serverConsumerManifest:{moduleLoading:null,moduleMap:s,serverModuleMap:(0,o.getServerModuleMap)()}})}},28461:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>c,generateMetadata:()=>u});var n=t(17750),i=t(90398),o=t(3401),a=e([n,i]);[n,i]=a.then?(await a)():a;let u=({params:e,searchParams:r})=>(0,i.generatePageMetadata)({config:n.A,params:e,searchParams:r}),c=({params:e,searchParams:r})=>(0,i.NotFoundPage)({config:n.A,params:e,searchParams:r,importMap:o.m});s()}catch(e){s(e)}})},88564:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>c,generateMetadata:()=>u});var n=t(17750),i=t(90398),o=t(3401),a=e([n,i]);[n,i]=a.then?(await a)():a;let u=({params:e,searchParams:r})=>(0,i.generatePageMetadata)({config:n.A,params:e,searchParams:r}),c=({params:e,searchParams:r})=>(0,i.RootPage)({config:n.A,params:e,searchParams:r,importMap:o.m});s()}catch(e){s(e)}})},19136:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{$$RSC_SERVER_ACTION_0:()=>l,default:()=>d});var n=t(8732),i=t(88977);t(98063);var o=t(17750);t(63046);var a=t(84739);t(82015);var u=t(3401);t(44023);var c=e([o,a]);[o,a]=c.then?(await c)():c;let l=async function(e){return(0,a.handleServerFunctions)({...e,config:o.A,importMap:u.m})},p=(0,i.A)(l,"40fb2ba5fdcf9f0094269b16676dc4cb1768812611",null),d=({children:e})=>(0,n.jsx)(a.RootLayout,{config:o.A,importMap:u.m,serverFunction:p,children:e});s()}catch(e){s(e)}})},81764:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\components\\\\SyncButton.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\components\\SyncButton.tsx","default")},63046:()=>{},44023:()=>{},3401:(e,r,t)=>{"use strict";t.d(r,{m:()=>s});let s={"/components/SyncButton#default":t(81764).default}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5994,2259,1112,7750],()=>t(58102));module.exports=s})();