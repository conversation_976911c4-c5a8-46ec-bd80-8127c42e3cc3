import React, { useState, useEffect } from 'react';
import { Download, AlertCircle, RefreshCw, Database, Users, FileText, Cloud } from 'lucide-react';

// Simple Card components for CMS
const Card = ({ children, className = '' }: { children: React.ReactNode; className?: string }) => (
  <div className={`bg-white rounded-lg border shadow-sm ${className}`}>
    {children}
  </div>
);

const CardContent = ({ children, className = '' }: { children: React.ReactNode; className?: string }) => (
  <div className={`p-6 ${className}`}>
    {children}
  </div>
);

const Button = ({ children, onClick, disabled = false, className = '', variant = 'default' }: {
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
  variant?: 'default' | 'outline';
}) => {
  const baseClasses = 'px-4 py-2 rounded-md font-medium transition-colors';
  const variantClasses = variant === 'outline'
    ? 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
    : 'bg-blue-600 text-white hover:bg-blue-700';
  const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer';

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${variantClasses} ${disabledClasses} ${className}`}
    >
      {children}
    </button>
  );
};

const Separator = () => <hr className="border-gray-200 my-4" />;

const CardHeader = ({ children, className = '' }: { children: React.ReactNode; className?: string }) => (
  <div className={`p-6 pb-0 ${className}`}>
    {children}
  </div>
);

const CardTitle = ({ children, className = '' }: { children: React.ReactNode; className?: string }) => (
  <h3 className={`text-lg font-semibold ${className}`}>
    {children}
  </h3>
);

const CardDescription = ({ children, className = '' }: { children: React.ReactNode; className?: string }) => (
  <p className={`text-sm text-gray-600 mt-1 ${className}`}>
    {children}
  </p>
);

interface SyncStats {
  challenges: {
    cmsChallenge: number;
    mainAppCmsChallenge: number;
    mainAppTotalChallenge: number;
  };
  users: {
    cmsUsers: number;
    mainAppCmsUsers: number;
    mainAppTotalUsers: number;
    availableToImport: number;
  };
}

interface SyncResult {
  total: number;
  synced: number;
  skipped: number;
  errors: number;
}

export function SyncDashboard() {
  const [isLoading, setIsLoading] = useState(false);
  const [stats, setStats] = useState<SyncStats | null>(null);
  const [lastChallengeSync, setLastChallengeSync] = useState<SyncResult | null>(null);
  const [lastUserSync, setLastUserSync] = useState<SyncResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Fetch sync statistics
  const fetchStats = async () => {
    try {
      setError(null);
      console.log('🔄 [SYNC-DASHBOARD] Starting to fetch stats...');

      // Get auth token (you'll need to implement this based on your auth system)
      const token = localStorage.getItem('payload-token') || '';

      const [challengeResponse, userResponse] = await Promise.all([
        fetch('/api/sync/challenges', {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }),
        fetch('/api/sync/users', {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        }),
      ]);

      console.log('📊 [SYNC-DASHBOARD] API responses:', {
        challengeOk: challengeResponse.ok,
        userOk: userResponse.ok
      });

      if (!challengeResponse.ok || !userResponse.ok) {
        throw new Error('Failed to fetch sync statistics');
      }

      const challengeData = await challengeResponse.json();
      const userData = await userResponse.json();

      console.log('📊 [SYNC-DASHBOARD] Parsed data:', {
        challengeData,
        userData
      });

      // Also fetch main app stats for import numbers
      let mainAppStats = { cmsUsers: 0, totalUsers: 0, availableToImport: 0 };
      try {
        const mainAppResponse = await fetch('http://localhost:3000/api/sync/users');
        if (mainAppResponse.ok) {
          const mainAppData = await mainAppResponse.json();
          console.log('📊 [SYNC-DASHBOARD] Main app data:', mainAppData);
          if (mainAppData.success) {
            mainAppStats = mainAppData.stats;
          }
        }
      } catch (error) {
        console.warn('Could not fetch main app stats:', error);
      }

      if (challengeData.success && userData.success) {
        const finalStats = {
          challenges: challengeData.stats || {
            cmsChallenge: 0,
            mainAppCmsChallenge: 0,
            mainAppTotalChallenge: 0
          },
          users: {
            cmsUsers: userData.stats?.cmsUsers || 0,
            mainAppCmsUsers: userData.stats?.mainAppCmsUsers || mainAppStats.cmsUsers || 0,
            mainAppTotalUsers: userData.stats?.mainAppTotalUsers || mainAppStats.totalUsers || 0,
            availableToImport: mainAppStats.availableToImport || 0
          },
        };

        console.log('✅ [SYNC-DASHBOARD] Setting final stats:', finalStats);
        setStats(finalStats);
      } else {
        // Set default values if API calls fail
        setStats({
          challenges: {
            cmsChallenge: 0,
            mainAppCmsChallenge: 0,
            mainAppTotalChallenge: 0
          },
          users: {
            cmsUsers: 0,
            mainAppCmsUsers: 0,
            mainAppTotalUsers: 0,
            availableToImport: 0
          }
        });
      }
    } catch (error) {
      console.error('Error fetching sync stats:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch sync statistics');

      // Set default stats even on error to show 0s instead of dashes
      setStats({
        challenges: {
          cmsChallenge: 0,
          mainAppCmsChallenge: 0,
          mainAppTotalChallenge: 0
        },
        users: {
          cmsUsers: 0,
          mainAppCmsUsers: 0,
          mainAppTotalUsers: 0,
          availableToImport: 0
        }
      });
    }
  };

  // Sync challenges to main application
  const handleChallengeSync = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const token = localStorage.getItem('payload-token') || '';
      
      const response = await fetch('/api/sync/challenges', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      const data = await response.json();

      if (data.success) {
        setLastChallengeSync(data.stats);
        await fetchStats(); // Refresh stats
        
        // Show success message
        alert(`Challenge sync completed! ${data.stats.synced} challenges synced successfully.`);
      } else {
        throw new Error(data.error || 'Challenge sync failed');
      }
    } catch (error) {
      console.error('Error syncing challenges:', error);
      setError(error instanceof Error ? error.message : 'Failed to sync challenges');
    } finally {
      setIsLoading(false);
    }
  };

  // Sync users to main application
  const handleUserSync = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const token = localStorage.getItem('payload-token') || '';
      
      const response = await fetch('/api/sync/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      const data = await response.json();

      if (data.success) {
        setLastUserSync(data.stats);
        await fetchStats(); // Refresh stats
        
        // Show success message
        alert(`User sync completed! ${data.stats.synced} users synced successfully.`);
      } else {
        throw new Error(data.error || 'User sync failed');
      }
    } catch (error) {
      console.error('Error syncing users:', error);
      setError(error instanceof Error ? error.message : 'Failed to sync users');
    } finally {
      setIsLoading(false);
    }
  };

  // Load stats on component mount
  useEffect(() => {
    fetchStats();
  }, []);

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      {/* Header */}
      <div style={{ marginBottom: '32px', borderBottom: '1px solid #e5e7eb', paddingBottom: '16px' }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div>
            <h2 style={{ fontSize: '24px', fontWeight: 'bold', color: '#1f2937', marginBottom: '8px' }}>
              🔄 Sync Management
            </h2>
            <p style={{ color: '#6b7280' }}>
              Synchronize content and users between CMS and main application
            </p>
          </div>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            backgroundColor: '#f3f4f6',
            padding: '8px 12px',
            borderRadius: '6px',
            fontSize: '14px',
            fontWeight: '500'
          }}>
            🛡️ Admin Only
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-800">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm font-medium">{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Sync Statistics */}
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Challenge Sync Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Challenge Sync Status
              </CardTitle>
              <CardDescription>
                Synchronization status between CMS and main application challenges
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Cloud className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-900">CMS Challenges</span>
                  </div>
                  <p className="text-2xl font-bold text-blue-600">{stats.challenges.cmsChallenge}</p>
                </div>
                <div className="p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Database className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium text-green-900">Synced to Main App</span>
                  </div>
                  <p className="text-2xl font-bold text-green-600">{stats.challenges.mainAppCmsChallenge}</p>
                </div>
              </div>
              
              <Separator />
              
              <Button
                onClick={handleChallengeSync}
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Syncing Challenges...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Sync Challenges to Main App
                  </>
                )}
              </Button>
              
              {lastChallengeSync && (
                <div className="text-sm text-gray-600 space-y-1">
                  <p className="font-medium">Last Sync Result:</p>
                  <div className="flex justify-between">
                    <span>Total: {lastChallengeSync.total}</span>
                    <span className="text-green-600">Synced: {lastChallengeSync.synced}</span>
                    {lastChallengeSync.errors > 0 && (
                      <span className="text-red-600">Errors: {lastChallengeSync.errors}</span>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* User Sync Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                User Sync Status
              </CardTitle>
              <CardDescription>
                Synchronization status between CMS and main application users
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="p-3 bg-purple-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-purple-600" />
                    <span className="text-sm font-medium text-purple-900">CMS Users</span>
                  </div>
                  <p className="text-2xl font-bold text-purple-600">{stats.users.cmsUsers || 0}</p>
                </div>
                <div className="p-3 bg-orange-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Database className="h-4 w-4 text-orange-600" />
                    <span className="text-sm font-medium text-orange-900">Synced to Main App</span>
                  </div>
                  <p className="text-2xl font-bold text-orange-600">{stats.users.mainAppCmsUsers || 0}</p>
                </div>
              </div>
              
              <Separator />
              
              <Button
                onClick={handleUserSync}
                disabled={isLoading}
                className="w-full"
                variant="outline"
              >
                {isLoading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Syncing Users...
                  </>
                ) : (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Sync Users to Main App
                  </>
                )}
              </Button>
              
              {lastUserSync && (
                <div className="text-sm text-gray-600 space-y-1">
                  <p className="font-medium">Last Sync Result:</p>
                  <div className="flex justify-between">
                    <span>Total: {lastUserSync.total}</span>
                    <span className="text-green-600">Synced: {lastUserSync.synced}</span>
                    {lastUserSync.errors > 0 && (
                      <span className="text-red-600">Errors: {lastUserSync.errors}</span>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Import User Challenges Section */}
      {stats && (
        <div className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Download className="h-5 w-5" />
                Import User Challenges
              </CardTitle>
              <CardDescription>
                Import user-created challenges from main application to CMS for review
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="p-3 bg-blue-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium text-blue-900">Available to Import</span>
                  </div>
                  <p className="text-2xl font-bold text-blue-600">{stats.users.availableToImport || 0}</p>
                </div>
                <div className="p-3 bg-green-50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Database className="h-4 w-4 text-green-600" />
                    <span className="text-sm font-medium text-green-900">Already Imported</span>
                  </div>
                  <p className="text-2xl font-bold text-green-600">0</p>
                </div>
              </div>

              <Separator />

              <Button
                onClick={() => alert('Import functionality coming soon!')}
                disabled={isLoading || (stats.users.availableToImport || 0) === 0}
                className="w-full"
                variant="outline"
              >
                <Download className="h-4 w-4 mr-2" />
                Import User Challenges from Main App
              </Button>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Refresh Button */}
      <div className="flex justify-center">
        <Button
          variant="outline"
          onClick={fetchStats}
          disabled={isLoading}
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh Statistics
        </Button>
      </div>

      {/* Info Panel */}
      <Card className="bg-yellow-50 border-yellow-200">
        <CardContent className="pt-6">
          <div className="flex items-start gap-2">
            <AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5" />
            <div className="text-sm text-yellow-800">
              <p className="font-medium mb-2">Sync Information:</p>
              <ul className="list-disc list-inside space-y-1 text-xs">
                <li><strong>Challenge Sync:</strong> Publishes CMS challenges to the main application for users to access</li>
                <li><strong>User Sync:</strong> Creates accounts in the main application for CMS content creators and educators</li>
                <li><strong>Admin Only:</strong> Only CMS administrators can perform sync operations</li>
                <li><strong>Safe Operation:</strong> Sync operations are idempotent and won't create duplicates</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
