"use strict";exports.id=5165,exports.ids=[5165],exports.modules={85165:(a,b,c)=>{c.r(b),c.d(b,{fromTokenFile:()=>i,fromWebToken:()=>g});var d=c(21905),e=c(71930),f=c(29021);let g=a=>async b=>{a.logger?.debug("@aws-sdk/credential-provider-web-identity - fromWebToken");let{roleArn:d,roleSessionName:e,webIdentityToken:f,providerId:g,policyArns:h,policy:i,durationSeconds:j}=a,{roleAssumerWithWebIdentity:k}=a;if(!k){let{getDefaultRoleAssumerWithWebIdentity:d}=await c.e(2686).then(c.bind(c,32686));k=d({...a.clientConfig,credentialProviderLogger:a.logger,parentClientConfig:{...b?.callerClientConfig,...a.parentClientConfig}},a.clientPlugins)}return k({RoleArn:d,RoleSessionName:e??`aws-sdk-js-session-${Date.now()}`,WebIdentityToken:f,ProviderId:g,PolicyArns:h,Policy:i,DurationSeconds:j})},h="AWS_WEB_IDENTITY_TOKEN_FILE",i=(a={})=>async()=>{a.logger?.debug("@aws-sdk/credential-provider-web-identity - fromTokenFile");let b=a?.webIdentityTokenFile??process.env[h],c=a?.roleArn??process.env.AWS_ROLE_ARN,i=a?.roleSessionName??process.env.AWS_ROLE_SESSION_NAME;if(!b||!c)throw new e.C1("Web identity configuration not specified",{logger:a.logger});let j=await g({...a,webIdentityToken:(0,f.readFileSync)(b,{encoding:"ascii"}),roleArn:c,roleSessionName:i})();return b===process.env[h]&&(0,d.g)(j,"CREDENTIALS_ENV_VARS_STS_WEB_ID_TOKEN","h"),j}}};