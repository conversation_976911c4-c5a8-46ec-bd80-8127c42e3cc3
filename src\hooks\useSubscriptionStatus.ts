import { useState, useEffect, useCallback } from 'react';
import { type SubscriptionStatus, type AccessControlResult } from '@/lib/subscription-utils';
import { useAuth } from './useAuth';

export const useSubscriptionStatus = () => {
  const { user } = useAuth();
  const [subscriptionStatus, setSubscriptionStatus] = useState<SubscriptionStatus | null>(null);
  const [hasActiveSubscription, setHasActiveSubscription] = useState(false);
  const [showRenewalBanner, setShowRenewalBanner] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [accessResult, setAccessResult] = useState<AccessControlResult | null>(null);

  const refreshSubscriptionStatus = useCallback(async () => {
    if (!user) {
      setSubscriptionStatus(null);
      setHasActiveSubscription(false);
      setAccessResult(null);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);

      // Call the API endpoint instead of using Prisma directly
      const response = await fetch(`/api/subscription/access-check?userId=${user.id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch subscription status');
      }

      const accessCheck: AccessControlResult = await response.json();
      setAccessResult(accessCheck);
      setSubscriptionStatus(accessCheck.subscriptionStatus || null);
      setHasActiveSubscription(accessCheck.hasAccess);

      // Show renewal banner if trial is expiring soon or subscription is expiring
      if (accessCheck.daysUntilExpiry !== undefined && accessCheck.daysUntilExpiry <= 3 && accessCheck.daysUntilExpiry > 0) {
        setShowRenewalBanner(true);
      } else {
        setShowRenewalBanner(false);
      }

    } catch (error) {
      console.error('Error fetching subscription status:', error);
      setSubscriptionStatus(null);
      setHasActiveSubscription(false);
      setAccessResult(null);
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  useEffect(() => {
    // Only refresh if user changes, not on every component mount
    if (user?.id) {
      refreshSubscriptionStatus();
    }
  }, [user?.id, refreshSubscriptionStatus]);

  return {
    subscriptionStatus,
    hasActiveSubscription,
    showRenewalBanner,
    setShowRenewalBanner,
    refreshSubscriptionStatus,
    isLoading,
    accessResult,
    isTrialExpired: accessResult?.isTrialExpired || false,
    daysUntilExpiry: accessResult?.daysUntilExpiry
  };
};
