import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

const CMS_BASE_URL = process.env.CMS_BASE_URL || 'http://localhost:3001';

// GET /api/challenges/test-schema - Test schema compatibility between CMS and Supabase
export async function GET() {
  try {
    console.log('🧪 [TEST] Testing CMS to Supabase schema compatibility');

    // Fetch CMS challenges
    const cmsUrl = `${CMS_BASE_URL}/my-route`;
    const response = await fetch(cmsUrl, {
      headers: {
        'Content-Type': 'application/json',
      },
      signal: AbortSignal.timeout(10000),
    });

    if (!response.ok) {
      throw new Error(`CMS API error: ${response.status}`);
    }

    const data = await response.json();
    const cmsChallenge = data.challenges || [];

    console.log(`📦 [TEST] Found ${cmsChallenge.length} CMS challenges`);

    const results = {
      totalChallenges: cmsChallenge.length,
      compatible: 0,
      incompatible: 0,
      issues: [] as any[],
      sampleMappings: [] as any[]
    };

    for (const challenge of cmsChallenge.slice(0, 3)) { // Test first 3 challenges
      const issues = [];
      
      // Check required fields for Supabase
      if (!challenge.title) issues.push('Missing title');
      if (!challenge.description) issues.push('Missing description');
      if (!challenge.difficulty) issues.push('Missing difficulty');
      if (!challenge.category) issues.push('Missing category (maps to type)');
      
      // Check if prompt exists or can be derived
      const hasPrompt = challenge.prompt || challenge.instructions || challenge.description;
      if (!hasPrompt) issues.push('Missing prompt (no fallback available)');

      // Check difficulty values
      if (challenge.difficulty && !['easy', 'medium', 'hard'].includes(challenge.difficulty)) {
        issues.push(`Invalid difficulty: ${challenge.difficulty}`);
      }

      // Check category/type mapping
      const validTypes = ['art', 'story', 'music', 'game', 'coding', 'video'];
      if (challenge.category && !validTypes.includes(challenge.category)) {
        issues.push(`Invalid category: ${challenge.category}`);
      }

      if (issues.length === 0) {
        results.compatible++;
      } else {
        results.incompatible++;
        results.issues.push({
          challengeId: challenge.id,
          title: challenge.title,
          issues
        });
      }

      // Create sample mapping
      const mapping = {
        cms: {
          id: challenge.id,
          title: challenge.title,
          description: challenge.description,
          category: challenge.category,
          difficulty: challenge.difficulty,
          prompt: challenge.prompt,
          instructions: challenge.instructions,
          status: challenge.status,
          is_active: challenge.is_active
        },
        supabase: {
          id: `cms-${challenge.id}`,
          title: challenge.title,
          description: challenge.description,
          type: challenge.category,
          difficulty: challenge.difficulty,
          prompt: challenge.prompt || challenge.instructions || challenge.description,
          is_active: challenge.is_active !== false && challenge.status === 'published',
          created_by: 'cms'
        },
        compatible: issues.length === 0,
        issues
      };

      results.sampleMappings.push(mapping);
    }

    // Test database connection
    const dbChallenges = await prisma.challenge.findMany({
      where: { created_by: 'cms' },
      take: 5
    });

    return NextResponse.json({
      success: true,
      message: 'Schema compatibility test completed',
      results,
      database: {
        cmsChallenge: dbChallenges.length,
        sampleDbChallenges: dbChallenges.map(c => ({
          id: c.id,
          title: c.title,
          type: c.type,
          difficulty: c.difficulty,
          is_active: c.is_active
        }))
      }
    });

  } catch (error) {
    console.error('❌ [TEST] Error testing schema compatibility:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to test schema compatibility',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
