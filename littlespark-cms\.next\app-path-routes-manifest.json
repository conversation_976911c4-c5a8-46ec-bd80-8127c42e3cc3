{"/_not-found/page": "/_not-found", "/api/auth/verify/route": "/api/auth/verify", "/api/sync/challenges/route": "/api/sync/challenges", "/api/sync/from-main-app/route": "/api/sync/from-main-app", "/api/sync/users/route": "/api/sync/users", "/api/test-data/route": "/api/test-data", "/(payload)/api/[...slug]/route": "/api/[...slug]", "/(payload)/api/graphql-playground/route": "/api/graphql-playground", "/(payload)/api/graphql/route": "/api/graphql", "/my-route/route": "/my-route", "/(payload)/admin/dashboard/page": "/admin/dashboard", "/(payload)/admin/sync/page": "/admin/sync", "/(frontend)/page": "/", "/(payload)/admin/[[...segments]]/page": "/admin/[[...segments]]"}