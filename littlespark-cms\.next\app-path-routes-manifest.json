{"/api/sync/from-main-app/route": "/api/sync/from-main-app", "/api/auth/verify/route": "/api/auth/verify", "/api/sync/challenges/route": "/api/sync/challenges", "/api/sync/users/route": "/api/sync/users", "/api/test-data/route": "/api/test-data", "/(payload)/api/graphql-playground/route": "/api/graphql-playground", "/(payload)/api/[...slug]/route": "/api/[...slug]", "/(payload)/api/graphql/route": "/api/graphql", "/my-route/route": "/my-route"}