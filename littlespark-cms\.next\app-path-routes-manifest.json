{"/_not-found/page": "/_not-found", "/api/auth/verify/route": "/api/auth/verify", "/api/sync/from-main-app/route": "/api/sync/from-main-app", "/api/sync/users/route": "/api/sync/users", "/api/test-data/route": "/api/test-data", "/api/sync/challenges/route": "/api/sync/challenges", "/(payload)/api/[...slug]/route": "/api/[...slug]", "/(payload)/api/graphql/route": "/api/graphql", "/(payload)/api/graphql-playground/route": "/api/graphql-playground", "/my-route/route": "/my-route", "/(payload)/admin/dashboard/page": "/admin/dashboard", "/(payload)/admin/[[...segments]]/page": "/admin/[[...segments]]", "/(frontend)/page": "/"}