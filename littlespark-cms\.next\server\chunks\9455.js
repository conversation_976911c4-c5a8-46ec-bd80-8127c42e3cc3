"use strict";exports.id=9455,exports.ids=[9455],exports.modules={22232:(a,b,c)=>{c.d(b,{CG:()=>f,Y2:()=>e,cJ:()=>g});var d=c(70525);let e=(a,b)=>(0,d.w)(a,b).then(a=>{if(a.length)try{return JSON.parse(a)}catch(b){throw b?.name==="SyntaxError"&&Object.defineProperty(b,"$responseBodyText",{value:a}),b}return{}}),f=async(a,b)=>{let c=await e(a,b);return c.message=c.message??c.Message,c},g=(a,b)=>{let c=(a,b)=>Object.keys(a).find(a=>a.toLowerCase()===b.toLowerCase()),d=a=>{let b=a;return"number"==typeof b&&(b=b.toString()),b.indexOf(",")>=0&&(b=b.split(",")[0]),b.indexOf(":")>=0&&(b=b.split(":")[0]),b.indexOf("#")>=0&&(b=b.split("#")[1]),b},e=c(a.headers,"x-amzn-errortype");if(void 0!==e)return d(a.headers[e]);if(b&&"object"==typeof b){let a=c(b,"code");if(a&&void 0!==b[a])return d(b[a]);if(void 0!==b.__type)return d(b.__type)}}},28125:(a,b,c)=>{c.d(b,{l:()=>f});var d=c(31734),e=c(26719);function f(a,b){return new g(a,b)}class g{constructor(a,b){this.input=a,this.context=b,this.query={},this.method="",this.headers={},this.path="",this.body=null,this.hostname="",this.resolvePathStack=[]}async build(){let{hostname:a,protocol:b="https",port:c,path:e}=await this.context.endpoint();for(let a of(this.path=e,this.resolvePathStack))a(this.path);return new d.Kd({protocol:b,hostname:this.hostname||a,port:c,method:this.method,path:this.path,query:this.query,body:this.body,headers:this.headers})}hn(a){return this.hostname=a,this}bp(a){return this.resolvePathStack.push(b=>{this.path=`${b?.endsWith("/")?b.slice(0,-1):b||""}`+a}),this}p(a,b,c,d){return this.resolvePathStack.push(f=>{this.path=((a,b,c,d,f,g)=>{if(null!=b&&void 0!==b[c]){let b=d();if(b.length<=0)throw Error("Empty value provided for input HTTP label: "+c+".");a=a.replace(f,g?b.split("/").map(a=>(0,e.$)(a)).join("/"):(0,e.$)(b))}else throw Error("No value provided for input HTTP label: "+c+".");return a})(f,this.input,a,b,c,d)}),this}h(a){return this.headers=a,this}q(a){return this.query=a,this}b(a){return this.body=a,this}m(a){return this.method=a,this}}},39455:(a,b,c)=>{c.d(b,{GetRoleCredentialsCommand:()=>F,SSOClient:()=>aJ});var d=c(59727),e=c(8839),f=c(35639);let g={UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}};class h extends f.TJ{constructor(a){super(a),Object.setPrototypeOf(this,h.prototype)}}class i extends h{name="InvalidRequestException";$fault="client";constructor(a){super({name:"InvalidRequestException",$fault:"client",...a}),Object.setPrototypeOf(this,i.prototype)}}class j extends h{name="ResourceNotFoundException";$fault="client";constructor(a){super({name:"ResourceNotFoundException",$fault:"client",...a}),Object.setPrototypeOf(this,j.prototype)}}class k extends h{name="TooManyRequestsException";$fault="client";constructor(a){super({name:"TooManyRequestsException",$fault:"client",...a}),Object.setPrototypeOf(this,k.prototype)}}class l extends h{name="UnauthorizedException";$fault="client";constructor(a){super({name:"UnauthorizedException",$fault:"client",...a}),Object.setPrototypeOf(this,l.prototype)}}let m=a=>({...a,...a.accessToken&&{accessToken:f.$H}}),n=a=>({...a,...a.roleCredentials&&{roleCredentials:(a=>({...a,...a.secretAccessKey&&{secretAccessKey:f.$H},...a.sessionToken&&{sessionToken:f.$H}}))(a.roleCredentials)}});var o=c(22232),p=c(28125);let q=async(a,b)=>{let c,d=(0,p.l)(a,b),e=(0,f.Tj)({},f.eU,{[E]:a[A]});d.bp("/federation/credentials");let g=(0,f.Tj)({[D]:[,(0,f.Y0)(a[C],"roleName")],[B]:[,(0,f.Y0)(a[z],"accountId")]});return d.m("GET").h(e).q(g).b(c),d.build()},r=async(a,b)=>{if(200!==a.statusCode&&a.statusCode>=300)return s(a,b);let c=(0,f.Tj)({$metadata:y(a)}),d=(0,f.Y0)((0,f.Xk)(await (0,o.Y2)(a.body,b)),"body");return Object.assign(c,(0,f.s)(d,{roleCredentials:f.Ss})),c},s=async(a,b)=>{let c={...a,body:await (0,o.CG)(a.body,b)},d=(0,o.cJ)(a,c.body);switch(d){case"InvalidRequestException":case"com.amazonaws.sso#InvalidRequestException":throw await u(c,b);case"ResourceNotFoundException":case"com.amazonaws.sso#ResourceNotFoundException":throw await v(c,b);case"TooManyRequestsException":case"com.amazonaws.sso#TooManyRequestsException":throw await w(c,b);case"UnauthorizedException":case"com.amazonaws.sso#UnauthorizedException":throw await x(c,b);default:return t({output:a,parsedBody:c.body,errorCode:d})}},t=(0,f.jr)(h),u=async(a,b)=>{let c=(0,f.Tj)({}),d=a.body;Object.assign(c,(0,f.s)(d,{message:f.lK}));let e=new i({$metadata:y(a),...c});return(0,f.Mw)(e,a.body)},v=async(a,b)=>{let c=(0,f.Tj)({}),d=a.body;Object.assign(c,(0,f.s)(d,{message:f.lK}));let e=new j({$metadata:y(a),...c});return(0,f.Mw)(e,a.body)},w=async(a,b)=>{let c=(0,f.Tj)({}),d=a.body;Object.assign(c,(0,f.s)(d,{message:f.lK}));let e=new k({$metadata:y(a),...c});return(0,f.Mw)(e,a.body)},x=async(a,b)=>{let c=(0,f.Tj)({}),d=a.body;Object.assign(c,(0,f.s)(d,{message:f.lK}));let e=new l({$metadata:y(a),...c});return(0,f.Mw)(e,a.body)},y=a=>({httpStatusCode:a.statusCode,requestId:a.headers["x-amzn-requestid"]??a.headers["x-amzn-request-id"]??a.headers["x-amz-request-id"],extendedRequestId:a.headers["x-amz-id-2"],cfId:a.headers["x-amz-cf-id"]}),z="accountId",A="accessToken",B="account_id",C="roleName",D="role_name",E="x-amz-sso_bearer_token";class F extends f.uB.classBuilder().ep(g).m(function(a,b,c,f){return[(0,e.TM)(c,this.serialize,this.deserialize),(0,d.rD)(c,a.getEndpointParameterInstructions())]}).s("SWBPortalService","GetRoleCredentials",{}).n("SSOClient","GetRoleCredentialsCommand").f(m,n).ser(q).de(r).build(){}var G=c(36576),H=c(43208),I=c(22218),J=c(59883),K=c(6284),L=c(7821),M=c(60043),N=c(18154),O=c(68270),P=c(71508),Q=c(78577),R=c(90378);let S=async(a,b,c)=>({operation:(0,R.u)(b).operation,region:await (0,R.t)(a.region)()||(()=>{throw Error("expected `region` to be configured for `aws.auth#sigv4`")})()}),T=a=>{let b=[];switch(a.operation){case"GetRoleCredentials":case"ListAccountRoles":case"ListAccounts":case"Logout":b.push({schemeId:"smithy.api#noAuth"});break;default:b.push({schemeId:"aws.auth#sigv4",signingProperties:{name:"awsssoportal",region:a.region},propertiesExtractor:(a,b)=>({signingProperties:{config:a,context:b}})})}return b},U={rE:"3.855.0"};var V=c(95941),W=c(30438),X=c(54285),Y=c(5334),Z=c(1226),$=c(66426),_=c(11396),aa=c(75843),ab=c(75089),ac=c(4553),ad=c(68378),ae=c(139),af=c(37979),ag=c(92171),ah=c(90599);let ai="required",aj="argv",ak="isSet",al="booleanEquals",am="error",an="endpoint",ao="tree",ap="PartitionResult",aq="getAttr",ar={[ai]:!1,type:"String"},as={[ai]:!0,default:!1,type:"Boolean"},at={ref:"Endpoint"},au={fn:al,[aj]:[{ref:"UseFIPS"},!0]},av={fn:al,[aj]:[{ref:"UseDualStack"},!0]},aw={},ax={fn:aq,[aj]:[{ref:ap},"supportsFIPS"]},ay={ref:ap},az={fn:al,[aj]:[!0,{fn:aq,[aj]:[ay,"supportsDualStack"]}]},aA=[au],aB=[av],aC=[{ref:"Region"}],aD={version:"1.0",parameters:{Region:ar,UseDualStack:as,UseFIPS:as,Endpoint:ar},rules:[{conditions:[{fn:ak,[aj]:[at]}],rules:[{conditions:aA,error:"Invalid Configuration: FIPS and custom endpoint are not supported",type:am},{conditions:aB,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",type:am},{endpoint:{url:at,properties:aw,headers:aw},type:an}],type:ao},{conditions:[{fn:ak,[aj]:aC}],rules:[{conditions:[{fn:"aws.partition",[aj]:aC,assign:ap}],rules:[{conditions:[au,av],rules:[{conditions:[{fn:al,[aj]:[!0,ax]},az],rules:[{endpoint:{url:"https://portal.sso-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:aw,headers:aw},type:an}],type:ao},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",type:am}],type:ao},{conditions:aA,rules:[{conditions:[{fn:al,[aj]:[ax,!0]}],rules:[{conditions:[{fn:"stringEquals",[aj]:[{fn:aq,[aj]:[ay,"name"]},"aws-us-gov"]}],endpoint:{url:"https://portal.sso.{Region}.amazonaws.com",properties:aw,headers:aw},type:an},{endpoint:{url:"https://portal.sso-fips.{Region}.{PartitionResult#dnsSuffix}",properties:aw,headers:aw},type:an}],type:ao},{error:"FIPS is enabled but this partition does not support FIPS",type:am}],type:ao},{conditions:aB,rules:[{conditions:[az],rules:[{endpoint:{url:"https://portal.sso.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:aw,headers:aw},type:an}],type:ao},{error:"DualStack is enabled but this partition does not support DualStack",type:am}],type:ao},{endpoint:{url:"https://portal.sso.{Region}.{PartitionResult#dnsSuffix}",properties:aw,headers:aw},type:an}],type:ao}],type:ao},{error:"Invalid Configuration: Missing Region",type:am}]},aE=new ah.kS({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS"]}),aF=(a,b={})=>aE.get(a,()=>(0,ah.sO)(aD,{endpointParams:a,logger:b.logger}));ah.mw.aws=ag.UF;var aG=c(52262),aH=c(41352),aI=c(31734);class aJ extends f.Kj{config;constructor(...[a]){let b=(a=>{(0,f.I9)(process.version);let b=(0,aG.I)(a),c=()=>b().then(f.lT),d=(a=>({apiVersion:"2019-06-10",base64Decoder:a?.base64Decoder??ae.E,base64Encoder:a?.base64Encoder??ae.n,disableHostPrefix:a?.disableHostPrefix??!1,endpointProvider:a?.endpointProvider??aF,extensions:a?.extensions??[],httpAuthSchemeProvider:a?.httpAuthSchemeProvider??T,httpAuthSchemes:a?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:a=>a.getIdentityProvider("aws.auth#sigv4"),signer:new ab.f2},{schemeId:"smithy.api#noAuth",identityProvider:a=>a.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new ac.m}],logger:a?.logger??new f.N4,serviceId:a?.serviceId??"SSO",urlParser:a?.urlParser??ad.D,utf8Decoder:a?.utf8Decoder??af.ar,utf8Encoder:a?.utf8Encoder??af.Pq}))(a);(0,V.I)(process.version);let e={profile:a?.profile,logger:d.logger};return{...d,...a,runtime:"node",defaultsMode:b,authSchemePreference:a?.authSchemePreference??(0,Z.Z)(W.$,e),bodyLengthChecker:a?.bodyLengthChecker??_.n,defaultUserAgentProvider:a?.defaultUserAgentProvider??(0,X.pf)({serviceId:d.serviceId,clientVersion:U.rE}),maxAttempts:a?.maxAttempts??(0,Z.Z)(P.qs,a),region:a?.region??(0,Z.Z)(K.GG,{...K.zH,...e}),requestHandler:$.$c.create(a?.requestHandler??c),retryMode:a?.retryMode??(0,Z.Z)({...P.kN,default:async()=>(await c()).retryMode||aa.L0},a),sha256:a?.sha256??Y.V.bind(null,"sha256"),streamCollector:a?.streamCollector??$.kv,useDualstackEndpoint:a?.useDualstackEndpoint??(0,Z.Z)(K.e$,e),useFipsEndpoint:a?.useFipsEndpoint??(0,Z.Z)(K.Ko,e),userAgentAppId:a?.userAgentAppId??(0,Z.Z)(X.hV,e)}})(a||{});super(b),this.initConfig=b;let c=(a=>Object.assign(a,{useDualstackEndpoint:a.useDualstackEndpoint??!1,useFipsEndpoint:a.useFipsEndpoint??!1,defaultSigningName:"awsssoportal"}))(b),e=(0,J.Dc)(c),g=(0,P.$z)(e),h=(0,K.TD)(g),i=(0,G.OV)(h),j=((a,b)=>{let c=Object.assign((0,aH.Rq)(a),(0,f.xA)(a),(0,aI.eS)(a),(a=>{let b=a.httpAuthSchemes,c=a.httpAuthSchemeProvider,d=a.credentials;return{setHttpAuthScheme(a){let c=b.findIndex(b=>b.schemeId===a.schemeId);-1===c?b.push(a):b.splice(c,1,a)},httpAuthSchemes:()=>b,setHttpAuthSchemeProvider(a){c=a},httpAuthSchemeProvider:()=>c,setCredentials(a){d=a},credentials:()=>d}})(a));return b.forEach(a=>a.configure(c)),Object.assign(a,(0,aH.$3)(c),(0,f.uv)(c),(0,aI.jt)(c),(a=>({httpAuthSchemes:a.httpAuthSchemes(),httpAuthSchemeProvider:a.httpAuthSchemeProvider(),credentials:a.credentials()}))(c))})((a=>Object.assign((0,Q.h)(a),{authSchemePreference:(0,R.t)(a.authSchemePreference??[])}))((0,d.Co)(i)),a?.extensions||[]);this.config=j,this.middlewareStack.use((0,J.sM)(this.config)),this.middlewareStack.use((0,P.ey)(this.config)),this.middlewareStack.use((0,O.vK)(this.config)),this.middlewareStack.use((0,G.TC)(this.config)),this.middlewareStack.use((0,H.Y7)(this.config)),this.middlewareStack.use((0,I.n4)(this.config)),this.middlewareStack.use((0,L.w)(this.config,{httpAuthSchemeParametersProvider:S,identityProviderConfigProvider:async a=>new M.h({"aws.auth#sigv4":a.credentials})})),this.middlewareStack.use((0,N.l)(this.config))}destroy(){super.destroy()}}},70525:(a,b,c)=>{c.d(b,{w:()=>e});var d=c(35639);let e=(a,b)=>(0,d.Px)(a,b).then(a=>b.utf8Encoder(a))}};