import type { CollectionConfig } from 'payload'

export const EducationalResources: CollectionConfig = {
  slug: 'educational-resources',
  dbName: 'edu_resources', // Shorter database name
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'type', 'subject', 'ageGroup', 'status'],
    group: 'Content',
  },
  access: {
    read: () => true,
    create: ({ req: { user } }) => {
      return (user as unknown as { role: string })?.role === 'content-creator' || (user as unknown as { role: string })?.role === 'admin'
    },
    update: ({ req: { user } }) => {
      return (user as unknown as { role: string })?.role === 'content-creator' || (user as unknown as { role: string })?.role === 'admin'
    },
    delete: ({ req: { user } }) => {
      return (user as unknown as { role: string })?.role === 'admin'
    },
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      admin: {
        description: 'Title of the educational resource',
      },
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
      hooks: {
        beforeValidate: [
          ({ value, data }) => {
            if (!value && data?.title) {
              return data.title
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/(^-|-$)/g, '')
            }
            return value
          },
        ],
      },
    },
    {
      name: 'description',
      type: 'textarea',
      required: true,
      admin: {
        description: 'Brief description of the resource',
      },
    },
    {
      name: 'type',
      type: 'select',
      required: true,
      options: [
        { label: 'Tutorial', value: 'tutorial' },
        { label: 'Guide', value: 'guide' },
        { label: 'Reference Sheet', value: 'reference' },
        { label: 'Video Lesson', value: 'video' },
        { label: 'Interactive Activity', value: 'interactive' },
        { label: 'Worksheet', value: 'worksheet' },
        { label: 'Tips & Tricks', value: 'tips' },
      ],
    },
    {
      name: 'subject',
      type: 'select',
      required: true,
      hasMany: true,
      options: [
        { label: 'Art Techniques', value: 'art-techniques' },
        { label: 'Color Theory', value: 'color-theory' },
        { label: 'Drawing Basics', value: 'drawing-basics' },
        { label: 'Creative Writing', value: 'creative-writing' },
        { label: 'Storytelling', value: 'storytelling' },
        { label: 'Music Theory', value: 'music-theory' },
        { label: 'Coding Basics', value: 'coding-basics' },
        { label: 'Game Design', value: 'game-design' },
        { label: 'Video Creation', value: 'video-creation' },
        { label: 'Digital Art', value: 'digital-art' },
      ],
    },
    {
      name: 'ageGroup',
      type: 'select',
      required: true,
      hasMany: true,
      options: [
        { label: '6-8 years', value: '6-8' },
        { label: '9-11 years', value: '9-11' },
        { label: '12-14 years', value: '12-14' },
      ],
    },
    {
      name: 'content',
      type: 'textarea',
      required: true,
      admin: {
        description: 'Main content of the educational resource',
        rows: 10,
      },
    },
    {
      name: 'media',
      type: 'array',
      fields: [
        {
          name: 'file',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
        {
          name: 'caption',
          type: 'text',
        },
        {
          name: 'type',
          type: 'select',
          options: [
            { label: 'Main Image', value: 'main' },
            { label: 'Step Image', value: 'step' },
            { label: 'Example', value: 'example' },
            { label: 'Video', value: 'video' },
            { label: 'Audio', value: 'audio' },
            { label: 'Downloadable', value: 'download' },
          ],
          required: true,
        },
      ],
    },
    {
      name: 'downloadableFiles',
      type: 'array',
      dbName: 'downloads', // Shorter database name
      fields: [
        {
          name: 'file',
          type: 'upload',
          relationTo: 'media',
          required: true,
        },
        {
          name: 'title',
          type: 'text',
          required: true,
        },
        {
          name: 'description',
          type: 'text',
        },
        {
          name: 'fileType',
          type: 'select',
          dbName: 'file_type', // Shorter database name
          options: [
            { label: 'PDF Worksheet', value: 'pdf' },
            { label: 'Template', value: 'template' },
            { label: 'Reference Chart', value: 'chart' },
            { label: 'Audio File', value: 'audio' },
            { label: 'Video File', value: 'video' },
          ],
        },
      ],
      admin: {
        description: 'Files that children/parents can download',
      },
    },
    {
      name: 'relatedChallenges',
      type: 'relationship',
      relationTo: 'challenges',
      hasMany: true,
      admin: {
        description: 'Challenges that use this educational resource',
      },
    },
    {
      name: 'prerequisites',
      type: 'array',
      fields: [
        {
          name: 'skill',
          type: 'text',
          required: true,
        },
        {
          name: 'level',
          type: 'select',
          options: [
            { label: 'Beginner', value: 'beginner' },
            { label: 'Intermediate', value: 'intermediate' },
            { label: 'Advanced', value: 'advanced' },
          ],
        },
      ],
      admin: {
        description: 'Skills children should have before using this resource',
      },
    },
    {
      name: 'learningOutcomes',
      type: 'array',
      required: true,
      fields: [
        {
          name: 'outcome',
          type: 'text',
          required: true,
        },
      ],
      admin: {
        description: 'What children will learn from this resource',
      },
    },
    {
      name: 'estimatedReadTime',
      type: 'number',
      admin: {
        description: 'Estimated reading/viewing time in minutes',
      },
    },
    {
      name: 'difficulty',
      type: 'select',
      required: true,
      options: [
        { label: 'Beginner', value: 'beginner' },
        { label: 'Intermediate', value: 'intermediate' },
        { label: 'Advanced', value: 'advanced' },
      ],
    },
    {
      name: 'subscriptionTier',
      type: 'select',
      required: true,
      options: [
        { label: 'Free', value: 'free' },
        { label: 'Premium', value: 'premium' },
      ],
      defaultValue: 'free',
    },
    {
      name: 'featured',
      type: 'checkbox',
      defaultValue: false,
    },
    {
      name: 'tags',
      type: 'array',
      fields: [
        {
          name: 'tag',
          type: 'text',
          required: true,
        },
      ],
      admin: {
        description: 'Tags for better searchability',
      },
    },
    {
      name: 'status',
      type: 'select',
      required: true,
      options: [
        { label: 'Draft', value: 'draft' },
        { label: 'Under Review', value: 'review' },
        { label: 'Published', value: 'published' },
        { label: 'Archived', value: 'archived' },
      ],
      defaultValue: 'draft',
    },
    {
      name: 'publishedAt',
      type: 'date',
      admin: {
        condition: (data) => data.status === 'published',
      },
    },
    {
      name: 'createdBy',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        readOnly: true,
      },
    },
  ],
  hooks: {
    beforeChange: [
      ({ data, operation, req }) => {
        if (data.status === 'published' && !data.publishedAt) {
          data.publishedAt = new Date()
        }
        
        if (operation === 'create' && req.user) {
          data.createdBy = req.user.id
        }
        
        return data
      },
    ],
  },
  versions: {
    drafts: true,
    maxPerDoc: 5,
  },
}
