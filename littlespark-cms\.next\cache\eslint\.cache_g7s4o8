[{"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(frontend)\\layout.tsx": "1", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(frontend)\\page.tsx": "2", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\importMap.js": "3", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\[[...segments]]\\not-found.tsx": "4", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\[[...segments]]\\page.tsx": "5", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\api\\graphql\\route.ts": "6", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\api\\graphql-playground\\route.ts": "7", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\api\\[...slug]\\route.ts": "8", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\layout.tsx": "9", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\my-route\\route.ts": "10", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\Challenges.ts": "11", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\EducationalResources.ts": "12", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\Media.ts": "13", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\StoryTemplates.ts": "14", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\Users.ts": "15", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\payload-types.ts": "16", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\payload.config.ts": "17", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\dashboard\\page.tsx": "18", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\sync\\page.tsx": "19", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\sync\\sync.js": "20", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\sync-button.js": "21", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\auth\\verify\\route.ts": "22", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\sync\\challenges\\route.ts": "23", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\sync\\from-main-app\\route.ts": "24", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\sync\\users\\route.ts": "25", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\test-data\\route.ts": "26", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\components\\AdminHeader.tsx": "27", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\components\\AdminOnly.tsx": "28", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\components\\SyncButton.tsx": "29", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\components\\SyncDashboard.tsx": "30", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\middleware\\adminAuth.ts": "31", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\404.tsx": "32", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\500.tsx": "33"}, {"size": 400, "mtime": 1753804502000, "results": "34", "hashOfConfig": "35"}, {"size": 1820, "mtime": 1753915579431, "results": "36", "hashOfConfig": "35"}, {"size": 208, "mtime": 1753910933648, "results": "37", "hashOfConfig": "38"}, {"size": 731, "mtime": 1753804502000, "results": "39", "hashOfConfig": "35"}, {"size": 715, "mtime": 1753804502000, "results": "40", "hashOfConfig": "35"}, {"size": 315, "mtime": 1753804502000, "results": "41", "hashOfConfig": "35"}, {"size": 305, "mtime": 1753804502000, "results": "42", "hashOfConfig": "35"}, {"size": 550, "mtime": 1753804502000, "results": "43", "hashOfConfig": "35"}, {"size": 810, "mtime": 1753804502000, "results": "44", "hashOfConfig": "35"}, {"size": 1232, "mtime": 1753919017712, "results": "45", "hashOfConfig": "35"}, {"size": 9913, "mtime": 1753901513187, "results": "46", "hashOfConfig": "35"}, {"size": 8063, "mtime": 1753919049274, "results": "47", "hashOfConfig": "35"}, {"size": 255, "mtime": 1753804502000, "results": "48", "hashOfConfig": "35"}, {"size": 8010, "mtime": 1753919069995, "results": "49", "hashOfConfig": "35"}, {"size": 2673, "mtime": 1753808932736, "results": "50", "hashOfConfig": "35"}, {"size": 18696, "mtime": 1753902090582, "results": "51", "hashOfConfig": "35"}, {"size": 1432, "mtime": 1753937149435, "results": "52", "hashOfConfig": "35"}, {"size": 5979, "mtime": 1753918838540, "results": "53", "hashOfConfig": "35"}, {"size": 19153, "mtime": 1753918862767, "results": "54", "hashOfConfig": "35"}, {"size": 11152, "mtime": 1753901641768, "results": "55", "hashOfConfig": "38"}, {"size": 2069, "mtime": 1753904666750, "results": "56", "hashOfConfig": "38"}, {"size": 1993, "mtime": 1753918877449, "results": "57", "hashOfConfig": "35"}, {"size": 6541, "mtime": 1753919507884, "results": "58", "hashOfConfig": "35"}, {"size": 5715, "mtime": 1753918968187, "results": "59", "hashOfConfig": "35"}, {"size": 5807, "mtime": 1753918989752, "results": "60", "hashOfConfig": "35"}, {"size": 8392, "mtime": 1753917116420, "results": "61", "hashOfConfig": "35"}, {"size": 1259, "mtime": 1753904432940, "results": "62", "hashOfConfig": "35"}, {"size": 5084, "mtime": 1753919492361, "results": "63", "hashOfConfig": "35"}, {"size": 1030, "mtime": 1753903955858, "results": "64", "hashOfConfig": "35"}, {"size": 18485, "mtime": 1753919339258, "results": "65", "hashOfConfig": "35"}, {"size": 3339, "mtime": 1753919326311, "results": "66", "hashOfConfig": "35"}, {"size": 220, "mtime": 1753938196196, "results": "67", "hashOfConfig": "35"}, {"size": 206, "mtime": 1753938213148, "results": "68", "hashOfConfig": "35"}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "4girfc", {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1vu8xsk", {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(frontend)\\layout.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(frontend)\\page.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\importMap.js", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\[[...segments]]\\not-found.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\[[...segments]]\\page.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\api\\graphql\\route.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\api\\graphql-playground\\route.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\api\\[...slug]\\route.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\layout.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\my-route\\route.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\Challenges.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\EducationalResources.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\Media.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\StoryTemplates.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\Users.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\payload-types.ts", [], ["168", "169", "170", "171", "172"], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\payload.config.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\sync\\page.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\sync\\sync.js", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\sync-button.js", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\auth\\verify\\route.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\sync\\challenges\\route.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\sync\\from-main-app\\route.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\sync\\users\\route.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\test-data\\route.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\components\\AdminHeader.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\components\\AdminOnly.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\components\\SyncButton.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\components\\SyncDashboard.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\middleware\\adminAuth.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\404.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\500.tsx", [], [], {"ruleId": "173", "severity": 1, "message": "174", "line": 68, "column": 11, "nodeType": "175", "messageId": "176", "endLine": 68, "endColumn": 13, "suggestions": "177", "suppressions": "178"}, {"ruleId": "173", "severity": 1, "message": "174", "line": 79, "column": 21, "nodeType": "175", "messageId": "176", "endLine": 79, "endColumn": 23, "suggestions": "179", "suppressions": "180"}, {"ruleId": "173", "severity": 1, "message": "174", "line": 93, "column": 12, "nodeType": "175", "messageId": "176", "endLine": 93, "endColumn": 14, "suggestions": "181", "suppressions": "182"}, {"ruleId": "173", "severity": 1, "message": "174", "line": 94, "column": 18, "nodeType": "175", "messageId": "176", "endLine": 94, "endColumn": 20, "suggestions": "183", "suppressions": "184"}, {"ruleId": "173", "severity": 1, "message": "185", "line": 855, "column": 20, "nodeType": "186", "messageId": "187", "endLine": 855, "endColumn": 34, "suggestions": "188", "suppressions": "189"}, "@typescript-eslint/no-empty-object-type", "The `{}` (\"empty object\") type allows any non-nullish value, including literals like `0` and `\"\"`.\n- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.\n- If you want a type meaning \"any object\", you probably want `object` instead.\n- If you want a type meaning \"any value\", you probably want `unknown` instead.", "TSTypeLiteral", "noEmptyObject", ["190", "191"], ["192"], ["193", "194"], ["195"], ["196", "197"], ["198"], ["199", "200"], ["201"], "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["202"], ["203"], {"messageId": "204", "data": "205", "fix": "206", "desc": "207"}, {"messageId": "204", "data": "208", "fix": "209", "desc": "210"}, {"kind": "211", "justification": "212"}, {"messageId": "204", "data": "213", "fix": "214", "desc": "207"}, {"messageId": "204", "data": "215", "fix": "216", "desc": "210"}, {"kind": "211", "justification": "212"}, {"messageId": "204", "data": "217", "fix": "218", "desc": "207"}, {"messageId": "204", "data": "219", "fix": "220", "desc": "210"}, {"kind": "211", "justification": "212"}, {"messageId": "204", "data": "221", "fix": "222", "desc": "207"}, {"messageId": "204", "data": "223", "fix": "224", "desc": "210"}, {"kind": "211", "justification": "212"}, {"messageId": "225", "fix": "226", "desc": "227"}, {"kind": "211", "justification": "212"}, "replaceEmptyObjectType", {"replacement": "228"}, {"range": "229", "text": "228"}, "Replace `{}` with `object`.", {"replacement": "230"}, {"range": "231", "text": "230"}, "Replace `{}` with `unknown`.", "directive", "", {"replacement": "228"}, {"range": "232", "text": "228"}, {"replacement": "230"}, {"range": "233", "text": "230"}, {"replacement": "228"}, {"range": "234", "text": "228"}, {"replacement": "230"}, {"range": "235", "text": "230"}, {"replacement": "228"}, {"range": "236", "text": "228"}, {"replacement": "230"}, {"range": "237", "text": "230"}, "replaceEmptyInterfaceWithSuper", {"range": "238", "text": "239"}, "Replace empty interface with a type alias.", "object", [1506, 1508], "unknown", [1506, 1508], [1847, 1849], [1847, 1849], [2567, 2569], [2567, 2569], [2588, 2590], [2588, 2590], [18652, 18694], "type GeneratedTypes = Config"]