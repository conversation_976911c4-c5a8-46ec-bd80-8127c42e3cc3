[{"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(frontend)\\layout.tsx": "1", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(frontend)\\page.tsx": "2", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\importMap.js": "3", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\[[...segments]]\\not-found.tsx": "4", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\[[...segments]]\\page.tsx": "5", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\api\\graphql\\route.ts": "6", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\api\\graphql-playground\\route.ts": "7", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\api\\[...slug]\\route.ts": "8", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\layout.tsx": "9", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\my-route\\route.ts": "10", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\Challenges.ts": "11", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\EducationalResources.ts": "12", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\Media.ts": "13", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\StoryTemplates.ts": "14", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\Users.ts": "15", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\payload-types.ts": "16", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\payload.config.ts": "17", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\dashboard\\page.tsx": "18", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\sync\\page.tsx": "19", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\sync\\sync.js": "20", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\sync-button.js": "21", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\auth\\verify\\route.ts": "22", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\sync\\challenges\\route.ts": "23", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\sync\\from-main-app\\route.ts": "24", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\sync\\users\\route.ts": "25", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\test-data\\route.ts": "26", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\components\\AdminHeader.tsx": "27", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\components\\AdminOnly.tsx": "28", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\components\\SyncButton.tsx": "29", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\components\\SyncDashboard.tsx": "30", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\middleware\\adminAuth.ts": "31"}, {"size": 400, "mtime": 1753804502000, "results": "32", "hashOfConfig": "33"}, {"size": 1816, "mtime": 1753804502000, "results": "34", "hashOfConfig": "33"}, {"size": 208, "mtime": 1753910933648, "results": "35", "hashOfConfig": "36"}, {"size": 731, "mtime": 1753804502000, "results": "37", "hashOfConfig": "33"}, {"size": 715, "mtime": 1753804502000, "results": "38", "hashOfConfig": "33"}, {"size": 315, "mtime": 1753804502000, "results": "39", "hashOfConfig": "33"}, {"size": 305, "mtime": 1753804502000, "results": "40", "hashOfConfig": "33"}, {"size": 550, "mtime": 1753804502000, "results": "41", "hashOfConfig": "33"}, {"size": 810, "mtime": 1753804502000, "results": "42", "hashOfConfig": "33"}, {"size": 1205, "mtime": 1753821701056, "results": "43", "hashOfConfig": "33"}, {"size": 9913, "mtime": 1753901513187, "results": "44", "hashOfConfig": "33"}, {"size": 7943, "mtime": 1753815174835, "results": "45", "hashOfConfig": "33"}, {"size": 255, "mtime": 1753804502000, "results": "46", "hashOfConfig": "33"}, {"size": 7890, "mtime": 1753815120952, "results": "47", "hashOfConfig": "33"}, {"size": 2673, "mtime": 1753808932736, "results": "48", "hashOfConfig": "33"}, {"size": 18696, "mtime": 1753902090582, "results": "49", "hashOfConfig": "33"}, {"size": 1410, "mtime": 1753910925952, "results": "50", "hashOfConfig": "33"}, {"size": 5930, "mtime": 1753904514696, "results": "51", "hashOfConfig": "33"}, {"size": 18968, "mtime": 1753906100439, "results": "52", "hashOfConfig": "33"}, {"size": 11152, "mtime": 1753901641768, "results": "53", "hashOfConfig": "36"}, {"size": 2069, "mtime": 1753904666750, "results": "54", "hashOfConfig": "36"}, {"size": 1910, "mtime": 1753901812266, "results": "55", "hashOfConfig": "33"}, {"size": 6559, "mtime": 1753909994674, "results": "56", "hashOfConfig": "33"}, {"size": 5982, "mtime": 1753901763934, "results": "57", "hashOfConfig": "33"}, {"size": 5435, "mtime": 1753910240873, "results": "58", "hashOfConfig": "33"}, {"size": 8336, "mtime": 1753907068796, "results": "59", "hashOfConfig": "33"}, {"size": 1259, "mtime": 1753904432940, "results": "60", "hashOfConfig": "33"}, {"size": 5073, "mtime": 1753901797589, "results": "61", "hashOfConfig": "33"}, {"size": 1030, "mtime": 1753903955858, "results": "62", "hashOfConfig": "33"}, {"size": 16601, "mtime": 1753911103304, "results": "63", "hashOfConfig": "33"}, {"size": 3103, "mtime": 1753901675718, "results": "64", "hashOfConfig": "33"}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "4girfc", {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1vu8xsk", {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 2, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 42, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(frontend)\\layout.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(frontend)\\page.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\importMap.js", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\[[...segments]]\\not-found.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\[[...segments]]\\page.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\api\\graphql\\route.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\api\\graphql-playground\\route.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\api\\[...slug]\\route.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\layout.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\my-route\\route.ts", ["158"], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\Challenges.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\EducationalResources.ts", ["159", "160", "161", "162", "163"], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\Media.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\StoryTemplates.ts", ["164", "165", "166", "167", "168"], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\Users.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\payload-types.ts", [], ["169", "170", "171", "172", "173"], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\payload.config.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\dashboard\\page.tsx", ["174", "175", "176"], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\sync\\page.tsx", ["177"], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\sync\\sync.js", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\sync-button.js", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\auth\\verify\\route.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\sync\\challenges\\route.ts", ["178", "179", "180"], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\sync\\from-main-app\\route.ts", ["181"], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\sync\\users\\route.ts", ["182", "183"], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\test-data\\route.ts", ["184", "185", "186"], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\components\\AdminHeader.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\components\\AdminOnly.tsx", ["187", "188", "189", "190"], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\components\\SyncButton.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\components\\SyncDashboard.tsx", ["191", "192", "193", "194", "195", "196", "197", "198", "199", "200", "201", "202", "203", "204", "205", "206", "207", "208", "209", "210", "211", "212", "213", "214", "215", "216", "217", "218", "219", "220", "221", "222", "223", "224", "225", "226", "227", "228", "229", "230", "231", "232"], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\middleware\\adminAuth.ts", ["233", "234", "235", "236"], [], {"ruleId": "237", "severity": 1, "message": "238", "line": 4, "column": 27, "nodeType": null, "messageId": "239", "endLine": 4, "endColumn": 34}, {"ruleId": "240", "severity": 1, "message": "241", "line": 14, "column": 23, "nodeType": "242", "messageId": "243", "endLine": 14, "endColumn": 26, "suggestions": "244"}, {"ruleId": "240", "severity": 1, "message": "241", "line": 14, "column": 68, "nodeType": "242", "messageId": "243", "endLine": 14, "endColumn": 71, "suggestions": "245"}, {"ruleId": "240", "severity": 1, "message": "241", "line": 17, "column": 23, "nodeType": "242", "messageId": "243", "endLine": 17, "endColumn": 26, "suggestions": "246"}, {"ruleId": "240", "severity": 1, "message": "241", "line": 17, "column": 68, "nodeType": "242", "messageId": "243", "endLine": 17, "endColumn": 71, "suggestions": "247"}, {"ruleId": "240", "severity": 1, "message": "241", "line": 20, "column": 23, "nodeType": "242", "messageId": "243", "endLine": 20, "endColumn": 26, "suggestions": "248"}, {"ruleId": "240", "severity": 1, "message": "241", "line": 14, "column": 23, "nodeType": "242", "messageId": "243", "endLine": 14, "endColumn": 26, "suggestions": "249"}, {"ruleId": "240", "severity": 1, "message": "241", "line": 14, "column": 68, "nodeType": "242", "messageId": "243", "endLine": 14, "endColumn": 71, "suggestions": "250"}, {"ruleId": "240", "severity": 1, "message": "241", "line": 17, "column": 23, "nodeType": "242", "messageId": "243", "endLine": 17, "endColumn": 26, "suggestions": "251"}, {"ruleId": "240", "severity": 1, "message": "241", "line": 17, "column": 68, "nodeType": "242", "messageId": "243", "endLine": 17, "endColumn": 71, "suggestions": "252"}, {"ruleId": "240", "severity": 1, "message": "241", "line": 20, "column": 23, "nodeType": "242", "messageId": "243", "endLine": 20, "endColumn": 26, "suggestions": "253"}, {"ruleId": "254", "severity": 1, "message": "255", "line": 68, "column": 11, "nodeType": "256", "messageId": "257", "endLine": 68, "endColumn": 13, "suggestions": "258", "suppressions": "259"}, {"ruleId": "254", "severity": 1, "message": "255", "line": 79, "column": 21, "nodeType": "256", "messageId": "257", "endLine": 79, "endColumn": 23, "suggestions": "260", "suppressions": "261"}, {"ruleId": "254", "severity": 1, "message": "255", "line": 93, "column": 12, "nodeType": "256", "messageId": "257", "endLine": 93, "endColumn": 14, "suggestions": "262", "suppressions": "263"}, {"ruleId": "254", "severity": 1, "message": "255", "line": 94, "column": 18, "nodeType": "256", "messageId": "257", "endLine": 94, "endColumn": 20, "suggestions": "264", "suppressions": "265"}, {"ruleId": "254", "severity": 1, "message": "266", "line": 855, "column": 20, "nodeType": "267", "messageId": "268", "endLine": 855, "endColumn": 34, "suggestions": "269", "suppressions": "270"}, {"ruleId": "271", "severity": 2, "message": "272", "line": 131, "column": 11, "nodeType": "273", "endLine": 142, "endColumn": 12}, {"ruleId": "271", "severity": 2, "message": "274", "line": 145, "column": 11, "nodeType": "273", "endLine": 156, "endColumn": 12}, {"ruleId": "271", "severity": 2, "message": "275", "line": 159, "column": 11, "nodeType": "273", "endLine": 170, "endColumn": 12}, {"ruleId": "276", "severity": 2, "message": "277", "line": 365, "column": 86, "nodeType": "278", "messageId": "279", "suggestions": "280"}, {"ruleId": "237", "severity": 1, "message": "238", "line": 9, "column": 28, "nodeType": null, "messageId": "239", "endLine": 9, "endColumn": 35}, {"ruleId": "237", "severity": 1, "message": "281", "line": 77, "column": 22, "nodeType": null, "messageId": "239", "endLine": 77, "endColumn": 31}, {"ruleId": "237", "severity": 1, "message": "238", "line": 135, "column": 27, "nodeType": null, "messageId": "239", "endLine": 135, "endColumn": 34}, {"ruleId": "237", "severity": 1, "message": "282", "line": 94, "column": 15, "nodeType": null, "messageId": "239", "endLine": 94, "endColumn": 27}, {"ruleId": "283", "severity": 2, "message": "284", "line": 53, "column": 9, "nodeType": "267", "messageId": "285", "endLine": 53, "endColumn": 21, "fix": "286"}, {"ruleId": "237", "severity": 1, "message": "238", "line": 120, "column": 27, "nodeType": null, "messageId": "239", "endLine": 120, "endColumn": 34}, {"ruleId": "237", "severity": 1, "message": "238", "line": 6, "column": 28, "nodeType": null, "messageId": "239", "endLine": 6, "endColumn": 35}, {"ruleId": "237", "severity": 1, "message": "282", "line": 160, "column": 15, "nodeType": null, "messageId": "239", "endLine": 160, "endColumn": 27}, {"ruleId": "237", "severity": 1, "message": "238", "line": 199, "column": 27, "nodeType": null, "messageId": "239", "endLine": 199, "endColumn": 34}, {"ruleId": "237", "severity": 1, "message": "287", "line": 20, "column": 49, "nodeType": null, "messageId": "239", "endLine": 20, "endColumn": 59}, {"ruleId": "288", "severity": 1, "message": "289", "line": 27, "column": 6, "nodeType": "290", "endLine": 27, "endColumn": 8, "suggestions": "291"}, {"ruleId": "283", "severity": 2, "message": "292", "line": 66, "column": 9, "nodeType": "267", "messageId": "285", "endLine": 66, "endColumn": 14, "fix": "293"}, {"ruleId": "283", "severity": 2, "message": "294", "line": 71, "column": 14, "nodeType": "267", "messageId": "285", "endLine": 71, "endColumn": 20, "fix": "295"}, {"ruleId": "296", "severity": 2, "message": "297", "line": 245, "column": 10, "nodeType": "298", "messageId": "299", "endLine": 245, "endColumn": 14}, {"ruleId": "296", "severity": 2, "message": "300", "line": 246, "column": 12, "nodeType": "298", "messageId": "299", "endLine": 246, "endColumn": 23}, {"ruleId": "296", "severity": 2, "message": "301", "line": 248, "column": 16, "nodeType": "298", "messageId": "299", "endLine": 248, "endColumn": 27}, {"ruleId": "296", "severity": 2, "message": "297", "line": 259, "column": 12, "nodeType": "298", "messageId": "299", "endLine": 259, "endColumn": 16}, {"ruleId": "296", "severity": 2, "message": "302", "line": 260, "column": 14, "nodeType": "298", "messageId": "299", "endLine": 260, "endColumn": 24}, {"ruleId": "296", "severity": 2, "message": "303", "line": 261, "column": 16, "nodeType": "298", "messageId": "299", "endLine": 261, "endColumn": 25}, {"ruleId": "296", "severity": 2, "message": "304", "line": 262, "column": 18, "nodeType": "298", "messageId": "299", "endLine": 262, "endColumn": 26}, {"ruleId": "296", "severity": 2, "message": "305", "line": 265, "column": 16, "nodeType": "298", "messageId": "299", "endLine": 265, "endColumn": 31}, {"ruleId": "296", "severity": 2, "message": "300", "line": 269, "column": 14, "nodeType": "298", "messageId": "299", "endLine": 269, "endColumn": 25}, {"ruleId": "296", "severity": 2, "message": "306", "line": 273, "column": 22, "nodeType": "298", "messageId": "299", "endLine": 273, "endColumn": 27}, {"ruleId": "296", "severity": 2, "message": "307", "line": 280, "column": 22, "nodeType": "298", "messageId": "299", "endLine": 280, "endColumn": 30}, {"ruleId": "296", "severity": 2, "message": "308", "line": 287, "column": 16, "nodeType": "298", "messageId": "299", "endLine": 287, "endColumn": 25}, {"ruleId": "296", "severity": 2, "message": "309", "line": 289, "column": 16, "nodeType": "298", "messageId": "299", "endLine": 289, "endColumn": 22}, {"ruleId": "296", "severity": 2, "message": "310", "line": 296, "column": 22, "nodeType": "298", "messageId": "299", "endLine": 296, "endColumn": 31}, {"ruleId": "296", "severity": 2, "message": "311", "line": 301, "column": 22, "nodeType": "298", "messageId": "299", "endLine": 301, "endColumn": 26}, {"ruleId": "296", "severity": 2, "message": "297", "line": 323, "column": 12, "nodeType": "298", "messageId": "299", "endLine": 323, "endColumn": 16}, {"ruleId": "296", "severity": 2, "message": "302", "line": 324, "column": 14, "nodeType": "298", "messageId": "299", "endLine": 324, "endColumn": 24}, {"ruleId": "296", "severity": 2, "message": "303", "line": 325, "column": 16, "nodeType": "298", "messageId": "299", "endLine": 325, "endColumn": 25}, {"ruleId": "296", "severity": 2, "message": "312", "line": 326, "column": 18, "nodeType": "298", "messageId": "299", "endLine": 326, "endColumn": 23}, {"ruleId": "296", "severity": 2, "message": "305", "line": 329, "column": 16, "nodeType": "298", "messageId": "299", "endLine": 329, "endColumn": 31}, {"ruleId": "296", "severity": 2, "message": "300", "line": 333, "column": 14, "nodeType": "298", "messageId": "299", "endLine": 333, "endColumn": 25}, {"ruleId": "296", "severity": 2, "message": "312", "line": 337, "column": 22, "nodeType": "298", "messageId": "299", "endLine": 337, "endColumn": 27}, {"ruleId": "296", "severity": 2, "message": "307", "line": 344, "column": 22, "nodeType": "298", "messageId": "299", "endLine": 344, "endColumn": 30}, {"ruleId": "296", "severity": 2, "message": "308", "line": 351, "column": 16, "nodeType": "298", "messageId": "299", "endLine": 351, "endColumn": 25}, {"ruleId": "296", "severity": 2, "message": "309", "line": 353, "column": 16, "nodeType": "298", "messageId": "299", "endLine": 353, "endColumn": 22}, {"ruleId": "296", "severity": 2, "message": "310", "line": 361, "column": 22, "nodeType": "298", "messageId": "299", "endLine": 361, "endColumn": 31}, {"ruleId": "296", "severity": 2, "message": "311", "line": 366, "column": 22, "nodeType": "298", "messageId": "299", "endLine": 366, "endColumn": 26}, {"ruleId": "296", "severity": 2, "message": "297", "line": 392, "column": 12, "nodeType": "298", "messageId": "299", "endLine": 392, "endColumn": 16}, {"ruleId": "296", "severity": 2, "message": "302", "line": 393, "column": 14, "nodeType": "298", "messageId": "299", "endLine": 393, "endColumn": 24}, {"ruleId": "296", "severity": 2, "message": "303", "line": 394, "column": 16, "nodeType": "298", "messageId": "299", "endLine": 394, "endColumn": 25}, {"ruleId": "296", "severity": 2, "message": "305", "line": 398, "column": 16, "nodeType": "298", "messageId": "299", "endLine": 398, "endColumn": 31}, {"ruleId": "296", "severity": 2, "message": "300", "line": 402, "column": 14, "nodeType": "298", "messageId": "299", "endLine": 402, "endColumn": 25}, {"ruleId": "296", "severity": 2, "message": "312", "line": 406, "column": 22, "nodeType": "298", "messageId": "299", "endLine": 406, "endColumn": 27}, {"ruleId": "296", "severity": 2, "message": "307", "line": 413, "column": 22, "nodeType": "298", "messageId": "299", "endLine": 413, "endColumn": 30}, {"ruleId": "296", "severity": 2, "message": "308", "line": 420, "column": 16, "nodeType": "298", "messageId": "299", "endLine": 420, "endColumn": 25}, {"ruleId": "296", "severity": 2, "message": "309", "line": 422, "column": 16, "nodeType": "298", "messageId": "299", "endLine": 422, "endColumn": 22}, {"ruleId": "296", "severity": 2, "message": "309", "line": 438, "column": 10, "nodeType": "298", "messageId": "299", "endLine": 438, "endColumn": 16}, {"ruleId": "296", "severity": 2, "message": "310", "line": 443, "column": 12, "nodeType": "298", "messageId": "299", "endLine": 443, "endColumn": 21}, {"ruleId": "296", "severity": 2, "message": "297", "line": 449, "column": 8, "nodeType": "298", "messageId": "299", "endLine": 449, "endColumn": 12}, {"ruleId": "296", "severity": 2, "message": "300", "line": 450, "column": 10, "nodeType": "298", "messageId": "299", "endLine": 450, "endColumn": 21}, {"ruleId": "296", "severity": 2, "message": "301", "line": 452, "column": 14, "nodeType": "298", "messageId": "299", "endLine": 452, "endColumn": 25}, {"ruleId": "276", "severity": 2, "message": "277", "line": 459, "column": 92, "nodeType": "278", "messageId": "279", "suggestions": "313"}, {"ruleId": "240", "severity": 1, "message": "241", "line": 7, "column": 10, "nodeType": "242", "messageId": "243", "endLine": 7, "endColumn": 13, "suggestions": "314"}, {"ruleId": "240", "severity": 1, "message": "241", "line": 94, "column": 37, "nodeType": "242", "messageId": "243", "endLine": 94, "endColumn": 40, "suggestions": "315"}, {"ruleId": "240", "severity": 1, "message": "241", "line": 113, "column": 38, "nodeType": "242", "messageId": "243", "endLine": 113, "endColumn": 41, "suggestions": "316"}, {"ruleId": "240", "severity": 1, "message": "241", "line": 113, "column": 69, "nodeType": "242", "messageId": "243", "endLine": 113, "endColumn": 72, "suggestions": "317"}, "@typescript-eslint/no-unused-vars", "'request' is defined but never used. Allowed unused args must match /^_/u.", "unusedVar", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["318", "319"], ["320", "321"], ["322", "323"], ["324", "325"], ["326", "327"], ["328", "329"], ["330", "331"], ["332", "333"], ["334", "335"], ["336", "337"], "@typescript-eslint/no-empty-object-type", "The `{}` (\"empty object\") type allows any non-nullish value, including literals like `0` and `\"\"`.\n- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.\n- If you want a type meaning \"any object\", you probably want `object` instead.\n- If you want a type meaning \"any value\", you probably want `unknown` instead.", "TSTypeLiteral", "noEmptyObject", ["338", "339"], ["340"], ["341", "342"], ["343"], ["344", "345"], ["346"], ["347", "348"], ["349"], "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["350"], ["351"], "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/admin/collections/challenges/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "JSXOpeningElement", "Do not use an `<a>` element to navigate to `/admin/collections/story-templates/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/admin/collections/educational-resources/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["352", "353", "354", "355"], "'jsonError' is defined but never used. Allowed unused caught errors must match /^(_|ignore)/u.", "'newChallenge' is assigned a value but never used. Allowed unused vars must match /^_/u.", "prefer-const", "'skippedCount' is never reassigned. Use 'const' instead.", "useConst", {"range": "356", "text": "357"}, "'permission' is assigned a value but never used. Allowed unused args must match /^_/u.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'checkUserPermissions'. Either include it or remove the dependency array.", "ArrayExpression", ["358"], "'token' is never reassigned. Use 'const' instead.", {"range": "359", "text": "360"}, "'cookie' is never reassigned. Use 'const' instead.", {"range": "361", "text": "362"}, "react/jsx-no-undef", "'Card' is not defined.", "JSXIdentifier", "undefined", "'CardContent' is not defined.", "'AlertCircle' is not defined.", "'CardHeader' is not defined.", "'CardTitle' is not defined.", "'FileText' is not defined.", "'CardDescription' is not defined.", "'Cloud' is not defined.", "'Database' is not defined.", "'Separator' is not defined.", "'Button' is not defined.", "'RefreshCw' is not defined.", "'Sync' is not defined.", "'Users' is not defined.", ["363", "364", "365", "366"], ["367", "368"], ["369", "370"], ["371", "372"], ["373", "374"], {"messageId": "375", "fix": "376", "desc": "377"}, {"messageId": "378", "fix": "379", "desc": "380"}, {"messageId": "375", "fix": "381", "desc": "377"}, {"messageId": "378", "fix": "382", "desc": "380"}, {"messageId": "375", "fix": "383", "desc": "377"}, {"messageId": "378", "fix": "384", "desc": "380"}, {"messageId": "375", "fix": "385", "desc": "377"}, {"messageId": "378", "fix": "386", "desc": "380"}, {"messageId": "375", "fix": "387", "desc": "377"}, {"messageId": "378", "fix": "388", "desc": "380"}, {"messageId": "375", "fix": "389", "desc": "377"}, {"messageId": "378", "fix": "390", "desc": "380"}, {"messageId": "375", "fix": "391", "desc": "377"}, {"messageId": "378", "fix": "392", "desc": "380"}, {"messageId": "375", "fix": "393", "desc": "377"}, {"messageId": "378", "fix": "394", "desc": "380"}, {"messageId": "375", "fix": "395", "desc": "377"}, {"messageId": "378", "fix": "396", "desc": "380"}, {"messageId": "375", "fix": "397", "desc": "377"}, {"messageId": "378", "fix": "398", "desc": "380"}, {"messageId": "399", "data": "400", "fix": "401", "desc": "402"}, {"messageId": "399", "data": "403", "fix": "404", "desc": "405"}, {"kind": "406", "justification": "407"}, {"messageId": "399", "data": "408", "fix": "409", "desc": "402"}, {"messageId": "399", "data": "410", "fix": "411", "desc": "405"}, {"kind": "406", "justification": "407"}, {"messageId": "399", "data": "412", "fix": "413", "desc": "402"}, {"messageId": "399", "data": "414", "fix": "415", "desc": "405"}, {"kind": "406", "justification": "407"}, {"messageId": "399", "data": "416", "fix": "417", "desc": "402"}, {"messageId": "399", "data": "418", "fix": "419", "desc": "405"}, {"kind": "406", "justification": "407"}, {"messageId": "420", "fix": "421", "desc": "422"}, {"kind": "406", "justification": "407"}, {"messageId": "423", "data": "424", "fix": "425", "desc": "426"}, {"messageId": "423", "data": "427", "fix": "428", "desc": "429"}, {"messageId": "423", "data": "430", "fix": "431", "desc": "432"}, {"messageId": "423", "data": "433", "fix": "434", "desc": "435"}, [1484, 1505], "const skippedCount = 0;", {"desc": "436", "fix": "437"}, [1742, 1792], "const token = localStorage.getItem('payload-token');", [1911, 1921], "const cookie", {"messageId": "423", "data": "438", "fix": "439", "desc": "426"}, {"messageId": "423", "data": "440", "fix": "441", "desc": "429"}, {"messageId": "423", "data": "442", "fix": "443", "desc": "432"}, {"messageId": "423", "data": "444", "fix": "445", "desc": "435"}, {"messageId": "375", "fix": "446", "desc": "377"}, {"messageId": "378", "fix": "447", "desc": "380"}, {"messageId": "375", "fix": "448", "desc": "377"}, {"messageId": "378", "fix": "449", "desc": "380"}, {"messageId": "375", "fix": "450", "desc": "377"}, {"messageId": "378", "fix": "451", "desc": "380"}, {"messageId": "375", "fix": "452", "desc": "377"}, {"messageId": "378", "fix": "453", "desc": "380"}, "suggestUnknown", {"range": "454", "text": "455"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "456", "text": "457"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "458", "text": "455"}, {"range": "459", "text": "457"}, {"range": "460", "text": "455"}, {"range": "461", "text": "457"}, {"range": "462", "text": "455"}, {"range": "463", "text": "457"}, {"range": "464", "text": "455"}, {"range": "465", "text": "457"}, {"range": "466", "text": "455"}, {"range": "467", "text": "457"}, {"range": "468", "text": "455"}, {"range": "469", "text": "457"}, {"range": "470", "text": "455"}, {"range": "471", "text": "457"}, {"range": "472", "text": "455"}, {"range": "473", "text": "457"}, {"range": "474", "text": "455"}, {"range": "475", "text": "457"}, "replaceEmptyObjectType", {"replacement": "476"}, {"range": "477", "text": "476"}, "Replace `{}` with `object`.", {"replacement": "455"}, {"range": "478", "text": "455"}, "Replace `{}` with `unknown`.", "directive", "", {"replacement": "476"}, {"range": "479", "text": "476"}, {"replacement": "455"}, {"range": "480", "text": "455"}, {"replacement": "476"}, {"range": "481", "text": "476"}, {"replacement": "455"}, {"range": "482", "text": "455"}, {"replacement": "476"}, {"range": "483", "text": "476"}, {"replacement": "455"}, {"range": "484", "text": "455"}, "replaceEmptyInterfaceWithSuper", {"range": "485", "text": "486"}, "Replace empty interface with a type alias.", "replaceWithAlt", {"alt": "487"}, {"range": "488", "text": "489"}, "Replace with `&apos;`.", {"alt": "490"}, {"range": "491", "text": "492"}, "Replace with `&lsquo;`.", {"alt": "493"}, {"range": "494", "text": "495"}, "Replace with `&#39;`.", {"alt": "496"}, {"range": "497", "text": "498"}, "Replace with `&rsquo;`.", "Update the dependencies array to be: [checkUserPermissions]", {"range": "499", "text": "500"}, {"alt": "487"}, {"range": "501", "text": "489"}, {"alt": "490"}, {"range": "502", "text": "492"}, {"alt": "493"}, {"range": "503", "text": "495"}, {"alt": "496"}, {"range": "504", "text": "498"}, {"range": "505", "text": "455"}, {"range": "506", "text": "457"}, {"range": "507", "text": "455"}, {"range": "508", "text": "457"}, {"range": "509", "text": "455"}, {"range": "510", "text": "457"}, {"range": "511", "text": "455"}, {"range": "512", "text": "457"}, [418, 421], "unknown", [418, 421], "never", [463, 466], [463, 466], [552, 555], [552, 555], [597, 600], [597, 600], [686, 689], [686, 689], [413, 416], [413, 416], [458, 461], [458, 461], [547, 550], [547, 550], [592, 595], [592, 595], [681, 684], [681, 684], "object", [1506, 1508], [1506, 1508], [1847, 1849], [1847, 1849], [2567, 2569], [2567, 2569], [2588, 2590], [2588, 2590], [18652, 18694], "type GeneratedTypes = Config", "&apos;", [12804, 12863], " Sync operations are idempotent and won&apos;t create duplicates", "&lsquo;", [12804, 12863], " Sync operations are idempotent and won&lsquo;t create duplicates", "&#39;", [12804, 12863], " Sync operations are idempotent and won&#39;t create duplicates", "&rsquo;", [12804, 12863], " Sync operations are idempotent and won&rsquo;t create duplicates", [702, 704], "[checkUserPermissions]", [16409, 16468], [16409, 16468], [16409, 16468], [16409, 16468], [198, 201], [198, 201], [2232, 2235], [2232, 2235], [2690, 2693], [2690, 2693], [2721, 2724], [2721, 2724]]