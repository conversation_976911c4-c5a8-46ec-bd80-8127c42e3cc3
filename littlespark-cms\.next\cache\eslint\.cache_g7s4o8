[{"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(frontend)\\layout.tsx": "1", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(frontend)\\page.tsx": "2", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\importMap.js": "3", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\[[...segments]]\\not-found.tsx": "4", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\[[...segments]]\\page.tsx": "5", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\api\\graphql\\route.ts": "6", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\api\\graphql-playground\\route.ts": "7", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\api\\[...slug]\\route.ts": "8", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\layout.tsx": "9", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\my-route\\route.ts": "10", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\Challenges.ts": "11", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\EducationalResources.ts": "12", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\Media.ts": "13", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\StoryTemplates.ts": "14", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\Users.ts": "15", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\payload-types.ts": "16", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\payload.config.ts": "17", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\dashboard\\page.tsx": "18", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\sync\\page.tsx": "19", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\sync\\sync.js": "20", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\sync-button.js": "21", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\auth\\verify\\route.ts": "22", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\sync\\challenges\\route.ts": "23", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\sync\\from-main-app\\route.ts": "24", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\sync\\users\\route.ts": "25", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\test-data\\route.ts": "26", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\components\\AdminHeader.tsx": "27", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\components\\AdminOnly.tsx": "28", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\components\\SyncButton.tsx": "29", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\components\\SyncDashboard.tsx": "30", "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\middleware\\adminAuth.ts": "31"}, {"size": 400, "mtime": 1753804502000, "results": "32", "hashOfConfig": "33"}, {"size": 1820, "mtime": 1753915579431, "results": "34", "hashOfConfig": "33"}, {"size": 208, "mtime": 1753910933648, "results": "35", "hashOfConfig": "36"}, {"size": 731, "mtime": 1753804502000, "results": "37", "hashOfConfig": "33"}, {"size": 715, "mtime": 1753804502000, "results": "38", "hashOfConfig": "33"}, {"size": 315, "mtime": 1753804502000, "results": "39", "hashOfConfig": "33"}, {"size": 305, "mtime": 1753804502000, "results": "40", "hashOfConfig": "33"}, {"size": 550, "mtime": 1753804502000, "results": "41", "hashOfConfig": "33"}, {"size": 810, "mtime": 1753804502000, "results": "42", "hashOfConfig": "33"}, {"size": 1232, "mtime": 1753919017712, "results": "43", "hashOfConfig": "33"}, {"size": 9913, "mtime": 1753901513187, "results": "44", "hashOfConfig": "33"}, {"size": 8063, "mtime": 1753919049274, "results": "45", "hashOfConfig": "33"}, {"size": 255, "mtime": 1753804502000, "results": "46", "hashOfConfig": "33"}, {"size": 8010, "mtime": 1753919069995, "results": "47", "hashOfConfig": "33"}, {"size": 2673, "mtime": 1753808932736, "results": "48", "hashOfConfig": "33"}, {"size": 18696, "mtime": 1753902090582, "results": "49", "hashOfConfig": "33"}, {"size": 1410, "mtime": 1753910925952, "results": "50", "hashOfConfig": "33"}, {"size": 5979, "mtime": 1753918838540, "results": "51", "hashOfConfig": "33"}, {"size": 19153, "mtime": 1753918862767, "results": "52", "hashOfConfig": "33"}, {"size": 11152, "mtime": 1753901641768, "results": "53", "hashOfConfig": "36"}, {"size": 2069, "mtime": 1753904666750, "results": "54", "hashOfConfig": "36"}, {"size": 1993, "mtime": 1753918877449, "results": "55", "hashOfConfig": "33"}, {"size": 6540, "mtime": 1753918914506, "results": "56", "hashOfConfig": "33"}, {"size": 5715, "mtime": 1753918968187, "results": "57", "hashOfConfig": "33"}, {"size": 5807, "mtime": 1753918989752, "results": "58", "hashOfConfig": "33"}, {"size": 8392, "mtime": 1753917116420, "results": "59", "hashOfConfig": "33"}, {"size": 1259, "mtime": 1753904432940, "results": "60", "hashOfConfig": "33"}, {"size": 5034, "mtime": 1753919174917, "results": "61", "hashOfConfig": "33"}, {"size": 1030, "mtime": 1753903955858, "results": "62", "hashOfConfig": "33"}, {"size": 18485, "mtime": 1753919339258, "results": "63", "hashOfConfig": "33"}, {"size": 3339, "mtime": 1753919326311, "results": "64", "hashOfConfig": "33"}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "4girfc", {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1vu8xsk", {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(frontend)\\layout.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(frontend)\\page.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\importMap.js", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\[[...segments]]\\not-found.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\[[...segments]]\\page.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\api\\graphql\\route.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\api\\graphql-playground\\route.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\api\\[...slug]\\route.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\layout.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\my-route\\route.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\Challenges.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\EducationalResources.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\Media.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\StoryTemplates.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\collections\\Users.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\payload-types.ts", [], ["158", "159", "160", "161", "162"], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\payload.config.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\sync\\page.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\sync\\sync.js", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\admin\\sync-button.js", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\auth\\verify\\route.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\sync\\challenges\\route.ts", ["163"], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\sync\\from-main-app\\route.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\sync\\users\\route.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\test-data\\route.ts", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\components\\AdminHeader.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\components\\AdminOnly.tsx", ["164"], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\components\\SyncButton.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\components\\SyncDashboard.tsx", [], [], "C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\middleware\\adminAuth.ts", [], [], {"ruleId": "165", "severity": 1, "message": "166", "line": 68, "column": 11, "nodeType": "167", "messageId": "168", "endLine": 68, "endColumn": 13, "suggestions": "169", "suppressions": "170"}, {"ruleId": "165", "severity": 1, "message": "166", "line": 79, "column": 21, "nodeType": "167", "messageId": "168", "endLine": 79, "endColumn": 23, "suggestions": "171", "suppressions": "172"}, {"ruleId": "165", "severity": 1, "message": "166", "line": 93, "column": 12, "nodeType": "167", "messageId": "168", "endLine": 93, "endColumn": 14, "suggestions": "173", "suppressions": "174"}, {"ruleId": "165", "severity": 1, "message": "166", "line": 94, "column": 18, "nodeType": "167", "messageId": "168", "endLine": 94, "endColumn": 20, "suggestions": "175", "suppressions": "176"}, {"ruleId": "165", "severity": 1, "message": "177", "line": 855, "column": 20, "nodeType": "178", "messageId": "179", "endLine": 855, "endColumn": 34, "suggestions": "180", "suppressions": "181"}, {"ruleId": "182", "severity": 1, "message": "183", "line": 9, "column": 28, "nodeType": null, "messageId": "184", "endLine": 9, "endColumn": 35}, {"ruleId": "185", "severity": 1, "message": "186", "line": 26, "column": 6, "nodeType": "187", "endLine": 26, "endColumn": 8, "suggestions": "188"}, "@typescript-eslint/no-empty-object-type", "The `{}` (\"empty object\") type allows any non-nullish value, including literals like `0` and `\"\"`.\n- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.\n- If you want a type meaning \"any object\", you probably want `object` instead.\n- If you want a type meaning \"any value\", you probably want `unknown` instead.", "TSTypeLiteral", "noEmptyObject", ["189", "190"], ["191"], ["192", "193"], ["194"], ["195", "196"], ["197"], ["198", "199"], ["200"], "An interface declaring no members is equivalent to its supertype.", "Identifier", "noEmptyInterfaceWithSuper", ["201"], ["202"], "@typescript-eslint/no-unused-vars", "'request' is defined but never used. Allowed unused args must match /^_/u.", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'checkUserPermissions'. Either include it or remove the dependency array.", "ArrayExpression", ["203"], {"messageId": "204", "data": "205", "fix": "206", "desc": "207"}, {"messageId": "204", "data": "208", "fix": "209", "desc": "210"}, {"kind": "211", "justification": "212"}, {"messageId": "204", "data": "213", "fix": "214", "desc": "207"}, {"messageId": "204", "data": "215", "fix": "216", "desc": "210"}, {"kind": "211", "justification": "212"}, {"messageId": "204", "data": "217", "fix": "218", "desc": "207"}, {"messageId": "204", "data": "219", "fix": "220", "desc": "210"}, {"kind": "211", "justification": "212"}, {"messageId": "204", "data": "221", "fix": "222", "desc": "207"}, {"messageId": "204", "data": "223", "fix": "224", "desc": "210"}, {"kind": "211", "justification": "212"}, {"messageId": "225", "fix": "226", "desc": "227"}, {"kind": "211", "justification": "212"}, {"desc": "228", "fix": "229"}, "replaceEmptyObjectType", {"replacement": "230"}, {"range": "231", "text": "230"}, "Replace `{}` with `object`.", {"replacement": "232"}, {"range": "233", "text": "232"}, "Replace `{}` with `unknown`.", "directive", "", {"replacement": "230"}, {"range": "234", "text": "230"}, {"replacement": "232"}, {"range": "235", "text": "232"}, {"replacement": "230"}, {"range": "236", "text": "230"}, {"replacement": "232"}, {"range": "237", "text": "232"}, {"replacement": "230"}, {"range": "238", "text": "230"}, {"replacement": "232"}, {"range": "239", "text": "232"}, "replaceEmptyInterfaceWithSuper", {"range": "240", "text": "241"}, "Replace empty interface with a type alias.", "Update the dependencies array to be: [checkUserPermissions]", {"range": "242", "text": "243"}, "object", [1506, 1508], "unknown", [1506, 1508], [1847, 1849], [1847, 1849], [2567, 2569], [2567, 2569], [2588, 2590], [2588, 2590], [18652, 18694], "type GeneratedTypes = Config", [659, 661], "[checkUserPermissions]"]