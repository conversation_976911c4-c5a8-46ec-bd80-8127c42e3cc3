(()=>{var n={};n.id=8139,n.ids=[8139],n.modules={91043:n=>{"use strict";n.exports=require("@aws-sdk/client-s3")},10846:n=>{"use strict";n.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:n=>{"use strict";n.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},29294:n=>{"use strict";n.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:n=>{"use strict";n.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74552:n=>{"use strict";n.exports=require("pino")},14007:n=>{"use strict";n.exports=require("pino-pretty")},79428:n=>{"use strict";n.exports=require("buffer")},79646:n=>{"use strict";n.exports=require("child_process")},55511:n=>{"use strict";n.exports=require("crypto")},14985:n=>{"use strict";n.exports=require("dns")},94735:n=>{"use strict";n.exports=require("events")},29021:n=>{"use strict";n.exports=require("fs")},79748:n=>{"use strict";n.exports=require("fs/promises")},81630:n=>{"use strict";n.exports=require("http")},73496:n=>{"use strict";n.exports=require("http2")},55591:n=>{"use strict";n.exports=require("https")},8086:n=>{"use strict";n.exports=require("module")},91645:n=>{"use strict";n.exports=require("net")},21820:n=>{"use strict";n.exports=require("os")},33873:n=>{"use strict";n.exports=require("path")},19771:n=>{"use strict";n.exports=require("process")},11997:n=>{"use strict";n.exports=require("punycode")},4984:n=>{"use strict";n.exports=require("readline")},27910:n=>{"use strict";n.exports=require("stream")},34631:n=>{"use strict";n.exports=require("tls")},79551:n=>{"use strict";n.exports=require("url")},28354:n=>{"use strict";n.exports=require("util")},74075:n=>{"use strict";n.exports=require("zlib")},28855:n=>{"use strict";n.exports=import("@libsql/client")},34589:n=>{"use strict";n.exports=require("node:assert")},16698:n=>{"use strict";n.exports=require("node:async_hooks")},4573:n=>{"use strict";n.exports=require("node:buffer")},37540:n=>{"use strict";n.exports=require("node:console")},77598:n=>{"use strict";n.exports=require("node:crypto")},53053:n=>{"use strict";n.exports=require("node:diagnostics_channel")},40610:n=>{"use strict";n.exports=require("node:dns")},78474:n=>{"use strict";n.exports=require("node:events")},73024:n=>{"use strict";n.exports=require("node:fs")},51455:n=>{"use strict";n.exports=require("node:fs/promises")},37067:n=>{"use strict";n.exports=require("node:http")},32467:n=>{"use strict";n.exports=require("node:http2")},98995:n=>{"use strict";n.exports=require("node:module")},77030:n=>{"use strict";n.exports=require("node:net")},48161:n=>{"use strict";n.exports=require("node:os")},76760:n=>{"use strict";n.exports=require("node:path")},643:n=>{"use strict";n.exports=require("node:perf_hooks")},1708:n=>{"use strict";n.exports=require("node:process")},41792:n=>{"use strict";n.exports=require("node:querystring")},80099:n=>{"use strict";n.exports=require("node:sqlite")},57075:n=>{"use strict";n.exports=require("node:stream")},37830:n=>{"use strict";n.exports=require("node:stream/web")},41692:n=>{"use strict";n.exports=require("node:tls")},73136:n=>{"use strict";n.exports=require("node:url")},57975:n=>{"use strict";n.exports=require("node:util")},73429:n=>{"use strict";n.exports=require("node:util/types")},75919:n=>{"use strict";n.exports=require("node:worker_threads")},38522:n=>{"use strict";n.exports=require("node:zlib")},56738:(n,t,r)=>{"use strict";r.a(n,async(n,e)=>{try{r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>c,serverHooks:()=>d,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>u});var a=r(42706),i=r(28203),s=r(45994),o=r(99432),l=n([o]);o=(l.then?(await l)():l)[0];let c=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/(payload)/api/graphql-playground/route",pathname:"/api/graphql-playground",filename:"route",bundlePath:"app/(payload)/api/graphql-playground/route"},resolvedPagePath:"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\api\\graphql-playground\\route.ts",nextConfigOutput:"standalone",userland:o}),{workAsyncStorage:p,workUnitAsyncStorage:u,serverHooks:d}=c;function f(){return(0,s.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:u})}e()}catch(n){e(n)}})},69972:()=>{},29652:()=>{},54161:(n,t,r)=>{var e=r(66071),a=r(4527);function i(n){(n=function(n){var t={};for(var r in n)t[r]=n[r];return t}(n||{})).whiteList=n.whiteList||e.whiteList,n.onAttr=n.onAttr||e.onAttr,n.onIgnoreAttr=n.onIgnoreAttr||e.onIgnoreAttr,n.safeAttrValue=n.safeAttrValue||e.safeAttrValue,this.options=n}r(15724),i.prototype.process=function(n){if(!(n=(n=n||"").toString()))return"";var t=this.options,r=t.whiteList,e=t.onAttr,i=t.onIgnoreAttr,s=t.safeAttrValue;return a(n,function(n,t,a,o,l){var f=r[a],c=!1;if(!0===f?c=f:"function"==typeof f?c=f(o):f instanceof RegExp&&(c=f.test(o)),!0!==c&&(c=!1),o=s(a,o)){var p={position:t,sourcePosition:n,source:l,isWhite:c};if(c){var u=e(a,o,p);return null==u?a+":"+o:u}var u=i(a,o,p);if(null!=u)return u}})},n.exports=i},66071:(n,t)=>{function r(){var n={};return n["align-content"]=!1,n["align-items"]=!1,n["align-self"]=!1,n["alignment-adjust"]=!1,n["alignment-baseline"]=!1,n.all=!1,n["anchor-point"]=!1,n.animation=!1,n["animation-delay"]=!1,n["animation-direction"]=!1,n["animation-duration"]=!1,n["animation-fill-mode"]=!1,n["animation-iteration-count"]=!1,n["animation-name"]=!1,n["animation-play-state"]=!1,n["animation-timing-function"]=!1,n.azimuth=!1,n["backface-visibility"]=!1,n.background=!0,n["background-attachment"]=!0,n["background-clip"]=!0,n["background-color"]=!0,n["background-image"]=!0,n["background-origin"]=!0,n["background-position"]=!0,n["background-repeat"]=!0,n["background-size"]=!0,n["baseline-shift"]=!1,n.binding=!1,n.bleed=!1,n["bookmark-label"]=!1,n["bookmark-level"]=!1,n["bookmark-state"]=!1,n.border=!0,n["border-bottom"]=!0,n["border-bottom-color"]=!0,n["border-bottom-left-radius"]=!0,n["border-bottom-right-radius"]=!0,n["border-bottom-style"]=!0,n["border-bottom-width"]=!0,n["border-collapse"]=!0,n["border-color"]=!0,n["border-image"]=!0,n["border-image-outset"]=!0,n["border-image-repeat"]=!0,n["border-image-slice"]=!0,n["border-image-source"]=!0,n["border-image-width"]=!0,n["border-left"]=!0,n["border-left-color"]=!0,n["border-left-style"]=!0,n["border-left-width"]=!0,n["border-radius"]=!0,n["border-right"]=!0,n["border-right-color"]=!0,n["border-right-style"]=!0,n["border-right-width"]=!0,n["border-spacing"]=!0,n["border-style"]=!0,n["border-top"]=!0,n["border-top-color"]=!0,n["border-top-left-radius"]=!0,n["border-top-right-radius"]=!0,n["border-top-style"]=!0,n["border-top-width"]=!0,n["border-width"]=!0,n.bottom=!1,n["box-decoration-break"]=!0,n["box-shadow"]=!0,n["box-sizing"]=!0,n["box-snap"]=!0,n["box-suppress"]=!0,n["break-after"]=!0,n["break-before"]=!0,n["break-inside"]=!0,n["caption-side"]=!1,n.chains=!1,n.clear=!0,n.clip=!1,n["clip-path"]=!1,n["clip-rule"]=!1,n.color=!0,n["color-interpolation-filters"]=!0,n["column-count"]=!1,n["column-fill"]=!1,n["column-gap"]=!1,n["column-rule"]=!1,n["column-rule-color"]=!1,n["column-rule-style"]=!1,n["column-rule-width"]=!1,n["column-span"]=!1,n["column-width"]=!1,n.columns=!1,n.contain=!1,n.content=!1,n["counter-increment"]=!1,n["counter-reset"]=!1,n["counter-set"]=!1,n.crop=!1,n.cue=!1,n["cue-after"]=!1,n["cue-before"]=!1,n.cursor=!1,n.direction=!1,n.display=!0,n["display-inside"]=!0,n["display-list"]=!0,n["display-outside"]=!0,n["dominant-baseline"]=!1,n.elevation=!1,n["empty-cells"]=!1,n.filter=!1,n.flex=!1,n["flex-basis"]=!1,n["flex-direction"]=!1,n["flex-flow"]=!1,n["flex-grow"]=!1,n["flex-shrink"]=!1,n["flex-wrap"]=!1,n.float=!1,n["float-offset"]=!1,n["flood-color"]=!1,n["flood-opacity"]=!1,n["flow-from"]=!1,n["flow-into"]=!1,n.font=!0,n["font-family"]=!0,n["font-feature-settings"]=!0,n["font-kerning"]=!0,n["font-language-override"]=!0,n["font-size"]=!0,n["font-size-adjust"]=!0,n["font-stretch"]=!0,n["font-style"]=!0,n["font-synthesis"]=!0,n["font-variant"]=!0,n["font-variant-alternates"]=!0,n["font-variant-caps"]=!0,n["font-variant-east-asian"]=!0,n["font-variant-ligatures"]=!0,n["font-variant-numeric"]=!0,n["font-variant-position"]=!0,n["font-weight"]=!0,n.grid=!1,n["grid-area"]=!1,n["grid-auto-columns"]=!1,n["grid-auto-flow"]=!1,n["grid-auto-rows"]=!1,n["grid-column"]=!1,n["grid-column-end"]=!1,n["grid-column-start"]=!1,n["grid-row"]=!1,n["grid-row-end"]=!1,n["grid-row-start"]=!1,n["grid-template"]=!1,n["grid-template-areas"]=!1,n["grid-template-columns"]=!1,n["grid-template-rows"]=!1,n["hanging-punctuation"]=!1,n.height=!0,n.hyphens=!1,n.icon=!1,n["image-orientation"]=!1,n["image-resolution"]=!1,n["ime-mode"]=!1,n["initial-letters"]=!1,n["inline-box-align"]=!1,n["justify-content"]=!1,n["justify-items"]=!1,n["justify-self"]=!1,n.left=!1,n["letter-spacing"]=!0,n["lighting-color"]=!0,n["line-box-contain"]=!1,n["line-break"]=!1,n["line-grid"]=!1,n["line-height"]=!1,n["line-snap"]=!1,n["line-stacking"]=!1,n["line-stacking-ruby"]=!1,n["line-stacking-shift"]=!1,n["line-stacking-strategy"]=!1,n["list-style"]=!0,n["list-style-image"]=!0,n["list-style-position"]=!0,n["list-style-type"]=!0,n.margin=!0,n["margin-bottom"]=!0,n["margin-left"]=!0,n["margin-right"]=!0,n["margin-top"]=!0,n["marker-offset"]=!1,n["marker-side"]=!1,n.marks=!1,n.mask=!1,n["mask-box"]=!1,n["mask-box-outset"]=!1,n["mask-box-repeat"]=!1,n["mask-box-slice"]=!1,n["mask-box-source"]=!1,n["mask-box-width"]=!1,n["mask-clip"]=!1,n["mask-image"]=!1,n["mask-origin"]=!1,n["mask-position"]=!1,n["mask-repeat"]=!1,n["mask-size"]=!1,n["mask-source-type"]=!1,n["mask-type"]=!1,n["max-height"]=!0,n["max-lines"]=!1,n["max-width"]=!0,n["min-height"]=!0,n["min-width"]=!0,n["move-to"]=!1,n["nav-down"]=!1,n["nav-index"]=!1,n["nav-left"]=!1,n["nav-right"]=!1,n["nav-up"]=!1,n["object-fit"]=!1,n["object-position"]=!1,n.opacity=!1,n.order=!1,n.orphans=!1,n.outline=!1,n["outline-color"]=!1,n["outline-offset"]=!1,n["outline-style"]=!1,n["outline-width"]=!1,n.overflow=!1,n["overflow-wrap"]=!1,n["overflow-x"]=!1,n["overflow-y"]=!1,n.padding=!0,n["padding-bottom"]=!0,n["padding-left"]=!0,n["padding-right"]=!0,n["padding-top"]=!0,n.page=!1,n["page-break-after"]=!1,n["page-break-before"]=!1,n["page-break-inside"]=!1,n["page-policy"]=!1,n.pause=!1,n["pause-after"]=!1,n["pause-before"]=!1,n.perspective=!1,n["perspective-origin"]=!1,n.pitch=!1,n["pitch-range"]=!1,n["play-during"]=!1,n.position=!1,n["presentation-level"]=!1,n.quotes=!1,n["region-fragment"]=!1,n.resize=!1,n.rest=!1,n["rest-after"]=!1,n["rest-before"]=!1,n.richness=!1,n.right=!1,n.rotation=!1,n["rotation-point"]=!1,n["ruby-align"]=!1,n["ruby-merge"]=!1,n["ruby-position"]=!1,n["shape-image-threshold"]=!1,n["shape-outside"]=!1,n["shape-margin"]=!1,n.size=!1,n.speak=!1,n["speak-as"]=!1,n["speak-header"]=!1,n["speak-numeral"]=!1,n["speak-punctuation"]=!1,n["speech-rate"]=!1,n.stress=!1,n["string-set"]=!1,n["tab-size"]=!1,n["table-layout"]=!1,n["text-align"]=!0,n["text-align-last"]=!0,n["text-combine-upright"]=!0,n["text-decoration"]=!0,n["text-decoration-color"]=!0,n["text-decoration-line"]=!0,n["text-decoration-skip"]=!0,n["text-decoration-style"]=!0,n["text-emphasis"]=!0,n["text-emphasis-color"]=!0,n["text-emphasis-position"]=!0,n["text-emphasis-style"]=!0,n["text-height"]=!0,n["text-indent"]=!0,n["text-justify"]=!0,n["text-orientation"]=!0,n["text-overflow"]=!0,n["text-shadow"]=!0,n["text-space-collapse"]=!0,n["text-transform"]=!0,n["text-underline-position"]=!0,n["text-wrap"]=!0,n.top=!1,n.transform=!1,n["transform-origin"]=!1,n["transform-style"]=!1,n.transition=!1,n["transition-delay"]=!1,n["transition-duration"]=!1,n["transition-property"]=!1,n["transition-timing-function"]=!1,n["unicode-bidi"]=!1,n["vertical-align"]=!1,n.visibility=!1,n["voice-balance"]=!1,n["voice-duration"]=!1,n["voice-family"]=!1,n["voice-pitch"]=!1,n["voice-range"]=!1,n["voice-rate"]=!1,n["voice-stress"]=!1,n["voice-volume"]=!1,n.volume=!1,n["white-space"]=!1,n.widows=!1,n.width=!0,n["will-change"]=!1,n["word-break"]=!0,n["word-spacing"]=!0,n["word-wrap"]=!0,n["wrap-flow"]=!1,n["wrap-through"]=!1,n["writing-mode"]=!1,n["z-index"]=!1,n}var e=/javascript\s*\:/img;t.whiteList=r(),t.getDefaultWhiteList=r,t.onAttr=function(n,t,r){},t.onIgnoreAttr=function(n,t,r){},t.safeAttrValue=function(n,t){return e.test(t)?"":t}},75086:(n,t,r)=>{var e=r(66071),a=r(54161);for(var i in(t=n.exports=function(n,t){return new a(t).process(n)}).FilterCSS=a,e)t[i]=e[i];"undefined"!=typeof window&&(window.filterCSS=n.exports)},4527:(n,t,r)=>{var e=r(15724);n.exports=function(n,t){";"!==(n=e.trimRight(n))[n.length-1]&&(n+=";");var r=n.length,a=!1,i=0,s=0,o="";function l(){if(!a){var r=e.trim(n.slice(i,s)),l=r.indexOf(":");if(-1!==l){var f=e.trim(r.slice(0,l)),c=e.trim(r.slice(l+1));if(f){var p=t(i,o.length,f,c,r);p&&(o+=p+"; ")}}}i=s+1}for(;s<r;s++){var f=n[s];if("/"===f&&"*"===n[s+1]){var c=n.indexOf("*/",s+2);if(-1===c)break;i=(s=c+1)+1,a=!1}else"("===f?a=!0:")"===f?a=!1:";"===f?a||l():"\n"===f&&l()}return e.trim(o)}},15724:n=>{n.exports={indexOf:function(n,t){var r,e;if(Array.prototype.indexOf)return n.indexOf(t);for(r=0,e=n.length;r<e;r++)if(n[r]===t)return r;return -1},forEach:function(n,t,r){var e,a;if(Array.prototype.forEach)return n.forEach(t,r);for(e=0,a=n.length;e<a;e++)t.call(r,n[e],e,n)},trim:function(n){return String.prototype.trim?n.trim():n.replace(/(^\s*)|(\s*$)/g,"")},trimRight:function(n){return String.prototype.trimRight?n.trimRight():n.replace(/(\s*$)/g,"")}}},95651:(n,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(){return{script:"\n    const loadingWrapper = document.getElementById('loading-wrapper');\n    if (loadingWrapper) {\n      loadingWrapper.classList.add('fadeOut');\n    }\n    ",container:'\n<style type="text/css">\n.fadeOut {\n  -webkit-animation: fadeOut 0.5s ease-out forwards;\n  animation: fadeOut 0.5s ease-out forwards;\n}\n\n@-webkit-keyframes fadeIn {\n  from {\n    opacity: 0;\n    -webkit-transform: translateY(-10px);\n    -ms-transform: translateY(-10px);\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    -webkit-transform: translateY(0);\n    -ms-transform: translateY(0);\n    transform: translateY(0);\n  }\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    -webkit-transform: translateY(-10px);\n    -ms-transform: translateY(-10px);\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    -webkit-transform: translateY(0);\n    -ms-transform: translateY(0);\n    transform: translateY(0);\n  }\n}\n\n@-webkit-keyframes fadeOut {\n  from {\n    opacity: 1;\n    -webkit-transform: translateY(0);\n    -ms-transform: translateY(0);\n    transform: translateY(0);\n  }\n  to {\n    opacity: 0;\n    -webkit-transform: translateY(-10px);\n    -ms-transform: translateY(-10px);\n    transform: translateY(-10px);\n  }\n}\n\n@keyframes fadeOut {\n  from {\n    opacity: 1;\n    -webkit-transform: translateY(0);\n    -ms-transform: translateY(0);\n    transform: translateY(0);\n  }\n  to {\n    opacity: 0;\n    -webkit-transform: translateY(-10px);\n    -ms-transform: translateY(-10px);\n    transform: translateY(-10px);\n  }\n}\n\n@-webkit-keyframes appearIn {\n  from {\n    opacity: 0;\n    -webkit-transform: translateY(0px);\n    -ms-transform: translateY(0px);\n    transform: translateY(0px);\n  }\n  to {\n    opacity: 1;\n    -webkit-transform: translateY(0);\n    -ms-transform: translateY(0);\n    transform: translateY(0);\n  }\n}\n\n@keyframes appearIn {\n  from {\n    opacity: 0;\n    -webkit-transform: translateY(0px);\n    -ms-transform: translateY(0px);\n    transform: translateY(0px);\n  }\n  to {\n    opacity: 1;\n    -webkit-transform: translateY(0);\n    -ms-transform: translateY(0);\n    transform: translateY(0);\n  }\n}\n\n@-webkit-keyframes scaleIn {\n  from {\n    -webkit-transform: scale(0);\n    -ms-transform: scale(0);\n    transform: scale(0);\n  }\n  to {\n    -webkit-transform: scale(1);\n    -ms-transform: scale(1);\n    transform: scale(1);\n  }\n}\n\n@keyframes scaleIn {\n  from {\n    -webkit-transform: scale(0);\n    -ms-transform: scale(0);\n    transform: scale(0);\n  }\n  to {\n    -webkit-transform: scale(1);\n    -ms-transform: scale(1);\n    transform: scale(1);\n  }\n}\n\n@-webkit-keyframes innerDrawIn {\n  0% {\n    stroke-dashoffset: 70;\n  }\n  50% {\n    stroke-dashoffset: 140;\n  }\n  100% {\n    stroke-dashoffset: 210;\n  }\n}\n\n@keyframes innerDrawIn {\n  0% {\n    stroke-dashoffset: 70;\n  }\n  50% {\n    stroke-dashoffset: 140;\n  }\n  100% {\n    stroke-dashoffset: 210;\n  }\n}\n\n@-webkit-keyframes outerDrawIn {\n  0% {\n    stroke-dashoffset: 76;\n  }\n  100% {\n    stroke-dashoffset: 152;\n  }\n}\n\n@keyframes outerDrawIn {\n  0% {\n    stroke-dashoffset: 76;\n  }\n  100% {\n    stroke-dashoffset: 152;\n  }\n}\n\n.hHWjkv {\n  -webkit-transform-origin: 0px 0px;\n  -ms-transform-origin: 0px 0px;\n  transform-origin: 0px 0px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 0.2222222222222222s;\n  animation: scaleIn 0.25s linear forwards 0.2222222222222222s;\n}\n\n.gCDOzd {\n  -webkit-transform-origin: 0px 0px;\n  -ms-transform-origin: 0px 0px;\n  transform-origin: 0px 0px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 0.4222222222222222s;\n  animation: scaleIn 0.25s linear forwards 0.4222222222222222s;\n}\n\n.hmCcxi {\n  -webkit-transform-origin: 0px 0px;\n  -ms-transform-origin: 0px 0px;\n  transform-origin: 0px 0px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 0.6222222222222222s;\n  animation: scaleIn 0.25s linear forwards 0.6222222222222222s;\n}\n\n.eHamQi {\n  -webkit-transform-origin: 0px 0px;\n  -ms-transform-origin: 0px 0px;\n  transform-origin: 0px 0px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 0.8222222222222223s;\n  animation: scaleIn 0.25s linear forwards 0.8222222222222223s;\n}\n\n.byhgGu {\n  -webkit-transform-origin: 0px 0px;\n  -ms-transform-origin: 0px 0px;\n  transform-origin: 0px 0px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 1.0222222222222221s;\n  animation: scaleIn 0.25s linear forwards 1.0222222222222221s;\n}\n\n.llAKP {\n  -webkit-transform-origin: 0px 0px;\n  -ms-transform-origin: 0px 0px;\n  transform-origin: 0px 0px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 1.2222222222222223s;\n  animation: scaleIn 0.25s linear forwards 1.2222222222222223s;\n}\n\n.bglIGM {\n  -webkit-transform-origin: 64px 28px;\n  -ms-transform-origin: 64px 28px;\n  transform-origin: 64px 28px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 0.2222222222222222s;\n  animation: scaleIn 0.25s linear forwards 0.2222222222222222s;\n}\n\n.ksxRII {\n  -webkit-transform-origin: 95.98500061035156px 46.510000228881836px;\n  -ms-transform-origin: 95.98500061035156px 46.510000228881836px;\n  transform-origin: 95.98500061035156px 46.510000228881836px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 0.4222222222222222s;\n  animation: scaleIn 0.25s linear forwards 0.4222222222222222s;\n}\n\n.cWrBmb {\n  -webkit-transform-origin: 95.97162628173828px 83.4900016784668px;\n  -ms-transform-origin: 95.97162628173828px 83.4900016784668px;\n  transform-origin: 95.97162628173828px 83.4900016784668px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 0.6222222222222222s;\n  animation: scaleIn 0.25s linear forwards 0.6222222222222222s;\n}\n\n.Wnusb {\n  -webkit-transform-origin: 64px 101.97999572753906px;\n  -ms-transform-origin: 64px 101.97999572753906px;\n  transform-origin: 64px 101.97999572753906px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 0.8222222222222223s;\n  animation: scaleIn 0.25s linear forwards 0.8222222222222223s;\n}\n\n.bfPqf {\n  -webkit-transform-origin: 32.03982162475586px 83.4900016784668px;\n  -ms-transform-origin: 32.03982162475586px 83.4900016784668px;\n  transform-origin: 32.03982162475586px 83.4900016784668px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 1.0222222222222221s;\n  animation: scaleIn 0.25s linear forwards 1.0222222222222221s;\n}\n\n.edRCTN {\n  -webkit-transform-origin: 32.033552169799805px 46.510000228881836px;\n  -ms-transform-origin: 32.033552169799805px 46.510000228881836px;\n  transform-origin: 32.033552169799805px 46.510000228881836px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 1.2222222222222223s;\n  animation: scaleIn 0.25s linear forwards 1.2222222222222223s;\n}\n\n.iEGVWn {\n  opacity: 0;\n  stroke-dasharray: 76;\n  -webkit-animation: outerDrawIn 0.5s ease-out forwards 0.3333333333333333s, appearIn 0.1s ease-out forwards 0.3333333333333333s;\n  animation: outerDrawIn 0.5s ease-out forwards 0.3333333333333333s, appearIn 0.1s ease-out forwards 0.3333333333333333s;\n  -webkit-animation-iteration-count: 1, 1;\n  animation-iteration-count: 1, 1;\n}\n\n.bsocdx {\n  opacity: 0;\n  stroke-dasharray: 76;\n  -webkit-animation: outerDrawIn 0.5s ease-out forwards 0.5333333333333333s, appearIn 0.1s ease-out forwards 0.5333333333333333s;\n  animation: outerDrawIn 0.5s ease-out forwards 0.5333333333333333s, appearIn 0.1s ease-out forwards 0.5333333333333333s;\n  -webkit-animation-iteration-count: 1, 1;\n  animation-iteration-count: 1, 1;\n}\n\n.jAZXmP {\n  opacity: 0;\n  stroke-dasharray: 76;\n  -webkit-animation: outerDrawIn 0.5s ease-out forwards 0.7333333333333334s, appearIn 0.1s ease-out forwards 0.7333333333333334s;\n  animation: outerDrawIn 0.5s ease-out forwards 0.7333333333333334s, appearIn 0.1s ease-out forwards 0.7333333333333334s;\n  -webkit-animation-iteration-count: 1, 1;\n  animation-iteration-count: 1, 1;\n}\n\n.hSeArx {\n  opacity: 0;\n  stroke-dasharray: 76;\n  -webkit-animation: outerDrawIn 0.5s ease-out forwards 0.9333333333333333s, appearIn 0.1s ease-out forwards 0.9333333333333333s;\n  animation: outerDrawIn 0.5s ease-out forwards 0.9333333333333333s, appearIn 0.1s ease-out forwards 0.9333333333333333s;\n  -webkit-animation-iteration-count: 1, 1;\n  animation-iteration-count: 1, 1;\n}\n\n.bVgqGk {\n  opacity: 0;\n  stroke-dasharray: 76;\n  -webkit-animation: outerDrawIn 0.5s ease-out forwards 1.1333333333333333s, appearIn 0.1s ease-out forwards 1.1333333333333333s;\n  animation: outerDrawIn 0.5s ease-out forwards 1.1333333333333333s, appearIn 0.1s ease-out forwards 1.1333333333333333s;\n  -webkit-animation-iteration-count: 1, 1;\n  animation-iteration-count: 1, 1;\n}\n\n.hEFqBt {\n  opacity: 0;\n  stroke-dasharray: 76;\n  -webkit-animation: outerDrawIn 0.5s ease-out forwards 1.3333333333333333s, appearIn 0.1s ease-out forwards 1.3333333333333333s;\n  animation: outerDrawIn 0.5s ease-out forwards 1.3333333333333333s, appearIn 0.1s ease-out forwards 1.3333333333333333s;\n  -webkit-animation-iteration-count: 1, 1;\n  animation-iteration-count: 1, 1;\n}\n\n.dzEKCM {\n  opacity: 0;\n  stroke-dasharray: 70;\n  -webkit-animation: innerDrawIn 1s ease-in-out forwards 1.3666666666666667s, appearIn 0.1s linear forwards 1.3666666666666667s;\n  animation: innerDrawIn 1s ease-in-out forwards 1.3666666666666667s, appearIn 0.1s linear forwards 1.3666666666666667s;\n  -webkit-animation-iteration-count: infinite, 1;\n  animation-iteration-count: infinite, 1;\n}\n\n.DYnPx {\n  opacity: 0;\n  stroke-dasharray: 70;\n  -webkit-animation: innerDrawIn 1s ease-in-out forwards 1.5333333333333332s, appearIn 0.1s linear forwards 1.5333333333333332s;\n  animation: innerDrawIn 1s ease-in-out forwards 1.5333333333333332s, appearIn 0.1s linear forwards 1.5333333333333332s;\n  -webkit-animation-iteration-count: infinite, 1;\n  animation-iteration-count: infinite, 1;\n}\n\n.hjPEAQ {\n  opacity: 0;\n  stroke-dasharray: 70;\n  -webkit-animation: innerDrawIn 1s ease-in-out forwards 1.7000000000000002s, appearIn 0.1s linear forwards 1.7000000000000002s;\n  animation: innerDrawIn 1s ease-in-out forwards 1.7000000000000002s, appearIn 0.1s linear forwards 1.7000000000000002s;\n  -webkit-animation-iteration-count: infinite, 1;\n  animation-iteration-count: infinite, 1;\n}\n\n#loading-wrapper {\n  position: absolute;\n  width: 100vw;\n  height: 100vh;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-align-items: center;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  -webkit-box-pack: center;\n  -webkit-justify-content: center;\n  -ms-flex-pack: center;\n  justify-content: center;\n  -webkit-flex-direction: column;\n  -ms-flex-direction: column;\n  flex-direction: column;\n}\n\n.logo {\n  width: 75px;\n  height: 75px;\n  margin-bottom: 20px;\n  opacity: 0;\n  -webkit-animation: fadeIn 0.5s ease-out forwards;\n  animation: fadeIn 0.5s ease-out forwards;\n}\n\n.text {\n  font-size: 32px;\n  font-weight: 200;\n  text-align: center;\n  color: rgba(255, 255, 255, 0.6);\n  opacity: 0;\n  -webkit-animation: fadeIn 0.5s ease-out forwards;\n  animation: fadeIn 0.5s ease-out forwards;\n}\n\n.dGfHfc {\n  font-weight: 400;\n}\n</style>\n<div id="loading-wrapper">\n<svg class="logo" viewBox="0 0 128 128" xmlns:xlink="http://www.w3.org/1999/xlink">\n  <title>GraphQL Playground Logo</title>\n  <defs>\n    <linearGradient id="linearGradient-1" x1="4.86%" x2="96.21%" y1="0%" y2="99.66%">\n      <stop stop-color="#E00082" stop-opacity=".8" offset="0%"></stop>\n      <stop stop-color="#E00082" offset="100%"></stop>\n    </linearGradient>\n  </defs>\n  <g>\n    <rect id="Gradient" width="127.96" height="127.96" y="1" fill="url(#linearGradient-1)" rx="4"></rect>\n    <path id="Border" fill="#E00082" fill-rule="nonzero" d="M4.7 2.84c-1.58 0-2.86 1.28-2.86 2.85v116.57c0 1.57 1.28 2.84 2.85 2.84h116.57c1.57 0 2.84-1.26 2.84-2.83V5.67c0-1.55-1.26-2.83-2.83-2.83H4.67zM4.7 0h116.58c3.14 0 5.68 2.55 5.68 5.7v116.58c0 3.14-2.54 5.68-5.68 5.68H4.68c-3.13 0-5.68-2.54-5.68-5.68V5.68C-1 2.56 1.55 0 4.7 0z"></path>\n    <path class="bglIGM" x="64" y="28" fill="#fff" d="M64 36c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8" style="transform: translate(100px, 100px);"></path>\n    <path class="ksxRII" x="95.98500061035156" y="46.510000228881836" fill="#fff" d="M89.04 50.52c-2.2-3.84-.9-8.73 2.94-10.96 3.83-2.2 8.72-.9 10.95 2.94 2.2 3.84.9 8.73-2.94 10.96-3.85 2.2-8.76.9-10.97-2.94"\n      style="transform: translate(100px, 100px);"></path>\n    <path class="cWrBmb" x="95.97162628173828" y="83.4900016784668" fill="#fff" d="M102.9 87.5c-2.2 3.84-7.1 5.15-10.94 2.94-3.84-2.2-5.14-7.12-2.94-10.96 2.2-3.84 7.12-5.15 10.95-2.94 3.86 2.23 5.16 7.12 2.94 10.96"\n      style="transform: translate(100px, 100px);"></path>\n    <path class="Wnusb" x="64" y="101.97999572753906" fill="#fff" d="M64 110c-4.43 0-8-3.6-8-8.02 0-4.44 3.57-8.02 8-8.02s8 3.58 8 8.02c0 4.4-3.57 8.02-8 8.02"\n      style="transform: translate(100px, 100px);"></path>\n    <path class="bfPqf" x="32.03982162475586" y="83.4900016784668" fill="#fff" d="M25.1 87.5c-2.2-3.84-.9-8.73 2.93-10.96 3.83-2.2 8.72-.9 10.95 2.94 2.2 3.84.9 8.73-2.94 10.96-3.85 2.2-8.74.9-10.95-2.94"\n      style="transform: translate(100px, 100px);"></path>\n    <path class="edRCTN" x="32.033552169799805" y="46.510000228881836" fill="#fff" d="M38.96 50.52c-2.2 3.84-7.12 5.15-10.95 2.94-3.82-2.2-5.12-7.12-2.92-10.96 2.2-3.84 7.12-5.15 10.95-2.94 3.83 2.23 5.14 7.12 2.94 10.96"\n      style="transform: translate(100px, 100px);"></path>\n    <path class="iEGVWn" stroke="#fff" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" d="M63.55 27.5l32.9 19-32.9-19z"></path>\n    <path class="bsocdx" stroke="#fff" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" d="M96 46v38-38z"></path>\n    <path class="jAZXmP" stroke="#fff" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" d="M96.45 84.5l-32.9 19 32.9-19z"></path>\n    <path class="hSeArx" stroke="#fff" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" d="M64.45 103.5l-32.9-19 32.9 19z"></path>\n    <path class="bVgqGk" stroke="#fff" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" d="M32 84V46v38z"></path>\n    <path class="hEFqBt" stroke="#fff" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" d="M31.55 46.5l32.9-19-32.9 19z"></path>\n    <path class="dzEKCM" id="Triangle-Bottom" stroke="#fff" stroke-width="4" d="M30 84h70" stroke-linecap="round"></path>\n    <path class="DYnPx" id="Triangle-Left" stroke="#fff" stroke-width="4" d="M65 26L30 87" stroke-linecap="round"></path>\n    <path class="hjPEAQ" id="Triangle-Right" stroke="#fff" stroke-width="4" d="M98 87L63 26" stroke-linecap="round"></path>\n  </g>\n</svg>\n<div class="text">Loading\n  <span class="dGfHfc">GraphQL Playground</span>\n</div>\n</div>\n'}}},19155:(n,t,r)=>{"use strict";var e=r(97745);t.p=e.renderPlaygroundPage},97745:function(n,t,r){"use strict";var e=this&&this.__assign||function(){return(e=Object.assign||function(n){for(var t,r=1,e=arguments.length;r<e;r++)for(var a in t=arguments[r])Object.prototype.hasOwnProperty.call(t,a)&&(n[a]=t[a]);return n}).apply(this,arguments)};Object.defineProperty(t,"__esModule",{value:!0});var a=r(72525),i=r(95651),s=function(n){return a.filterXSS(n,{whiteList:[],stripIgnoreTag:!0,stripIgnoreTagBody:["script"]})},o=i.default(),l="playground-config",f=function(n){var t=n.version,r=n.cdnUrl,e=void 0===r?"//cdn.jsdelivr.net/npm":r,a=n.faviconUrl,i=function(n,r){return s(e+"/"+n+(t?"@"+t:"")+"/"+r||"")};return'\n    <link \n      rel="stylesheet" \n      href="'+i("graphql-playground-react","build/static/css/index.css")+'"\n    />\n    '+("string"==typeof a?'<link rel="shortcut icon" href="'+s(a||"")+'" />':"")+"\n    "+(void 0===a?'<link rel="shortcut icon" href="'+i("graphql-playground-react","build/favicon.png")+'" />':"")+'\n    <script \n      src="'+i("graphql-playground-react","build/static/js/middleware.js")+'"\n    ></script>\n'};t.renderPlaygroundPage=function(n){var t=e(e({},n),{canSaveConfig:!1});return n.subscriptionsEndpoint&&(t.subscriptionEndpoint=s(n.subscriptionsEndpoint||"")),n.config&&(t.configString=JSON.stringify(n.config,null,2)),t.endpoint||t.configString?t.endpoint&&(t.endpoint=s(t.endpoint||"")):console.warn("WARNING: You didn't provide an endpoint and don't have a .graphqlconfig. Make sure you have at least one of them."),'\n  <!DOCTYPE html>\n  <html>\n  <head>\n    <meta charset=utf-8 />\n    <meta name="viewport" content="user-scalable=no, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, minimal-ui">\n    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700|Source+Code+Pro:400,700" rel="stylesheet">\n    <title>'+(t.title||"GraphQL Playground")+"</title>\n    "+("react"===t.env||"electron"===t.env?"":f(t))+'\n  </head>\n  <body>\n    <style type="text/css">\n      html {\n        font-family: "Open Sans", sans-serif;\n        overflow: hidden;\n      }\n  \n      body {\n        margin: 0;\n        background: #172a3a;\n      }\n\n      #'+l+" {\n        display: none;\n      }\n  \n      .playgroundIn {\n        -webkit-animation: playgroundIn 0.5s ease-out forwards;\n        animation: playgroundIn 0.5s ease-out forwards;\n      }\n  \n      @-webkit-keyframes playgroundIn {\n        from {\n          opacity: 0;\n          -webkit-transform: translateY(10px);\n          -ms-transform: translateY(10px);\n          transform: translateY(10px);\n        }\n        to {\n          opacity: 1;\n          -webkit-transform: translateY(0);\n          -ms-transform: translateY(0);\n          transform: translateY(0);\n        }\n      }\n  \n      @keyframes playgroundIn {\n        from {\n          opacity: 0;\n          -webkit-transform: translateY(10px);\n          -ms-transform: translateY(10px);\n          transform: translateY(10px);\n        }\n        to {\n          opacity: 1;\n          -webkit-transform: translateY(0);\n          -ms-transform: translateY(0);\n          transform: translateY(0);\n        }\n      }\n    </style>\n    "+o.container+"\n    "+a.filterXSS('<div id="'+l+'">'+JSON.stringify(t)+"</div>",{whiteList:{div:["id"]}})+'\n    <div id="root" />\n    <script type="text/javascript">\n      window.addEventListener(\'load\', function (event) {\n        '+o.script+"\n  \n        const root = document.getElementById('root');\n        root.classList.add('playgroundIn');\n        const configText = document.getElementById('"+l+'\').innerText;\n        \n        if(configText && configText.length) {\n          try {\n            GraphQLPlayground.init(root, JSON.parse(configText));\n          }\n          catch(err) {\n            console.error("could not find config")\n          }\n        }\n        else {\n          GraphQLPlayground.init(root);\n        }\n      })\n    </script>\n  </body>\n  </html>\n'}},65528:(n,t,r)=>{var e=r(75086).FilterCSS,a=r(75086).getDefaultWhiteList,i=r(13213);function s(){return{a:["target","href","title"],abbr:["title"],address:[],area:["shape","coords","href","alt"],article:[],aside:[],audio:["autoplay","controls","crossorigin","loop","muted","preload","src"],b:[],bdi:["dir"],bdo:["dir"],big:[],blockquote:["cite"],br:[],caption:[],center:[],cite:[],code:[],col:["align","valign","span","width"],colgroup:["align","valign","span","width"],dd:[],del:["datetime"],details:["open"],div:[],dl:[],dt:[],em:[],figcaption:[],figure:[],font:["color","size","face"],footer:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],header:[],hr:[],i:[],img:["src","alt","title","width","height","loading"],ins:["datetime"],kbd:[],li:[],mark:[],nav:[],ol:[],p:[],pre:[],s:[],section:[],small:[],span:[],sub:[],summary:[],sup:[],strong:[],strike:[],table:["width","border","align","valign"],tbody:["align","valign"],td:["width","rowspan","colspan","align","valign"],tfoot:["align","valign"],th:["width","rowspan","colspan","align","valign"],thead:["align","valign"],tr:["rowspan","align","valign"],tt:[],u:[],ul:[],video:["autoplay","controls","crossorigin","loop","muted","playsinline","poster","preload","src","height","width"]}}var o=new e;function l(n){return n.replace(f,"&lt;").replace(c,"&gt;")}var f=/</g,c=/>/g,p=/"/g,u=/&quot;/g,d=/&#([a-zA-Z0-9]*);?/gim,m=/&colon;?/gim,g=/&newline;?/gim,h=/((j\s*a\s*v\s*a|v\s*b|l\s*i\s*v\s*e)\s*s\s*c\s*r\s*i\s*p\s*t\s*|m\s*o\s*c\s*h\s*a):/gi,w=/e\s*x\s*p\s*r\s*e\s*s\s*s\s*i\s*o\s*n\s*\(.*/gi,x=/u\s*r\s*l\s*\(.*/gi;function b(n){return n.replace(p,"&quot;")}function k(n){return n.replace(u,'"')}function y(n){return n.replace(d,function(n,t){return"x"===t[0]||"X"===t[0]?String.fromCharCode(parseInt(t.substr(1),16)):String.fromCharCode(parseInt(t,10))})}function v(n){return n.replace(m,":").replace(g," ")}function I(n){for(var t="",r=0,e=n.length;r<e;r++)t+=32>n.charCodeAt(r)?" ":n.charAt(r);return i.trim(t)}function q(n){return n=I(n=v(n=y(n=k(n))))}function A(n){return n=l(n=b(n))}t.whiteList=s(),t.getDefaultWhiteList=s,t.onTag=function(n,t,r){},t.onIgnoreTag=function(n,t,r){},t.onTagAttr=function(n,t,r){},t.onIgnoreTagAttr=function(n,t,r){},t.safeAttrValue=function(n,t,r,e){if(r=q(r),"href"===t||"src"===t){if("#"===(r=i.trim(r)))return"#";if(!("http://"===r.substr(0,7)||"https://"===r.substr(0,8)||"mailto:"===r.substr(0,7)||"tel:"===r.substr(0,4)||"data:image/"===r.substr(0,11)||"ftp://"===r.substr(0,6)||"./"===r.substr(0,2)||"../"===r.substr(0,3)||"#"===r[0]||"/"===r[0]))return""}else if("background"===t){if(h.lastIndex=0,h.test(r))return""}else if("style"===t){if(w.lastIndex=0,w.test(r)||(x.lastIndex=0,x.test(r)&&(h.lastIndex=0,h.test(r))))return"";!1!==e&&(r=(e=e||o).process(r))}return r=A(r)},t.escapeHtml=l,t.escapeQuote=b,t.unescapeQuote=k,t.escapeHtmlEntities=y,t.escapeDangerHtml5Entities=v,t.clearNonPrintableCharacter=I,t.friendlyAttrValue=q,t.escapeAttrValue=A,t.onIgnoreTagStripAll=function(){return""},t.StripTagBody=function(n,t){"function"!=typeof t&&(t=function(){});var r=!Array.isArray(n),e=[],a=!1;return{onIgnoreTag:function(s,o,l){if(r?0:-1===i.indexOf(n,s))return t(s,o,l);if(!l.isClosing)return a||(a=l.position),"[removed]";var f="[/removed]",c=l.position+f.length;return e.push([!1!==a?a:l.position,c]),a=!1,f},remove:function(n){var t="",r=0;return i.forEach(e,function(e){t+=n.slice(r,e[0]),r=e[1]}),t+=n.slice(r)}}},t.stripCommentTag=function(n){for(var t="",r=0;r<n.length;){var e=n.indexOf("<!--",r);if(-1===e){t+=n.slice(r);break}t+=n.slice(r,e);var a=n.indexOf("-->",e);if(-1===a)break;r=a+3}return t},t.stripBlankChar=function(n){var t=n.split("");return(t=t.filter(function(n){var t=n.charCodeAt(0);return 127!==t&&(!(t<=31)||10===t||13===t)})).join("")},t.attributeWrapSign='"',t.cssFilter=o,t.getDefaultCSSWhiteList=a},72525:(n,t,r)=>{var e=r(65528),a=r(23238),i=r(6323);function s(n,t){return new i(t).process(n)}(t=n.exports=s).filterXSS=s,t.FilterXSS=i,function(){for(var n in e)t[n]=e[n];for(var r in a)t[r]=a[r]}(),"undefined"!=typeof window&&(window.filterXSS=n.exports),"undefined"!=typeof self&&"undefined"!=typeof DedicatedWorkerGlobalScope&&self instanceof DedicatedWorkerGlobalScope&&(self.filterXSS=n.exports)},23238:(n,t,r)=>{var e=r(13213),a=/[^a-zA-Z0-9\\_:.-]/gim;function i(n){return'"'===n[0]&&'"'===n[n.length-1]||"'"===n[0]&&"'"===n[n.length-1]?n.substr(1,n.length-2):n}t.parseTag=function(n,t,r){"use strict";var a="",i=0,s=!1,o=!1,l=0,f=n.length,c="",p="";n:for(l=0;l<f;l++){var u=n.charAt(l);if(!1===s){if("<"===u){s=l;continue}}else if(!1===o){if("<"===u){a+=r(n.slice(i,l)),s=l,i=l;continue}if(">"===u||l===f-1){a+=r(n.slice(i,s)),c=function(n){var t,r=e.spaceIndex(n);return t=-1===r?n.slice(1,-1):n.slice(1,r+1),"/"===(t=e.trim(t).toLowerCase()).slice(0,1)&&(t=t.slice(1)),"/"===t.slice(-1)&&(t=t.slice(0,-1)),t}(p=n.slice(s,l+1)),a+=t(s,a.length,c,p,"</"===p.slice(0,2)),i=l+1,s=!1;continue}if('"'===u||"'"===u)for(var d=1,m=n.charAt(l-d);""===m.trim()||"="===m;){if("="===m){o=u;continue n}m=n.charAt(l-++d)}}else if(u===o){o=!1;continue}}return i<f&&(a+=r(n.substr(i))),a},t.parseAttr=function(n,t){"use strict";var r=0,s=0,o=[],l=!1,f=n.length;function c(n,r){if(!((n=(n=e.trim(n)).replace(a,"").toLowerCase()).length<1)){var i=t(n,r||"");i&&o.push(i)}}for(var p=0;p<f;p++){var u,d=n.charAt(p);if(!1===l&&"="===d){l=n.slice(r,p),r=p+1,s='"'===n.charAt(r)||"'"===n.charAt(r)?r:function(n,t){for(;t<n.length;t++){var r=n[t];if(" "!==r){if("'"===r||'"'===r)return t;return -1}}}(n,p+1);continue}if(!1!==l&&p===s){if(-1===(u=n.indexOf(d,p+1)))break;c(l,e.trim(n.slice(s+1,u))),l=!1,r=(p=u)+1;continue}if(/\s|\n|\t/.test(d)){if(n=n.replace(/\s|\n|\t/g," "),!1===l){if(-1===(u=function(n,t){for(;t<n.length;t++){var r=n[t];if(" "!==r){if("="===r)return t;return -1}}}(n,p))){c(e.trim(n.slice(r,p))),l=!1,r=p+1;continue}p=u-1;continue}if(-1!==(u=function(n,t){for(;t>0;t--){var r=n[t];if(" "!==r){if("="===r)return t;return -1}}}(n,p-1)))continue;c(l,i(e.trim(n.slice(r,p)))),l=!1,r=p+1;continue}}return r<n.length&&(!1===l?c(n.slice(r)):c(l,i(e.trim(n.slice(r))))),e.trim(o.join(" "))}},13213:n=>{n.exports={indexOf:function(n,t){var r,e;if(Array.prototype.indexOf)return n.indexOf(t);for(r=0,e=n.length;r<e;r++)if(n[r]===t)return r;return -1},forEach:function(n,t,r){var e,a;if(Array.prototype.forEach)return n.forEach(t,r);for(e=0,a=n.length;e<a;e++)t.call(r,n[e],e,n)},trim:function(n){return String.prototype.trim?n.trim():n.replace(/(^\s*)|(\s*$)/g,"")},spaceIndex:function(n){var t=/\s|\n|\t/.exec(n);return t?t.index:-1}}},6323:(n,t,r)=>{var e=r(75086).FilterCSS,a=r(65528),i=r(23238),s=i.parseTag,o=i.parseAttr,l=r(13213);function f(n){(n=function(n){var t={};for(var r in n)t[r]=n[r];return t}(n||{})).stripIgnoreTag&&(n.onIgnoreTag&&console.error('Notes: cannot use these two options "stripIgnoreTag" and "onIgnoreTag" at the same time'),n.onIgnoreTag=a.onIgnoreTagStripAll),n.whiteList||n.allowList?n.whiteList=function(n){var t={};for(var r in n)Array.isArray(n[r])?t[r.toLowerCase()]=n[r].map(function(n){return n.toLowerCase()}):t[r.toLowerCase()]=n[r];return t}(n.whiteList||n.allowList):n.whiteList=a.whiteList,this.attributeWrapSign=!0===n.singleQuotedAttributeValue?"'":a.attributeWrapSign,n.onTag=n.onTag||a.onTag,n.onTagAttr=n.onTagAttr||a.onTagAttr,n.onIgnoreTag=n.onIgnoreTag||a.onIgnoreTag,n.onIgnoreTagAttr=n.onIgnoreTagAttr||a.onIgnoreTagAttr,n.safeAttrValue=n.safeAttrValue||a.safeAttrValue,n.escapeHtml=n.escapeHtml||a.escapeHtml,this.options=n,!1===n.css?this.cssFilter=!1:(n.css=n.css||{},this.cssFilter=new e(n.css))}f.prototype.process=function(n){if(!(n=(n=n||"").toString()))return"";var t=this.options,r=t.whiteList,e=t.onTag,i=t.onIgnoreTag,f=t.onTagAttr,c=t.onIgnoreTagAttr,p=t.safeAttrValue,u=t.escapeHtml,d=this.attributeWrapSign,m=this.cssFilter;t.stripBlankChar&&(n=a.stripBlankChar(n)),t.allowCommentTag||(n=a.stripCommentTag(n));var g=!1;t.stripIgnoreTagBody&&(i=(g=a.StripTagBody(t.stripIgnoreTagBody,i)).onIgnoreTag);var h=s(n,function(n,t,a,s,g){var h={sourcePosition:n,position:t,isClosing:g,isWhite:Object.prototype.hasOwnProperty.call(r,a)},w=e(a,s,h);if(null!=w)return w;if(h.isWhite){if(h.isClosing)return"</"+a+">";var x=function(n){var t=l.spaceIndex(n);if(-1===t)return{html:"",closing:"/"===n[n.length-2]};var r="/"===(n=l.trim(n.slice(t+1,-1)))[n.length-1];return r&&(n=l.trim(n.slice(0,-1))),{html:n,closing:r}}(s),b=r[a],k=o(x.html,function(n,t){var r=-1!==l.indexOf(b,n),e=f(a,n,t,r);return null!=e?e:r?(t=p(a,n,t,m))?n+"="+d+t+d:n:null!=(e=c(a,n,t,r))?e:void 0});return s="<"+a,k&&(s+=" "+k),x.closing&&(s+=" /"),s+=">"}return null!=(w=i(a,s,h))?w:u(s)},u);return g&&(h=g.remove(h)),h},n.exports=f},99432:(n,t,r)=>{"use strict";r.a(n,async(n,e)=>{try{r.r(t),r.d(t,{GET:()=>o});var a=r(17750);r(63046);var i=r(63191),s=n([a]);a=(s.then?(await s)():s)[0];let o=(0,i.f)(a.A);e()}catch(n){e(n)}})},63046:()=>{},63191:(n,t,r)=>{"use strict";r.d(t,{f:()=>i});var e=r(19155),a=r(9574);let i=n=>async t=>{let r=await (0,a.o)({config:n,request:t});return r.payload.config.graphQL.disable||r.payload.config.graphQL.disablePlaygroundInProduction?new Response("Route Not Found",{status:404}):new Response((0,e.p)({endpoint:`${r.payload.config.routes.api}${r.payload.config.routes.graphQL}`,settings:{"request.credentials":"include"}}),{headers:{"Content-Type":"text/html"},status:200})}}};var t=require("../../../../webpack-runtime.js");t.C(n);var r=n=>t(t.s=n),e=t.X(0,[9572,9574,6425],()=>r(56738));module.exports=e})();