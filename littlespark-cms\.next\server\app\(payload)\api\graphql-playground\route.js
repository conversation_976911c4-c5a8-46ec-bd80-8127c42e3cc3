(()=>{var a={};a.id=8139,a.ids=[8139],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},643:a=>{"use strict";a.exports=require("node:perf_hooks")},1708:a=>{"use strict";a.exports=require("node:process")},3644:a=>{a.exports={indexOf:function(a,b){var c,d;if(Array.prototype.indexOf)return a.indexOf(b);for(c=0,d=a.length;c<d;c++)if(a[c]===b)return c;return -1},forEach:function(a,b,c){var d,e;if(Array.prototype.forEach)return a.forEach(b,c);for(d=0,e=a.length;d<e;d++)b.call(c,a[d],d,a)},trim:function(a){return String.prototype.trim?a.trim():a.replace(/(^\s*)|(\s*$)/g,"")},spaceIndex:function(a){var b=/\s|\n|\t/.exec(a);return b?b.index:-1}}},4573:a=>{"use strict";a.exports=require("node:buffer")},4984:a=>{"use strict";a.exports=require("readline")},7640:(a,b,c)=>{"use strict";c.d(b,{f:()=>f});var d=c(33922),e=c(18963);let f=a=>async b=>{let c=await (0,e.o)({config:a,request:b});return c.payload.config.graphQL.disable||c.payload.config.graphQL.disablePlaygroundInProduction?new Response("Route Not Found",{status:404}):new Response((0,d.p)({endpoint:`${c.payload.config.routes.api}${c.payload.config.routes.graphQL}`,settings:{"request.credentials":"include"}}),{headers:{"Content-Type":"text/html"},status:200})}},8086:a=>{"use strict";a.exports=require("module")},9288:a=>{"use strict";a.exports=require("sharp")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},12431:(a,b,c)=>{"use strict";c.d(b,{Y:()=>e});var d=c(44747);let e=({config:a,cookies:b,headers:c})=>{let e=Object.keys(a.i18n.supportedLanguages),f=b.get(`${a.cookiePrefix||"payload"}-lng`),g="string"==typeof f?f:f?.value;if(g&&e.includes(g))return g;let h=c.get("Accept-Language")?(0,d.R8)(c.get("Accept-Language")):void 0;return h&&e.includes(h)?h:a.i18n.fallbackLanguage}},14007:a=>{"use strict";a.exports=require("pino-pretty")},14985:a=>{"use strict";a.exports=require("dns")},16698:a=>{"use strict";a.exports=require("node:async_hooks")},17067:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{GET:()=>h});var e=c(81329);c(63046);var f=c(7640),g=a([e]);e=(g.then?(await g)():g)[0];let h=(0,f.f)(e.A);d()}catch(a){d(a)}})},18963:(a,b,c)=>{"use strict";c.d(b,{o:()=>l});var d=c(3439),e=c(92055),f=c(20416),g=c(60656),h=c(65143),i=c(91506),j=c(12431),k=c(54750);let l=async({canSetHeaders:a,config:b,params:c,request:l})=>{let m=(0,k.J)(l.headers),n=await (0,h.nm0)({config:b,cron:!0}),{config:o}=n,p=o.localization,q=new URL(l.url),{pathname:r,searchParams:s}=q,t=!o.graphQL.disable&&r===`${o.routes.api}${o.routes.graphQL}`,u=(0,j.Y)({config:o,cookies:m,headers:l.headers}),v=await (0,d.L)({config:o.i18n,context:"api",language:u}),w=s.get("fallback-locale")||s.get("fallbackLocale"),x=s.get("locale"),y=w,{search:z}=q,A=z?e.q(z,{arrayLimit:1e3,depth:10,ignoreQueryPrefix:!0}):{};if(p){let a=(0,i.T)({fallbackLocale:y,locale:x,localization:p});y=a.fallbackLocale,x=a.locale}let B=Object.assign(l,{context:{},fallbackLocale:y,hash:q.hash,host:q.host,href:q.href,i18n:v,locale:x,origin:q.origin,pathname:q.pathname,payload:n,payloadAPI:t?"GraphQL":"REST",payloadDataLoader:void 0,payloadUploadSizes:{},port:q.port,protocol:q.protocol,query:A,routeParams:c||{},search:q.search,searchParams:q.searchParams,t:v.t,transactionID:void 0,user:null});B.payloadDataLoader=(0,g.Y)(B);let{responseHeaders:C,user:D}=await (0,f.F)({canSetHeaders:a,headers:B.headers,isGraphQL:t,payload:n});return B.user=D,C&&(B.responseHeaders=C),B}},19771:a=>{"use strict";a.exports=require("process")},21820:a=>{"use strict";a.exports=require("os")},25384:(a,b)=>{function c(){var a={};return a["align-content"]=!1,a["align-items"]=!1,a["align-self"]=!1,a["alignment-adjust"]=!1,a["alignment-baseline"]=!1,a.all=!1,a["anchor-point"]=!1,a.animation=!1,a["animation-delay"]=!1,a["animation-direction"]=!1,a["animation-duration"]=!1,a["animation-fill-mode"]=!1,a["animation-iteration-count"]=!1,a["animation-name"]=!1,a["animation-play-state"]=!1,a["animation-timing-function"]=!1,a.azimuth=!1,a["backface-visibility"]=!1,a.background=!0,a["background-attachment"]=!0,a["background-clip"]=!0,a["background-color"]=!0,a["background-image"]=!0,a["background-origin"]=!0,a["background-position"]=!0,a["background-repeat"]=!0,a["background-size"]=!0,a["baseline-shift"]=!1,a.binding=!1,a.bleed=!1,a["bookmark-label"]=!1,a["bookmark-level"]=!1,a["bookmark-state"]=!1,a.border=!0,a["border-bottom"]=!0,a["border-bottom-color"]=!0,a["border-bottom-left-radius"]=!0,a["border-bottom-right-radius"]=!0,a["border-bottom-style"]=!0,a["border-bottom-width"]=!0,a["border-collapse"]=!0,a["border-color"]=!0,a["border-image"]=!0,a["border-image-outset"]=!0,a["border-image-repeat"]=!0,a["border-image-slice"]=!0,a["border-image-source"]=!0,a["border-image-width"]=!0,a["border-left"]=!0,a["border-left-color"]=!0,a["border-left-style"]=!0,a["border-left-width"]=!0,a["border-radius"]=!0,a["border-right"]=!0,a["border-right-color"]=!0,a["border-right-style"]=!0,a["border-right-width"]=!0,a["border-spacing"]=!0,a["border-style"]=!0,a["border-top"]=!0,a["border-top-color"]=!0,a["border-top-left-radius"]=!0,a["border-top-right-radius"]=!0,a["border-top-style"]=!0,a["border-top-width"]=!0,a["border-width"]=!0,a.bottom=!1,a["box-decoration-break"]=!0,a["box-shadow"]=!0,a["box-sizing"]=!0,a["box-snap"]=!0,a["box-suppress"]=!0,a["break-after"]=!0,a["break-before"]=!0,a["break-inside"]=!0,a["caption-side"]=!1,a.chains=!1,a.clear=!0,a.clip=!1,a["clip-path"]=!1,a["clip-rule"]=!1,a.color=!0,a["color-interpolation-filters"]=!0,a["column-count"]=!1,a["column-fill"]=!1,a["column-gap"]=!1,a["column-rule"]=!1,a["column-rule-color"]=!1,a["column-rule-style"]=!1,a["column-rule-width"]=!1,a["column-span"]=!1,a["column-width"]=!1,a.columns=!1,a.contain=!1,a.content=!1,a["counter-increment"]=!1,a["counter-reset"]=!1,a["counter-set"]=!1,a.crop=!1,a.cue=!1,a["cue-after"]=!1,a["cue-before"]=!1,a.cursor=!1,a.direction=!1,a.display=!0,a["display-inside"]=!0,a["display-list"]=!0,a["display-outside"]=!0,a["dominant-baseline"]=!1,a.elevation=!1,a["empty-cells"]=!1,a.filter=!1,a.flex=!1,a["flex-basis"]=!1,a["flex-direction"]=!1,a["flex-flow"]=!1,a["flex-grow"]=!1,a["flex-shrink"]=!1,a["flex-wrap"]=!1,a.float=!1,a["float-offset"]=!1,a["flood-color"]=!1,a["flood-opacity"]=!1,a["flow-from"]=!1,a["flow-into"]=!1,a.font=!0,a["font-family"]=!0,a["font-feature-settings"]=!0,a["font-kerning"]=!0,a["font-language-override"]=!0,a["font-size"]=!0,a["font-size-adjust"]=!0,a["font-stretch"]=!0,a["font-style"]=!0,a["font-synthesis"]=!0,a["font-variant"]=!0,a["font-variant-alternates"]=!0,a["font-variant-caps"]=!0,a["font-variant-east-asian"]=!0,a["font-variant-ligatures"]=!0,a["font-variant-numeric"]=!0,a["font-variant-position"]=!0,a["font-weight"]=!0,a.grid=!1,a["grid-area"]=!1,a["grid-auto-columns"]=!1,a["grid-auto-flow"]=!1,a["grid-auto-rows"]=!1,a["grid-column"]=!1,a["grid-column-end"]=!1,a["grid-column-start"]=!1,a["grid-row"]=!1,a["grid-row-end"]=!1,a["grid-row-start"]=!1,a["grid-template"]=!1,a["grid-template-areas"]=!1,a["grid-template-columns"]=!1,a["grid-template-rows"]=!1,a["hanging-punctuation"]=!1,a.height=!0,a.hyphens=!1,a.icon=!1,a["image-orientation"]=!1,a["image-resolution"]=!1,a["ime-mode"]=!1,a["initial-letters"]=!1,a["inline-box-align"]=!1,a["justify-content"]=!1,a["justify-items"]=!1,a["justify-self"]=!1,a.left=!1,a["letter-spacing"]=!0,a["lighting-color"]=!0,a["line-box-contain"]=!1,a["line-break"]=!1,a["line-grid"]=!1,a["line-height"]=!1,a["line-snap"]=!1,a["line-stacking"]=!1,a["line-stacking-ruby"]=!1,a["line-stacking-shift"]=!1,a["line-stacking-strategy"]=!1,a["list-style"]=!0,a["list-style-image"]=!0,a["list-style-position"]=!0,a["list-style-type"]=!0,a.margin=!0,a["margin-bottom"]=!0,a["margin-left"]=!0,a["margin-right"]=!0,a["margin-top"]=!0,a["marker-offset"]=!1,a["marker-side"]=!1,a.marks=!1,a.mask=!1,a["mask-box"]=!1,a["mask-box-outset"]=!1,a["mask-box-repeat"]=!1,a["mask-box-slice"]=!1,a["mask-box-source"]=!1,a["mask-box-width"]=!1,a["mask-clip"]=!1,a["mask-image"]=!1,a["mask-origin"]=!1,a["mask-position"]=!1,a["mask-repeat"]=!1,a["mask-size"]=!1,a["mask-source-type"]=!1,a["mask-type"]=!1,a["max-height"]=!0,a["max-lines"]=!1,a["max-width"]=!0,a["min-height"]=!0,a["min-width"]=!0,a["move-to"]=!1,a["nav-down"]=!1,a["nav-index"]=!1,a["nav-left"]=!1,a["nav-right"]=!1,a["nav-up"]=!1,a["object-fit"]=!1,a["object-position"]=!1,a.opacity=!1,a.order=!1,a.orphans=!1,a.outline=!1,a["outline-color"]=!1,a["outline-offset"]=!1,a["outline-style"]=!1,a["outline-width"]=!1,a.overflow=!1,a["overflow-wrap"]=!1,a["overflow-x"]=!1,a["overflow-y"]=!1,a.padding=!0,a["padding-bottom"]=!0,a["padding-left"]=!0,a["padding-right"]=!0,a["padding-top"]=!0,a.page=!1,a["page-break-after"]=!1,a["page-break-before"]=!1,a["page-break-inside"]=!1,a["page-policy"]=!1,a.pause=!1,a["pause-after"]=!1,a["pause-before"]=!1,a.perspective=!1,a["perspective-origin"]=!1,a.pitch=!1,a["pitch-range"]=!1,a["play-during"]=!1,a.position=!1,a["presentation-level"]=!1,a.quotes=!1,a["region-fragment"]=!1,a.resize=!1,a.rest=!1,a["rest-after"]=!1,a["rest-before"]=!1,a.richness=!1,a.right=!1,a.rotation=!1,a["rotation-point"]=!1,a["ruby-align"]=!1,a["ruby-merge"]=!1,a["ruby-position"]=!1,a["shape-image-threshold"]=!1,a["shape-outside"]=!1,a["shape-margin"]=!1,a.size=!1,a.speak=!1,a["speak-as"]=!1,a["speak-header"]=!1,a["speak-numeral"]=!1,a["speak-punctuation"]=!1,a["speech-rate"]=!1,a.stress=!1,a["string-set"]=!1,a["tab-size"]=!1,a["table-layout"]=!1,a["text-align"]=!0,a["text-align-last"]=!0,a["text-combine-upright"]=!0,a["text-decoration"]=!0,a["text-decoration-color"]=!0,a["text-decoration-line"]=!0,a["text-decoration-skip"]=!0,a["text-decoration-style"]=!0,a["text-emphasis"]=!0,a["text-emphasis-color"]=!0,a["text-emphasis-position"]=!0,a["text-emphasis-style"]=!0,a["text-height"]=!0,a["text-indent"]=!0,a["text-justify"]=!0,a["text-orientation"]=!0,a["text-overflow"]=!0,a["text-shadow"]=!0,a["text-space-collapse"]=!0,a["text-transform"]=!0,a["text-underline-position"]=!0,a["text-wrap"]=!0,a.top=!1,a.transform=!1,a["transform-origin"]=!1,a["transform-style"]=!1,a.transition=!1,a["transition-delay"]=!1,a["transition-duration"]=!1,a["transition-property"]=!1,a["transition-timing-function"]=!1,a["unicode-bidi"]=!1,a["vertical-align"]=!1,a.visibility=!1,a["voice-balance"]=!1,a["voice-duration"]=!1,a["voice-family"]=!1,a["voice-pitch"]=!1,a["voice-range"]=!1,a["voice-rate"]=!1,a["voice-stress"]=!1,a["voice-volume"]=!1,a.volume=!1,a["white-space"]=!1,a.widows=!1,a.width=!0,a["will-change"]=!1,a["word-break"]=!0,a["word-spacing"]=!0,a["word-wrap"]=!0,a["wrap-flow"]=!1,a["wrap-through"]=!1,a["writing-mode"]=!1,a["z-index"]=!1,a}var d=/javascript\s*\:/img;b.whiteList=c(),b.getDefaultWhiteList=c,b.onAttr=function(a,b,c){},b.onIgnoreAttr=function(a,b,c){},b.safeAttrValue=function(a,b){return d.test(b)?"":b}},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},28855:a=>{"use strict";a.exports=import("@libsql/client")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29652:()=>{},32467:a=>{"use strict";a.exports=require("node:http2")},33873:a=>{"use strict";a.exports=require("path")},33922:(a,b,c)=>{"use strict";b.p=c(82418).renderPlaygroundPage},34589:a=>{"use strict";a.exports=require("node:assert")},34631:a=>{"use strict";a.exports=require("tls")},37067:a=>{"use strict";a.exports=require("node:http")},37540:a=>{"use strict";a.exports=require("node:console")},37830:a=>{"use strict";a.exports=require("node:stream/web")},38522:a=>{"use strict";a.exports=require("node:zlib")},39085:a=>{a.exports={indexOf:function(a,b){var c,d;if(Array.prototype.indexOf)return a.indexOf(b);for(c=0,d=a.length;c<d;c++)if(a[c]===b)return c;return -1},forEach:function(a,b,c){var d,e;if(Array.prototype.forEach)return a.forEach(b,c);for(d=0,e=a.length;d<e;d++)b.call(c,a[d],d,a)},trim:function(a){return String.prototype.trim?a.trim():a.replace(/(^\s*)|(\s*$)/g,"")},trimRight:function(a){return String.prototype.trimRight?a.trimRight():a.replace(/(\s*$)/g,"")}}},39495:(a,b,c)=>{var d=c(57853).FilterCSS,e=c(57853).getDefaultWhiteList,f=c(3644);function g(){return{a:["target","href","title"],abbr:["title"],address:[],area:["shape","coords","href","alt"],article:[],aside:[],audio:["autoplay","controls","crossorigin","loop","muted","preload","src"],b:[],bdi:["dir"],bdo:["dir"],big:[],blockquote:["cite"],br:[],caption:[],center:[],cite:[],code:[],col:["align","valign","span","width"],colgroup:["align","valign","span","width"],dd:[],del:["datetime"],details:["open"],div:[],dl:[],dt:[],em:[],figcaption:[],figure:[],font:["color","size","face"],footer:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],header:[],hr:[],i:[],img:["src","alt","title","width","height","loading"],ins:["datetime"],kbd:[],li:[],mark:[],nav:[],ol:[],p:[],pre:[],s:[],section:[],small:[],span:[],sub:[],summary:[],sup:[],strong:[],strike:[],table:["width","border","align","valign"],tbody:["align","valign"],td:["width","rowspan","colspan","align","valign"],tfoot:["align","valign"],th:["width","rowspan","colspan","align","valign"],thead:["align","valign"],tr:["rowspan","align","valign"],tt:[],u:[],ul:[],video:["autoplay","controls","crossorigin","loop","muted","playsinline","poster","preload","src","height","width"]}}var h=new d;function i(a){return a.replace(j,"&lt;").replace(k,"&gt;")}var j=/</g,k=/>/g,l=/"/g,m=/&quot;/g,n=/&#([a-zA-Z0-9]*);?/gim,o=/&colon;?/gim,p=/&newline;?/gim,q=/((j\s*a\s*v\s*a|v\s*b|l\s*i\s*v\s*e)\s*s\s*c\s*r\s*i\s*p\s*t\s*|m\s*o\s*c\s*h\s*a):/gi,r=/e\s*x\s*p\s*r\s*e\s*s\s*s\s*i\s*o\s*n\s*\(.*/gi,s=/u\s*r\s*l\s*\(.*/gi;function t(a){return a.replace(l,"&quot;")}function u(a){return a.replace(m,'"')}function v(a){return a.replace(n,function(a,b){return"x"===b[0]||"X"===b[0]?String.fromCharCode(parseInt(b.substr(1),16)):String.fromCharCode(parseInt(b,10))})}function w(a){return a.replace(o,":").replace(p," ")}function x(a){for(var b="",c=0,d=a.length;c<d;c++)b+=32>a.charCodeAt(c)?" ":a.charAt(c);return f.trim(b)}function y(a){return a=x(a=w(a=v(a=u(a))))}function z(a){return a=i(a=t(a))}b.whiteList=g(),b.getDefaultWhiteList=g,b.onTag=function(a,b,c){},b.onIgnoreTag=function(a,b,c){},b.onTagAttr=function(a,b,c){},b.onIgnoreTagAttr=function(a,b,c){},b.safeAttrValue=function(a,b,c,d){if(c=y(c),"href"===b||"src"===b){if("#"===(c=f.trim(c)))return"#";if("http://"!==c.substr(0,7)&&"https://"!==c.substr(0,8)&&"mailto:"!==c.substr(0,7)&&"tel:"!==c.substr(0,4)&&"data:image/"!==c.substr(0,11)&&"ftp://"!==c.substr(0,6)&&"./"!==c.substr(0,2)&&"../"!==c.substr(0,3)&&"#"!==c[0]&&"/"!==c[0])return""}else if("background"===b){if(q.lastIndex=0,q.test(c))return""}else if("style"===b){if(r.lastIndex=0,r.test(c)||(s.lastIndex=0,s.test(c)&&(q.lastIndex=0,q.test(c))))return"";!1!==d&&(c=(d=d||h).process(c))}return c=z(c)},b.escapeHtml=i,b.escapeQuote=t,b.unescapeQuote=u,b.escapeHtmlEntities=v,b.escapeDangerHtml5Entities=w,b.clearNonPrintableCharacter=x,b.friendlyAttrValue=y,b.escapeAttrValue=z,b.onIgnoreTagStripAll=function(){return""},b.StripTagBody=function(a,b){"function"!=typeof b&&(b=function(){});var c=!Array.isArray(a),d=[],e=!1;return{onIgnoreTag:function(g,h,i){if(c?0:-1===f.indexOf(a,g))return b(g,h,i);if(!i.isClosing)return e||(e=i.position),"[removed]";var j="[/removed]",k=i.position+j.length;return d.push([!1!==e?e:i.position,k]),e=!1,j},remove:function(a){var b="",c=0;return f.forEach(d,function(d){b+=a.slice(c,d[0]),c=d[1]}),b+=a.slice(c)}}},b.stripCommentTag=function(a){for(var b="",c=0;c<a.length;){var d=a.indexOf("\x3c!--",c);if(-1===d){b+=a.slice(c);break}b+=a.slice(c,d);var e=a.indexOf("--\x3e",d);if(-1===e)break;c=e+3}return b},b.stripBlankChar=function(a){var b=a.split("");return(b=b.filter(function(a){var b=a.charCodeAt(0);return 127!==b&&(!(b<=31)||10===b||13===b)})).join("")},b.attributeWrapSign='"',b.cssFilter=h,b.getDefaultCSSWhiteList=e},40610:a=>{"use strict";a.exports=require("node:dns")},41692:a=>{"use strict";a.exports=require("node:tls")},41792:a=>{"use strict";a.exports=require("node:querystring")},44747:(a,b,c)=>{"use strict";c.d(b,{R8:()=>f,aG:()=>d});let d=["ar","fa","he"],e=["ar","az","bg","bn-BD","bn-IN","ca","cs","bn-BD","bn-IN","da","de","en","es","et","fa","fr","he","hr","hu","hy","it","ja","ko","lt","lv","my","nb","nl","pl","pt","ro","rs","rs-latin","ru","sk","sl","sv","th","tr","uk","vi","zh","zh-TW"];function f(a){let b;for(let{language:c}of a.split(",").map(a=>{let[b,c]=a.trim().split(";q=");return{language:b,quality:c?parseFloat(c):1}}).sort((a,b)=>b.quality-a.quality))!b&&e.includes(c)&&(b=c);return b}},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:a=>{"use strict";a.exports=require("node:os")},49182:(a,b,c)=>{var d=c(39495),e=c(81631),f=c(95636);function g(a,b){return new f(b).process(a)}for(var h in(b=a.exports=g).filterXSS=g,b.FilterXSS=f,d)b[h]=d[h];for(var i in e)b[i]=e[i];"undefined"!=typeof window&&(window.filterXSS=a.exports),"undefined"!=typeof self&&"undefined"!=typeof DedicatedWorkerGlobalScope&&self instanceof DedicatedWorkerGlobalScope&&(self.filterXSS=a.exports)},51455:a=>{"use strict";a.exports=require("node:fs/promises")},53053:a=>{"use strict";a.exports=require("node:diagnostics_channel")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},57075:a=>{"use strict";a.exports=require("node:stream")},57853:(a,b,c)=>{var d=c(25384),e=c(95626);for(var f in(b=a.exports=function(a,b){return new e(b).process(a)}).FilterCSS=e,d)b[f]=d[f];"undefined"!=typeof window&&(window.filterCSS=a.exports)},57975:a=>{"use strict";a.exports=require("node:util")},60150:(a,b,c)=>{var d=c(39085);a.exports=function(a,b){";"!==(a=d.trimRight(a))[a.length-1]&&(a+=";");var c=a.length,e=!1,f=0,g=0,h="";function i(){if(!e){var c=d.trim(a.slice(f,g)),i=c.indexOf(":");if(-1!==i){var j=d.trim(c.slice(0,i)),k=d.trim(c.slice(i+1));if(j){var l=b(f,h.length,j,k,c);l&&(h+=l+"; ")}}}f=g+1}for(;g<c;g++){var j=a[g];if("/"===j&&"*"===a[g+1]){var k=a.indexOf("*/",g+2);if(-1===k)break;f=(g=k+1)+1,e=!1}else"("===j?e=!0:")"===j?e=!1:";"===j?e||i():"\n"===j&&i()}return d.trim(h)}},61352:(a,b,c)=>{"use strict";c.d(b,{Ay:()=>i,_J:()=>g,j1:()=>h});let d=String.prototype.replace,e=/%20/g,f={RFC1738:"RFC1738",RFC3986:"RFC3986"},g={RFC1738:function(a){return d.call(a,e,"+")},RFC3986:function(a){return String(a)}},h=f.RFC1738;f.RFC3986;let i=f.RFC3986},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63046:()=>{},66946:(a,b,c)=>{"use strict";Object.defineProperty(b,"I",{enumerable:!0,get:function(){return g}});let d=c(30898),e=c(42471),f=c(47912);async function g(a,b,c,g){if((0,d.isNodeNextResponse)(b)){var h;b.statusCode=c.status,b.statusMessage=c.statusText;let d=["set-cookie","www-authenticate","proxy-authenticate","vary"];null==(h=c.headers)||h.forEach((a,c)=>{if("x-middleware-set-cookie"!==c.toLowerCase())if("set-cookie"===c.toLowerCase())for(let d of(0,f.splitCookiesString)(a))b.appendHeader(c,d);else{let e=void 0!==b.getHeader(c);(d.includes(c.toLowerCase())||!e)&&b.appendHeader(c,a)}});let{originalResponse:i}=b;c.body&&"HEAD"!==a.method?await (0,e.pipeToNodeResponse)(c.body,i,g):i.end()}}},69972:()=>{},73024:a=>{"use strict";a.exports=require("node:fs")},73136:a=>{"use strict";a.exports=require("node:url")},73429:a=>{"use strict";a.exports=require("node:util/types")},73496:a=>{"use strict";a.exports=require("http2")},74075:a=>{"use strict";a.exports=require("zlib")},74552:a=>{"use strict";a.exports=require("pino")},75919:a=>{"use strict";a.exports=require("node:worker_threads")},76760:a=>{"use strict";a.exports=require("node:path")},77030:a=>{"use strict";a.exports=require("node:net")},77438:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{handler:()=>x,patchFetch:()=>w,routeModule:()=>y,serverHooks:()=>B,workAsyncStorage:()=>z,workUnitAsyncStorage:()=>A});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(17067),v=a([u]);u=(v.then?(await v)():v)[0];let y=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/(payload)/api/graphql-playground/route",pathname:"/api/graphql-playground",filename:"route",bundlePath:"app/(payload)/api/graphql-playground/route"},distDir:".next",projectDir:"",resolvedPagePath:"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\api\\graphql-playground\\route.ts",nextConfigOutput:"",userland:u}),{workAsyncStorage:z,workUnitAsyncStorage:A,serverHooks:B}=y;function w(){return(0,g.patchFetch)({workAsyncStorage:z,workUnitAsyncStorage:A})}async function x(a,b,c){var d;let e="/(payload)/api/graphql-playground/route";"/index"===e&&(e="/");let g=await y.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:z,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(z.dynamicRoutes[E]||z.routes[D]);if(F&&!x){let a=!!z.routes[D],b=z.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||y.isDev||x||(G=D,G="/index"===G?"/":G);let H=!0===y.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:z,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>y.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>y.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await y.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await y.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:z,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await y.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}d()}catch(a){d(a)}})},77593:(a,b,c)=>{"use strict";c.d(b,{D4:()=>k,F7:()=>q,Pe:()=>o,gd:()=>n,h1:()=>j,kg:()=>p,lF:()=>l,oE:()=>m});var d=c(61352);let e=Object.prototype.hasOwnProperty,f=Array.isArray,g=function(){let a=[];for(let b=0;b<256;++b)a.push("%"+((b<16?"0":"")+b.toString(16)).toUpperCase());return a}(),h=function(a){for(;a.length>1;){let b=a.pop(),c=b.obj[b.prop];if(f(c)){let a=[];for(let b=0;b<c.length;++b)void 0!==c[b]&&a.push(c[b]);b.obj[b.prop]=a}}},i=function(a,b){let c=b&&b.plainObjects?Object.create(null):{};for(let b=0;b<a.length;++b)void 0!==a[b]&&(c[b]=a[b]);return c},j=function a(b,c,d){if(!c)return b;if("object"!=typeof c){if(f(b))b.push(c);else{if(!b||"object"!=typeof b)return[b,c];(d&&(d.plainObjects||d.allowPrototypes)||!e.call(Object.prototype,c))&&(b[c]=!0)}return b}if(!b||"object"!=typeof b)return[b].concat(c);let g=b;return(f(b)&&!f(c)&&(g=i(b,d)),f(b)&&f(c))?(c.forEach(function(c,f){if(e.call(b,f)){let e=b[f];e&&"object"==typeof e&&c&&"object"==typeof c?b[f]=a(e,c,d):b.push(c)}else b[f]=c}),b):Object.keys(c).reduce(function(b,f){let g=c[f];return e.call(b,f)?b[f]=a(b[f],g,d):b[f]=g,b},g)},k=function(a,b,c){let d=a.replace(/\+/g," ");if("iso-8859-1"===c)return d.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(d)}catch(a){return d}},l=function(a,b,c,e,f){if(0===a.length)return a;let h=a;if("symbol"==typeof a?h=Symbol.prototype.toString.call(a):"string"!=typeof a&&(h=String(a)),"iso-8859-1"===c)return escape(h).replace(/%u[0-9a-f]{4}/gi,function(a){return"%26%23"+parseInt(a.slice(2),16)+"%3B"});let i="";for(let a=0;a<h.length;a+=1024){let b=h.length>=1024?h.slice(a,a+1024):h,c=[];for(let a=0;a<b.length;++a){let e=b.charCodeAt(a);if(45===e||46===e||95===e||126===e||e>=48&&e<=57||e>=65&&e<=90||e>=97&&e<=122||f===d.j1&&(40===e||41===e)){c[c.length]=b.charAt(a);continue}if(e<128){c[c.length]=g[e];continue}if(e<2048){c[c.length]=g[192|e>>6]+g[128|63&e];continue}if(e<55296||e>=57344){c[c.length]=g[224|e>>12]+g[128|e>>6&63]+g[128|63&e];continue}a+=1,e=65536+((1023&e)<<10|1023&b.charCodeAt(a)),c[c.length]=g[240|e>>18]+g[128|e>>12&63]+g[128|e>>6&63]+g[128|63&e]}i+=c.join("")}return i},m=function(a){let b=[{obj:{o:a},prop:"o"}],c=[];for(let a=0;a<b.length;++a){let d=b[a],e=d.obj[d.prop],f=Object.keys(e);for(let a=0;a<f.length;++a){let d=f[a],g=e[d];"object"==typeof g&&null!==g&&-1===c.indexOf(g)&&(b.push({obj:e,prop:d}),c.push(g))}}return h(b),a},n=function(a){return"[object RegExp]"===Object.prototype.toString.call(a)},o=function(a){return!!a&&"object"==typeof a&&!!(a.constructor&&a.constructor.isBuffer&&a.constructor.isBuffer(a))},p=function(a,b){return[].concat(a,b)},q=function(a,b){if(f(a)){let c=[];for(let d=0;d<a.length;d+=1)c.push(b(a[d]));return c}return b(a)}},77598:a=>{"use strict";a.exports=require("node:crypto")},78474:a=>{"use strict";a.exports=require("node:events")},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},79748:a=>{"use strict";a.exports=require("fs/promises")},80099:a=>{"use strict";a.exports=require("node:sqlite")},81630:a=>{"use strict";a.exports=require("http")},81631:(a,b,c)=>{var d=c(3644),e=/[^a-zA-Z0-9\\_:.-]/gim;function f(a){return'"'===a[0]&&'"'===a[a.length-1]||"'"===a[0]&&"'"===a[a.length-1]?a.substr(1,a.length-2):a}b.parseTag=function(a,b,c){"use strict";var e="",f=0,g=!1,h=!1,i=0,j=a.length,k="",l="";a:for(i=0;i<j;i++){var m=a.charAt(i);if(!1===g){if("<"===m){g=i;continue}}else if(!1===h){if("<"===m){e+=c(a.slice(f,i)),g=i,f=i;continue}if(">"===m||i===j-1){e+=c(a.slice(f,g)),k=function(a){var b,c=d.spaceIndex(a);return b=-1===c?a.slice(1,-1):a.slice(1,c+1),"/"===(b=d.trim(b).toLowerCase()).slice(0,1)&&(b=b.slice(1)),"/"===b.slice(-1)&&(b=b.slice(0,-1)),b}(l=a.slice(g,i+1)),e+=b(g,e.length,k,l,"</"===l.slice(0,2)),f=i+1,g=!1;continue}if('"'===m||"'"===m)for(var n=1,o=a.charAt(i-n);""===o.trim()||"="===o;){if("="===o){h=m;continue a}o=a.charAt(i-++n)}}else if(m===h){h=!1;continue}}return f<j&&(e+=c(a.substr(f))),e},b.parseAttr=function(a,b){"use strict";var c=0,g=0,h=[],i=!1,j=a.length;function k(a,c){if(!((a=(a=d.trim(a)).replace(e,"").toLowerCase()).length<1)){var f=b(a,c||"");f&&h.push(f)}}for(var l=0;l<j;l++){var m,n=a.charAt(l);if(!1===i&&"="===n){i=a.slice(c,l),c=l+1,g='"'===a.charAt(c)||"'"===a.charAt(c)?c:function(a,b){for(;b<a.length;b++){var c=a[b];if(" "!==c){if("'"===c||'"'===c)return b;return -1}}}(a,l+1);continue}if(!1!==i&&l===g){if(-1===(m=a.indexOf(n,l+1)))break;k(i,d.trim(a.slice(g+1,m))),i=!1,c=(l=m)+1;continue}if(/\s|\n|\t/.test(n)){if(a=a.replace(/\s|\n|\t/g," "),!1===i){if(-1===(m=function(a,b){for(;b<a.length;b++){var c=a[b];if(" "!==c){if("="===c)return b;return -1}}}(a,l))){k(d.trim(a.slice(c,l))),i=!1,c=l+1;continue}l=m-1;continue}if(-1!==(m=function(a,b){for(;b>0;b--){var c=a[b];if(" "!==c){if("="===c)return b;return -1}}}(a,l-1)))continue;k(i,f(d.trim(a.slice(c,l)))),i=!1,c=l+1;continue}}return c<a.length&&(!1===i?k(a.slice(c)):k(i,f(d.trim(a.slice(c))))),d.trim(h.join(" "))}},82418:function(a,b,c){"use strict";var d=this&&this.__assign||function(){return(d=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<d;c++)for(var e in b=arguments[c])Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e]);return a}).apply(this,arguments)};Object.defineProperty(b,"__esModule",{value:!0});var e=c(49182),f=c(86524),g=function(a){return e.filterXSS(a,{whiteList:[],stripIgnoreTag:!0,stripIgnoreTagBody:["script"]})},h=f.default(),i="playground-config",j=function(a){var b=a.version,c=a.cdnUrl,d=void 0===c?"//cdn.jsdelivr.net/npm":c,e=a.faviconUrl,f=function(a,c){return g(d+"/"+a+(b?"@"+b:"")+"/"+c||"")};return'\n    <link \n      rel="stylesheet" \n      href="'+f("graphql-playground-react","build/static/css/index.css")+'"\n    />\n    '+("string"==typeof e?'<link rel="shortcut icon" href="'+g(e||"")+'" />':"")+"\n    "+(void 0===e?'<link rel="shortcut icon" href="'+f("graphql-playground-react","build/favicon.png")+'" />':"")+'\n    <script \n      src="'+f("graphql-playground-react","build/static/js/middleware.js")+'"\n    ><\/script>\n'};b.renderPlaygroundPage=function(a){var b=d(d({},a),{canSaveConfig:!1});return a.subscriptionsEndpoint&&(b.subscriptionEndpoint=g(a.subscriptionsEndpoint||"")),a.config&&(b.configString=JSON.stringify(a.config,null,2)),b.endpoint||b.configString?b.endpoint&&(b.endpoint=g(b.endpoint||"")):console.warn("WARNING: You didn't provide an endpoint and don't have a .graphqlconfig. Make sure you have at least one of them."),'\n  <!DOCTYPE html>\n  <html>\n  <head>\n    <meta charset=utf-8 />\n    <meta name="viewport" content="user-scalable=no, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, minimal-ui">\n    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700|Source+Code+Pro:400,700" rel="stylesheet">\n    <title>'+(b.title||"GraphQL Playground")+"</title>\n    "+("react"===b.env||"electron"===b.env?"":j(b))+'\n  </head>\n  <body>\n    <style type="text/css">\n      html {\n        font-family: "Open Sans", sans-serif;\n        overflow: hidden;\n      }\n  \n      body {\n        margin: 0;\n        background: #172a3a;\n      }\n\n      #'+i+" {\n        display: none;\n      }\n  \n      .playgroundIn {\n        -webkit-animation: playgroundIn 0.5s ease-out forwards;\n        animation: playgroundIn 0.5s ease-out forwards;\n      }\n  \n      @-webkit-keyframes playgroundIn {\n        from {\n          opacity: 0;\n          -webkit-transform: translateY(10px);\n          -ms-transform: translateY(10px);\n          transform: translateY(10px);\n        }\n        to {\n          opacity: 1;\n          -webkit-transform: translateY(0);\n          -ms-transform: translateY(0);\n          transform: translateY(0);\n        }\n      }\n  \n      @keyframes playgroundIn {\n        from {\n          opacity: 0;\n          -webkit-transform: translateY(10px);\n          -ms-transform: translateY(10px);\n          transform: translateY(10px);\n        }\n        to {\n          opacity: 1;\n          -webkit-transform: translateY(0);\n          -ms-transform: translateY(0);\n          transform: translateY(0);\n        }\n      }\n    </style>\n    "+h.container+"\n    "+e.filterXSS('<div id="'+i+'">'+JSON.stringify(b)+"</div>",{whiteList:{div:["id"]}})+'\n    <div id="root" />\n    <script type="text/javascript">\n      window.addEventListener(\'load\', function (event) {\n        '+h.script+"\n  \n        const root = document.getElementById('root');\n        root.classList.add('playgroundIn');\n        const configText = document.getElementById('"+i+'\').innerText;\n        \n        if(configText && configText.length) {\n          try {\n            GraphQLPlayground.init(root, JSON.parse(configText));\n          }\n          catch(err) {\n            console.error("could not find config")\n          }\n        }\n        else {\n          GraphQLPlayground.init(root);\n        }\n      })\n    <\/script>\n  </body>\n  </html>\n'}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},86524:(a,b)=>{"use strict";Object.defineProperty(b,"__esModule",{value:!0}),b.default=function(){return{script:"\n    const loadingWrapper = document.getElementById('loading-wrapper');\n    if (loadingWrapper) {\n      loadingWrapper.classList.add('fadeOut');\n    }\n    ",container:'\n<style type="text/css">\n.fadeOut {\n  -webkit-animation: fadeOut 0.5s ease-out forwards;\n  animation: fadeOut 0.5s ease-out forwards;\n}\n\n@-webkit-keyframes fadeIn {\n  from {\n    opacity: 0;\n    -webkit-transform: translateY(-10px);\n    -ms-transform: translateY(-10px);\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    -webkit-transform: translateY(0);\n    -ms-transform: translateY(0);\n    transform: translateY(0);\n  }\n}\n\n@keyframes fadeIn {\n  from {\n    opacity: 0;\n    -webkit-transform: translateY(-10px);\n    -ms-transform: translateY(-10px);\n    transform: translateY(-10px);\n  }\n  to {\n    opacity: 1;\n    -webkit-transform: translateY(0);\n    -ms-transform: translateY(0);\n    transform: translateY(0);\n  }\n}\n\n@-webkit-keyframes fadeOut {\n  from {\n    opacity: 1;\n    -webkit-transform: translateY(0);\n    -ms-transform: translateY(0);\n    transform: translateY(0);\n  }\n  to {\n    opacity: 0;\n    -webkit-transform: translateY(-10px);\n    -ms-transform: translateY(-10px);\n    transform: translateY(-10px);\n  }\n}\n\n@keyframes fadeOut {\n  from {\n    opacity: 1;\n    -webkit-transform: translateY(0);\n    -ms-transform: translateY(0);\n    transform: translateY(0);\n  }\n  to {\n    opacity: 0;\n    -webkit-transform: translateY(-10px);\n    -ms-transform: translateY(-10px);\n    transform: translateY(-10px);\n  }\n}\n\n@-webkit-keyframes appearIn {\n  from {\n    opacity: 0;\n    -webkit-transform: translateY(0px);\n    -ms-transform: translateY(0px);\n    transform: translateY(0px);\n  }\n  to {\n    opacity: 1;\n    -webkit-transform: translateY(0);\n    -ms-transform: translateY(0);\n    transform: translateY(0);\n  }\n}\n\n@keyframes appearIn {\n  from {\n    opacity: 0;\n    -webkit-transform: translateY(0px);\n    -ms-transform: translateY(0px);\n    transform: translateY(0px);\n  }\n  to {\n    opacity: 1;\n    -webkit-transform: translateY(0);\n    -ms-transform: translateY(0);\n    transform: translateY(0);\n  }\n}\n\n@-webkit-keyframes scaleIn {\n  from {\n    -webkit-transform: scale(0);\n    -ms-transform: scale(0);\n    transform: scale(0);\n  }\n  to {\n    -webkit-transform: scale(1);\n    -ms-transform: scale(1);\n    transform: scale(1);\n  }\n}\n\n@keyframes scaleIn {\n  from {\n    -webkit-transform: scale(0);\n    -ms-transform: scale(0);\n    transform: scale(0);\n  }\n  to {\n    -webkit-transform: scale(1);\n    -ms-transform: scale(1);\n    transform: scale(1);\n  }\n}\n\n@-webkit-keyframes innerDrawIn {\n  0% {\n    stroke-dashoffset: 70;\n  }\n  50% {\n    stroke-dashoffset: 140;\n  }\n  100% {\n    stroke-dashoffset: 210;\n  }\n}\n\n@keyframes innerDrawIn {\n  0% {\n    stroke-dashoffset: 70;\n  }\n  50% {\n    stroke-dashoffset: 140;\n  }\n  100% {\n    stroke-dashoffset: 210;\n  }\n}\n\n@-webkit-keyframes outerDrawIn {\n  0% {\n    stroke-dashoffset: 76;\n  }\n  100% {\n    stroke-dashoffset: 152;\n  }\n}\n\n@keyframes outerDrawIn {\n  0% {\n    stroke-dashoffset: 76;\n  }\n  100% {\n    stroke-dashoffset: 152;\n  }\n}\n\n.hHWjkv {\n  -webkit-transform-origin: 0px 0px;\n  -ms-transform-origin: 0px 0px;\n  transform-origin: 0px 0px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 0.2222222222222222s;\n  animation: scaleIn 0.25s linear forwards 0.2222222222222222s;\n}\n\n.gCDOzd {\n  -webkit-transform-origin: 0px 0px;\n  -ms-transform-origin: 0px 0px;\n  transform-origin: 0px 0px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 0.4222222222222222s;\n  animation: scaleIn 0.25s linear forwards 0.4222222222222222s;\n}\n\n.hmCcxi {\n  -webkit-transform-origin: 0px 0px;\n  -ms-transform-origin: 0px 0px;\n  transform-origin: 0px 0px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 0.6222222222222222s;\n  animation: scaleIn 0.25s linear forwards 0.6222222222222222s;\n}\n\n.eHamQi {\n  -webkit-transform-origin: 0px 0px;\n  -ms-transform-origin: 0px 0px;\n  transform-origin: 0px 0px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 0.8222222222222223s;\n  animation: scaleIn 0.25s linear forwards 0.8222222222222223s;\n}\n\n.byhgGu {\n  -webkit-transform-origin: 0px 0px;\n  -ms-transform-origin: 0px 0px;\n  transform-origin: 0px 0px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 1.0222222222222221s;\n  animation: scaleIn 0.25s linear forwards 1.0222222222222221s;\n}\n\n.llAKP {\n  -webkit-transform-origin: 0px 0px;\n  -ms-transform-origin: 0px 0px;\n  transform-origin: 0px 0px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 1.2222222222222223s;\n  animation: scaleIn 0.25s linear forwards 1.2222222222222223s;\n}\n\n.bglIGM {\n  -webkit-transform-origin: 64px 28px;\n  -ms-transform-origin: 64px 28px;\n  transform-origin: 64px 28px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 0.2222222222222222s;\n  animation: scaleIn 0.25s linear forwards 0.2222222222222222s;\n}\n\n.ksxRII {\n  -webkit-transform-origin: 95.98500061035156px 46.510000228881836px;\n  -ms-transform-origin: 95.98500061035156px 46.510000228881836px;\n  transform-origin: 95.98500061035156px 46.510000228881836px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 0.4222222222222222s;\n  animation: scaleIn 0.25s linear forwards 0.4222222222222222s;\n}\n\n.cWrBmb {\n  -webkit-transform-origin: 95.97162628173828px 83.4900016784668px;\n  -ms-transform-origin: 95.97162628173828px 83.4900016784668px;\n  transform-origin: 95.97162628173828px 83.4900016784668px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 0.6222222222222222s;\n  animation: scaleIn 0.25s linear forwards 0.6222222222222222s;\n}\n\n.Wnusb {\n  -webkit-transform-origin: 64px 101.97999572753906px;\n  -ms-transform-origin: 64px 101.97999572753906px;\n  transform-origin: 64px 101.97999572753906px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 0.8222222222222223s;\n  animation: scaleIn 0.25s linear forwards 0.8222222222222223s;\n}\n\n.bfPqf {\n  -webkit-transform-origin: 32.03982162475586px 83.4900016784668px;\n  -ms-transform-origin: 32.03982162475586px 83.4900016784668px;\n  transform-origin: 32.03982162475586px 83.4900016784668px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 1.0222222222222221s;\n  animation: scaleIn 0.25s linear forwards 1.0222222222222221s;\n}\n\n.edRCTN {\n  -webkit-transform-origin: 32.033552169799805px 46.510000228881836px;\n  -ms-transform-origin: 32.033552169799805px 46.510000228881836px;\n  transform-origin: 32.033552169799805px 46.510000228881836px;\n  -webkit-transform: scale(0);\n  -ms-transform: scale(0);\n  transform: scale(0);\n  -webkit-animation: scaleIn 0.25s linear forwards 1.2222222222222223s;\n  animation: scaleIn 0.25s linear forwards 1.2222222222222223s;\n}\n\n.iEGVWn {\n  opacity: 0;\n  stroke-dasharray: 76;\n  -webkit-animation: outerDrawIn 0.5s ease-out forwards 0.3333333333333333s, appearIn 0.1s ease-out forwards 0.3333333333333333s;\n  animation: outerDrawIn 0.5s ease-out forwards 0.3333333333333333s, appearIn 0.1s ease-out forwards 0.3333333333333333s;\n  -webkit-animation-iteration-count: 1, 1;\n  animation-iteration-count: 1, 1;\n}\n\n.bsocdx {\n  opacity: 0;\n  stroke-dasharray: 76;\n  -webkit-animation: outerDrawIn 0.5s ease-out forwards 0.5333333333333333s, appearIn 0.1s ease-out forwards 0.5333333333333333s;\n  animation: outerDrawIn 0.5s ease-out forwards 0.5333333333333333s, appearIn 0.1s ease-out forwards 0.5333333333333333s;\n  -webkit-animation-iteration-count: 1, 1;\n  animation-iteration-count: 1, 1;\n}\n\n.jAZXmP {\n  opacity: 0;\n  stroke-dasharray: 76;\n  -webkit-animation: outerDrawIn 0.5s ease-out forwards 0.7333333333333334s, appearIn 0.1s ease-out forwards 0.7333333333333334s;\n  animation: outerDrawIn 0.5s ease-out forwards 0.7333333333333334s, appearIn 0.1s ease-out forwards 0.7333333333333334s;\n  -webkit-animation-iteration-count: 1, 1;\n  animation-iteration-count: 1, 1;\n}\n\n.hSeArx {\n  opacity: 0;\n  stroke-dasharray: 76;\n  -webkit-animation: outerDrawIn 0.5s ease-out forwards 0.9333333333333333s, appearIn 0.1s ease-out forwards 0.9333333333333333s;\n  animation: outerDrawIn 0.5s ease-out forwards 0.9333333333333333s, appearIn 0.1s ease-out forwards 0.9333333333333333s;\n  -webkit-animation-iteration-count: 1, 1;\n  animation-iteration-count: 1, 1;\n}\n\n.bVgqGk {\n  opacity: 0;\n  stroke-dasharray: 76;\n  -webkit-animation: outerDrawIn 0.5s ease-out forwards 1.1333333333333333s, appearIn 0.1s ease-out forwards 1.1333333333333333s;\n  animation: outerDrawIn 0.5s ease-out forwards 1.1333333333333333s, appearIn 0.1s ease-out forwards 1.1333333333333333s;\n  -webkit-animation-iteration-count: 1, 1;\n  animation-iteration-count: 1, 1;\n}\n\n.hEFqBt {\n  opacity: 0;\n  stroke-dasharray: 76;\n  -webkit-animation: outerDrawIn 0.5s ease-out forwards 1.3333333333333333s, appearIn 0.1s ease-out forwards 1.3333333333333333s;\n  animation: outerDrawIn 0.5s ease-out forwards 1.3333333333333333s, appearIn 0.1s ease-out forwards 1.3333333333333333s;\n  -webkit-animation-iteration-count: 1, 1;\n  animation-iteration-count: 1, 1;\n}\n\n.dzEKCM {\n  opacity: 0;\n  stroke-dasharray: 70;\n  -webkit-animation: innerDrawIn 1s ease-in-out forwards 1.3666666666666667s, appearIn 0.1s linear forwards 1.3666666666666667s;\n  animation: innerDrawIn 1s ease-in-out forwards 1.3666666666666667s, appearIn 0.1s linear forwards 1.3666666666666667s;\n  -webkit-animation-iteration-count: infinite, 1;\n  animation-iteration-count: infinite, 1;\n}\n\n.DYnPx {\n  opacity: 0;\n  stroke-dasharray: 70;\n  -webkit-animation: innerDrawIn 1s ease-in-out forwards 1.5333333333333332s, appearIn 0.1s linear forwards 1.5333333333333332s;\n  animation: innerDrawIn 1s ease-in-out forwards 1.5333333333333332s, appearIn 0.1s linear forwards 1.5333333333333332s;\n  -webkit-animation-iteration-count: infinite, 1;\n  animation-iteration-count: infinite, 1;\n}\n\n.hjPEAQ {\n  opacity: 0;\n  stroke-dasharray: 70;\n  -webkit-animation: innerDrawIn 1s ease-in-out forwards 1.7000000000000002s, appearIn 0.1s linear forwards 1.7000000000000002s;\n  animation: innerDrawIn 1s ease-in-out forwards 1.7000000000000002s, appearIn 0.1s linear forwards 1.7000000000000002s;\n  -webkit-animation-iteration-count: infinite, 1;\n  animation-iteration-count: infinite, 1;\n}\n\n#loading-wrapper {\n  position: absolute;\n  width: 100vw;\n  height: 100vh;\n  display: -webkit-box;\n  display: -webkit-flex;\n  display: -ms-flexbox;\n  display: flex;\n  -webkit-align-items: center;\n  -webkit-box-align: center;\n  -ms-flex-align: center;\n  align-items: center;\n  -webkit-box-pack: center;\n  -webkit-justify-content: center;\n  -ms-flex-pack: center;\n  justify-content: center;\n  -webkit-flex-direction: column;\n  -ms-flex-direction: column;\n  flex-direction: column;\n}\n\n.logo {\n  width: 75px;\n  height: 75px;\n  margin-bottom: 20px;\n  opacity: 0;\n  -webkit-animation: fadeIn 0.5s ease-out forwards;\n  animation: fadeIn 0.5s ease-out forwards;\n}\n\n.text {\n  font-size: 32px;\n  font-weight: 200;\n  text-align: center;\n  color: rgba(255, 255, 255, 0.6);\n  opacity: 0;\n  -webkit-animation: fadeIn 0.5s ease-out forwards;\n  animation: fadeIn 0.5s ease-out forwards;\n}\n\n.dGfHfc {\n  font-weight: 400;\n}\n</style>\n<div id="loading-wrapper">\n<svg class="logo" viewBox="0 0 128 128" xmlns:xlink="http://www.w3.org/1999/xlink">\n  <title>GraphQL Playground Logo</title>\n  <defs>\n    <linearGradient id="linearGradient-1" x1="4.86%" x2="96.21%" y1="0%" y2="99.66%">\n      <stop stop-color="#E00082" stop-opacity=".8" offset="0%"></stop>\n      <stop stop-color="#E00082" offset="100%"></stop>\n    </linearGradient>\n  </defs>\n  <g>\n    <rect id="Gradient" width="127.96" height="127.96" y="1" fill="url(#linearGradient-1)" rx="4"></rect>\n    <path id="Border" fill="#E00082" fill-rule="nonzero" d="M4.7 2.84c-1.58 0-2.86 1.28-2.86 2.85v116.57c0 1.57 1.28 2.84 2.85 2.84h116.57c1.57 0 2.84-1.26 2.84-2.83V5.67c0-1.55-1.26-2.83-2.83-2.83H4.67zM4.7 0h116.58c3.14 0 5.68 2.55 5.68 5.7v116.58c0 3.14-2.54 5.68-5.68 5.68H4.68c-3.13 0-5.68-2.54-5.68-5.68V5.68C-1 2.56 1.55 0 4.7 0z"></path>\n    <path class="bglIGM" x="64" y="28" fill="#fff" d="M64 36c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8" style="transform: translate(100px, 100px);"></path>\n    <path class="ksxRII" x="95.98500061035156" y="46.510000228881836" fill="#fff" d="M89.04 50.52c-2.2-3.84-.9-8.73 2.94-10.96 3.83-2.2 8.72-.9 10.95 2.94 2.2 3.84.9 8.73-2.94 10.96-3.85 2.2-8.76.9-10.97-2.94"\n      style="transform: translate(100px, 100px);"></path>\n    <path class="cWrBmb" x="95.97162628173828" y="83.4900016784668" fill="#fff" d="M102.9 87.5c-2.2 3.84-7.1 5.15-10.94 2.94-3.84-2.2-5.14-7.12-2.94-10.96 2.2-3.84 7.12-5.15 10.95-2.94 3.86 2.23 5.16 7.12 2.94 10.96"\n      style="transform: translate(100px, 100px);"></path>\n    <path class="Wnusb" x="64" y="101.97999572753906" fill="#fff" d="M64 110c-4.43 0-8-3.6-8-8.02 0-4.44 3.57-8.02 8-8.02s8 3.58 8 8.02c0 4.4-3.57 8.02-8 8.02"\n      style="transform: translate(100px, 100px);"></path>\n    <path class="bfPqf" x="32.03982162475586" y="83.4900016784668" fill="#fff" d="M25.1 87.5c-2.2-3.84-.9-8.73 2.93-10.96 3.83-2.2 8.72-.9 10.95 2.94 2.2 3.84.9 8.73-2.94 10.96-3.85 2.2-8.74.9-10.95-2.94"\n      style="transform: translate(100px, 100px);"></path>\n    <path class="edRCTN" x="32.033552169799805" y="46.510000228881836" fill="#fff" d="M38.96 50.52c-2.2 3.84-7.12 5.15-10.95 2.94-3.82-2.2-5.12-7.12-2.92-10.96 2.2-3.84 7.12-5.15 10.95-2.94 3.83 2.23 5.14 7.12 2.94 10.96"\n      style="transform: translate(100px, 100px);"></path>\n    <path class="iEGVWn" stroke="#fff" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" d="M63.55 27.5l32.9 19-32.9-19z"></path>\n    <path class="bsocdx" stroke="#fff" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" d="M96 46v38-38z"></path>\n    <path class="jAZXmP" stroke="#fff" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" d="M96.45 84.5l-32.9 19 32.9-19z"></path>\n    <path class="hSeArx" stroke="#fff" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" d="M64.45 103.5l-32.9-19 32.9 19z"></path>\n    <path class="bVgqGk" stroke="#fff" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" d="M32 84V46v38z"></path>\n    <path class="hEFqBt" stroke="#fff" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" d="M31.55 46.5l32.9-19-32.9 19z"></path>\n    <path class="dzEKCM" id="Triangle-Bottom" stroke="#fff" stroke-width="4" d="M30 84h70" stroke-linecap="round"></path>\n    <path class="DYnPx" id="Triangle-Left" stroke="#fff" stroke-width="4" d="M65 26L30 87" stroke-linecap="round"></path>\n    <path class="hjPEAQ" id="Triangle-Right" stroke="#fff" stroke-width="4" d="M98 87L63 26" stroke-linecap="round"></path>\n  </g>\n</svg>\n<div class="text">Loading\n  <span class="dGfHfc">GraphQL Playground</span>\n</div>\n</div>\n'}}},91043:a=>{"use strict";a.exports=require("@aws-sdk/client-s3")},91645:a=>{"use strict";a.exports=require("net")},92055:(a,b,c)=>{"use strict";c.d(b,{q:()=>m});var d=c(77593);let e=Object.prototype.hasOwnProperty,f=Array.isArray,g={allowDots:!1,allowEmptyArrays:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decodeDotInKeys:!1,decoder:d.D4,delimiter:"&",depth:5,duplicates:"combine",ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},h=function(a,b){return a&&"string"==typeof a&&b.comma&&a.indexOf(",")>-1?a.split(","):a},i=function(a,b){let c,i={__proto__:null},j=b.ignoreQueryPrefix?a.replace(/^\?/,""):a,k=b.parameterLimit===1/0?void 0:b.parameterLimit,l=j.split(b.delimiter,k),m=-1,n=b.charset;if(b.charsetSentinel)for(c=0;c<l.length;++c)0===l[c].indexOf("utf8=")&&("utf8=%E2%9C%93"===l[c]?n="utf-8":"utf8=%26%2310003%3B"===l[c]&&(n="iso-8859-1"),m=c,c=l.length);for(c=0;c<l.length;++c){let a,j;if(c===m)continue;let k=l[c],o=k.indexOf("]="),p=-1===o?k.indexOf("="):o+1;-1===p?(a=b.decoder(k,g.decoder,n,"key"),j=b.strictNullHandling?null:""):(a=b.decoder(k.slice(0,p),g.decoder,n,"key"),j=d.F7(h(k.slice(p+1),b),function(a){return b.decoder(a,g.decoder,n,"value")})),j&&b.interpretNumericEntities&&"iso-8859-1"===n&&(j=j.replace(/&#(\d+);/g,function(a,b){return String.fromCharCode(parseInt(b,10))})),k.indexOf("[]=")>-1&&(j=f(j)?[j]:j);let q=e.call(i,a);q&&"combine"===b.duplicates?i[a]=d.kg(i[a],j):q&&"last"!==b.duplicates||(i[a]=j)}return i},j=function(a,b,c,d){let e=d?b:h(b,c);for(let b=a.length-1;b>=0;--b){let d,f=a[b];if("[]"===f&&c.parseArrays)d=c.allowEmptyArrays&&""===e?[]:[].concat(e);else{d=c.plainObjects?Object.create(null):{};let a="["===f.charAt(0)&&"]"===f.charAt(f.length-1)?f.slice(1,-1):f,b=c.decodeDotInKeys?a.replace(/%2E/g,"."):a,g=parseInt(b,10);c.parseArrays||""!==b?!isNaN(g)&&f!==b&&String(g)===b&&g>=0&&c.parseArrays&&g<=c.arrayLimit?(d=[])[g]=e:"__proto__"!==b&&(d[b]=e):d={0:e}}e=d}return e},k=function(a,b,c,d){if(!a)return;let f=c.allowDots?a.replace(/\.([^.[]+)/g,"[$1]"):a,g=/(\[[^[\]]*])/g,h=c.depth>0&&/(\[[^[\]]*])/.exec(f),i=h?f.slice(0,h.index):f,k=[];if(i){if(!c.plainObjects&&e.call(Object.prototype,i)&&!c.allowPrototypes)return;k.push(i)}let l=0;for(;c.depth>0&&null!==(h=g.exec(f))&&l<c.depth;){if(l+=1,!c.plainObjects&&e.call(Object.prototype,h[1].slice(1,-1))&&!c.allowPrototypes)return;k.push(h[1])}return h&&k.push("["+f.slice(h.index)+"]"),j(k,b,c,d)},l=function(a){if(!a)return g;if(void 0!==a.allowEmptyArrays&&"boolean"!=typeof a.allowEmptyArrays)throw TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if(void 0!==a.decodeDotInKeys&&"boolean"!=typeof a.decodeDotInKeys)throw TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==a.decoder&&void 0!==a.decoder&&"function"!=typeof a.decoder)throw TypeError("Decoder has to be a function.");if(void 0!==a.charset&&"utf-8"!==a.charset&&"iso-8859-1"!==a.charset)throw TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");let b=void 0===a.charset?g.charset:a.charset,c=void 0===a.duplicates?g.duplicates:a.duplicates;if("combine"!==c&&"first"!==c&&"last"!==c)throw TypeError("The duplicates option must be either combine, first, or last");return{allowDots:void 0===a.allowDots?!0===a.decodeDotInKeys||g.allowDots:!!a.allowDots,allowEmptyArrays:"boolean"==typeof a.allowEmptyArrays?!!a.allowEmptyArrays:g.allowEmptyArrays,allowPrototypes:"boolean"==typeof a.allowPrototypes?a.allowPrototypes:g.allowPrototypes,allowSparse:"boolean"==typeof a.allowSparse?a.allowSparse:g.allowSparse,arrayLimit:"number"==typeof a.arrayLimit?a.arrayLimit:g.arrayLimit,charset:b,charsetSentinel:"boolean"==typeof a.charsetSentinel?a.charsetSentinel:g.charsetSentinel,comma:"boolean"==typeof a.comma?a.comma:g.comma,decodeDotInKeys:"boolean"==typeof a.decodeDotInKeys?a.decodeDotInKeys:g.decodeDotInKeys,decoder:"function"==typeof a.decoder?a.decoder:g.decoder,delimiter:"string"==typeof a.delimiter||d.gd(a.delimiter)?a.delimiter:g.delimiter,depth:"number"==typeof a.depth||!1===a.depth?+a.depth:g.depth,duplicates:c,ignoreQueryPrefix:!0===a.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof a.interpretNumericEntities?a.interpretNumericEntities:g.interpretNumericEntities,parameterLimit:"number"==typeof a.parameterLimit?a.parameterLimit:g.parameterLimit,parseArrays:!1!==a.parseArrays,plainObjects:"boolean"==typeof a.plainObjects?a.plainObjects:g.plainObjects,strictNullHandling:"boolean"==typeof a.strictNullHandling?a.strictNullHandling:g.strictNullHandling}};function m(a,b){let c=l(b);if(""===a||null==a)return c.plainObjects?Object.create(null):{};let e="string"==typeof a?i(a,c):a,f=c.plainObjects?Object.create(null):{},g=Object.keys(e);for(let b=0;b<g.length;++b){let h=g[b],i=k(h,e[h],c,"string"==typeof a);f=d.h1(f,i,c)}return!0===c.allowSparse?f:d.oE(f)}},94735:a=>{"use strict";a.exports=require("events")},95626:(a,b,c)=>{var d=c(25384),e=c(60150);function f(a){(a=function(a){var b={};for(var c in a)b[c]=a[c];return b}(a||{})).whiteList=a.whiteList||d.whiteList,a.onAttr=a.onAttr||d.onAttr,a.onIgnoreAttr=a.onIgnoreAttr||d.onIgnoreAttr,a.safeAttrValue=a.safeAttrValue||d.safeAttrValue,this.options=a}c(39085),f.prototype.process=function(a){if(!(a=(a=a||"").toString()))return"";var b=this.options,c=b.whiteList,d=b.onAttr,f=b.onIgnoreAttr,g=b.safeAttrValue;return e(a,function(a,b,e,h,i){var j=c[e],k=!1;if(!0===j?k=j:"function"==typeof j?k=j(h):j instanceof RegExp&&(k=j.test(h)),!0!==k&&(k=!1),h=g(e,h)){var l={position:b,sourcePosition:a,source:i,isWhite:k};if(k){var m=d(e,h,l);return null==m?e+":"+h:m}var m=f(e,h,l);if(null!=m)return m}})},a.exports=f},95636:(a,b,c)=>{var d=c(57853).FilterCSS,e=c(39495),f=c(81631),g=f.parseTag,h=f.parseAttr,i=c(3644);function j(a){(a=function(a){var b={};for(var c in a)b[c]=a[c];return b}(a||{})).stripIgnoreTag&&(a.onIgnoreTag&&console.error('Notes: cannot use these two options "stripIgnoreTag" and "onIgnoreTag" at the same time'),a.onIgnoreTag=e.onIgnoreTagStripAll),a.whiteList||a.allowList?a.whiteList=function(a){var b={};for(var c in a)Array.isArray(a[c])?b[c.toLowerCase()]=a[c].map(function(a){return a.toLowerCase()}):b[c.toLowerCase()]=a[c];return b}(a.whiteList||a.allowList):a.whiteList=e.whiteList,this.attributeWrapSign=!0===a.singleQuotedAttributeValue?"'":e.attributeWrapSign,a.onTag=a.onTag||e.onTag,a.onTagAttr=a.onTagAttr||e.onTagAttr,a.onIgnoreTag=a.onIgnoreTag||e.onIgnoreTag,a.onIgnoreTagAttr=a.onIgnoreTagAttr||e.onIgnoreTagAttr,a.safeAttrValue=a.safeAttrValue||e.safeAttrValue,a.escapeHtml=a.escapeHtml||e.escapeHtml,this.options=a,!1===a.css?this.cssFilter=!1:(a.css=a.css||{},this.cssFilter=new d(a.css))}j.prototype.process=function(a){if(!(a=(a=a||"").toString()))return"";var b=this.options,c=b.whiteList,d=b.onTag,f=b.onIgnoreTag,j=b.onTagAttr,k=b.onIgnoreTagAttr,l=b.safeAttrValue,m=b.escapeHtml,n=this.attributeWrapSign,o=this.cssFilter;b.stripBlankChar&&(a=e.stripBlankChar(a)),b.allowCommentTag||(a=e.stripCommentTag(a));var p=!1;b.stripIgnoreTagBody&&(f=(p=e.StripTagBody(b.stripIgnoreTagBody,f)).onIgnoreTag);var q=g(a,function(a,b,e,g,p){var q={sourcePosition:a,position:b,isClosing:p,isWhite:Object.prototype.hasOwnProperty.call(c,e)},r=d(e,g,q);if(null!=r)return r;if(q.isWhite){if(q.isClosing)return"</"+e+">";var s=function(a){var b=i.spaceIndex(a);if(-1===b)return{html:"",closing:"/"===a[a.length-2]};var c="/"===(a=i.trim(a.slice(b+1,-1)))[a.length-1];return c&&(a=i.trim(a.slice(0,-1))),{html:a,closing:c}}(g),t=c[e],u=h(s.html,function(a,b){var c=-1!==i.indexOf(t,a),d=j(e,a,b,c);return null!=d?d:c?(b=l(e,a,b,o))?a+"="+n+b+n:a:null!=(d=k(e,a,b,c))?d:void 0});return g="<"+e,u&&(g+=" "+u),s.closing&&(g+=" /"),g+=">"}return null!=(r=f(e,g,q))?r:m(g)},m);return p&&(q=p.remove(q)),q},a.exports=j},96559:(a,b,c)=>{"use strict";a.exports=c(44870)},98995:a=>{"use strict";a.exports=require("node:module")}};var b=require("../../../../webpack-runtime.js");b.C(a);var c=b.X(0,[1103,9598,6622],()=>b(b.s=77438));module.exports=c})();