'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';

interface SyncStats {
  challenges: {
    cmsChallenge: number;
    mainAppCmsChallenge: number;
    mainAppTotalChallenge: number;
  };
  users: {
    cmsUsers: number;
    mainAppCmsUsers: number;
    mainAppTotalUsers: number;
  };
}

interface SyncResult {
  total: number;
  synced: number;
  skipped: number;
  errors: number;
}

export default function SyncPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [stats, setStats] = useState<SyncStats | null>(null);
  const [lastChallengeSync, setLastChallengeSync] = useState<SyncResult | null>(null);
  const [lastUserSync, setLastUserSync] = useState<SyncResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/signin?redirect=/admin/sync');
    }
  }, [user, loading, router]);

  useEffect(() => {
    if (user) {
      fetchStats();
    }
  }, [user]);

  const showError = (message: string) => {
    setError(message);
    setTimeout(() => setError(null), 5000);
  };

  const showSuccess = (message: string) => {
    // Simple success notification
    alert(message);
  };

  const fetchStats = async () => {
    try {
      setError(null);
      
      const response = await fetch('http://localhost:3001/api/sync/challenges');
      
      if (!response.ok) {
        throw new Error(`Failed to fetch sync statistics: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success) {
        setStats({
          challenges: data.stats,
          users: { cmsUsers: 0, mainAppCmsUsers: 0, mainAppTotalUsers: 0 } // Placeholder
        });
      } else {
        throw new Error('Invalid response from sync API');
      }
    } catch (error) {
      console.error('Error fetching sync stats:', error);
      showError(error instanceof Error ? error.message : 'Failed to fetch sync statistics');
    }
  };

  const handleChallengeSync = async () => {
    if (isLoading) return;
    
    setIsLoading(true);
    
    try {
      const response = await fetch('http://localhost:3001/api/sync/challenges', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (data.success) {
        setLastChallengeSync(data.stats);
        await fetchStats(); // Refresh stats
        showSuccess(`Challenge sync completed! ${data.stats.synced} challenges synced successfully.`);
      } else {
        throw new Error(data.error || 'Challenge sync failed');
      }
    } catch (error) {
      console.error('Error syncing challenges:', error);
      showError(error instanceof Error ? error.message : 'Failed to sync challenges');
    } finally {
      setIsLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading sync panel...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container max-w-7xl mx-auto py-8 px-4">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">🔄 Sync Management</h1>
          <p className="text-gray-600">
            Synchronize content and users between CMS and main application
          </p>
          <div className="mt-4 inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
            🛡️ Admin Access
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <div className="text-red-600 mr-2">❌</div>
              <span className="text-red-800 font-medium">{error}</span>
            </div>
          </div>
        )}

        {/* Sync Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {/* Challenge Sync Card */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
              📝 Challenge Sync
            </h2>
            <p className="text-gray-600 mb-6 text-sm">
              Sync published challenges from CMS to main application
            </p>
            
            {stats && (
              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="bg-blue-50 p-4 rounded-lg text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {stats.challenges.cmsChallenge || '0'}
                  </div>
                  <div className="text-xs text-blue-800 font-medium">CMS Challenges</div>
                </div>
                <div className="bg-green-50 p-4 rounded-lg text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {stats.challenges.mainAppCmsChallenge || '0'}
                  </div>
                  <div className="text-xs text-green-800 font-medium">Synced to Main App</div>
                </div>
              </div>
            )}

            <button
              onClick={handleChallengeSync}
              disabled={isLoading}
              className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                isLoading
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
            >
              {isLoading ? '🔄 Syncing Challenges...' : '🔄 Sync Challenges to Main App'}
            </button>

            {lastChallengeSync && (
              <div className="mt-4 text-sm text-gray-600">
                <p className="font-medium">Last Sync Result:</p>
                <div className="flex justify-between text-xs">
                  <span>Total: {lastChallengeSync.total}</span>
                  <span className="text-green-600">Synced: {lastChallengeSync.synced}</span>
                  {lastChallengeSync.errors > 0 && (
                    <span className="text-red-600">Errors: {lastChallengeSync.errors}</span>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* User Sync Card */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
              👥 User Sync
            </h2>
            <p className="text-gray-600 mb-6 text-sm">
              Sync CMS users to main application for content access
            </p>
            
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div className="bg-purple-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {stats?.users.cmsUsers || '0'}
                </div>
                <div className="text-xs text-purple-800 font-medium">CMS Users</div>
              </div>
              <div className="bg-orange-50 p-4 rounded-lg text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {stats?.users.mainAppCmsUsers || '0'}
                </div>
                <div className="text-xs text-orange-800 font-medium">Synced to Main App</div>
              </div>
            </div>

            <button
              disabled={true}
              className="w-full py-3 px-4 rounded-lg font-medium bg-gray-300 text-gray-500 cursor-not-allowed"
            >
              🔄 Sync Users (Coming Soon)
            </button>
          </div>
        </div>

        {/* Refresh Button */}
        <div className="text-center mb-8">
          <button
            onClick={fetchStats}
            disabled={isLoading}
            className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
          >
            🔄 Refresh Statistics
          </button>
        </div>

        {/* Info Panel */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-yellow-800 mb-3">
            ℹ️ Sync Information
          </h3>
          <ul className="text-sm text-yellow-800 space-y-2">
            <li><strong>Challenge Sync:</strong> Publishes CMS challenges to the main application for users to access</li>
            <li><strong>User Sync:</strong> Creates accounts in the main application for CMS content creators and educators</li>
            <li><strong>Admin Only:</strong> Only authenticated users can perform sync operations</li>
            <li><strong>Safe Operation:</strong> Sync operations are idempotent and won't create duplicates</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
