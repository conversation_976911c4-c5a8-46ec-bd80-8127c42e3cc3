"use strict";exports.id=7674,exports.ids=[7674],exports.modules={87674:(e,t,r)=>{r.d(t,{fromHttp:()=>w});var o=r(6884),n=r(52934),s=r(53438),a=r(79748),i=r.n(a);let c=(e,t)=>{if("https:"!==e.protocol&&"*************"!==e.hostname&&"**************"!==e.hostname&&"[fd00:ec2::23]"!==e.hostname){if(e.hostname.includes("[")){if("[::1]"===e.hostname||"[0000:0000:0000:0000:0000:0000:0000:0001]"===e.hostname)return}else{if("localhost"===e.hostname)return;let t=e.hostname.split("."),r=e=>{let t=parseInt(e,10);return 0<=t&&t<=255};if("127"===t[0]&&r(t[1])&&r(t[2])&&r(t[3])&&4===t.length)return}throw new s.C1(`URL not accepted. It must either be HTTPS or match one of the following:
  - loopback CIDR *********/8 or [::1/128]
  - ECS container host *************
  - EKS container host ************** or [fd00:ec2::23]`,{logger:t})}};var l=r(37250),d=r(14723),h=r(4645);async function p(e,t){let r=(0,h.c9)(e.body),o=await r.transformToString();if(200===e.statusCode){let e=JSON.parse(o);if("string"!=typeof e.AccessKeyId||"string"!=typeof e.SecretAccessKey||"string"!=typeof e.Token||"string"!=typeof e.Expiration)throw new s.C1("HTTP credential provider response not of the required format, an object matching: { AccessKeyId: string, SecretAccessKey: string, Token: string, Expiration: string(rfc3339) }",{logger:t});return{accessKeyId:e.AccessKeyId,secretAccessKey:e.SecretAccessKey,sessionToken:e.Token,expiration:(0,d.EI)(e.Expiration)}}if(e.statusCode>=400&&e.statusCode<500){let r={};try{r=JSON.parse(o)}catch(e){}throw Object.assign(new s.C1(`Server responded with status: ${e.statusCode}`,{logger:t}),{Code:r.Code,Message:r.Message})}throw new s.C1(`Server responded with status: ${e.statusCode}`,{logger:t})}let u=(e,t,r)=>async()=>{for(let o=0;o<t;++o)try{return await e()}catch(e){await new Promise(e=>setTimeout(e,r))}return await e()},w=(e={})=>{let t;e.logger?.debug("@aws-sdk/credential-provider-http - fromHttp");let r=e.awsContainerCredentialsRelativeUri??process.env.AWS_CONTAINER_CREDENTIALS_RELATIVE_URI,a=e.awsContainerCredentialsFullUri??process.env.AWS_CONTAINER_CREDENTIALS_FULL_URI,d=e.awsContainerAuthorizationToken??process.env.AWS_CONTAINER_AUTHORIZATION_TOKEN,h=e.awsContainerAuthorizationTokenFile??process.env.AWS_CONTAINER_AUTHORIZATION_TOKEN_FILE,w=e.logger?.constructor?.name!=="NoOpLogger"&&e.logger?e.logger.warn:console.warn;if(r&&a&&(w("@aws-sdk/credential-provider-http: you have set both awsContainerCredentialsRelativeUri and awsContainerCredentialsFullUri."),w("awsContainerCredentialsFullUri will take precedence.")),d&&h&&(w("@aws-sdk/credential-provider-http: you have set both awsContainerAuthorizationToken and awsContainerAuthorizationTokenFile."),w("awsContainerAuthorizationToken will take precedence.")),a)t=a;else if(r)t=`http://*************${r}`;else throw new s.C1(`No HTTP credential provider host provided.
Set AWS_CONTAINER_CREDENTIALS_FULL_URI or AWS_CONTAINER_CREDENTIALS_RELATIVE_URI.`,{logger:e.logger});let C=new URL(t);c(C,e.logger);let g=new n.$c({requestTimeout:e.timeout??1e3,connectionTimeout:e.timeout??1e3});return u(async()=>{let t=function(e){return new l.Kd({protocol:e.protocol,hostname:e.hostname,port:Number(e.port),path:e.pathname,query:Array.from(e.searchParams.entries()).reduce((e,[t,r])=>(e[t]=r,e),{}),fragment:e.hash})}(C);d?t.headers.Authorization=d:h&&(t.headers.Authorization=(await i().readFile(h)).toString());try{let e=await g.handle(t);return p(e.response).then(e=>(0,o.g)(e,"CREDENTIALS_HTTP","z"))}catch(t){throw new s.C1(String(t),{logger:e.logger})}},e.maxRetries??3,e.timeout??1e3)}}};