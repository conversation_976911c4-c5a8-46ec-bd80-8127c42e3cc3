(()=>{var e={};e.id=6676,e.ids=[6676],e.modules={91043:e=>{"use strict";e.exports=require("@aws-sdk/client-s3")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74552:e=>{"use strict";e.exports=require("pino")},14007:e=>{"use strict";e.exports=require("pino-pretty")},82015:e=>{"use strict";e.exports=require("react")},22326:e=>{"use strict";e.exports=require("react-dom")},8732:e=>{"use strict";e.exports=require("react/jsx-runtime")},79428:e=>{"use strict";e.exports=require("buffer")},79646:e=>{"use strict";e.exports=require("child_process")},55511:e=>{"use strict";e.exports=require("crypto")},14985:e=>{"use strict";e.exports=require("dns")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},73496:e=>{"use strict";e.exports=require("http2")},55591:e=>{"use strict";e.exports=require("https")},8086:e=>{"use strict";e.exports=require("module")},91645:e=>{"use strict";e.exports=require("net")},21820:e=>{"use strict";e.exports=require("os")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},11997:e=>{"use strict";e.exports=require("punycode")},4984:e=>{"use strict";e.exports=require("readline")},27910:e=>{"use strict";e.exports=require("stream")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},28855:e=>{"use strict";e.exports=import("@libsql/client")},34589:e=>{"use strict";e.exports=require("node:assert")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},4573:e=>{"use strict";e.exports=require("node:buffer")},37540:e=>{"use strict";e.exports=require("node:console")},77598:e=>{"use strict";e.exports=require("node:crypto")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},40610:e=>{"use strict";e.exports=require("node:dns")},78474:e=>{"use strict";e.exports=require("node:events")},73024:e=>{"use strict";e.exports=require("node:fs")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},37067:e=>{"use strict";e.exports=require("node:http")},32467:e=>{"use strict";e.exports=require("node:http2")},98995:e=>{"use strict";e.exports=require("node:module")},77030:e=>{"use strict";e.exports=require("node:net")},48161:e=>{"use strict";e.exports=require("node:os")},76760:e=>{"use strict";e.exports=require("node:path")},643:e=>{"use strict";e.exports=require("node:perf_hooks")},1708:e=>{"use strict";e.exports=require("node:process")},41792:e=>{"use strict";e.exports=require("node:querystring")},80099:e=>{"use strict";e.exports=require("node:sqlite")},57075:e=>{"use strict";e.exports=require("node:stream")},37830:e=>{"use strict";e.exports=require("node:stream/web")},41692:e=>{"use strict";e.exports=require("node:tls")},73136:e=>{"use strict";e.exports=require("node:url")},57975:e=>{"use strict";e.exports=require("node:util")},73429:e=>{"use strict";e.exports=require("node:util/types")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},38522:e=>{"use strict";e.exports=require("node:zlib")},39727:()=>{},47990:()=>{},34600:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>p,tree:()=>l});var s=r(70260),n=r(28203),i=r(25155),o=r.n(i),a=r(67292),u={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>a[e]);r.d(t,u);let l=["",{children:["(frontend)",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,36603)),"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(frontend)\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,90644)),"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(frontend)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"]}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(frontend)\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(frontend)/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},85821:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,13219,23)),Promise.resolve().then(r.t.bind(r,34863,23)),Promise.resolve().then(r.t.bind(r,25155,23)),Promise.resolve().then(r.t.bind(r,9350,23)),Promise.resolve().then(r.t.bind(r,96313,23)),Promise.resolve().then(r.t.bind(r,48530,23)),Promise.resolve().then(r.t.bind(r,88921,23))},13437:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,66959,23)),Promise.resolve().then(r.t.bind(r,33875,23)),Promise.resolve().then(r.t.bind(r,88903,23)),Promise.resolve().then(r.t.bind(r,84178,23)),Promise.resolve().then(r.t.bind(r,86013,23)),Promise.resolve().then(r.t.bind(r,87190,23)),Promise.resolve().then(r.t.bind(r,61365,23))},87057:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,71066,23))},2737:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,41902,23))},61028:()=>{},68644:()=>{},41902:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return x}});let s=r(25488),n=r(81063),i=r(8732),o=n._(r(82015)),a=s._(r(22326)),u=s._(r(59153)),l=r(42034),d=r(94653),c=r(48156);r(76831);let p=r(84055),f=s._(r(21628)),h=r(73727),m={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1};function g(e,t,r,s,n,i,o){let a=null==e?void 0:e.src;e&&e["data-loaded-src"]!==a&&(e["data-loaded-src"]=a,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&n(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let s=!1,n=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>s,isPropagationStopped:()=>n,persist:()=>{},preventDefault:()=>{s=!0,t.preventDefault()},stopPropagation:()=>{n=!0,t.stopPropagation()}})}(null==s?void 0:s.current)&&s.current(e)}}))}function y(e){return o.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let b=(0,o.forwardRef)((e,t)=>{let{src:r,srcSet:s,sizes:n,height:a,width:u,decoding:l,className:d,style:c,fetchPriority:p,placeholder:f,loading:m,unoptimized:b,fill:v,onLoadRef:x,onLoadingCompleteRef:w,setBlurComplete:S,setShowAltText:_,sizesInput:j,onLoad:k,onError:P,...C}=e,A=(0,o.useCallback)(e=>{e&&(P&&(e.src=e.src),e.complete&&g(e,f,x,w,S,b,j))},[r,f,x,w,S,P,b,j]),q=(0,h.useMergedRef)(t,A);return(0,i.jsx)("img",{...C,...y(p),loading:m,width:u,height:a,decoding:l,"data-nimg":v?"fill":"1",className:d,style:c,sizes:n,srcSet:s,src:r,ref:q,onLoad:e=>{g(e.currentTarget,f,x,w,S,b,j)},onError:e=>{_(!0),"empty"!==f&&S(!0),P&&P(e)}})});function v(e){let{isAppRouter:t,imgAttributes:r}=e,s={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...y(r.fetchPriority)};return t&&a.default.preload?(a.default.preload(r.src,s),null):(0,i.jsx)(u.default,{children:(0,i.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...s},"__nimg-"+r.src+r.srcSet+r.sizes)})}let x=(0,o.forwardRef)((e,t)=>{let r=(0,o.useContext)(p.RouterContext),s=(0,o.useContext)(c.ImageConfigContext),n=(0,o.useMemo)(()=>{let e=m||s||d.imageConfigDefault,t=[...e.deviceSizes,...e.imageSizes].sort((e,t)=>e-t),r=e.deviceSizes.sort((e,t)=>e-t);return{...e,allSizes:t,deviceSizes:r}},[s]),{onLoad:a,onLoadingComplete:u}=e,h=(0,o.useRef)(a);(0,o.useEffect)(()=>{h.current=a},[a]);let g=(0,o.useRef)(u);(0,o.useEffect)(()=>{g.current=u},[u]);let[y,x]=(0,o.useState)(!1),[w,S]=(0,o.useState)(!1),{props:_,meta:j}=(0,l.getImgProps)(e,{defaultLoader:f.default,imgConf:n,blurComplete:y,showAltText:w});return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(b,{..._,unoptimized:j.unoptimized,placeholder:j.placeholder,fill:j.fill,onLoadRef:h,onLoadingCompleteRef:g,setBlurComplete:x,setShowAltText:S,sizesInput:e.sizes,ref:t}),j.priority?(0,i.jsx)(v,{isAppRouter:!r,imgAttributes:_}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},73727:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return n}});let s=r(82015);function n(e,t){let r=(0,s.useRef)(()=>{}),n=(0,s.useRef)(()=>{});return(0,s.useMemo)(()=>e&&t?s=>{null===s?(r.current(),n.current()):(r.current=i(e,s),n.current=i(t,s))}:e||t,[e,t])}function i(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},32782:(e,t,r)=>{"use strict";e.exports=r(8104).vendored.contexts.AmpContext},6302:(e,t,r)=>{"use strict";e.exports=r(8104).vendored.contexts.HeadManagerContext},48156:(e,t,r)=>{"use strict";e.exports=r(8104).vendored.contexts.ImageConfigContext},84055:(e,t,r)=>{"use strict";e.exports=r(8104).vendored.contexts.RouterContext},62677:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:s=!1}=void 0===e?{}:e;return t||r&&s}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},42034:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return a}}),r(76831);let s=r(38337),n=r(94653);function i(e){return void 0!==e.default}function o(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function a(e,t){var r;let a,u,l,{src:d,sizes:c,unoptimized:p=!1,priority:f=!1,loading:h,className:m,quality:g,width:y,height:b,fill:v=!1,style:x,overrideSrc:w,onLoad:S,onLoadingComplete:_,placeholder:j="empty",blurDataURL:k,fetchPriority:P,decoding:C="async",layout:A,objectFit:q,objectPosition:R,lazyBoundary:O,lazyRoot:E,...M}=e,{imgConf:z,showAltText:D,blurComplete:I,defaultLoader:T}=t,L=z||n.imageConfigDefault;if("allSizes"in L)a=L;else{let e=[...L.deviceSizes,...L.imageSizes].sort((e,t)=>e-t),t=L.deviceSizes.sort((e,t)=>e-t);a={...L,allSizes:e,deviceSizes:t}}if(void 0===T)throw Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config");let $=M.loader||T;delete M.loader,delete M.srcSet;let N="__next_img_default"in $;if(N){if("custom"===a.loader)throw Error('Image with src "'+d+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}else{let e=$;$=t=>{let{config:r,...s}=t;return e(s)}}if(A){"fill"===A&&(v=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[A];e&&(x={...x,...e});let t={responsive:"100vw",fill:"100vw"}[A];t&&!c&&(c=t)}let U="",G=o(y),B=o(b);if((r=d)&&"object"==typeof r&&(i(r)||void 0!==r.src)){let e=i(d)?d.default:d;if(!e.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e));if(!e.height||!e.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e));if(u=e.blurWidth,l=e.blurHeight,k=k||e.blurDataURL,U=e.src,!v){if(G||B){if(G&&!B){let t=G/e.width;B=Math.round(e.height*t)}else if(!G&&B){let t=B/e.height;G=Math.round(e.width*t)}}else G=e.width,B=e.height}}let W=!f&&("lazy"===h||void 0===h);(!(d="string"==typeof d?d:U)||d.startsWith("data:")||d.startsWith("blob:"))&&(p=!0,W=!1),a.unoptimized&&(p=!0),N&&d.endsWith(".svg")&&!a.dangerouslyAllowSVG&&(p=!0);let H=o(g),F=Object.assign(v?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:q,objectPosition:R}:{},D?{}:{color:"transparent"},x),V=I||"empty"===j?null:"blur"===j?'url("data:image/svg+xml;charset=utf-8,'+(0,s.getImageBlurSvg)({widthInt:G,heightInt:B,blurWidth:u,blurHeight:l,blurDataURL:k||"",objectFit:F.objectFit})+'")':'url("'+j+'")',J=V?{backgroundSize:F.objectFit||"cover",backgroundPosition:F.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:V}:{},Y=function(e){let{config:t,src:r,unoptimized:s,width:n,quality:i,sizes:o,loader:a}=e;if(s)return{src:r,srcSet:void 0,sizes:void 0};let{widths:u,kind:l}=function(e,t,r){let{deviceSizes:s,allSizes:n}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let s;s=e.exec(r);s)t.push(parseInt(s[2]));if(t.length){let e=.01*Math.min(...t);return{widths:n.filter(t=>t>=s[0]*e),kind:"w"}}return{widths:n,kind:"w"}}return"number"!=typeof t?{widths:s,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>n.find(t=>t>=e)||n[n.length-1]))],kind:"x"}}(t,n,o),d=u.length-1;return{sizes:o||"w"!==l?o:"100vw",srcSet:u.map((e,s)=>a({config:t,src:r,quality:i,width:e})+" "+("w"===l?e:s+1)+l).join(", "),src:a({config:t,src:r,quality:i,width:u[d]})}}({config:a,src:d,unoptimized:p,width:G,quality:H,sizes:c,loader:$});return{props:{...M,loading:W?"lazy":h,fetchPriority:P,width:G,height:B,decoding:C,className:m,style:{...F,...J},sizes:Y.sizes,srcSet:Y.srcSet,src:w||Y.src},meta:{unoptimized:p,priority:f,placeholder:j,fill:v}}}},59153:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return m},defaultHead:function(){return c}});let s=r(25488),n=r(81063),i=r(8732),o=n._(r(82015)),a=s._(r(87440)),u=r(32782),l=r(6302),d=r(62677);function c(e){void 0===e&&(e=!1);let t=[(0,i.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,i.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function p(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===o.default.Fragment?e.concat(o.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(76831);let f=["name","httpEquiv","charSet","itemProp"];function h(e,t){let{inAmpMode:r}=t;return e.reduce(p,[]).reverse().concat(c(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,s={};return n=>{let i=!0,o=!1;if(n.key&&"number"!=typeof n.key&&n.key.indexOf("$")>0){o=!0;let t=n.key.slice(n.key.indexOf("$")+1);e.has(t)?i=!1:e.add(t)}switch(n.type){case"title":case"base":t.has(n.type)?i=!1:t.add(n.type);break;case"meta":for(let e=0,t=f.length;e<t;e++){let t=f[e];if(n.props.hasOwnProperty(t)){if("charSet"===t)r.has(t)?i=!1:r.add(t);else{let e=n.props[t],r=s[t]||new Set;("name"!==t||!o)&&r.has(e)?i=!1:(r.add(e),s[t]=r)}}}}return i}}()).reverse().map((e,t)=>{let s=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,o.default.cloneElement(e,t)}return o.default.cloneElement(e,{key:s})})}let m=function(e){let{children:t}=e,r=(0,o.useContext)(u.AmpStateContext),s=(0,o.useContext)(l.HeadManagerContext);return(0,i.jsx)(a.default,{reduceComponentsToState:h,headManager:s,inAmpMode:(0,d.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},38337:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:s,blurHeight:n,blurDataURL:i,objectFit:o}=e,a=s?40*s:t,u=n?40*n:r,l=a&&u?"viewBox='0 0 "+a+" "+u+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+l+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(l?"none":"contain"===o?"xMidYMid":"cover"===o?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+i+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},94653:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return s}});let r=["default","imgix","cloudinary","akamai","custom"],s={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],unoptimized:!1}},21628:(e,t)=>{"use strict";function r(e){let{config:t,src:r,width:s,quality:n}=e;return t.path+"?url="+encodeURIComponent(r)+"&w="+s+"&q="+(n||75)}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}}),r.__next_img_default=!0;let s=r},87440:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let s=r(82015),n=()=>{},i=()=>{};function o(e){var t;let{headManager:r,reduceComponentsToState:o}=e;function a(){if(r&&r.mountedInstances){let t=s.Children.toArray(Array.from(r.mountedInstances).filter(Boolean));r.updateHead(o(t,e))}}return null==r||null==(t=r.mountedInstances)||t.add(e.children),a(),n(()=>{var t;return null==r||null==(t=r.mountedInstances)||t.add(e.children),()=>{var t;null==r||null==(t=r.mountedInstances)||t.delete(e.children)}}),n(()=>(r&&(r._pendingUpdate=a),()=>{r&&(r._pendingUpdate=a)})),i(()=>(r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null),()=>{r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null)})),null}},44512:(e,t,r)=>{"use strict";r.d(t,{b3:()=>s.b}),r(97200);var s=r(83009);r(46250)},35635:(e,t,r)=>{"use strict";r.d(t,{default:()=>n.a});var s=r(38516),n=r.n(s)},71066:(e,t,r)=>{let{createProxy:s}=r(73439);e.exports=s("C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\node_modules\\next\\dist\\client\\image-component.js")},97200:(e,t,r)=>{"use strict";r(46620),r(9181),r(29294),r(63033),r(10436),r(82312),r(60457);let s=r(37301);r(676);let n=new WeakMap;(0,s.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t){let r=e?`Route "${e}" `:"This route ";return Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`)});function i(){return this.getAll().map(e=>[e.name,e]).values()}function o(e){for(let e of this.getAll())this.delete(e.name);return e}},46250:(e,t,r)=>{"use strict";let s=r(63033),n=r(29294),i=r(10436),o=r(37301),a=r(82312),u=r(42490);new WeakMap;class l{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){d("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){d("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}function d(e){let t=n.workAsyncStorage.getStore(),r=s.workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`);if("unstable-cache"===r.type)throw Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`)}if(t.dynamicShouldError)throw new a.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);if(r){if("prerender"===r.type){let s=Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`);(0,i.abortAndThrowOnSynchronousRequestDataAccess)(t.route,e,s,r)}else if("prerender-ppr"===r.type)(0,i.postponeWithTracking)(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let s=new u.DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`);throw t.dynamicUsageDescription=e,t.dynamicUsageStack=s.stack,s}}}}(0,o.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t){let r=e?`Route "${e}" `:"This route ";return Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`)})},83009:(e,t,r)=>{"use strict";Object.defineProperty(t,"b",{enumerable:!0,get:function(){return d}});let s=r(9785),n=r(29294),i=r(63033),o=r(10436),a=r(82312),u=r(60457),l=r(37301);function d(){let e=n.workAsyncStorage.getStore(),t=i.workUnitAsyncStorage.getStore();if(e){if(e.forceStatic)return p(s.HeadersAdapter.seal(new Headers({})));if(t){if("cache"===t.type)throw Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`);if("unstable-cache"===t.type)throw Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`);if("after"===t.phase)throw Error(`Route ${e.route} used "headers" inside "unstable_after(...)". This is not supported. If you need this data inside an "unstable_after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/unstable_after`)}if(e.dynamicShouldError)throw new a.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`);if(t){if("prerender"===t.type)return function(e,t){let r=c.get(t);if(r)return r;let s=(0,u.makeHangingPromise)(t.renderSignal,"`headers()`");return c.set(t,s),Object.defineProperties(s,{append:{value:function(){let r=`\`headers().append(${f(arguments[0])}, ...)\``,s=h(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,s,t)}},delete:{value:function(){let r=`\`headers().delete(${f(arguments[0])})\``,s=h(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,s,t)}},get:{value:function(){let r=`\`headers().get(${f(arguments[0])})\``,s=h(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,s,t)}},has:{value:function(){let r=`\`headers().has(${f(arguments[0])})\``,s=h(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,s,t)}},set:{value:function(){let r=`\`headers().set(${f(arguments[0])}, ...)\``,s=h(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,s,t)}},getSetCookie:{value:function(){let r="`headers().getSetCookie()`",s=h(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,s,t)}},forEach:{value:function(){let r="`headers().forEach(...)`",s=h(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,s,t)}},keys:{value:function(){let r="`headers().keys()`",s=h(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,s,t)}},values:{value:function(){let r="`headers().values()`",s=h(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,s,t)}},entries:{value:function(){let r="`headers().entries()`",s=h(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,s,t)}},[Symbol.iterator]:{value:function(){let r="`headers()[Symbol.iterator]()`",s=h(e,r);(0,o.abortAndThrowOnSynchronousRequestDataAccess)(e,r,s,t)}}}),s}(e.route,t);"prerender-ppr"===t.type?(0,o.postponeWithTracking)(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&(0,o.throwToInterruptStaticGeneration)("headers",e,t)}(0,o.trackDynamicDataInDynamicRender)(e,t)}return p((0,i.getExpectedRequestStore)("headers").headers)}r(676);let c=new WeakMap;function p(e){let t=c.get(e);if(t)return t;let r=Promise.resolve(e);return c.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function f(e){return"string"==typeof e?`'${e}'`:"..."}function h(e,t){let r=e?`Route "${e}" `:"This route ";return Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`)}(0,l.createDedupedByCallsiteServerErrorLoggerDev)(h)},9785:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return i},ReadonlyHeadersError:function(){return n}});let s=r(20614);class n extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new n}}class i extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,n){if("symbol"==typeof r)return s.ReflectAdapter.get(t,r,n);let i=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===i);if(void 0!==o)return s.ReflectAdapter.get(t,o,n)},set(t,r,n,i){if("symbol"==typeof r)return s.ReflectAdapter.set(t,r,n,i);let o=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===o);return s.ReflectAdapter.set(t,a??r,n,i)},has(t,r){if("symbol"==typeof r)return s.ReflectAdapter.has(t,r);let n=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0!==i&&s.ReflectAdapter.has(t,i)},deleteProperty(t,r){if("symbol"==typeof r)return s.ReflectAdapter.deleteProperty(t,r);let n=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===n);return void 0===i||s.ReflectAdapter.deleteProperty(t,i)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return n.callable;default:return s.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new i(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,s]of this.entries())e.call(t,s,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},46620:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return p},ReadonlyRequestCookiesError:function(){return a},RequestCookiesAdapter:function(){return u},appendMutableCookies:function(){return c},areCookiesMutableInCurrentPhase:function(){return h},getModifiedCookieValues:function(){return d},responseCookiesToRequestCookies:function(){return g},wrapWithMutableAccessCheck:function(){return f}});let s=r(9181),n=r(20614),i=r(29294),o=r(63033);class a extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new a}}class u{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return a.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}}let l=Symbol.for("next.mutated.cookies");function d(e){let t=e[l];return t&&Array.isArray(t)&&0!==t.length?t:[]}function c(e,t){let r=d(t);if(0===r.length)return!1;let n=new s.ResponseCookies(e),i=n.getAll();for(let e of r)n.set(e);for(let e of i)n.set(e);return!0}class p{static wrap(e,t){let r=new s.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let o=[],a=new Set,u=()=>{let e=i.workAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),o=r.getAll().filter(e=>a.has(e.name)),t){let e=[];for(let t of o){let r=new s.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},d=new Proxy(r,{get(e,t,r){switch(t){case l:return o;case"delete":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),d}finally{u()}};case"set":return function(...t){a.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),d}finally{u()}};default:return n.ReflectAdapter.get(e,t,r)}}});return d}}function f(e){let t=new Proxy(e,{get(e,r,s){switch(r){case"delete":return function(...r){return m("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return m("cookies().set"),e.set(...r),t};default:return n.ReflectAdapter.get(e,r,s)}}});return t}function h(e){return"action"===e.phase}function m(e){if(!h((0,o.getExpectedRequestStore)(e)))throw new a}function g(e){let t=new s.RequestCookies(new Headers);for(let r of e.getAll())t.set(r);return t}},42326:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return a}}),r(75843);let s=r(96749),n=r(62833);function i(e){return void 0!==e.default}function o(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function a(e,t){var r;let a,u,l,{src:d,sizes:c,unoptimized:p=!1,priority:f=!1,loading:h,className:m,quality:g,width:y,height:b,fill:v=!1,style:x,overrideSrc:w,onLoad:S,onLoadingComplete:_,placeholder:j="empty",blurDataURL:k,fetchPriority:P,decoding:C="async",layout:A,objectFit:q,objectPosition:R,lazyBoundary:O,lazyRoot:E,...M}=e,{imgConf:z,showAltText:D,blurComplete:I,defaultLoader:T}=t,L=z||n.imageConfigDefault;if("allSizes"in L)a=L;else{let e=[...L.deviceSizes,...L.imageSizes].sort((e,t)=>e-t),t=L.deviceSizes.sort((e,t)=>e-t);a={...L,allSizes:e,deviceSizes:t}}if(void 0===T)throw Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config");let $=M.loader||T;delete M.loader,delete M.srcSet;let N="__next_img_default"in $;if(N){if("custom"===a.loader)throw Error('Image with src "'+d+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}else{let e=$;$=t=>{let{config:r,...s}=t;return e(s)}}if(A){"fill"===A&&(v=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[A];e&&(x={...x,...e});let t={responsive:"100vw",fill:"100vw"}[A];t&&!c&&(c=t)}let U="",G=o(y),B=o(b);if((r=d)&&"object"==typeof r&&(i(r)||void 0!==r.src)){let e=i(d)?d.default:d;if(!e.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e));if(!e.height||!e.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e));if(u=e.blurWidth,l=e.blurHeight,k=k||e.blurDataURL,U=e.src,!v){if(G||B){if(G&&!B){let t=G/e.width;B=Math.round(e.height*t)}else if(!G&&B){let t=B/e.height;G=Math.round(e.width*t)}}else G=e.width,B=e.height}}let W=!f&&("lazy"===h||void 0===h);(!(d="string"==typeof d?d:U)||d.startsWith("data:")||d.startsWith("blob:"))&&(p=!0,W=!1),a.unoptimized&&(p=!0),N&&d.endsWith(".svg")&&!a.dangerouslyAllowSVG&&(p=!0);let H=o(g),F=Object.assign(v?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:q,objectPosition:R}:{},D?{}:{color:"transparent"},x),V=I||"empty"===j?null:"blur"===j?'url("data:image/svg+xml;charset=utf-8,'+(0,s.getImageBlurSvg)({widthInt:G,heightInt:B,blurWidth:u,blurHeight:l,blurDataURL:k||"",objectFit:F.objectFit})+'")':'url("'+j+'")',J=V?{backgroundSize:F.objectFit||"cover",backgroundPosition:F.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:V}:{},Y=function(e){let{config:t,src:r,unoptimized:s,width:n,quality:i,sizes:o,loader:a}=e;if(s)return{src:r,srcSet:void 0,sizes:void 0};let{widths:u,kind:l}=function(e,t,r){let{deviceSizes:s,allSizes:n}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let s;s=e.exec(r);s)t.push(parseInt(s[2]));if(t.length){let e=.01*Math.min(...t);return{widths:n.filter(t=>t>=s[0]*e),kind:"w"}}return{widths:n,kind:"w"}}return"number"!=typeof t?{widths:s,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>n.find(t=>t>=e)||n[n.length-1]))],kind:"x"}}(t,n,o),d=u.length-1;return{sizes:o||"w"!==l?o:"100vw",srcSet:u.map((e,s)=>a({config:t,src:r,quality:i,width:e})+" "+("w"===l?e:s+1)+l).join(", "),src:a({config:t,src:r,quality:i,width:u[d]})}}({config:a,src:d,unoptimized:p,width:G,quality:H,sizes:c,loader:$});return{props:{...M,loading:W?"lazy":h,fetchPriority:P,width:G,height:B,decoding:C,className:m,style:{...F,...J},sizes:Y.sizes,srcSet:Y.srcSet,src:w||Y.src},meta:{unoptimized:p,priority:f,placeholder:j,fill:v}}}},96749:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:s,blurHeight:n,blurDataURL:i,objectFit:o}=e,a=s?40*s:t,u=n?40*n:r,l=a&&u?"viewBox='0 0 "+a+" "+u+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+l+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(l?"none":"contain"===o?"xMidYMid":"cover"===o?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+i+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},62833:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return s}});let r=["default","imgix","cloudinary","akamai","custom"],s={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",localPatterns:void 0,remotePatterns:[],unoptimized:!1}},38516:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return u},getImageProps:function(){return a}});let s=r(73264),n=r(42326),i=r(71066),o=s._(r(56352));function a(e){let{props:t}=(0,n.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let u=i.Image},56352:(e,t)=>{"use strict";function r(e){let{config:t,src:r,width:s,quality:n}=e;return t.path+"?url="+encodeURIComponent(r)+"&w="+s+"&q="+(n||75)}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}}),r.__next_img_default=!0;let s=r},75843:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},90644:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>n});var s=r(8732);r(82015),r(19557);let n={description:"A blank template using Payload in a Next.js app.",title:"Payload Blank Template"};async function i(e){let{children:t}=e;return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{children:(0,s.jsx)("main",{children:t})})})}},36603:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>c});var n=r(8732),i=r(44512),o=r(35635),a=r(45415);r(82015);var u=r(79551),l=r(17750);r(19557);var d=e([l]);async function c(){let e=await (0,i.b3)(),t=await l.A,r=await (0,a.nm0)({config:t}),{user:s}=await r.auth({headers:e}),d=`vscode://file/${(0,u.fileURLToPath)("file:///C:/Users/<USER>/kavya-git/spark-new/littlespark-cms/src/app/(frontend)/page.tsx")}`;return(0,n.jsxs)("div",{className:"home",children:[(0,n.jsxs)("div",{className:"content",children:[(0,n.jsxs)("picture",{children:[(0,n.jsx)("source",{srcSet:"https://raw.githubusercontent.com/payloadcms/payload/main/packages/ui/src/assets/payload-favicon.svg"}),(0,n.jsx)(o.default,{alt:"Payload Logo",height:65,src:"https://raw.githubusercontent.com/payloadcms/payload/main/packages/ui/src/assets/payload-favicon.svg",width:65})]}),!s&&(0,n.jsx)("h1",{children:"Welcome to your new project."}),s&&(0,n.jsxs)("h1",{children:["Welcome back, ",s.email]}),(0,n.jsxs)("div",{className:"links",children:[(0,n.jsx)("a",{className:"admin",href:t.routes.admin,rel:"noopener noreferrer",target:"_blank",children:"Go to admin panel"}),(0,n.jsx)("a",{className:"docs",href:"https://payloadcms.com/docs",rel:"noopener noreferrer",target:"_blank",children:"Documentation"})]})]}),(0,n.jsxs)("div",{className:"footer",children:[(0,n.jsx)("p",{children:"Update this page by editing"}),(0,n.jsx)("a",{className:"codeLink",href:d,children:(0,n.jsx)("code",{children:"app/(frontend)/page.tsx"})})]})]})}l=(d.then?(await d)():d)[0],s()}catch(e){s(e)}})},19557:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[5994,2259,5415,1112,7750],()=>r(34600));module.exports=s})();