'use client'

import React from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

export default function DashboardPage() {
  const router = useRouter()

  const handleSyncClick = () => {
    router.push('/admin/sync')
  }

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      {/* Header */}
      <div style={{ marginBottom: '30px', textAlign: 'center' }}>
        <h1 style={{ fontSize: '32px', fontWeight: 'bold', marginBottom: '10px', color: '#333' }}>
          🎯 Little Spark CMS
        </h1>
        <p style={{ color: '#666', fontSize: '18px', marginBottom: '20px' }}>
          Content Management System Dashboard
        </p>
        
        {/* Sync Button - Prominent */}
        <button
          type="button"
          onClick={handleSyncClick}
          style={{
            backgroundColor: '#0070f3',
            color: 'white',
            border: 'none',
            padding: '15px 30px',
            borderRadius: '8px',
            fontSize: '16px',
            fontWeight: '600',
            cursor: 'pointer',
            boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
            display: 'inline-flex',
            alignItems: 'center',
            gap: '10px',
            transition: 'all 0.3s ease',
            marginBottom: '30px'
          }}
          onMouseOver={(e) => {
            e.currentTarget.style.backgroundColor = '#0056b3'
            e.currentTarget.style.transform = 'translateY(-2px)'
            e.currentTarget.style.boxShadow = '0 6px 20px rgba(0,0,0,0.2)'
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.backgroundColor = '#0070f3'
            e.currentTarget.style.transform = 'translateY(0)'
            e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)'
          }}
        >
          <span style={{ fontSize: '20px' }}>🔄</span>
          <span>Open Sync Management Panel</span>
        </button>
      </div>

      {/* Quick Stats */}
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', 
        gap: '20px',
        marginBottom: '30px'
      }}>
        <div style={{
          backgroundColor: '#f8f9fa',
          padding: '20px',
          borderRadius: '12px',
          textAlign: 'center',
          border: '1px solid #e9ecef'
        }}>
          <div style={{ fontSize: '24px', marginBottom: '10px' }}>📝</div>
          <h3 style={{ color: '#495057', marginBottom: '5px' }}>Challenges</h3>
          <p style={{ color: '#6c757d', fontSize: '14px' }}>Manage creative challenges</p>
        </div>

        <div style={{
          backgroundColor: '#f8f9fa',
          padding: '20px',
          borderRadius: '12px',
          textAlign: 'center',
          border: '1px solid #e9ecef'
        }}>
          <div style={{ fontSize: '24px', marginBottom: '10px' }}>📚</div>
          <h3 style={{ color: '#495057', marginBottom: '5px' }}>Story Templates</h3>
          <p style={{ color: '#6c757d', fontSize: '14px' }}>Create story templates</p>
        </div>

        <div style={{
          backgroundColor: '#f8f9fa',
          padding: '20px',
          borderRadius: '12px',
          textAlign: 'center',
          border: '1px solid #e9ecef'
        }}>
          <div style={{ fontSize: '24px', marginBottom: '10px' }}>🎓</div>
          <h3 style={{ color: '#495057', marginBottom: '5px' }}>Educational Resources</h3>
          <p style={{ color: '#6c757d', fontSize: '14px' }}>Learning materials</p>
        </div>

        <div style={{
          backgroundColor: '#f8f9fa',
          padding: '20px',
          borderRadius: '12px',
          textAlign: 'center',
          border: '1px solid #e9ecef'
        }}>
          <div style={{ fontSize: '24px', marginBottom: '10px' }}>👥</div>
          <h3 style={{ color: '#495057', marginBottom: '5px' }}>Users</h3>
          <p style={{ color: '#6c757d', fontSize: '14px' }}>Manage CMS users</p>
        </div>
      </div>

      {/* Quick Actions */}
      <div style={{ 
        backgroundColor: '#fff3cd', 
        border: '1px solid #ffeaa7', 
        borderRadius: '8px', 
        padding: '20px',
        textAlign: 'center'
      }}>
        <h3 style={{ color: '#856404', marginBottom: '10px' }}>
          🚀 Quick Actions
        </h3>
        <p style={{ color: '#856404', fontSize: '14px', marginBottom: '15px' }}>
          Use the navigation menu to access collections, or click the Sync button above to manage content synchronization.
        </p>
        <div style={{ display: 'flex', justifyContent: 'center', gap: '10px', flexWrap: 'wrap' }}>
          <Link 
            href="/admin/collections/challenges"
            style={{
              backgroundColor: '#28a745',
              color: 'white',
              padding: '8px 16px',
              borderRadius: '6px',
              textDecoration: 'none',
              fontSize: '14px',
              fontWeight: '500'
            }}
          >
            📝 Challenges
          </Link>
            <Link 
            href="/admin/collections/story-templates"
            style={{
              backgroundColor: '#007bff',
              color: 'white',
              padding: '8px 16px',
              borderRadius: '6px',
              textDecoration: 'none',
              fontSize: '14px',
              fontWeight: '500'
            }}
          >
            📚 Story Templates
          </Link>
          <Link 
            href="/admin/collections/educational-resources"
            style={{
              backgroundColor: '#17a2b8',
              color: 'white',
              padding: '8px 16px',
              borderRadius: '6px',
              textDecoration: 'none',
              fontSize: '14px',
              fontWeight: '500'
            }}
          >
            🎓 Educational Resources
          </Link>
        </div>
      </div>
    </div>
  )
}
