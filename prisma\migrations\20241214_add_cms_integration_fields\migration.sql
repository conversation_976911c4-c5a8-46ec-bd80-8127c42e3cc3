-- Add CMS integration fields to profiles table
ALTER TABLE "profiles" ADD COLUMN "cms_user_id" TEXT;
ALTER TABLE "profiles" ADD COLUMN "is_cms_user" BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE "profiles" ADD COLUMN "cms_role" TEXT;
ALTER TABLE "profiles" ADD COLUMN "cms_specialties" TEXT[] DEFAULT ARRAY[]::TEXT[];
ALTER TABLE "profiles" ADD COLUMN "bio" TEXT;

-- Add content_hash column to user_content table for duplicate prevention (if table exists)
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'user_content') THEN
        ALTER TABLE "user_content" ADD COLUMN IF NOT EXISTS "content_hash" TEXT;
        
        -- Create index for efficient duplicate checking
        CREATE INDEX IF NOT EXISTS "user_content_user_id_content_hash_idx" ON "user_content"("user_id", "content_hash");
    END IF;
END $$;
