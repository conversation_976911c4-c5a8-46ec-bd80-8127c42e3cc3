"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_date-fns_locale_de_js"],{

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/de.js":
/*!********************************************!*\
  !*** ./node_modules/date-fns/locale/de.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   de: () => (/* binding */ de),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _de_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./de/_lib/formatDistance.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/de/_lib/formatDistance.js\");\n/* harmony import */ var _de_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./de/_lib/formatLong.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/de/_lib/formatLong.js\");\n/* harmony import */ var _de_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./de/_lib/formatRelative.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/de/_lib/formatRelative.js\");\n/* harmony import */ var _de_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./de/_lib/localize.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/de/_lib/localize.js\");\n/* harmony import */ var _de_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./de/_lib/match.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/de/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary German locale.\n * @language German\n * @iso-639-2 deu\n * <AUTHOR> Eilmsteiner [@DeMuu](https://github.com/DeMuu)\n * <AUTHOR> [@asia-t](https://github.com/asia-t)\n * <AUTHOR> Vuong Ngo [@vanvuongngo](https://github.com/vanvuongngo)\n * <AUTHOR> [@pex](https://github.com/pex)\n * <AUTHOR> Keck [@Philipp91](https://github.com/Philipp91)\n */ const de = {\n    code: \"de\",\n    formatDistance: _de_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _de_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _de_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _de_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _de_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 1 /* Monday */ ,\n        firstWeekContainsDate: 4\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (de);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/de.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/de/_lib/formatDistance.js":
/*!****************************************************************!*\
  !*** ./node_modules/date-fns/locale/de/_lib/formatDistance.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\nconst formatDistanceLocale = {\n    lessThanXSeconds: {\n        standalone: {\n            one: \"weniger als 1 Sekunde\",\n            other: \"weniger als {{count}} Sekunden\"\n        },\n        withPreposition: {\n            one: \"weniger als 1 Sekunde\",\n            other: \"weniger als {{count}} Sekunden\"\n        }\n    },\n    xSeconds: {\n        standalone: {\n            one: \"1 Sekunde\",\n            other: \"{{count}} Sekunden\"\n        },\n        withPreposition: {\n            one: \"1 Sekunde\",\n            other: \"{{count}} Sekunden\"\n        }\n    },\n    halfAMinute: {\n        standalone: \"eine halbe Minute\",\n        withPreposition: \"einer halben Minute\"\n    },\n    lessThanXMinutes: {\n        standalone: {\n            one: \"weniger als 1 Minute\",\n            other: \"weniger als {{count}} Minuten\"\n        },\n        withPreposition: {\n            one: \"weniger als 1 Minute\",\n            other: \"weniger als {{count}} Minuten\"\n        }\n    },\n    xMinutes: {\n        standalone: {\n            one: \"1 Minute\",\n            other: \"{{count}} Minuten\"\n        },\n        withPreposition: {\n            one: \"1 Minute\",\n            other: \"{{count}} Minuten\"\n        }\n    },\n    aboutXHours: {\n        standalone: {\n            one: \"etwa 1 Stunde\",\n            other: \"etwa {{count}} Stunden\"\n        },\n        withPreposition: {\n            one: \"etwa 1 Stunde\",\n            other: \"etwa {{count}} Stunden\"\n        }\n    },\n    xHours: {\n        standalone: {\n            one: \"1 Stunde\",\n            other: \"{{count}} Stunden\"\n        },\n        withPreposition: {\n            one: \"1 Stunde\",\n            other: \"{{count}} Stunden\"\n        }\n    },\n    xDays: {\n        standalone: {\n            one: \"1 Tag\",\n            other: \"{{count}} Tage\"\n        },\n        withPreposition: {\n            one: \"1 Tag\",\n            other: \"{{count}} Tagen\"\n        }\n    },\n    aboutXWeeks: {\n        standalone: {\n            one: \"etwa 1 Woche\",\n            other: \"etwa {{count}} Wochen\"\n        },\n        withPreposition: {\n            one: \"etwa 1 Woche\",\n            other: \"etwa {{count}} Wochen\"\n        }\n    },\n    xWeeks: {\n        standalone: {\n            one: \"1 Woche\",\n            other: \"{{count}} Wochen\"\n        },\n        withPreposition: {\n            one: \"1 Woche\",\n            other: \"{{count}} Wochen\"\n        }\n    },\n    aboutXMonths: {\n        standalone: {\n            one: \"etwa 1 Monat\",\n            other: \"etwa {{count}} Monate\"\n        },\n        withPreposition: {\n            one: \"etwa 1 Monat\",\n            other: \"etwa {{count}} Monaten\"\n        }\n    },\n    xMonths: {\n        standalone: {\n            one: \"1 Monat\",\n            other: \"{{count}} Monate\"\n        },\n        withPreposition: {\n            one: \"1 Monat\",\n            other: \"{{count}} Monaten\"\n        }\n    },\n    aboutXYears: {\n        standalone: {\n            one: \"etwa 1 Jahr\",\n            other: \"etwa {{count}} Jahre\"\n        },\n        withPreposition: {\n            one: \"etwa 1 Jahr\",\n            other: \"etwa {{count}} Jahren\"\n        }\n    },\n    xYears: {\n        standalone: {\n            one: \"1 Jahr\",\n            other: \"{{count}} Jahre\"\n        },\n        withPreposition: {\n            one: \"1 Jahr\",\n            other: \"{{count}} Jahren\"\n        }\n    },\n    overXYears: {\n        standalone: {\n            one: \"mehr als 1 Jahr\",\n            other: \"mehr als {{count}} Jahre\"\n        },\n        withPreposition: {\n            one: \"mehr als 1 Jahr\",\n            other: \"mehr als {{count}} Jahren\"\n        }\n    },\n    almostXYears: {\n        standalone: {\n            one: \"fast 1 Jahr\",\n            other: \"fast {{count}} Jahre\"\n        },\n        withPreposition: {\n            one: \"fast 1 Jahr\",\n            other: \"fast {{count}} Jahren\"\n        }\n    }\n};\nconst formatDistance = (token, count, options)=>{\n    let result;\n    const tokenValue = (options === null || options === void 0 ? void 0 : options.addSuffix) ? formatDistanceLocale[token].withPreposition : formatDistanceLocale[token].standalone;\n    if (typeof tokenValue === \"string\") {\n        result = tokenValue;\n    } else if (count === 1) {\n        result = tokenValue.one;\n    } else {\n        result = tokenValue.other.replace(\"{{count}}\", String(count));\n    }\n    if (options === null || options === void 0 ? void 0 : options.addSuffix) {\n        if (options.comparison && options.comparison > 0) {\n            return \"in \" + result;\n        } else {\n            return \"vor \" + result;\n        }\n    }\n    return result;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/de/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/de/_lib/formatLong.js":
/*!************************************************************!*\
  !*** ./node_modules/date-fns/locale/de/_lib/formatLong.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\n// DIN 5008: https://de.wikipedia.org/wiki/Datumsformat#DIN_5008\nconst dateFormats = {\n    full: \"EEEE, do MMMM y\",\n    long: \"do MMMM y\",\n    medium: \"do MMM y\",\n    short: \"dd.MM.y\"\n};\nconst timeFormats = {\n    full: \"HH:mm:ss zzzz\",\n    long: \"HH:mm:ss z\",\n    medium: \"HH:mm:ss\",\n    short: \"HH:mm\"\n};\nconst dateTimeFormats = {\n    full: \"{{date}} 'um' {{time}}\",\n    long: \"{{date}} 'um' {{time}}\",\n    medium: \"{{date}} {{time}}\",\n    short: \"{{date}} {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"full\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"full\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/de/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/de/_lib/formatRelative.js":
/*!****************************************************************!*\
  !*** ./node_modules/date-fns/locale/de/_lib/formatRelative.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\nconst formatRelativeLocale = {\n    lastWeek: \"'letzten' eeee 'um' p\",\n    yesterday: \"'gestern um' p\",\n    today: \"'heute um' p\",\n    tomorrow: \"'morgen um' p\",\n    nextWeek: \"eeee 'um' p\",\n    other: \"P\"\n};\nconst formatRelative = (token, _date, _baseDate, _options)=>formatRelativeLocale[token];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9kYXRlLWZucy9sb2NhbGUvZGUvX2xpYi9mb3JtYXRSZWxhdGl2ZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsTUFBTUEsdUJBQXVCO0lBQzNCQyxVQUFVO0lBQ1ZDLFdBQVc7SUFDWEMsT0FBTztJQUNQQyxVQUFVO0lBQ1ZDLFVBQVU7SUFDVkMsT0FBTztBQUNUO0FBRU8sTUFBTUMsaUJBQWlCLENBQUNDLE9BQU9DLE9BQU9DLFdBQVdDLFdBQ3REWCxvQkFBb0IsQ0FBQ1EsTUFBTSxDQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJhdmkxXFxrYXZ5YS1naXRcXHNwYXJrLW5ld1xcbGl0dGxlc3BhcmstY21zXFxub2RlX21vZHVsZXNcXGRhdGUtZm5zXFxsb2NhbGVcXGRlXFxfbGliXFxmb3JtYXRSZWxhdGl2ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBmb3JtYXRSZWxhdGl2ZUxvY2FsZSA9IHtcbiAgbGFzdFdlZWs6IFwiJ2xldHp0ZW4nIGVlZWUgJ3VtJyBwXCIsXG4gIHllc3RlcmRheTogXCInZ2VzdGVybiB1bScgcFwiLFxuICB0b2RheTogXCInaGV1dGUgdW0nIHBcIixcbiAgdG9tb3Jyb3c6IFwiJ21vcmdlbiB1bScgcFwiLFxuICBuZXh0V2VlazogXCJlZWVlICd1bScgcFwiLFxuICBvdGhlcjogXCJQXCIsXG59O1xuXG5leHBvcnQgY29uc3QgZm9ybWF0UmVsYXRpdmUgPSAodG9rZW4sIF9kYXRlLCBfYmFzZURhdGUsIF9vcHRpb25zKSA9PlxuICBmb3JtYXRSZWxhdGl2ZUxvY2FsZVt0b2tlbl07XG4iXSwibmFtZXMiOlsiZm9ybWF0UmVsYXRpdmVMb2NhbGUiLCJsYXN0V2VlayIsInllc3RlcmRheSIsInRvZGF5IiwidG9tb3Jyb3ciLCJuZXh0V2VlayIsIm90aGVyIiwiZm9ybWF0UmVsYXRpdmUiLCJ0b2tlbiIsIl9kYXRlIiwiX2Jhc2VEYXRlIiwiX29wdGlvbnMiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/de/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/de/_lib/localize.js":
/*!**********************************************************!*\
  !*** ./node_modules/date-fns/locale/de/_lib/localize.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\nconst eraValues = {\n    narrow: [\n        \"v.Chr.\",\n        \"n.Chr.\"\n    ],\n    abbreviated: [\n        \"v.Chr.\",\n        \"n.Chr.\"\n    ],\n    wide: [\n        \"vor Christus\",\n        \"nach Christus\"\n    ]\n};\nconst quarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"Q1\",\n        \"Q2\",\n        \"Q3\",\n        \"Q4\"\n    ],\n    wide: [\n        \"1. Quartal\",\n        \"2. Quartal\",\n        \"3. Quartal\",\n        \"4. Quartal\"\n    ]\n};\n// Note: in German, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nconst monthValues = {\n    narrow: [\n        \"J\",\n        \"F\",\n        \"M\",\n        \"A\",\n        \"M\",\n        \"J\",\n        \"J\",\n        \"A\",\n        \"S\",\n        \"O\",\n        \"N\",\n        \"D\"\n    ],\n    abbreviated: [\n        \"Jan\",\n        \"Feb\",\n        \"Mär\",\n        \"Apr\",\n        \"Mai\",\n        \"Jun\",\n        \"Jul\",\n        \"Aug\",\n        \"Sep\",\n        \"Okt\",\n        \"Nov\",\n        \"Dez\"\n    ],\n    wide: [\n        \"Januar\",\n        \"Februar\",\n        \"März\",\n        \"April\",\n        \"Mai\",\n        \"Juni\",\n        \"Juli\",\n        \"August\",\n        \"September\",\n        \"Oktober\",\n        \"November\",\n        \"Dezember\"\n    ]\n};\n// https://st.unicode.org/cldr-apps/v#/de/Gregorian/\nconst formattingMonthValues = {\n    narrow: monthValues.narrow,\n    abbreviated: [\n        \"Jan.\",\n        \"Feb.\",\n        \"März\",\n        \"Apr.\",\n        \"Mai\",\n        \"Juni\",\n        \"Juli\",\n        \"Aug.\",\n        \"Sep.\",\n        \"Okt.\",\n        \"Nov.\",\n        \"Dez.\"\n    ],\n    wide: monthValues.wide\n};\nconst dayValues = {\n    narrow: [\n        \"S\",\n        \"M\",\n        \"D\",\n        \"M\",\n        \"D\",\n        \"F\",\n        \"S\"\n    ],\n    short: [\n        \"So\",\n        \"Mo\",\n        \"Di\",\n        \"Mi\",\n        \"Do\",\n        \"Fr\",\n        \"Sa\"\n    ],\n    abbreviated: [\n        \"So.\",\n        \"Mo.\",\n        \"Di.\",\n        \"Mi.\",\n        \"Do.\",\n        \"Fr.\",\n        \"Sa.\"\n    ],\n    wide: [\n        \"Sonntag\",\n        \"Montag\",\n        \"Dienstag\",\n        \"Mittwoch\",\n        \"Donnerstag\",\n        \"Freitag\",\n        \"Samstag\"\n    ]\n};\n// https://www.unicode.org/cldr/charts/32/summary/de.html#1881\nconst dayPeriodValues = {\n    narrow: {\n        am: \"vm.\",\n        pm: \"nm.\",\n        midnight: \"Mitternacht\",\n        noon: \"Mittag\",\n        morning: \"Morgen\",\n        afternoon: \"Nachm.\",\n        evening: \"Abend\",\n        night: \"Nacht\"\n    },\n    abbreviated: {\n        am: \"vorm.\",\n        pm: \"nachm.\",\n        midnight: \"Mitternacht\",\n        noon: \"Mittag\",\n        morning: \"Morgen\",\n        afternoon: \"Nachmittag\",\n        evening: \"Abend\",\n        night: \"Nacht\"\n    },\n    wide: {\n        am: \"vormittags\",\n        pm: \"nachmittags\",\n        midnight: \"Mitternacht\",\n        noon: \"Mittag\",\n        morning: \"Morgen\",\n        afternoon: \"Nachmittag\",\n        evening: \"Abend\",\n        night: \"Nacht\"\n    }\n};\nconst formattingDayPeriodValues = {\n    narrow: {\n        am: \"vm.\",\n        pm: \"nm.\",\n        midnight: \"Mitternacht\",\n        noon: \"Mittag\",\n        morning: \"morgens\",\n        afternoon: \"nachm.\",\n        evening: \"abends\",\n        night: \"nachts\"\n    },\n    abbreviated: {\n        am: \"vorm.\",\n        pm: \"nachm.\",\n        midnight: \"Mitternacht\",\n        noon: \"Mittag\",\n        morning: \"morgens\",\n        afternoon: \"nachmittags\",\n        evening: \"abends\",\n        night: \"nachts\"\n    },\n    wide: {\n        am: \"vormittags\",\n        pm: \"nachmittags\",\n        midnight: \"Mitternacht\",\n        noon: \"Mittag\",\n        morning: \"morgens\",\n        afternoon: \"nachmittags\",\n        evening: \"abends\",\n        night: \"nachts\"\n    }\n};\nconst ordinalNumber = (dirtyNumber)=>{\n    const number = Number(dirtyNumber);\n    return number + \".\";\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        formattingValues: formattingMonthValues,\n        defaultWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"wide\",\n        formattingValues: formattingDayPeriodValues,\n        defaultFormattingWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/de/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/de/_lib/match.js":
/*!*******************************************************!*\
  !*** ./node_modules/date-fns/locale/de/_lib/match.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^(\\d+)(\\.)?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^(v\\.? ?Chr\\.?|n\\.? ?Chr\\.?)/i,\n    abbreviated: /^(v\\.? ?Chr\\.?|n\\.? ?Chr\\.?)/i,\n    wide: /^(vor Christus|vor unserer Zeitrechnung|nach Christus|unserer Zeitrechnung)/i\n};\nconst parseEraPatterns = {\n    any: [\n        /^v/i,\n        /^n/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]/i,\n    abbreviated: /^q[1234]/i,\n    wide: /^[1234](\\.)? Quartal/i\n};\nconst parseQuarterPatterns = {\n    any: [\n        /1/i,\n        /2/i,\n        /3/i,\n        /4/i\n    ]\n};\nconst matchMonthPatterns = {\n    narrow: /^[jfmasond]/i,\n    abbreviated: /^(j[aä]n|feb|mär[z]?|apr|mai|jun[i]?|jul[i]?|aug|sep|okt|nov|dez)\\.?/i,\n    wide: /^(januar|februar|märz|april|mai|juni|juli|august|september|oktober|november|dezember)/i\n};\nconst parseMonthPatterns = {\n    narrow: [\n        /^j/i,\n        /^f/i,\n        /^m/i,\n        /^a/i,\n        /^m/i,\n        /^j/i,\n        /^j/i,\n        /^a/i,\n        /^s/i,\n        /^o/i,\n        /^n/i,\n        /^d/i\n    ],\n    any: [\n        /^j[aä]/i,\n        /^f/i,\n        /^mär/i,\n        /^ap/i,\n        /^mai/i,\n        /^jun/i,\n        /^jul/i,\n        /^au/i,\n        /^s/i,\n        /^o/i,\n        /^n/i,\n        /^d/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^[smdmf]/i,\n    short: /^(so|mo|di|mi|do|fr|sa)/i,\n    abbreviated: /^(son?|mon?|die?|mit?|don?|fre?|sam?)\\.?/i,\n    wide: /^(sonntag|montag|dienstag|mittwoch|donnerstag|freitag|samstag)/i\n};\nconst parseDayPatterns = {\n    any: [\n        /^so/i,\n        /^mo/i,\n        /^di/i,\n        /^mi/i,\n        /^do/i,\n        /^f/i,\n        /^sa/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    narrow: /^(vm\\.?|nm\\.?|Mitternacht|Mittag|morgens|nachm\\.?|abends|nachts)/i,\n    abbreviated: /^(vorm\\.?|nachm\\.?|Mitternacht|Mittag|morgens|nachm\\.?|abends|nachts)/i,\n    wide: /^(vormittags|nachmittags|Mitternacht|Mittag|morgens|nachmittags|abends|nachts)/i\n};\nconst parseDayPeriodPatterns = {\n    any: {\n        am: /^v/i,\n        pm: /^n/i,\n        midnight: /^Mitte/i,\n        noon: /^Mitta/i,\n        morning: /morgens/i,\n        afternoon: /nachmittags/i,\n        evening: /abends/i,\n        night: /nachts/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9kYXRlLWZucy9sb2NhbGUvZGUvX2xpYi9tYXRjaC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBMEQ7QUFDYztBQUV4RSxNQUFNRSw0QkFBNEI7QUFDbEMsTUFBTUMsNEJBQTRCO0FBRWxDLE1BQU1DLG1CQUFtQjtJQUN2QkMsUUFBUTtJQUNSQyxhQUFhO0lBQ2JDLE1BQU07QUFDUjtBQUNBLE1BQU1DLG1CQUFtQjtJQUN2QkMsS0FBSztRQUFDO1FBQU87S0FBTTtBQUNyQjtBQUVBLE1BQU1DLHVCQUF1QjtJQUMzQkwsUUFBUTtJQUNSQyxhQUFhO0lBQ2JDLE1BQU07QUFDUjtBQUNBLE1BQU1JLHVCQUF1QjtJQUMzQkYsS0FBSztRQUFDO1FBQU07UUFBTTtRQUFNO0tBQUs7QUFDL0I7QUFFQSxNQUFNRyxxQkFBcUI7SUFDekJQLFFBQVE7SUFDUkMsYUFDRTtJQUNGQyxNQUFNO0FBQ1I7QUFDQSxNQUFNTSxxQkFBcUI7SUFDekJSLFFBQVE7UUFDTjtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7S0FDRDtJQUVESSxLQUFLO1FBQ0g7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0Q7QUFDSDtBQUVBLE1BQU1LLG1CQUFtQjtJQUN2QlQsUUFBUTtJQUNSVSxPQUFPO0lBQ1BULGFBQWE7SUFDYkMsTUFBTTtBQUNSO0FBQ0EsTUFBTVMsbUJBQW1CO0lBQ3ZCUCxLQUFLO1FBQUM7UUFBUTtRQUFRO1FBQVE7UUFBUTtRQUFRO1FBQU87S0FBTztBQUM5RDtBQUVBLE1BQU1RLHlCQUF5QjtJQUM3QlosUUFBUTtJQUNSQyxhQUNFO0lBQ0ZDLE1BQU07QUFDUjtBQUNBLE1BQU1XLHlCQUF5QjtJQUM3QlQsS0FBSztRQUNIVSxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsVUFBVTtRQUNWQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxTQUFTO1FBQ1RDLE9BQU87SUFDVDtBQUNGO0FBRU8sTUFBTUMsUUFBUTtJQUNuQkMsZUFBZTNCLGdGQUFtQkEsQ0FBQztRQUNqQzRCLGNBQWMzQjtRQUNkNEIsY0FBYzNCO1FBQ2Q0QixlQUFlLENBQUNDLFFBQVVDLFNBQVNEO0lBQ3JDO0lBRUFFLEtBQUtsQyxrRUFBWUEsQ0FBQztRQUNoQm1DLGVBQWUvQjtRQUNmZ0MsbUJBQW1CO1FBQ25CQyxlQUFlN0I7UUFDZjhCLG1CQUFtQjtJQUNyQjtJQUVBQyxTQUFTdkMsa0VBQVlBLENBQUM7UUFDcEJtQyxlQUFlekI7UUFDZjBCLG1CQUFtQjtRQUNuQkMsZUFBZTFCO1FBQ2YyQixtQkFBbUI7UUFDbkJQLGVBQWUsQ0FBQ1MsUUFBVUEsUUFBUTtJQUNwQztJQUVBQyxPQUFPekMsa0VBQVlBLENBQUM7UUFDbEJtQyxlQUFldkI7UUFDZndCLG1CQUFtQjtRQUNuQkMsZUFBZXhCO1FBQ2Z5QixtQkFBbUI7SUFDckI7SUFFQUksS0FBSzFDLGtFQUFZQSxDQUFDO1FBQ2hCbUMsZUFBZXJCO1FBQ2ZzQixtQkFBbUI7UUFDbkJDLGVBQWVyQjtRQUNmc0IsbUJBQW1CO0lBQ3JCO0lBRUFLLFdBQVczQyxrRUFBWUEsQ0FBQztRQUN0Qm1DLGVBQWVsQjtRQUNmbUIsbUJBQW1CO1FBQ25CQyxlQUFlbkI7UUFDZm9CLG1CQUFtQjtJQUNyQjtBQUNGLEVBQUUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmF2aTFcXGthdnlhLWdpdFxcc3BhcmstbmV3XFxsaXR0bGVzcGFyay1jbXNcXG5vZGVfbW9kdWxlc1xcZGF0ZS1mbnNcXGxvY2FsZVxcZGVcXF9saWJcXG1hdGNoLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGJ1aWxkTWF0Y2hGbiB9IGZyb20gXCIuLi8uLi9fbGliL2J1aWxkTWF0Y2hGbi5qc1wiO1xuaW1wb3J0IHsgYnVpbGRNYXRjaFBhdHRlcm5GbiB9IGZyb20gXCIuLi8uLi9fbGliL2J1aWxkTWF0Y2hQYXR0ZXJuRm4uanNcIjtcblxuY29uc3QgbWF0Y2hPcmRpbmFsTnVtYmVyUGF0dGVybiA9IC9eKFxcZCspKFxcLik/L2k7XG5jb25zdCBwYXJzZU9yZGluYWxOdW1iZXJQYXR0ZXJuID0gL1xcZCsvaTtcblxuY29uc3QgbWF0Y2hFcmFQYXR0ZXJucyA9IHtcbiAgbmFycm93OiAvXih2XFwuPyA/Q2hyXFwuP3xuXFwuPyA/Q2hyXFwuPykvaSxcbiAgYWJicmV2aWF0ZWQ6IC9eKHZcXC4/ID9DaHJcXC4/fG5cXC4/ID9DaHJcXC4/KS9pLFxuICB3aWRlOiAvXih2b3IgQ2hyaXN0dXN8dm9yIHVuc2VyZXIgWmVpdHJlY2hudW5nfG5hY2ggQ2hyaXN0dXN8dW5zZXJlciBaZWl0cmVjaG51bmcpL2ksXG59O1xuY29uc3QgcGFyc2VFcmFQYXR0ZXJucyA9IHtcbiAgYW55OiBbL152L2ksIC9ebi9pXSxcbn07XG5cbmNvbnN0IG1hdGNoUXVhcnRlclBhdHRlcm5zID0ge1xuICBuYXJyb3c6IC9eWzEyMzRdL2ksXG4gIGFiYnJldmlhdGVkOiAvXnFbMTIzNF0vaSxcbiAgd2lkZTogL15bMTIzNF0oXFwuKT8gUXVhcnRhbC9pLFxufTtcbmNvbnN0IHBhcnNlUXVhcnRlclBhdHRlcm5zID0ge1xuICBhbnk6IFsvMS9pLCAvMi9pLCAvMy9pLCAvNC9pXSxcbn07XG5cbmNvbnN0IG1hdGNoTW9udGhQYXR0ZXJucyA9IHtcbiAgbmFycm93OiAvXltqZm1hc29uZF0vaSxcbiAgYWJicmV2aWF0ZWQ6XG4gICAgL14oalthw6RdbnxmZWJ8bcOkclt6XT98YXByfG1haXxqdW5baV0/fGp1bFtpXT98YXVnfHNlcHxva3R8bm92fGRleilcXC4/L2ksXG4gIHdpZGU6IC9eKGphbnVhcnxmZWJydWFyfG3DpHJ6fGFwcmlsfG1haXxqdW5pfGp1bGl8YXVndXN0fHNlcHRlbWJlcnxva3RvYmVyfG5vdmVtYmVyfGRlemVtYmVyKS9pLFxufTtcbmNvbnN0IHBhcnNlTW9udGhQYXR0ZXJucyA9IHtcbiAgbmFycm93OiBbXG4gICAgL15qL2ksXG4gICAgL15mL2ksXG4gICAgL15tL2ksXG4gICAgL15hL2ksXG4gICAgL15tL2ksXG4gICAgL15qL2ksXG4gICAgL15qL2ksXG4gICAgL15hL2ksXG4gICAgL15zL2ksXG4gICAgL15vL2ksXG4gICAgL15uL2ksXG4gICAgL15kL2ksXG4gIF0sXG5cbiAgYW55OiBbXG4gICAgL15qW2HDpF0vaSxcbiAgICAvXmYvaSxcbiAgICAvXm3DpHIvaSxcbiAgICAvXmFwL2ksXG4gICAgL15tYWkvaSxcbiAgICAvXmp1bi9pLFxuICAgIC9eanVsL2ksXG4gICAgL15hdS9pLFxuICAgIC9ecy9pLFxuICAgIC9eby9pLFxuICAgIC9ebi9pLFxuICAgIC9eZC9pLFxuICBdLFxufTtcblxuY29uc3QgbWF0Y2hEYXlQYXR0ZXJucyA9IHtcbiAgbmFycm93OiAvXltzbWRtZl0vaSxcbiAgc2hvcnQ6IC9eKHNvfG1vfGRpfG1pfGRvfGZyfHNhKS9pLFxuICBhYmJyZXZpYXRlZDogL14oc29uP3xtb24/fGRpZT98bWl0P3xkb24/fGZyZT98c2FtPylcXC4/L2ksXG4gIHdpZGU6IC9eKHNvbm50YWd8bW9udGFnfGRpZW5zdGFnfG1pdHR3b2NofGRvbm5lcnN0YWd8ZnJlaXRhZ3xzYW1zdGFnKS9pLFxufTtcbmNvbnN0IHBhcnNlRGF5UGF0dGVybnMgPSB7XG4gIGFueTogWy9ec28vaSwgL15tby9pLCAvXmRpL2ksIC9ebWkvaSwgL15kby9pLCAvXmYvaSwgL15zYS9pXSxcbn07XG5cbmNvbnN0IG1hdGNoRGF5UGVyaW9kUGF0dGVybnMgPSB7XG4gIG5hcnJvdzogL14odm1cXC4/fG5tXFwuP3xNaXR0ZXJuYWNodHxNaXR0YWd8bW9yZ2Vuc3xuYWNobVxcLj98YWJlbmRzfG5hY2h0cykvaSxcbiAgYWJicmV2aWF0ZWQ6XG4gICAgL14odm9ybVxcLj98bmFjaG1cXC4/fE1pdHRlcm5hY2h0fE1pdHRhZ3xtb3JnZW5zfG5hY2htXFwuP3xhYmVuZHN8bmFjaHRzKS9pLFxuICB3aWRlOiAvXih2b3JtaXR0YWdzfG5hY2htaXR0YWdzfE1pdHRlcm5hY2h0fE1pdHRhZ3xtb3JnZW5zfG5hY2htaXR0YWdzfGFiZW5kc3xuYWNodHMpL2ksXG59O1xuY29uc3QgcGFyc2VEYXlQZXJpb2RQYXR0ZXJucyA9IHtcbiAgYW55OiB7XG4gICAgYW06IC9edi9pLFxuICAgIHBtOiAvXm4vaSxcbiAgICBtaWRuaWdodDogL15NaXR0ZS9pLFxuICAgIG5vb246IC9eTWl0dGEvaSxcbiAgICBtb3JuaW5nOiAvbW9yZ2Vucy9pLFxuICAgIGFmdGVybm9vbjogL25hY2htaXR0YWdzL2ksIC8vIHdpbGwgbmV2ZXIgYmUgbWF0Y2hlZC4gQWZ0ZXJub29uIGlzIG1hdGNoZWQgYnkgYHBtYFxuICAgIGV2ZW5pbmc6IC9hYmVuZHMvaSxcbiAgICBuaWdodDogL25hY2h0cy9pLCAvLyB3aWxsIG5ldmVyIGJlIG1hdGNoZWQuIE5pZ2h0IGlzIG1hdGNoZWQgYnkgYHBtYFxuICB9LFxufTtcblxuZXhwb3J0IGNvbnN0IG1hdGNoID0ge1xuICBvcmRpbmFsTnVtYmVyOiBidWlsZE1hdGNoUGF0dGVybkZuKHtcbiAgICBtYXRjaFBhdHRlcm46IG1hdGNoT3JkaW5hbE51bWJlclBhdHRlcm4sXG4gICAgcGFyc2VQYXR0ZXJuOiBwYXJzZU9yZGluYWxOdW1iZXJQYXR0ZXJuLFxuICAgIHZhbHVlQ2FsbGJhY2s6ICh2YWx1ZSkgPT4gcGFyc2VJbnQodmFsdWUpLFxuICB9KSxcblxuICBlcmE6IGJ1aWxkTWF0Y2hGbih7XG4gICAgbWF0Y2hQYXR0ZXJuczogbWF0Y2hFcmFQYXR0ZXJucyxcbiAgICBkZWZhdWx0TWF0Y2hXaWR0aDogXCJ3aWRlXCIsXG4gICAgcGFyc2VQYXR0ZXJuczogcGFyc2VFcmFQYXR0ZXJucyxcbiAgICBkZWZhdWx0UGFyc2VXaWR0aDogXCJhbnlcIixcbiAgfSksXG5cbiAgcXVhcnRlcjogYnVpbGRNYXRjaEZuKHtcbiAgICBtYXRjaFBhdHRlcm5zOiBtYXRjaFF1YXJ0ZXJQYXR0ZXJucyxcbiAgICBkZWZhdWx0TWF0Y2hXaWR0aDogXCJ3aWRlXCIsXG4gICAgcGFyc2VQYXR0ZXJuczogcGFyc2VRdWFydGVyUGF0dGVybnMsXG4gICAgZGVmYXVsdFBhcnNlV2lkdGg6IFwiYW55XCIsXG4gICAgdmFsdWVDYWxsYmFjazogKGluZGV4KSA9PiBpbmRleCArIDEsXG4gIH0pLFxuXG4gIG1vbnRoOiBidWlsZE1hdGNoRm4oe1xuICAgIG1hdGNoUGF0dGVybnM6IG1hdGNoTW9udGhQYXR0ZXJucyxcbiAgICBkZWZhdWx0TWF0Y2hXaWR0aDogXCJ3aWRlXCIsXG4gICAgcGFyc2VQYXR0ZXJuczogcGFyc2VNb250aFBhdHRlcm5zLFxuICAgIGRlZmF1bHRQYXJzZVdpZHRoOiBcImFueVwiLFxuICB9KSxcblxuICBkYXk6IGJ1aWxkTWF0Y2hGbih7XG4gICAgbWF0Y2hQYXR0ZXJuczogbWF0Y2hEYXlQYXR0ZXJucyxcbiAgICBkZWZhdWx0TWF0Y2hXaWR0aDogXCJ3aWRlXCIsXG4gICAgcGFyc2VQYXR0ZXJuczogcGFyc2VEYXlQYXR0ZXJucyxcbiAgICBkZWZhdWx0UGFyc2VXaWR0aDogXCJhbnlcIixcbiAgfSksXG5cbiAgZGF5UGVyaW9kOiBidWlsZE1hdGNoRm4oe1xuICAgIG1hdGNoUGF0dGVybnM6IG1hdGNoRGF5UGVyaW9kUGF0dGVybnMsXG4gICAgZGVmYXVsdE1hdGNoV2lkdGg6IFwid2lkZVwiLFxuICAgIHBhcnNlUGF0dGVybnM6IHBhcnNlRGF5UGVyaW9kUGF0dGVybnMsXG4gICAgZGVmYXVsdFBhcnNlV2lkdGg6IFwiYW55XCIsXG4gIH0pLFxufTtcbiJdLCJuYW1lcyI6WyJidWlsZE1hdGNoRm4iLCJidWlsZE1hdGNoUGF0dGVybkZuIiwibWF0Y2hPcmRpbmFsTnVtYmVyUGF0dGVybiIsInBhcnNlT3JkaW5hbE51bWJlclBhdHRlcm4iLCJtYXRjaEVyYVBhdHRlcm5zIiwibmFycm93IiwiYWJicmV2aWF0ZWQiLCJ3aWRlIiwicGFyc2VFcmFQYXR0ZXJucyIsImFueSIsIm1hdGNoUXVhcnRlclBhdHRlcm5zIiwicGFyc2VRdWFydGVyUGF0dGVybnMiLCJtYXRjaE1vbnRoUGF0dGVybnMiLCJwYXJzZU1vbnRoUGF0dGVybnMiLCJtYXRjaERheVBhdHRlcm5zIiwic2hvcnQiLCJwYXJzZURheVBhdHRlcm5zIiwibWF0Y2hEYXlQZXJpb2RQYXR0ZXJucyIsInBhcnNlRGF5UGVyaW9kUGF0dGVybnMiLCJhbSIsInBtIiwibWlkbmlnaHQiLCJub29uIiwibW9ybmluZyIsImFmdGVybm9vbiIsImV2ZW5pbmciLCJuaWdodCIsIm1hdGNoIiwib3JkaW5hbE51bWJlciIsIm1hdGNoUGF0dGVybiIsInBhcnNlUGF0dGVybiIsInZhbHVlQ2FsbGJhY2siLCJ2YWx1ZSIsInBhcnNlSW50IiwiZXJhIiwibWF0Y2hQYXR0ZXJucyIsImRlZmF1bHRNYXRjaFdpZHRoIiwicGFyc2VQYXR0ZXJucyIsImRlZmF1bHRQYXJzZVdpZHRoIiwicXVhcnRlciIsImluZGV4IiwibW9udGgiLCJkYXkiLCJkYXlQZXJpb2QiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/de/_lib/match.js\n"));

/***/ })

}]);