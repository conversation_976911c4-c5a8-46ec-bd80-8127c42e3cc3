(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[250,687],{5334:(e,n,o)=>{Promise.resolve().then(o.bind(o,6146))},6146:(e,n,o)=>{"use strict";o.d(n,{default:()=>t});var r=o(5155);o(2115);let t=()=>(0,r.jsx)("li",{style:{listStyle:"none"},children:(0,r.jsxs)("button",{onClick:()=>{window.location.href="/admin/sync"},style:{display:"flex",alignItems:"center",gap:"8px",width:"100%",padding:"8px 16px",backgroundColor:"#0070f3",color:"white",border:"none",borderRadius:"6px",cursor:"pointer",fontSize:"14px",fontWeight:"500",textDecoration:"none",transition:"background-color 0.2s ease"},onMouseOver:e=>e.currentTarget.style.backgroundColor="#0056b3",onMouseOut:e=>e.currentTarget.style.backgroundColor="#0070f3",children:[(0,r.jsx)("span",{children:"\uD83D\uDD04"}),(0,r.jsx)("span",{children:"Sync to Main App"})]})})}},e=>{var n=n=>e(e.s=n);e.O(0,[441,517,358],()=>n(5334)),_N_E=e.O()}]);