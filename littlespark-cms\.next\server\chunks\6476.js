"use strict";exports.id=6476,exports.ids=[6476],exports.modules={18963:(a,b,c)=>{c.d(b,{o:()=>l});var d=c(3439),e=c(92055),f=c(20416),g=c(60656),h=c(65143),i=c(91506),j=c(12431),k=c(54750);let l=async({canSetHeaders:a,config:b,params:c,request:l})=>{let m=(0,k.J)(l.headers),n=await (0,h.nm0)({config:b,cron:!0}),{config:o}=n,p=o.localization,q=new URL(l.url),{pathname:r,searchParams:s}=q,t=!o.graphQL.disable&&r===`${o.routes.api}${o.routes.graphQL}`,u=(0,j.Y)({config:o,cookies:m,headers:l.headers}),v=await (0,d.L)({config:o.i18n,context:"api",language:u}),w=s.get("fallback-locale")||s.get("fallbackLocale"),x=s.get("locale"),y=w,{search:z}=q,A=z?e.q(z,{arrayLimit:1e3,depth:10,ignoreQueryPrefix:!0}):{};if(p){let a=(0,i.T)({fallbackLocale:y,locale:x,localization:p});y=a.fallbackLocale,x=a.locale}let B=Object.assign(l,{context:{},fallbackLocale:y,hash:q.hash,host:q.host,href:q.href,i18n:v,locale:x,origin:q.origin,pathname:q.pathname,payload:n,payloadAPI:t?"GraphQL":"REST",payloadDataLoader:void 0,payloadUploadSizes:{},port:q.port,protocol:q.protocol,query:A,routeParams:c||{},search:q.search,searchParams:q.searchParams,t:v.t,transactionID:void 0,user:null});B.payloadDataLoader=(0,g.Y)(B);let{responseHeaders:C,user:D}=await (0,f.F)({canSetHeaders:a,headers:B.headers,isGraphQL:t,payload:n});return B.user=D,C&&(B.responseHeaders=C),B}},57631:(a,b,c)=>{c.d(b,{SJ:()=>F,fG:()=>D,lw:()=>C,D9:()=>G,LO:()=>E,Uc:()=>H});var d=c(26250),e=c(50614),f=c(18963),g=c(60441),h=c(67862),i=c(90035),j=c(65143),k=c(74052),l=c(64932);let m=async({collection:a,config:b,err:c,req:e})=>{if("payloadInitError"in c&&!0===c.payloadInitError)return console.error(c),Response.json({message:"There was an error initializing Payload"},{status:d.h.INTERNAL_SERVER_ERROR});let f=e&&"payload"in e&&e?.payload;if(!f)try{f=await (0,j.nm0)({config:b,cron:!0})}catch(a){return Response.json({message:"There was an error initializing Payload"},{status:d.h.INTERNAL_SERVER_ERROR})}let m=(0,k.v)(c),n=c.status||d.h.INTERNAL_SERVER_ERROR;(0,l.v)({err:c,payload:f}),e.payload=f;let o=(0,g.y)({headers:new Headers,req:e}),{config:p}=f;return p.debug||c.isPublic||n!==d.h.INTERNAL_SERVER_ERROR||(m=(0,k.v)(new i.L("Something went wrong."))),p.debug&&!0===p.debug&&(m.stack=c.stack),a&&await a.config.hooks.afterError?.reduce(async(b,d)=>{await b;let f=await d({collection:a.config,context:e.context,error:c,req:e,result:m});f&&(m=f.response||m,n=f.status||n)},Promise.resolve()),await p.hooks.afterError?.reduce(async(b,d)=>{await b;let f=await d({collection:a?.config,context:e.context,error:c,req:e,result:m});f&&(m=f.response||m,n=f.status||n)},Promise.resolve()),Response.json(m,{headers:e.responseHeaders?(0,h.l)(e.responseHeaders,o):o,status:n})},n=(a,b)=>Response.json({message:`Route not found "${b??new URL(a.url).pathname}"`},{headers:(0,g.y)({headers:new Headers,req:a}),status:d.h.NOT_FOUND}),o=async({basePath:a="",config:b,path:c,request:i})=>{let j,k,l;if("post"===i.method.toLowerCase()&&("GET"===i.headers.get("X-Payload-HTTP-Method-Override")||"GET"===i.headers.get("X-HTTP-Method-Override"))){let d=await i.text(),e=`${i.url}?${new URLSearchParams(d).toString()}`;return await o({basePath:a,config:b,path:c,request:new Request(e,{cache:i.cache,credentials:i.credentials,headers:i.headers,method:"GET",signal:i.signal})})}try{let m;if(k=await (0,f.o)({canSetHeaders:!0,config:b,request:i}),k.method?.toLowerCase()==="options")return Response.json({},{headers:(0,g.y)({headers:new Headers,req:k}),status:200});let{payload:o}=k,{config:p}=o,q=`${a}${c??new URL(k.url).pathname}`;if(!q.startsWith(p.routes.api))return n(k,q);let r=q.replace(p.routes.api,""),s=!1;r.startsWith("/globals")&&(s=!0,r=r.replace("/globals",""));let t=r.split("/");t.shift();let u=t[0];u&&(s?m=o.globals.config.find(a=>a.slug===u):o.collections[u]&&(l=o.collections[u]));let v=p.endpoints;if(l?(v=l.config.endpoints,r=r.replace(`/${l.config.slug}`,"")):m&&(r=r.replace(`/${m.slug}`,""),v=m.endpoints),""===r&&(r="/"),!1===v)return Response.json({message:`Cannot ${k.method?.toUpperCase()} ${k.url}`},{headers:(0,g.y)({headers:new Headers,req:k}),status:d.h.NOT_IMPLEMENTED});let w=v?.find(a=>{if(a.method!==k.method?.toLowerCase())return!1;let b=(0,e.YW)(a.path,{decode:decodeURIComponent})(r);return!!b&&(k.routeParams=b.params,l?k.routeParams.collection=l.config.slug:m&&(k.routeParams.global=m.slug),!0)});if(w&&(j=w.handler),!j)return n(k,q);let x=await j(k);return new Response(x.body,{headers:(0,g.y)({headers:(0,h.l)(k.responseHeaders??new Headers,x.headers),req:k}),status:x.status,statusText:x.statusText})}catch(a){return m({collection:l,config:b,err:a,req:k})}};var p=c(37413),q=c(93033),r=c(79748),s=c(58380),t=c(33873);c(61120);var u=c(79551),v=c(36658);let w=({description:a,Fallback:b,fontFamily:c="Arial, sans-serif",Icon:d,importMap:e,leader:f,title:g})=>{let h=(0,v.f)({clientProps:{fill:"white"},Component:d,Fallback:b,importMap:e});return(0,p.jsxs)("div",{style:{backgroundColor:"#000",color:"#fff",display:"flex",flexDirection:"column",fontFamily:c,height:"100%",justifyContent:"space-between",padding:"100px",width:"100%"},children:[(0,p.jsxs)("div",{style:{display:"flex",flexDirection:"column",flexGrow:1,fontSize:50,height:"100%"},children:[f&&(0,p.jsx)("div",{style:{fontSize:30,marginBottom:10},children:f}),(0,p.jsx)("p",{style:{display:"-webkit-box",fontSize:90,lineHeight:1,marginBottom:0,marginTop:0,textOverflow:"ellipsis",WebkitBoxOrient:"vertical",WebkitLineClamp:2},children:g}),a&&(0,p.jsx)("p",{style:{display:"-webkit-box",flexGrow:1,fontSize:30,lineHeight:1,marginBottom:0,marginTop:40,textOverflow:"ellipsis",WebkitBoxOrient:"vertical",WebkitLineClamp:2},children:a})]}),(0,p.jsx)("div",{style:{alignItems:"flex-end",display:"flex",flexShrink:0,height:"38px",justifyContent:"center",width:"38px"},children:h})]})},x=(0,u.fileURLToPath)("file:///C:/Users/<USER>/kavya-git/spark-new/littlespark-cms/node_modules/@payloadcms/next/dist/routes/rest/og/index.js"),y=t.dirname(x),z=async a=>{let b=a.payload.config;if("off"===b.admin.meta.defaultOGImageType)return Response.json({error:"Open Graph images are disabled"},{status:400});try{let c,{searchParams:d}=new URL(a.url),e=d.has("title")?d.get("title")?.slice(0,100):"",f=d.has("leader")?d.get("leader")?.slice(0,100).replace("-"," "):"",g=d.has("description")?d.get("description"):"";try{c=r.readFile(t.join(y,"roboto-regular.woff"))}catch(b){a.payload.logger.error(`Error reading font file or not readable: ${b.message}`)}return new s.f((0,p.jsx)(w,{description:g,Fallback:q.Ou,fontFamily:"Roboto, sans-serif",Icon:b.admin?.components?.graphics?.Icon,importMap:a.payload.importMap,leader:f,title:e}),{...c?{fonts:[{name:"Roboto",data:await c,style:"normal",weight:400}]}:{},height:630,width:1200})}catch(b){return a.payload.logger.error(`Error generating Open Graph image: ${b.message}`),Response.json({error:`Internal Server Error: ${b.message}`},{status:500})}},A=!1,B=a=>async(b,c)=>{let d=await a;!1!==A||d.endpoints.some(a=>"/og"===a.path&&"get"===a.method)||d.endpoints.push({handler:z,method:"get",path:"/og"}),A=!0;let e=await c.params;return await o({config:a,path:e?`${d.routes.api}/${e.slug.join("/")}`:void 0,request:b})},C=B,D=B,E=B,F=B,G=B,H=B},58380:(a,b,c)=>{Object.defineProperty(b,"f",{enumerable:!0,get:function(){return d}});class d extends Response{static #a=this.displayName="ImageResponse";constructor(...a){let b=new ReadableStream({async start(b){let d=new(await Promise.resolve().then(c.bind(c,83725))).ImageResponse(...a);if(!d.body)return b.close();let e=d.body.getReader();for(;;){let{done:a,value:c}=await e.read();if(a)return b.close();b.enqueue(c)}}}),d=a[1]||{},e=new Headers({"content-type":"image/png","cache-control":"public, immutable, no-transform, max-age=31536000"});d.headers&&new Headers(d.headers).forEach((a,b)=>e.set(b,a)),super(b,{headers:e,status:d.status,statusText:d.statusText})}}},66946:(a,b,c)=>{Object.defineProperty(b,"I",{enumerable:!0,get:function(){return g}});let d=c(30898),e=c(42471),f=c(47912);async function g(a,b,c,g){if((0,d.isNodeNextResponse)(b)){var h;b.statusCode=c.status,b.statusMessage=c.statusText;let d=["set-cookie","www-authenticate","proxy-authenticate","vary"];null==(h=c.headers)||h.forEach((a,c)=>{if("x-middleware-set-cookie"!==c.toLowerCase())if("set-cookie"===c.toLowerCase())for(let d of(0,f.splitCookiesString)(a))b.appendHeader(c,d);else{let e=void 0!==b.getHeader(c);(d.includes(c.toLowerCase())||!e)&&b.appendHeader(c,a)}});let{originalResponse:i}=b;c.body&&"HEAD"!==a.method?await (0,e.pipeToNodeResponse)(c.body,i,g):i.end()}}},96559:(a,b,c)=>{a.exports=c(44870)}};