import { withPayload } from '@payloadcms/next/withPayload'

/** @type {import('next').NextConfig} */
const nextConfig = {
  // Your Next.js config here
  output: 'standalone',
  serverExternalPackages: ['@payloadcms/next'],
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  generateBuildId: async () => {
    return 'build-' + Date.now()
  },
  webpack: (webpackConfig, { isServer }) => {
    webpackConfig.resolve.extensionAlias = {
      '.cjs': ['.cts', '.cjs'],
      '.js': ['.ts', '.tsx', '.js', '.jsx'],
      '.mjs': ['.mts', '.mjs'],
    }

    // Skip problematic modules during build
    if (isServer) {
      webpackConfig.externals = webpackConfig.externals || []
      webpackConfig.externals.push({
        'react/jsx-runtime': 'commonjs react/jsx-runtime',
        'react': 'commonjs react',
        'react-dom': 'commonjs react-dom',
        'react-image-crop': 'commonjs react-image-crop'
      })
    }

    // Handle problematic CSS imports
    webpackConfig.module.rules.push({
      test: /ReactCrop\.css$/,
      use: 'ignore-loader'
    })

    return webpackConfig
  },
}

export default withPayload(nextConfig, { devBundleServerPackages: false })
