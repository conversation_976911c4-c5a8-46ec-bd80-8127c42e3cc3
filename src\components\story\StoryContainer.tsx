"use client";
import React, { useEffect } from "react";
import { StoryProvider, useStory } from "./StoryContext";
import StoryHeader from "./StoryHeader";
import StoryTitle from "./StoryTitle";
import ReaderAgeSelector from "./ReaderAgeSelector";
import StoryFormatSelector from "./StoryFormatSelector";
import StoryContent from "./StoryContent";
// import CreativeAssistants from "./CreativeAssistants";
import StoryActionButtons from "./StoryActionButtons";
import StoryHelpDialog from "./StoryHelpDialog";
import EducationalBenefits from "./EducationalBenefits";
import StoryLearningGoals from "./StoryLearningGoals";

interface ChallengeData {
    id: string;
    title: string;
    description: string;
    category: string;
    instructions: string;
    learningObjectives?: Array<{objective: string}>;
    materials?: Array<{material: string}>;
    estimatedTime?: number;
    difficulty?: string;
}

interface StoryPageContentProps {
    challengeData?: ChallengeData;
}

const StoryPageContent = ({ challengeData }: StoryPageContentProps) => {
    const { helpDialogOpen, setHelpDialogOpen, setChallengeData } = useStory();

    useEffect(() => {
        if (challengeData) {
            setChallengeData(challengeData);
        }
    }, [challengeData, setChallengeData]);

    return (
        <div className="container mx-auto px-4 py-8">
            <div className="max-w-6xl mx-auto">
                <StoryHeader />
                <div className="mb-6" />

                {/* Challenge Information */}
                {challengeData && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                        <h2 className="text-xl font-bold text-blue-900 mb-2">
                            📚 Challenge: {challengeData.title}
                        </h2>
                        <p className="text-blue-800 mb-3">{challengeData.description}</p>

                        <div className="grid md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <h4 className="font-semibold text-blue-900 mb-2">Instructions:</h4>
                                <p className="text-blue-700 whitespace-pre-line">{challengeData.instructions}</p>
                            </div>

                            <div>
                                <h4 className="font-semibold text-blue-900 mb-2">Learning Goals:</h4>
                                <ul className="text-blue-700 space-y-1">
                                    {challengeData.learningObjectives?.map((obj: {objective: string}, index: number) => (
                                        <li key={index}>• {obj.objective}</li>
                                    ))}
                                </ul>

                                {challengeData.materials && challengeData.materials.length > 0 && (
                                    <div className="mt-3">
                                        <h4 className="font-semibold text-blue-900 mb-2">Materials:</h4>
                                        <ul className="text-blue-700 space-y-1">
                                            {challengeData.materials.map((mat: {material: string}, index: number) => (
                                                <li key={index}>• {mat.material}</li>
                                            ))}
                                        </ul>
                                    </div>
                                )}
                            </div>
                        </div>

                        <div className="mt-4 flex items-center gap-4 text-sm text-blue-600">
                            <span>⏱️ {challengeData.estimatedTime} minutes</span>
                            <span>📊 {challengeData.difficulty}</span>
                        </div>
                    </div>
                )}

                <StoryTitle />
                <div className="mb-3" />
                <ReaderAgeSelector />
                <div className="mb-2" />
                <StoryFormatSelector />
                <div className="mb-6">
                    <h3 className="text-lg font-medium text-center mb-3">
                        Your Story
                    </h3>
                    <StoryContent />
                </div>
                <StoryActionButtons />
                <EducationalBenefits />
                <StoryLearningGoals />
                {/* <CreativeAssistants /> */}
                <StoryHelpDialog
                    open={helpDialogOpen}
                    onOpenChange={setHelpDialogOpen}
                />
            </div>
        </div>
    );
};

interface StoryContainerProps {
    challengeData?: {
        id?: string;
        title?: string;
        description?: string;
        instructions?: string;
        category?: string;
        difficulty?: string;
        estimatedTime?: number;
        learningObjectives?: Array<{ objective: string }>;
        materials?: Array<{ material: string }>;
    };
}

const StoryContainer = ({ challengeData }: StoryContainerProps) => {
    return (
        <StoryProvider>
            <StoryPageContent challengeData={challengeData as unknown as ChallengeData} />
        </StoryProvider>
    );
};

export default StoryContainer;
