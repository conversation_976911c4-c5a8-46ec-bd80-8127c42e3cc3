exports.id=6622,exports.ids=[6622],exports.modules={3499:(a,b,c)=>{"use strict";c.d(b,{N:()=>d});let d={slug:"challenges",admin:{useAsTitle:"title",defaultColumns:["title","category","ageGroup","difficulty","status","publishedAt"],group:"Content"},access:{create:({req:{user:a}})=>a?.role==="admin"||a?.role==="content-creator"||a?.role==="educator",read:()=>!0,update:({req:{user:a}})=>a?.role==="admin"||(a?.role==="content-creator"||a?.role==="educator")&&{createdBy:{equals:a.id}},delete:({req:{user:a}})=>a?.role==="admin"},fields:[{name:"title",type:"text",required:!0,admin:{description:"The main title of the creative challenge"}},{name:"slug",type:"text",required:!0,unique:!0,admin:{description:"URL-friendly version of the title"},hooks:{beforeValidate:[({value:a,data:b})=>!a&&b?.title?b.title.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/(^-|-$)/g,""):a]}},{name:"description",type:"textarea",required:!0,admin:{description:"Brief description of what children will create"}},{name:"prompt",type:"textarea",required:!0,admin:{description:"The main challenge prompt/question for users (required for Supabase compatibility)"}},{name:"category",type:"select",required:!0,options:[{label:"\uD83C\uDFA8 Art & Drawing",value:"art"},{label:"\uD83D\uDCDA Creative Writing",value:"story"},{label:"\uD83C\uDFB5 Music & Sound",value:"music"},{label:"\uD83C\uDFAE Game Design",value:"game"},{label:"\uD83D\uDCBB Coding & Logic",value:"coding"},{label:"\uD83C\uDFAC Video Creation",value:"video"}],admin:{description:'Choose the primary category - this determines which tool opens when users click "Start Challenge"',position:"sidebar"}},{name:"ageGroup",type:"select",required:!0,hasMany:!0,options:[{label:"6-8 years",value:"6-8"},{label:"9-11 years",value:"9-11"},{label:"12-14 years",value:"12-14"}],admin:{description:"Age groups this challenge is suitable for"}},{name:"difficulty",type:"select",required:!0,options:[{label:"Easy",value:"easy"},{label:"Medium",value:"medium"},{label:"Hard",value:"hard"}],admin:{description:"Difficulty level for children"}},{name:"estimatedTime",type:"number",required:!0,min:5,max:120,admin:{description:"Estimated completion time in minutes",step:5}},{name:"instructions",type:"textarea",required:!0,admin:{description:"Step-by-step instructions for completing the challenge",rows:8}},{name:"learningObjectives",type:"array",required:!0,minRows:1,maxRows:5,fields:[{name:"objective",type:"text",required:!0}],admin:{description:"What children will learn from this challenge"}},{name:"materials",type:"array",fields:[{name:"material",type:"text",required:!0},{name:"optional",type:"checkbox",defaultValue:!1}],admin:{description:"Materials needed to complete the challenge"}},{name:"media",type:"array",fields:[{name:"file",type:"upload",relationTo:"media",required:!0},{name:"caption",type:"text"},{name:"type",type:"select",options:[{label:"Tutorial Video",value:"tutorial"},{label:"Example Image",value:"example"},{label:"Reference Material",value:"reference"},{label:"Step Image",value:"step"}],required:!0},{name:"order",type:"number",defaultValue:0,admin:{description:"Display order (0 = first)"}}],admin:{description:"Images, videos, and other media for this challenge"}},{name:"subscriptionTier",type:"select",required:!0,options:[{label:"Free",value:"free"},{label:"Premium",value:"premium"}],defaultValue:"premium",admin:{description:"Whether this challenge requires a subscription"}},{name:"featured",type:"checkbox",defaultValue:!1,admin:{description:"Show this challenge prominently on the homepage"}},{name:"seasonal",type:"group",fields:[{name:"isSeasonalContent",type:"checkbox",defaultValue:!1},{name:"season",type:"select",options:[{label:"Spring",value:"spring"},{label:"Summer",value:"summer"},{label:"Fall/Autumn",value:"fall"},{label:"Winter",value:"winter"},{label:"Halloween",value:"halloween"},{label:"Christmas",value:"christmas"},{label:"New Year",value:"newyear"}],admin:{condition:(a,b)=>b?.isSeasonalContent}}]},{name:"is_active",type:"checkbox",defaultValue:!0,admin:{description:"Whether this challenge is active and available to users (Supabase compatibility)",position:"sidebar"}},{name:"status",type:"select",required:!0,options:[{label:"Draft",value:"draft"},{label:"Under Review",value:"review"},{label:"Published",value:"published"},{label:"Archived",value:"archived"}],defaultValue:"draft",admin:{description:"Publication status of this challenge"}},{name:"publishedAt",type:"date",admin:{condition:a=>"published"===a.status,description:"When this challenge was published"}},{name:"createdBy",type:"relationship",relationTo:"users",required:!0,admin:{description:"Content creator who made this challenge",position:"sidebar"},access:{update:({req:{user:a}})=>a?.role==="admin"}},{name:"mainAppId",type:"text",admin:{description:"ID from main application (for synced challenges)",readOnly:!0,position:"sidebar"}},{name:"isUserGenerated",type:"checkbox",defaultValue:!1,admin:{description:"Whether this challenge was created by users in the main app",readOnly:!0,position:"sidebar"}},{name:"syncStatus",type:"select",options:[{label:"Not Synced",value:"not_synced"},{label:"Synced to Main App",value:"synced_to_main"},{label:"Synced from Main App",value:"synced_from_main"},{label:"Sync Failed",value:"sync_failed"}],defaultValue:"not_synced",admin:{description:"Synchronization status with main application",position:"sidebar"}}],hooks:{beforeChange:[({data:a,operation:b,req:c})=>("published"!==a.status||a.publishedAt||(a.publishedAt=new Date),"create"===b&&c.user&&!a.createdBy&&(a.createdBy=c.user.id),a)],beforeValidate:[({data:a,operation:b,req:c})=>("create"===b&&c.user&&a&&!a.createdBy&&(a.createdBy=c.user.id),a)]},versions:{drafts:!0,maxPerDoc:10}}},38565:(a,b,c)=>{"use strict";c.d(b,{$:()=>d});let d={slug:"media",access:{read:()=>!0},fields:[{name:"alt",type:"text",required:!0}],upload:!0}},39727:()=>{},47990:()=>{},62737:(a,b,c)=>{"use strict";c.d(b,{f:()=>d});let d={slug:"story-templates",dbName:"story_templates",admin:{useAsTitle:"title",defaultColumns:["title","genre","ageGroup","status","publishedAt"],group:"Content"},access:{read:()=>!0,create:({req:{user:a}})=>a?.role==="content-creator"||a?.role==="admin",update:({req:{user:a}})=>a?.role==="content-creator"||a?.role==="admin",delete:({req:{user:a}})=>a?.role==="admin"},fields:[{name:"title",type:"text",required:!0,admin:{description:"Title of the story template"}},{name:"slug",type:"text",required:!0,unique:!0,hooks:{beforeValidate:[({value:a,data:b})=>!a&&b?.title?b.title.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/(^-|-$)/g,""):a]}},{name:"description",type:"textarea",required:!0,admin:{description:"Brief description of the story template"}},{name:"genre",type:"select",required:!0,hasMany:!0,options:[{label:"Adventure",value:"adventure"},{label:"Fantasy",value:"fantasy"},{label:"Science Fiction",value:"sci-fi"},{label:"Mystery",value:"mystery"},{label:"Friendship",value:"friendship"},{label:"Family",value:"family"},{label:"Animals",value:"animals"},{label:"Magic",value:"magic"},{label:"Space",value:"space"},{label:"Underwater",value:"underwater"}]},{name:"ageGroup",type:"select",required:!0,hasMany:!0,options:[{label:"6-8 years",value:"6-8"},{label:"9-11 years",value:"9-11"},{label:"12-14 years",value:"12-14"}]},{name:"storyPrompt",type:"textarea",required:!0,admin:{description:"The opening prompt that starts the story",rows:4}},{name:"characterOptions",type:"array",required:!0,minRows:2,maxRows:8,fields:[{name:"name",type:"text",required:!0},{name:"description",type:"text",required:!0},{name:"image",type:"upload",relationTo:"media"}],admin:{description:"Character options children can choose from"}},{name:"settingOptions",type:"array",required:!0,minRows:2,maxRows:6,fields:[{name:"name",type:"text",required:!0},{name:"description",type:"text",required:!0},{name:"image",type:"upload",relationTo:"media"}],admin:{description:"Setting options for the story"}},{name:"plotPoints",type:"array",required:!0,minRows:3,maxRows:10,fields:[{name:"title",type:"text",required:!0},{name:"description",type:"textarea",required:!0},{name:"order",type:"number",required:!0,defaultValue:1},{name:"optional",type:"checkbox",defaultValue:!1,admin:{description:"Whether this plot point is optional"}}],admin:{description:"Key plot points to guide the story structure"}},{name:"writingPrompts",type:"array",fields:[{name:"prompt",type:"text",required:!0},{name:"category",type:"select",options:[{label:"Character Development",value:"character"},{label:"Setting Description",value:"setting"},{label:"Action Scene",value:"action"},{label:"Dialogue",value:"dialogue"},{label:"Emotion",value:"emotion"}]}],admin:{description:"Additional writing prompts to help children"}},{name:"estimatedLength",type:"group",fields:[{name:"minWords",type:"number",defaultValue:100},{name:"maxWords",type:"number",defaultValue:500}],admin:{description:"Suggested story length"}},{name:"learningObjectives",type:"array",required:!0,fields:[{name:"objective",type:"text",required:!0}],admin:{description:"What children will learn from this story template"}},{name:"subscriptionTier",type:"select",required:!0,options:[{label:"Free",value:"free"},{label:"Premium",value:"premium"}],defaultValue:"premium"},{name:"featured",type:"checkbox",defaultValue:!1},{name:"seasonal",type:"group",fields:[{name:"isSeasonalContent",type:"checkbox",defaultValue:!1},{name:"season",type:"select",options:[{label:"Spring",value:"spring"},{label:"Summer",value:"summer"},{label:"Fall/Autumn",value:"fall"},{label:"Winter",value:"winter"},{label:"Halloween",value:"halloween"},{label:"Christmas",value:"christmas"},{label:"New Year",value:"newyear"}],admin:{condition:(a,b)=>b?.isSeasonalContent}}]},{name:"status",type:"select",required:!0,options:[{label:"Draft",value:"draft"},{label:"Under Review",value:"review"},{label:"Published",value:"published"},{label:"Archived",value:"archived"}],defaultValue:"draft"},{name:"publishedAt",type:"date",admin:{condition:a=>"published"===a.status}},{name:"createdBy",type:"relationship",relationTo:"users",admin:{readOnly:!0}}],hooks:{beforeChange:[({data:a,operation:b,req:c})=>("published"!==a.status||a.publishedAt||(a.publishedAt=new Date),"create"===b&&c.user&&(a.createdBy=c.user.id),a)]},versions:{drafts:!0,maxPerDoc:5}}},81329:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.d(b,{A:()=>v});var e=c(43680),f=c(42836),g=c(12898),h=c(33873),i=c.n(h),j=c(49655),k=c(79551),l=c(9288),m=c.n(l),n=c(86873),o=c(38565),p=c(3499),q=c(62737),r=c(93869),s=a([e]);e=(s.then?(await s)():s)[0];let t=(0,k.fileURLToPath)("file:///C:/Users/<USER>/kavya-git/spark-new/littlespark-cms/src/payload.config.ts"),u=i().dirname(t),v=(0,j.f)({admin:{user:n.z.slug,importMap:{baseDir:i().resolve(u)}},collections:[n.z,o.$,p.N,q.f,r.B],editor:(0,g.qf)(),secret:process.env.PAYLOAD_SECRET||"",typescript:{outputFile:i().resolve(u,"payload-types.ts")},db:(0,e.w)({client:{url:process.env.DATABASE_URI||"file:./cms.db"}}),sharp:m(),plugins:[(0,f.T)()]});d()}catch(a){d(a)}})},86873:(a,b,c)=>{"use strict";c.d(b,{z:()=>d});let d={slug:"users",admin:{useAsTitle:"email",defaultColumns:["email","firstName","lastName","role"],group:"Admin"},auth:!0,access:{create:({req:{user:a}})=>a?.role==="admin",read:({req:{user:a}})=>a?.role==="admin"||{id:{equals:a?.id}},update:({req:{user:a}})=>a?.role==="admin"||{id:{equals:a?.id}},delete:({req:{user:a}})=>a?.role==="admin"},fields:[{name:"firstName",type:"text",required:!0},{name:"lastName",type:"text",required:!0},{name:"role",type:"select",required:!0,options:[{label:"Super Admin",value:"admin"},{label:"Content Creator",value:"content-creator"},{label:"Educator",value:"educator"},{label:"Reviewer",value:"reviewer"}],defaultValue:"educator",admin:{description:"User role determines access permissions"}},{name:"bio",type:"textarea",admin:{description:"Brief bio about the content creator/educator"}},{name:"avatar",type:"upload",relationTo:"media",admin:{description:"Profile picture"}},{name:"specialties",type:"array",fields:[{name:"specialty",type:"select",options:[{label:"Art & Drawing",value:"art"},{label:"Creative Writing",value:"story"},{label:"Music & Sound",value:"music"},{label:"Coding & Logic",value:"coding"},{label:"Video Creation",value:"video"},{label:"Game Design",value:"game"}]}],admin:{description:"Areas of expertise for content creators"}},{name:"isActive",type:"checkbox",defaultValue:!0,admin:{description:"Whether this user account is active"}}]}},93869:(a,b,c)=>{"use strict";c.d(b,{B:()=>d});let d={slug:"educational-resources",dbName:"edu_resources",admin:{useAsTitle:"title",defaultColumns:["title","type","subject","ageGroup","status"],group:"Content"},access:{read:()=>!0,create:({req:{user:a}})=>a?.role==="content-creator"||a?.role==="admin",update:({req:{user:a}})=>a?.role==="content-creator"||a?.role==="admin",delete:({req:{user:a}})=>a?.role==="admin"},fields:[{name:"title",type:"text",required:!0,admin:{description:"Title of the educational resource"}},{name:"slug",type:"text",required:!0,unique:!0,hooks:{beforeValidate:[({value:a,data:b})=>!a&&b?.title?b.title.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/(^-|-$)/g,""):a]}},{name:"description",type:"textarea",required:!0,admin:{description:"Brief description of the resource"}},{name:"type",type:"select",required:!0,options:[{label:"Tutorial",value:"tutorial"},{label:"Guide",value:"guide"},{label:"Reference Sheet",value:"reference"},{label:"Video Lesson",value:"video"},{label:"Interactive Activity",value:"interactive"},{label:"Worksheet",value:"worksheet"},{label:"Tips & Tricks",value:"tips"}]},{name:"subject",type:"select",required:!0,hasMany:!0,options:[{label:"Art Techniques",value:"art-techniques"},{label:"Color Theory",value:"color-theory"},{label:"Drawing Basics",value:"drawing-basics"},{label:"Creative Writing",value:"creative-writing"},{label:"Storytelling",value:"storytelling"},{label:"Music Theory",value:"music-theory"},{label:"Coding Basics",value:"coding-basics"},{label:"Game Design",value:"game-design"},{label:"Video Creation",value:"video-creation"},{label:"Digital Art",value:"digital-art"}]},{name:"ageGroup",type:"select",required:!0,hasMany:!0,options:[{label:"6-8 years",value:"6-8"},{label:"9-11 years",value:"9-11"},{label:"12-14 years",value:"12-14"}]},{name:"content",type:"textarea",required:!0,admin:{description:"Main content of the educational resource",rows:10}},{name:"media",type:"array",fields:[{name:"file",type:"upload",relationTo:"media",required:!0},{name:"caption",type:"text"},{name:"type",type:"select",options:[{label:"Main Image",value:"main"},{label:"Step Image",value:"step"},{label:"Example",value:"example"},{label:"Video",value:"video"},{label:"Audio",value:"audio"},{label:"Downloadable",value:"download"}],required:!0}]},{name:"downloadableFiles",type:"array",dbName:"downloads",fields:[{name:"file",type:"upload",relationTo:"media",required:!0},{name:"title",type:"text",required:!0},{name:"description",type:"text"},{name:"fileType",type:"select",dbName:"file_type",options:[{label:"PDF Worksheet",value:"pdf"},{label:"Template",value:"template"},{label:"Reference Chart",value:"chart"},{label:"Audio File",value:"audio"},{label:"Video File",value:"video"}]}],admin:{description:"Files that children/parents can download"}},{name:"relatedChallenges",type:"relationship",relationTo:"challenges",hasMany:!0,admin:{description:"Challenges that use this educational resource"}},{name:"prerequisites",type:"array",fields:[{name:"skill",type:"text",required:!0},{name:"level",type:"select",options:[{label:"Beginner",value:"beginner"},{label:"Intermediate",value:"intermediate"},{label:"Advanced",value:"advanced"}]}],admin:{description:"Skills children should have before using this resource"}},{name:"learningOutcomes",type:"array",required:!0,fields:[{name:"outcome",type:"text",required:!0}],admin:{description:"What children will learn from this resource"}},{name:"estimatedReadTime",type:"number",admin:{description:"Estimated reading/viewing time in minutes"}},{name:"difficulty",type:"select",required:!0,options:[{label:"Beginner",value:"beginner"},{label:"Intermediate",value:"intermediate"},{label:"Advanced",value:"advanced"}]},{name:"subscriptionTier",type:"select",required:!0,options:[{label:"Free",value:"free"},{label:"Premium",value:"premium"}],defaultValue:"free"},{name:"featured",type:"checkbox",defaultValue:!1},{name:"tags",type:"array",fields:[{name:"tag",type:"text",required:!0}],admin:{description:"Tags for better searchability"}},{name:"status",type:"select",required:!0,options:[{label:"Draft",value:"draft"},{label:"Under Review",value:"review"},{label:"Published",value:"published"},{label:"Archived",value:"archived"}],defaultValue:"draft"},{name:"publishedAt",type:"date",admin:{condition:a=>"published"===a.status}},{name:"createdBy",type:"relationship",relationTo:"users",admin:{readOnly:!0}}],hooks:{beforeChange:[({data:a,operation:b,req:c})=>("published"!==a.status||a.publishedAt||(a.publishedAt=new Date),"create"===b&&c.user&&(a.createdBy=c.user.id),a)]},versions:{drafts:!0,maxPerDoc:5}}}};