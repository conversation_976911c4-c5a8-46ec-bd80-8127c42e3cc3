import { NextRequest, NextResponse } from 'next/server';
import { getPayload } from 'payload';
import config from '@payload-config';

// GET /api/auth/verify - Verify user authentication and permissions
export async function GET(request: NextRequest) {
  try {
    const payload = await getPayload({ config });
    
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'No valid authorization token provided',
          authenticated: false
        },
        { status: 401 }
      );
    }

    // const token = authHeader.substring(7); 
    
    // Verify the token and get user
    const headers = new Headers();
    headers.set('authorization', authHeader);
    const { user } = await payload.auth({ headers });
    
    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Invalid or expired token',
          authenticated: false
        },
        { status: 401 }
      );
    }

    // Return user information (excluding sensitive data)
    const userInfo = {
      id: user.id,
      email: user.email,
      role: user.role,
      isActive: user.isActive !== false,
      firstName: user.firstName,
      lastName: user.lastName,
    };

    return NextResponse.json({
      success: true,
      authenticated: true,
      user: userInfo,
      permissions: {
        isAdmin: user.role === 'admin',
        canSync: user.role === 'admin',
        canManageUsers: user.role === 'admin',
        canManageContent: ['admin', 'content-creator'].includes(user.role),
      }
    });

  } catch (error) {
    console.error('Auth verification error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Authentication verification failed',
        authenticated: false
      },
      { status: 500 }
    );
  }
}
