globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/(payload)/api/graphql-playground/route"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\node_modules\\@payloadcms\\next\\dist\\prod\\styles.css":{"id":2280,"name":"*","chunks":["139","static/chunks/app/(payload)/api/graphql-playground/route-dc22a060bd9bd96d.js"],"async":false},"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\node_modules\\@payloadcms\\next\\dist\\esm\\prod\\styles.css":{"id":2280,"name":"*","chunks":["139","static/chunks/app/(payload)/api/graphql-playground/route-dc22a060bd9bd96d.js"],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\":[],"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\api\\[...slug]\\route":["static/css/5491896ccd21f128.css"],"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\api\\graphql-playground\\route":["static/css/5491896ccd21f128.css"]},"rscModuleMapping":{"2280":{"*":{"id":"63046","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}