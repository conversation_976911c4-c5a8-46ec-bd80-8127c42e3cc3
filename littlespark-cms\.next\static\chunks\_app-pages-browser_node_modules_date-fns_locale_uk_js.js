"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_date-fns_locale_uk_js"],{

/***/ "(app-pages-browser)/./node_modules/date-fns/isSameWeek.js":
/*!*********************************************!*\
  !*** ./node_modules/date-fns/isSameWeek.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isSameWeek: () => (/* binding */ isSameWeek)\n/* harmony export */ });\n/* harmony import */ var _lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_lib/normalizeDates.js */ \"(app-pages-browser)/./node_modules/date-fns/_lib/normalizeDates.js\");\n/* harmony import */ var _startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./startOfWeek.js */ \"(app-pages-browser)/./node_modules/date-fns/startOfWeek.js\");\n\n\n/**\n * The {@link isSameWeek} function options.\n */ /**\n * @name isSameWeek\n * @category Week Helpers\n * @summary Are the given dates in the same week (and month and year)?\n *\n * @description\n * Are the given dates in the same week (and month and year)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same week (and month and year)\n *\n * @example\n * // Are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4))\n * //=> true\n *\n * @example\n * // If week starts with Monday,\n * // are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4), {\n *   weekStartsOn: 1\n * })\n * //=> false\n *\n * @example\n * // Are 1 January 2014 and 1 January 2015 in the same week?\n * const result = isSameWeek(new Date(2014, 0, 1), new Date(2015, 0, 1))\n * //=> false\n */ function isSameWeek(laterDate, earlierDate, options) {\n    const [laterDate_, earlierDate_] = (0,_lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__.normalizeDates)(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate);\n    return +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(laterDate_, options) === +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(earlierDate_, options);\n}\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (isSameWeek);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/isSameWeek.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/uk.js":
/*!********************************************!*\
  !*** ./node_modules/date-fns/locale/uk.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   uk: () => (/* binding */ uk)\n/* harmony export */ });\n/* harmony import */ var _uk_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./uk/_lib/formatDistance.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/uk/_lib/formatDistance.js\");\n/* harmony import */ var _uk_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./uk/_lib/formatLong.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/uk/_lib/formatLong.js\");\n/* harmony import */ var _uk_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./uk/_lib/formatRelative.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/uk/_lib/formatRelative.js\");\n/* harmony import */ var _uk_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./uk/_lib/localize.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/uk/_lib/localize.js\");\n/* harmony import */ var _uk_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./uk/_lib/match.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/uk/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary Ukrainian locale.\n * @language Ukrainian\n * @iso-639-2 ukr\n * <AUTHOR> Korzh [@korzhyk](https://github.com/korzhyk)\n * <AUTHOR> Shcherbyak [@shcherbyakdev](https://github.com/shcherbyakdev)\n */ const uk = {\n    code: \"uk\",\n    formatDistance: _uk_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _uk_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _uk_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _uk_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _uk_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 1 /* Monday */ ,\n        firstWeekContainsDate: 1\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (uk);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/uk.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/uk/_lib/formatDistance.js":
/*!****************************************************************!*\
  !*** ./node_modules/date-fns/locale/uk/_lib/formatDistance.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\nfunction declension(scheme, count) {\n    // scheme for count=1 exists\n    if (scheme.one !== undefined && count === 1) {\n        return scheme.one;\n    }\n    const rem10 = count % 10;\n    const rem100 = count % 100;\n    // 1, 21, 31, ...\n    if (rem10 === 1 && rem100 !== 11) {\n        return scheme.singularNominative.replace(\"{{count}}\", String(count));\n    // 2, 3, 4, 22, 23, 24, 32 ...\n    } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n        return scheme.singularGenitive.replace(\"{{count}}\", String(count));\n    // 5, 6, 7, 8, 9, 10, 11, ...\n    } else {\n        return scheme.pluralGenitive.replace(\"{{count}}\", String(count));\n    }\n}\nfunction buildLocalizeTokenFn(scheme) {\n    return (count, options)=>{\n        if (options && options.addSuffix) {\n            if (options.comparison && options.comparison > 0) {\n                if (scheme.future) {\n                    return declension(scheme.future, count);\n                } else {\n                    return \"за \" + declension(scheme.regular, count);\n                }\n            } else {\n                if (scheme.past) {\n                    return declension(scheme.past, count);\n                } else {\n                    return declension(scheme.regular, count) + \" тому\";\n                }\n            }\n        } else {\n            return declension(scheme.regular, count);\n        }\n    };\n}\nconst halfAtMinute = (_, options)=>{\n    if (options && options.addSuffix) {\n        if (options.comparison && options.comparison > 0) {\n            return \"за півхвилини\";\n        } else {\n            return \"півхвилини тому\";\n        }\n    }\n    return \"півхвилини\";\n};\nconst formatDistanceLocale = {\n    lessThanXSeconds: buildLocalizeTokenFn({\n        regular: {\n            one: \"менше секунди\",\n            singularNominative: \"менше {{count}} секунди\",\n            singularGenitive: \"менше {{count}} секунд\",\n            pluralGenitive: \"менше {{count}} секунд\"\n        },\n        future: {\n            one: \"менше, ніж за секунду\",\n            singularNominative: \"менше, ніж за {{count}} секунду\",\n            singularGenitive: \"менше, ніж за {{count}} секунди\",\n            pluralGenitive: \"менше, ніж за {{count}} секунд\"\n        }\n    }),\n    xSeconds: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} секунда\",\n            singularGenitive: \"{{count}} секунди\",\n            pluralGenitive: \"{{count}} секунд\"\n        },\n        past: {\n            singularNominative: \"{{count}} секунду тому\",\n            singularGenitive: \"{{count}} секунди тому\",\n            pluralGenitive: \"{{count}} секунд тому\"\n        },\n        future: {\n            singularNominative: \"за {{count}} секунду\",\n            singularGenitive: \"за {{count}} секунди\",\n            pluralGenitive: \"за {{count}} секунд\"\n        }\n    }),\n    halfAMinute: halfAtMinute,\n    lessThanXMinutes: buildLocalizeTokenFn({\n        regular: {\n            one: \"менше хвилини\",\n            singularNominative: \"менше {{count}} хвилини\",\n            singularGenitive: \"менше {{count}} хвилин\",\n            pluralGenitive: \"менше {{count}} хвилин\"\n        },\n        future: {\n            one: \"менше, ніж за хвилину\",\n            singularNominative: \"менше, ніж за {{count}} хвилину\",\n            singularGenitive: \"менше, ніж за {{count}} хвилини\",\n            pluralGenitive: \"менше, ніж за {{count}} хвилин\"\n        }\n    }),\n    xMinutes: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} хвилина\",\n            singularGenitive: \"{{count}} хвилини\",\n            pluralGenitive: \"{{count}} хвилин\"\n        },\n        past: {\n            singularNominative: \"{{count}} хвилину тому\",\n            singularGenitive: \"{{count}} хвилини тому\",\n            pluralGenitive: \"{{count}} хвилин тому\"\n        },\n        future: {\n            singularNominative: \"за {{count}} хвилину\",\n            singularGenitive: \"за {{count}} хвилини\",\n            pluralGenitive: \"за {{count}} хвилин\"\n        }\n    }),\n    aboutXHours: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"близько {{count}} години\",\n            singularGenitive: \"близько {{count}} годин\",\n            pluralGenitive: \"близько {{count}} годин\"\n        },\n        future: {\n            singularNominative: \"приблизно за {{count}} годину\",\n            singularGenitive: \"приблизно за {{count}} години\",\n            pluralGenitive: \"приблизно за {{count}} годин\"\n        }\n    }),\n    xHours: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} годину\",\n            singularGenitive: \"{{count}} години\",\n            pluralGenitive: \"{{count}} годин\"\n        }\n    }),\n    xDays: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} день\",\n            singularGenitive: \"{{count}} днi\",\n            pluralGenitive: \"{{count}} днів\"\n        }\n    }),\n    aboutXWeeks: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"близько {{count}} тижня\",\n            singularGenitive: \"близько {{count}} тижнів\",\n            pluralGenitive: \"близько {{count}} тижнів\"\n        },\n        future: {\n            singularNominative: \"приблизно за {{count}} тиждень\",\n            singularGenitive: \"приблизно за {{count}} тижні\",\n            pluralGenitive: \"приблизно за {{count}} тижнів\"\n        }\n    }),\n    xWeeks: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} тиждень\",\n            singularGenitive: \"{{count}} тижні\",\n            pluralGenitive: \"{{count}} тижнів\"\n        }\n    }),\n    aboutXMonths: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"близько {{count}} місяця\",\n            singularGenitive: \"близько {{count}} місяців\",\n            pluralGenitive: \"близько {{count}} місяців\"\n        },\n        future: {\n            singularNominative: \"приблизно за {{count}} місяць\",\n            singularGenitive: \"приблизно за {{count}} місяці\",\n            pluralGenitive: \"приблизно за {{count}} місяців\"\n        }\n    }),\n    xMonths: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} місяць\",\n            singularGenitive: \"{{count}} місяці\",\n            pluralGenitive: \"{{count}} місяців\"\n        }\n    }),\n    aboutXYears: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"близько {{count}} року\",\n            singularGenitive: \"близько {{count}} років\",\n            pluralGenitive: \"близько {{count}} років\"\n        },\n        future: {\n            singularNominative: \"приблизно за {{count}} рік\",\n            singularGenitive: \"приблизно за {{count}} роки\",\n            pluralGenitive: \"приблизно за {{count}} років\"\n        }\n    }),\n    xYears: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} рік\",\n            singularGenitive: \"{{count}} роки\",\n            pluralGenitive: \"{{count}} років\"\n        }\n    }),\n    overXYears: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"більше {{count}} року\",\n            singularGenitive: \"більше {{count}} років\",\n            pluralGenitive: \"більше {{count}} років\"\n        },\n        future: {\n            singularNominative: \"більше, ніж за {{count}} рік\",\n            singularGenitive: \"більше, ніж за {{count}} роки\",\n            pluralGenitive: \"більше, ніж за {{count}} років\"\n        }\n    }),\n    almostXYears: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"майже {{count}} рік\",\n            singularGenitive: \"майже {{count}} роки\",\n            pluralGenitive: \"майже {{count}} років\"\n        },\n        future: {\n            singularNominative: \"майже за {{count}} рік\",\n            singularGenitive: \"майже за {{count}} роки\",\n            pluralGenitive: \"майже за {{count}} років\"\n        }\n    })\n};\nconst formatDistance = (token, count, options)=>{\n    options = options || {};\n    return formatDistanceLocale[token](count, options);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/uk/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/uk/_lib/formatLong.js":
/*!************************************************************!*\
  !*** ./node_modules/date-fns/locale/uk/_lib/formatLong.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\nconst dateFormats = {\n    full: \"EEEE, do MMMM y 'р.'\",\n    long: \"do MMMM y 'р.'\",\n    medium: \"d MMM y 'р.'\",\n    short: \"dd.MM.y\"\n};\nconst timeFormats = {\n    full: \"H:mm:ss zzzz\",\n    long: \"H:mm:ss z\",\n    medium: \"H:mm:ss\",\n    short: \"H:mm\"\n};\nconst dateTimeFormats = {\n    full: \"{{date}} 'о' {{time}}\",\n    long: \"{{date}} 'о' {{time}}\",\n    medium: \"{{date}}, {{time}}\",\n    short: \"{{date}}, {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"full\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"full\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/uk/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/uk/_lib/formatRelative.js":
/*!****************************************************************!*\
  !*** ./node_modules/date-fns/locale/uk/_lib/formatRelative.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\n/* harmony import */ var _isSameWeek_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../isSameWeek.js */ \"(app-pages-browser)/./node_modules/date-fns/isSameWeek.js\");\n/* harmony import */ var _toDate_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../toDate.js */ \"(app-pages-browser)/./node_modules/date-fns/toDate.js\");\n\n\nconst accusativeWeekdays = [\n    \"неділю\",\n    \"понеділок\",\n    \"вівторок\",\n    \"середу\",\n    \"четвер\",\n    \"п’ятницю\",\n    \"суботу\"\n];\nfunction lastWeek(day) {\n    const weekday = accusativeWeekdays[day];\n    switch(day){\n        case 0:\n        case 3:\n        case 5:\n        case 6:\n            return \"'у минулу \" + weekday + \" о' p\";\n        case 1:\n        case 2:\n        case 4:\n            return \"'у минулий \" + weekday + \" о' p\";\n    }\n}\nfunction thisWeek(day) {\n    const weekday = accusativeWeekdays[day];\n    return \"'у \" + weekday + \" о' p\";\n}\nfunction nextWeek(day) {\n    const weekday = accusativeWeekdays[day];\n    switch(day){\n        case 0:\n        case 3:\n        case 5:\n        case 6:\n            return \"'у наступну \" + weekday + \" о' p\";\n        case 1:\n        case 2:\n        case 4:\n            return \"'у наступний \" + weekday + \" о' p\";\n    }\n}\nconst lastWeekFormat = (dirtyDate, baseDate, options)=>{\n    const date = (0,_toDate_js__WEBPACK_IMPORTED_MODULE_0__.toDate)(dirtyDate);\n    const day = date.getDay();\n    if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_1__.isSameWeek)(date, baseDate, options)) {\n        return thisWeek(day);\n    } else {\n        return lastWeek(day);\n    }\n};\nconst nextWeekFormat = (dirtyDate, baseDate, options)=>{\n    const date = (0,_toDate_js__WEBPACK_IMPORTED_MODULE_0__.toDate)(dirtyDate);\n    const day = date.getDay();\n    if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_1__.isSameWeek)(date, baseDate, options)) {\n        return thisWeek(day);\n    } else {\n        return nextWeek(day);\n    }\n};\nconst formatRelativeLocale = {\n    lastWeek: lastWeekFormat,\n    yesterday: \"'вчора о' p\",\n    today: \"'сьогодні о' p\",\n    tomorrow: \"'завтра о' p\",\n    nextWeek: nextWeekFormat,\n    other: \"P\"\n};\nconst formatRelative = (token, date, baseDate, options)=>{\n    const format = formatRelativeLocale[token];\n    if (typeof format === \"function\") {\n        return format(date, baseDate, options);\n    }\n    return format;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/uk/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/uk/_lib/localize.js":
/*!**********************************************************!*\
  !*** ./node_modules/date-fns/locale/uk/_lib/localize.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\nconst eraValues = {\n    narrow: [\n        \"до н.е.\",\n        \"н.е.\"\n    ],\n    abbreviated: [\n        \"до н. е.\",\n        \"н. е.\"\n    ],\n    wide: [\n        \"до нашої ери\",\n        \"нашої ери\"\n    ]\n};\nconst quarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"1-й кв.\",\n        \"2-й кв.\",\n        \"3-й кв.\",\n        \"4-й кв.\"\n    ],\n    wide: [\n        \"1-й квартал\",\n        \"2-й квартал\",\n        \"3-й квартал\",\n        \"4-й квартал\"\n    ]\n};\nconst monthValues = {\n    // ДСТУ 3582:2013\n    narrow: [\n        \"С\",\n        \"Л\",\n        \"Б\",\n        \"К\",\n        \"Т\",\n        \"Ч\",\n        \"Л\",\n        \"С\",\n        \"В\",\n        \"Ж\",\n        \"Л\",\n        \"Г\"\n    ],\n    abbreviated: [\n        \"січ.\",\n        \"лют.\",\n        \"берез.\",\n        \"квіт.\",\n        \"трав.\",\n        \"черв.\",\n        \"лип.\",\n        \"серп.\",\n        \"верес.\",\n        \"жовт.\",\n        \"листоп.\",\n        \"груд.\"\n    ],\n    wide: [\n        \"січень\",\n        \"лютий\",\n        \"березень\",\n        \"квітень\",\n        \"травень\",\n        \"червень\",\n        \"липень\",\n        \"серпень\",\n        \"вересень\",\n        \"жовтень\",\n        \"листопад\",\n        \"грудень\"\n    ]\n};\nconst formattingMonthValues = {\n    narrow: [\n        \"С\",\n        \"Л\",\n        \"Б\",\n        \"К\",\n        \"Т\",\n        \"Ч\",\n        \"Л\",\n        \"С\",\n        \"В\",\n        \"Ж\",\n        \"Л\",\n        \"Г\"\n    ],\n    abbreviated: [\n        \"січ.\",\n        \"лют.\",\n        \"берез.\",\n        \"квіт.\",\n        \"трав.\",\n        \"черв.\",\n        \"лип.\",\n        \"серп.\",\n        \"верес.\",\n        \"жовт.\",\n        \"листоп.\",\n        \"груд.\"\n    ],\n    wide: [\n        \"січня\",\n        \"лютого\",\n        \"березня\",\n        \"квітня\",\n        \"травня\",\n        \"червня\",\n        \"липня\",\n        \"серпня\",\n        \"вересня\",\n        \"жовтня\",\n        \"листопада\",\n        \"грудня\"\n    ]\n};\nconst dayValues = {\n    narrow: [\n        \"Н\",\n        \"П\",\n        \"В\",\n        \"С\",\n        \"Ч\",\n        \"П\",\n        \"С\"\n    ],\n    short: [\n        \"нд\",\n        \"пн\",\n        \"вт\",\n        \"ср\",\n        \"чт\",\n        \"пт\",\n        \"сб\"\n    ],\n    abbreviated: [\n        \"нед\",\n        \"пон\",\n        \"вів\",\n        \"сер\",\n        \"чтв\",\n        \"птн\",\n        \"суб\"\n    ],\n    wide: [\n        \"неділя\",\n        \"понеділок\",\n        \"вівторок\",\n        \"середа\",\n        \"четвер\",\n        \"п’ятниця\",\n        \"субота\"\n    ]\n};\nconst dayPeriodValues = {\n    narrow: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"півн.\",\n        noon: \"пол.\",\n        morning: \"ранок\",\n        afternoon: \"день\",\n        evening: \"веч.\",\n        night: \"ніч\"\n    },\n    abbreviated: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"півн.\",\n        noon: \"пол.\",\n        morning: \"ранок\",\n        afternoon: \"день\",\n        evening: \"веч.\",\n        night: \"ніч\"\n    },\n    wide: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"північ\",\n        noon: \"полудень\",\n        morning: \"ранок\",\n        afternoon: \"день\",\n        evening: \"вечір\",\n        night: \"ніч\"\n    }\n};\nconst formattingDayPeriodValues = {\n    narrow: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"півн.\",\n        noon: \"пол.\",\n        morning: \"ранку\",\n        afternoon: \"дня\",\n        evening: \"веч.\",\n        night: \"ночі\"\n    },\n    abbreviated: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"півн.\",\n        noon: \"пол.\",\n        morning: \"ранку\",\n        afternoon: \"дня\",\n        evening: \"веч.\",\n        night: \"ночі\"\n    },\n    wide: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"північ\",\n        noon: \"полудень\",\n        morning: \"ранку\",\n        afternoon: \"дня\",\n        evening: \"веч.\",\n        night: \"ночі\"\n    }\n};\nconst ordinalNumber = (dirtyNumber, options)=>{\n    const unit = String(options === null || options === void 0 ? void 0 : options.unit);\n    const number = Number(dirtyNumber);\n    let suffix;\n    if (unit === \"date\") {\n        if (number === 3 || number === 23) {\n            suffix = \"-є\";\n        } else {\n            suffix = \"-е\";\n        }\n    } else if (unit === \"minute\" || unit === \"second\" || unit === \"hour\") {\n        suffix = \"-а\";\n    } else {\n        suffix = \"-й\";\n    }\n    return number + suffix;\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        defaultWidth: \"wide\",\n        formattingValues: formattingMonthValues,\n        defaultFormattingWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"any\",\n        formattingValues: formattingDayPeriodValues,\n        defaultFormattingWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/uk/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/uk/_lib/match.js":
/*!*******************************************************!*\
  !*** ./node_modules/date-fns/locale/uk/_lib/match.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^(\\d+)(-?(е|й|є|а|я))?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^((до )?н\\.?\\s?е\\.?)/i,\n    abbreviated: /^((до )?н\\.?\\s?е\\.?)/i,\n    wide: /^(до нашої ери|нашої ери|наша ера)/i\n};\nconst parseEraPatterns = {\n    any: [\n        /^д/i,\n        /^н/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]/i,\n    abbreviated: /^[1234](-?[иі]?й?)? кв.?/i,\n    wide: /^[1234](-?[иі]?й?)? квартал/i\n};\nconst parseQuarterPatterns = {\n    any: [\n        /1/i,\n        /2/i,\n        /3/i,\n        /4/i\n    ]\n};\nconst matchMonthPatterns = {\n    narrow: /^[слбктчвжг]/i,\n    abbreviated: /^(січ|лют|бер(ез)?|квіт|трав|черв|лип|серп|вер(ес)?|жовт|лис(топ)?|груд)\\.?/i,\n    wide: /^(січень|січня|лютий|лютого|березень|березня|квітень|квітня|травень|травня|червня|червень|липень|липня|серпень|серпня|вересень|вересня|жовтень|жовтня|листопад[а]?|грудень|грудня)/i\n};\nconst parseMonthPatterns = {\n    narrow: [\n        /^с/i,\n        /^л/i,\n        /^б/i,\n        /^к/i,\n        /^т/i,\n        /^ч/i,\n        /^л/i,\n        /^с/i,\n        /^в/i,\n        /^ж/i,\n        /^л/i,\n        /^г/i\n    ],\n    any: [\n        /^сі/i,\n        /^лю/i,\n        /^б/i,\n        /^к/i,\n        /^т/i,\n        /^ч/i,\n        /^лип/i,\n        /^се/i,\n        /^в/i,\n        /^ж/i,\n        /^лис/i,\n        /^г/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^[нпвсч]/i,\n    short: /^(нд|пн|вт|ср|чт|пт|сб)\\.?/i,\n    abbreviated: /^(нед|пон|вів|сер|че?тв|птн?|суб)\\.?/i,\n    wide: /^(неділ[яі]|понеділ[ок][ка]|вівтор[ок][ка]|серед[аи]|четвер(га)?|п\\W*?ятниц[яі]|субот[аи])/i\n};\nconst parseDayPatterns = {\n    narrow: [\n        /^н/i,\n        /^п/i,\n        /^в/i,\n        /^с/i,\n        /^ч/i,\n        /^п/i,\n        /^с/i\n    ],\n    any: [\n        /^н/i,\n        /^п[он]/i,\n        /^в/i,\n        /^с[ер]/i,\n        /^ч/i,\n        /^п\\W*?[ят]/i,\n        /^с[уб]/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    narrow: /^([дп]п|півн\\.?|пол\\.?|ранок|ранку|день|дня|веч\\.?|ніч|ночі)/i,\n    abbreviated: /^([дп]п|півн\\.?|пол\\.?|ранок|ранку|день|дня|веч\\.?|ніч|ночі)/i,\n    wide: /^([дп]п|північ|полудень|ранок|ранку|день|дня|вечір|вечора|ніч|ночі)/i\n};\nconst parseDayPeriodPatterns = {\n    any: {\n        am: /^дп/i,\n        pm: /^пп/i,\n        midnight: /^півн/i,\n        noon: /^пол/i,\n        morning: /^р/i,\n        afternoon: /^д[ен]/i,\n        evening: /^в/i,\n        night: /^н/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value, 10)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/uk/_lib/match.js\n"));

/***/ })

}]);