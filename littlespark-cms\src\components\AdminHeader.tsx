'use client'

import React from 'react'

const AdminHeader: React.FC = () => {
  const handleSyncClick = () => {
    window.location.href = '/admin/sync'
  }

  return (
    <div style={{
      position: 'fixed',
      top: '0',
      right: '20px',
      zIndex: 9999,
      padding: '10px 0'
    }}>
      <button
        type="button"
        onClick={handleSyncClick}
        style={{
          backgroundColor: '#0070f3',
          color: 'white',
          border: 'none',
          padding: '8px 16px',
          borderRadius: '6px',
          fontSize: '14px',
          fontWeight: '500',
          cursor: 'pointer',
          boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
          display: 'flex',
          alignItems: 'center',
          gap: '6px',
          transition: 'all 0.2s ease'
        }}
        onMouseOver={(e) => {
          e.currentTarget.style.backgroundColor = '#0056b3'
          e.currentTarget.style.transform = 'translateY(-1px)'
        }}
        onMouseOut={(e) => {
          e.currentTarget.style.backgroundColor = '#0070f3'
          e.currentTarget.style.transform = 'translateY(0)'
        }}
      >
        <span>🔄</span>
        <span>Sync Panel</span>
      </button>
    </div>
  )
}

export default AdminHeader
