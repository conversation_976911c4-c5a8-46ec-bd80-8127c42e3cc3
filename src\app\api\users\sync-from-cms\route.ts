import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// Helper function to verify sync token
function verifySyncToken(req: NextRequest) {
  const authHeader = req.headers.get('authorization');
  const expectedToken = process.env.CMS_SYNC_TOKEN;
  
  if (!authHeader || !authHeader.startsWith('Bearer ') || !expectedToken) {
    return false;
  }
  
  const token = authHeader.substring(7);
  return token === expectedToken;
}

// POST /api/users/sync-from-cms - Receive synced user from CMS
export async function POST(request: NextRequest) {
  try {
    // Verify sync token
    if (!verifySyncToken(request)) {
      return NextResponse.json(
        { error: 'Unauthorized sync request' },
        { status: 401 }
      );
    }

    const userData = await request.json();
    
    console.log(`🔄 [MAIN-APP-SYNC] Receiving user from CMS: ${userData.email}`);

    // Validate required fields
    if (!userData.email || !userData.full_name) {
      return NextResponse.json(
        { error: 'Missing required fields: email, full_name' },
        { status: 400 }
      );
    }

    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email: userData.email }
    });

    if (existingUser) {
      // Update existing user profile
      const existingProfile = await prisma.profile.findUnique({
        where: { user_id: existingUser.id }
      });

      if (existingProfile) {
        const updatedProfile = await prisma.profile.update({
          where: { user_id: existingUser.id },
          data: {
            full_name: userData.full_name,
            bio: userData.bio,
            cms_user_id: userData.cms_user_id,
            is_cms_user: userData.is_cms_user,
            cms_role: userData.role,
            cms_specialties: userData.specialties,
            updated_at: new Date(),
          }
        });
        
        console.log(`✅ [MAIN-APP-SYNC] Updated user profile: ${userData.email}`);
        
        return NextResponse.json({
          success: true,
          action: 'updated',
          user: existingUser,
          profile: updatedProfile
        });
      }
    }

    // Create new user and profile
    const newUser = await prisma.user.create({
      data: {
        email: userData.email,
        email_verified: new Date(), // CMS users are pre-verified
      }
    });

    const newProfile = await prisma.profile.create({
      data: {
        user_id: newUser.id,
        email: userData.email,
        full_name: userData.full_name,
        bio: userData.bio,
        cms_user_id: userData.cms_user_id,
        is_cms_user: userData.is_cms_user,
        cms_role: userData.role,
        cms_specialties: userData.specialties,
        subscription_status: 'active', // CMS users get active status
        trial_used: true, // Skip trial for CMS users
      }
    });
    
    console.log(`🆕 [MAIN-APP-SYNC] Created user: ${userData.email}`);
    
    return NextResponse.json({
      success: true,
      action: 'created',
      user: newUser,
      profile: newProfile
    });

  } catch (error) {
    console.error('❌ [MAIN-APP-SYNC] Error syncing user from CMS:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to sync user from CMS',
        details: error.message
      },
      { status: 500 }
    );
  }
}
