import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

/**
 * Admin API to sync subscription statuses for accounts with payment/status mismatches
 * This fixes accounts that have successful payments but are still showing 'trialing' status
 */
export async function POST(request: NextRequest) {
  try {
    // Find all accounts with trialing status that have expired trials and successful payments
    const problematicAccounts = await prisma.profile.findMany({
      where: {
        subscription_status: 'trialing',
        trial_end: {
          lt: new Date() // Trial has expired
        }
      },
      include: {
        payments: {
          where: {
            status: 'succeeded',
            created_at: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Within last 30 days
            }
          },
          orderBy: {
            created_at: 'desc'
          }
        }
      }
    });

    const syncResults = [];

    for (const account of problematicAccounts) {
      if (account.payments.length > 0) {
        // Account has successful payments but is still trialing - fix it
        const latestPayment = account.payments[0];
        
        console.log(`Syncing account ${account.email}: trialing -> active (has ${account.payments.length} successful payments)`);
        
        // const updatedProfile = await prisma.profile.update({
        //   where: { id: account.id },
        //   data: {
        //     subscription_status: 'active',
        //     trial_used: true,
        //     updated_at: new Date()
        //   }
        // });

        syncResults.push({
          email: account.email,
          previousStatus: 'trialing',
          newStatus: 'active',
          paymentCount: account.payments.length,
          latestPaymentDate: latestPayment.payment_date,
          latestPaymentAmount: latestPayment.amount,
          trialEnd: account.trial_end,
          synced: true
        });
      } else {
        // Account has no successful payments - should remain trialing or be marked as expired
        syncResults.push({
          email: account.email,
          previousStatus: 'trialing',
          newStatus: 'trialing',
          paymentCount: 0,
          trialEnd: account.trial_end,
          synced: false,
          reason: 'No successful payments found'
        });
      }
    }

    // Also check for accounts that might need other status updates
    const incompleteAccounts = await prisma.profile.findMany({
      where: {
        subscription_status: 'incomplete'
      },
      include: {
        payments: {
          where: {
            status: 'succeeded',
            created_at: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Within last 30 days
            }
          },
          orderBy: {
            created_at: 'desc'
          }
        }
      }
    });

    for (const account of incompleteAccounts) {
      if (account.payments.length > 0) {
        // Account has successful payments but is marked incomplete - fix it
        const latestPayment = account.payments[0];
        
        console.log(`Syncing account ${account.email}: incomplete -> active (has ${account.payments.length} successful payments)`);
        
        await prisma.profile.update({
          where: { id: account.id },
          data: {
            subscription_status: 'active',
            updated_at: new Date()
          }
        });

        syncResults.push({
          email: account.email,
          previousStatus: 'incomplete',
          newStatus: 'active',
          paymentCount: account.payments.length,
          latestPaymentDate: latestPayment.payment_date,
          latestPaymentAmount: latestPayment.amount,
          synced: true
        });
      }
    }

    return NextResponse.json({
      success: true,
      message: `Synced ${syncResults.filter(r => r.synced).length} accounts`,
      totalAccountsChecked: problematicAccounts.length + incompleteAccounts.length,
      accountsFixed: syncResults.filter(r => r.synced).length,
      results: syncResults
    });

  } catch (error) {
    console.error('Error syncing subscription statuses:', error);
    return NextResponse.json(
      { error: 'Failed to sync subscription statuses', details: error.message },
      { status: 500 }
    );
  }
}

/**
 * GET endpoint to check which accounts need syncing without making changes
 */
export async function GET() {
  try {
    // Find accounts that need syncing
    const trialingWithPayments = await prisma.profile.findMany({
      where: {
        subscription_status: 'trialing',
        trial_end: {
          lt: new Date()
        }
      },
      include: {
        payments: {
          where: {
            status: 'succeeded',
            created_at: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            }
          }
        }
      }
    });

    const incompleteWithPayments = await prisma.profile.findMany({
      where: {
        subscription_status: 'incomplete'
      },
      include: {
        payments: {
          where: {
            status: 'succeeded',
            created_at: {
              gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            }
          }
        }
      }
    });

    const needsSync = [
      ...trialingWithPayments.filter(account => account.payments.length > 0),
      ...incompleteWithPayments.filter(account => account.payments.length > 0)
    ];

    return NextResponse.json({
      accountsNeedingSync: needsSync.length,
      trialingWithPayments: trialingWithPayments.filter(a => a.payments.length > 0).length,
      incompleteWithPayments: incompleteWithPayments.filter(a => a.payments.length > 0).length,
      accounts: needsSync.map(account => ({
        email: account.email,
        currentStatus: account.subscription_status,
        trialEnd: account.trial_end,
        paymentCount: account.payments.length,
        latestPayment: account.payments[0]?.payment_date
      }))
    });

  } catch (error) {
    console.error('Error checking sync status:', error);
    return NextResponse.json(
      { error: 'Failed to check sync status', details: error.message },
      { status: 500 }
    );
  }
}
