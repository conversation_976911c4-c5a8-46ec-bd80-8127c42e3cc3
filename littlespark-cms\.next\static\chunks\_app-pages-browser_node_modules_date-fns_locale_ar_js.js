"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_date-fns_locale_ar_js"],{

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/ar.js":
/*!********************************************!*\
  !*** ./node_modules/date-fns/locale/ar.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ar: () => (/* binding */ ar),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ar_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ar/_lib/formatDistance.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/ar/_lib/formatDistance.js\");\n/* harmony import */ var _ar_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ar/_lib/formatLong.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/ar/_lib/formatLong.js\");\n/* harmony import */ var _ar_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ar/_lib/formatRelative.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/ar/_lib/formatRelative.js\");\n/* harmony import */ var _ar_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ar/_lib/localize.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/ar/_lib/localize.js\");\n/* harmony import */ var _ar_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ar/_lib/match.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/ar/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary Arabic locale (Modern Standard Arabic - Al-fussha).\n * @language Modern Standard Arabic\n * @iso-639-2 ara\n * <AUTHOR> Hassan [@AbdallahAHO](https://github.com/AbdallahAHO)\n * <AUTHOR> Haj Kacem [@essana3](https://github.com/essana3)\n */ const ar = {\n    code: \"ar\",\n    formatDistance: _ar_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _ar_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _ar_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _ar_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _ar_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 6 /* Saturday */ ,\n        firstWeekContainsDate: 1\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9kYXRlLWZucy9sb2NhbGUvYXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE2RDtBQUNSO0FBQ1E7QUFDWjtBQUNOO0FBRTNDOzs7Ozs7O0NBT0MsR0FDTSxNQUFNSyxLQUFLO0lBQ2hCQyxNQUFNO0lBQ05OLGdCQUFnQkEscUVBQWNBO0lBQzlCQyxZQUFZQSw2REFBVUE7SUFDdEJDLGdCQUFnQkEscUVBQWNBO0lBQzlCQyxVQUFVQSx5REFBUUE7SUFDbEJDLE9BQU9BLG1EQUFLQTtJQUNaRyxTQUFTO1FBQ1BDLGNBQWMsRUFBRSxZQUFZO1FBQzVCQyx1QkFBdUI7SUFDekI7QUFDRixFQUFFO0FBRUYsb0NBQW9DO0FBQ3BDLGlFQUFlSixFQUFFQSxFQUFDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJhdmkxXFxrYXZ5YS1naXRcXHNwYXJrLW5ld1xcbGl0dGxlc3BhcmstY21zXFxub2RlX21vZHVsZXNcXGRhdGUtZm5zXFxsb2NhbGVcXGFyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGZvcm1hdERpc3RhbmNlIH0gZnJvbSBcIi4vYXIvX2xpYi9mb3JtYXREaXN0YW5jZS5qc1wiO1xuaW1wb3J0IHsgZm9ybWF0TG9uZyB9IGZyb20gXCIuL2FyL19saWIvZm9ybWF0TG9uZy5qc1wiO1xuaW1wb3J0IHsgZm9ybWF0UmVsYXRpdmUgfSBmcm9tIFwiLi9hci9fbGliL2Zvcm1hdFJlbGF0aXZlLmpzXCI7XG5pbXBvcnQgeyBsb2NhbGl6ZSB9IGZyb20gXCIuL2FyL19saWIvbG9jYWxpemUuanNcIjtcbmltcG9ydCB7IG1hdGNoIH0gZnJvbSBcIi4vYXIvX2xpYi9tYXRjaC5qc1wiO1xuXG4vKipcbiAqIEBjYXRlZ29yeSBMb2NhbGVzXG4gKiBAc3VtbWFyeSBBcmFiaWMgbG9jYWxlIChNb2Rlcm4gU3RhbmRhcmQgQXJhYmljIC0gQWwtZnVzc2hhKS5cbiAqIEBsYW5ndWFnZSBNb2Rlcm4gU3RhbmRhcmQgQXJhYmljXG4gKiBAaXNvLTYzOS0yIGFyYVxuICogQGF1dGhvciBBYmRhbGxhaCBIYXNzYW4gW0BBYmRhbGxhaEFIT10oaHR0cHM6Ly9naXRodWIuY29tL0FiZGFsbGFoQUhPKVxuICogQGF1dGhvciBLb3Vzc2F5IEhhaiBLYWNlbSBbQGVzc2FuYTNdKGh0dHBzOi8vZ2l0aHViLmNvbS9lc3NhbmEzKVxuICovXG5leHBvcnQgY29uc3QgYXIgPSB7XG4gIGNvZGU6IFwiYXJcIixcbiAgZm9ybWF0RGlzdGFuY2U6IGZvcm1hdERpc3RhbmNlLFxuICBmb3JtYXRMb25nOiBmb3JtYXRMb25nLFxuICBmb3JtYXRSZWxhdGl2ZTogZm9ybWF0UmVsYXRpdmUsXG4gIGxvY2FsaXplOiBsb2NhbGl6ZSxcbiAgbWF0Y2g6IG1hdGNoLFxuICBvcHRpb25zOiB7XG4gICAgd2Vla1N0YXJ0c09uOiA2IC8qIFNhdHVyZGF5ICovLFxuICAgIGZpcnN0V2Vla0NvbnRhaW5zRGF0ZTogMSxcbiAgfSxcbn07XG5cbi8vIEZhbGxiYWNrIGZvciBtb2R1bGFyaXplZCBpbXBvcnRzOlxuZXhwb3J0IGRlZmF1bHQgYXI7XG4iXSwibmFtZXMiOlsiZm9ybWF0RGlzdGFuY2UiLCJmb3JtYXRMb25nIiwiZm9ybWF0UmVsYXRpdmUiLCJsb2NhbGl6ZSIsIm1hdGNoIiwiYXIiLCJjb2RlIiwib3B0aW9ucyIsIndlZWtTdGFydHNPbiIsImZpcnN0V2Vla0NvbnRhaW5zRGF0ZSJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/ar.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/ar/_lib/formatDistance.js":
/*!****************************************************************!*\
  !*** ./node_modules/date-fns/locale/ar/_lib/formatDistance.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\nconst formatDistanceLocale = {\n    lessThanXSeconds: {\n        one: \"أقل من ثانية\",\n        two: \"أقل من ثانيتين\",\n        threeToTen: \"أقل من {{count}} ثواني\",\n        other: \"أقل من {{count}} ثانية\"\n    },\n    xSeconds: {\n        one: \"ثانية واحدة\",\n        two: \"ثانيتان\",\n        threeToTen: \"{{count}} ثواني\",\n        other: \"{{count}} ثانية\"\n    },\n    halfAMinute: \"نصف دقيقة\",\n    lessThanXMinutes: {\n        one: \"أقل من دقيقة\",\n        two: \"أقل من دقيقتين\",\n        threeToTen: \"أقل من {{count}} دقائق\",\n        other: \"أقل من {{count}} دقيقة\"\n    },\n    xMinutes: {\n        one: \"دقيقة واحدة\",\n        two: \"دقيقتان\",\n        threeToTen: \"{{count}} دقائق\",\n        other: \"{{count}} دقيقة\"\n    },\n    aboutXHours: {\n        one: \"ساعة واحدة تقريباً\",\n        two: \"ساعتين تقريبا\",\n        threeToTen: \"{{count}} ساعات تقريباً\",\n        other: \"{{count}} ساعة تقريباً\"\n    },\n    xHours: {\n        one: \"ساعة واحدة\",\n        two: \"ساعتان\",\n        threeToTen: \"{{count}} ساعات\",\n        other: \"{{count}} ساعة\"\n    },\n    xDays: {\n        one: \"يوم واحد\",\n        two: \"يومان\",\n        threeToTen: \"{{count}} أيام\",\n        other: \"{{count}} يوم\"\n    },\n    aboutXWeeks: {\n        one: \"أسبوع واحد تقريبا\",\n        two: \"أسبوعين تقريبا\",\n        threeToTen: \"{{count}} أسابيع تقريبا\",\n        other: \"{{count}} أسبوعا تقريبا\"\n    },\n    xWeeks: {\n        one: \"أسبوع واحد\",\n        two: \"أسبوعان\",\n        threeToTen: \"{{count}} أسابيع\",\n        other: \"{{count}} أسبوعا\"\n    },\n    aboutXMonths: {\n        one: \"شهر واحد تقريباً\",\n        two: \"شهرين تقريبا\",\n        threeToTen: \"{{count}} أشهر تقريبا\",\n        other: \"{{count}} شهرا تقريباً\"\n    },\n    xMonths: {\n        one: \"شهر واحد\",\n        two: \"شهران\",\n        threeToTen: \"{{count}} أشهر\",\n        other: \"{{count}} شهرا\"\n    },\n    aboutXYears: {\n        one: \"سنة واحدة تقريباً\",\n        two: \"سنتين تقريبا\",\n        threeToTen: \"{{count}} سنوات تقريباً\",\n        other: \"{{count}} سنة تقريباً\"\n    },\n    xYears: {\n        one: \"سنة واحد\",\n        two: \"سنتان\",\n        threeToTen: \"{{count}} سنوات\",\n        other: \"{{count}} سنة\"\n    },\n    overXYears: {\n        one: \"أكثر من سنة\",\n        two: \"أكثر من سنتين\",\n        threeToTen: \"أكثر من {{count}} سنوات\",\n        other: \"أكثر من {{count}} سنة\"\n    },\n    almostXYears: {\n        one: \"ما يقارب سنة واحدة\",\n        two: \"ما يقارب سنتين\",\n        threeToTen: \"ما يقارب {{count}} سنوات\",\n        other: \"ما يقارب {{count}} سنة\"\n    }\n};\nconst formatDistance = (token, count, options)=>{\n    const usageGroup = formatDistanceLocale[token];\n    let result;\n    if (typeof usageGroup === \"string\") {\n        result = usageGroup;\n    } else if (count === 1) {\n        result = usageGroup.one;\n    } else if (count === 2) {\n        result = usageGroup.two;\n    } else if (count <= 10) {\n        result = usageGroup.threeToTen.replace(\"{{count}}\", String(count));\n    } else {\n        result = usageGroup.other.replace(\"{{count}}\", String(count));\n    }\n    if (options === null || options === void 0 ? void 0 : options.addSuffix) {\n        if (options.comparison && options.comparison > 0) {\n            return \"خلال \" + result;\n        } else {\n            return \"منذ \" + result;\n        }\n    }\n    return result;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/ar/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/ar/_lib/formatLong.js":
/*!************************************************************!*\
  !*** ./node_modules/date-fns/locale/ar/_lib/formatLong.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\nconst dateFormats = {\n    full: \"EEEE، do MMMM y\",\n    long: \"do MMMM y\",\n    medium: \"d MMM y\",\n    short: \"dd/MM/yyyy\"\n};\nconst timeFormats = {\n    full: \"HH:mm:ss\",\n    long: \"HH:mm:ss\",\n    medium: \"HH:mm:ss\",\n    short: \"HH:mm\"\n};\nconst dateTimeFormats = {\n    full: \"{{date}} 'عند الساعة' {{time}}\",\n    long: \"{{date}} 'عند الساعة' {{time}}\",\n    medium: \"{{date}}, {{time}}\",\n    short: \"{{date}}, {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"full\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"full\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/ar/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/ar/_lib/formatRelative.js":
/*!****************************************************************!*\
  !*** ./node_modules/date-fns/locale/ar/_lib/formatRelative.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\nconst formatRelativeLocale = {\n    lastWeek: \"eeee 'الماضي عند الساعة' p\",\n    yesterday: \"'الأمس عند الساعة' p\",\n    today: \"'اليوم عند الساعة' p\",\n    tomorrow: \"'غدا عند الساعة' p\",\n    nextWeek: \"eeee 'القادم عند الساعة' p\",\n    other: \"P\"\n};\nconst formatRelative = (token)=>formatRelativeLocale[token];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9kYXRlLWZucy9sb2NhbGUvYXIvX2xpYi9mb3JtYXRSZWxhdGl2ZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsTUFBTUEsdUJBQXVCO0lBQzNCQyxVQUFVO0lBQ1ZDLFdBQVc7SUFDWEMsT0FBTztJQUNQQyxVQUFVO0lBQ1ZDLFVBQVU7SUFDVkMsT0FBTztBQUNUO0FBRU8sTUFBTUMsaUJBQWlCLENBQUNDLFFBQVVSLG9CQUFvQixDQUFDUSxNQUFNLENBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmF2aTFcXGthdnlhLWdpdFxcc3BhcmstbmV3XFxsaXR0bGVzcGFyay1jbXNcXG5vZGVfbW9kdWxlc1xcZGF0ZS1mbnNcXGxvY2FsZVxcYXJcXF9saWJcXGZvcm1hdFJlbGF0aXZlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGZvcm1hdFJlbGF0aXZlTG9jYWxlID0ge1xuICBsYXN0V2VlazogXCJlZWVlICfYp9mE2YXYp9i22Yog2LnZhtivINin2YTYs9in2LnYqScgcFwiLFxuICB5ZXN0ZXJkYXk6IFwiJ9in2YTYo9mF2LMg2LnZhtivINin2YTYs9in2LnYqScgcFwiLFxuICB0b2RheTogXCIn2KfZhNmK2YjZhSDYudmG2K8g2KfZhNiz2KfYudipJyBwXCIsXG4gIHRvbW9ycm93OiBcIifYutiv2Kcg2LnZhtivINin2YTYs9in2LnYqScgcFwiLFxuICBuZXh0V2VlazogXCJlZWVlICfYp9mE2YLYp9iv2YUg2LnZhtivINin2YTYs9in2LnYqScgcFwiLFxuICBvdGhlcjogXCJQXCIsXG59O1xuXG5leHBvcnQgY29uc3QgZm9ybWF0UmVsYXRpdmUgPSAodG9rZW4pID0+IGZvcm1hdFJlbGF0aXZlTG9jYWxlW3Rva2VuXTtcbiJdLCJuYW1lcyI6WyJmb3JtYXRSZWxhdGl2ZUxvY2FsZSIsImxhc3RXZWVrIiwieWVzdGVyZGF5IiwidG9kYXkiLCJ0b21vcnJvdyIsIm5leHRXZWVrIiwib3RoZXIiLCJmb3JtYXRSZWxhdGl2ZSIsInRva2VuIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/ar/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/ar/_lib/localize.js":
/*!**********************************************************!*\
  !*** ./node_modules/date-fns/locale/ar/_lib/localize.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\nconst eraValues = {\n    narrow: [\n        \"ق\",\n        \"ب\"\n    ],\n    abbreviated: [\n        \"ق.م.\",\n        \"ب.م.\"\n    ],\n    wide: [\n        \"قبل الميلاد\",\n        \"بعد الميلاد\"\n    ]\n};\nconst quarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"ر1\",\n        \"ر2\",\n        \"ر3\",\n        \"ر4\"\n    ],\n    wide: [\n        \"الربع الأول\",\n        \"الربع الثاني\",\n        \"الربع الثالث\",\n        \"الربع الرابع\"\n    ]\n};\nconst monthValues = {\n    narrow: [\n        \"ي\",\n        \"ف\",\n        \"م\",\n        \"أ\",\n        \"م\",\n        \"ي\",\n        \"ي\",\n        \"أ\",\n        \"س\",\n        \"أ\",\n        \"ن\",\n        \"د\"\n    ],\n    abbreviated: [\n        \"يناير\",\n        \"فبراير\",\n        \"مارس\",\n        \"أبريل\",\n        \"مايو\",\n        \"يونيو\",\n        \"يوليو\",\n        \"أغسطس\",\n        \"سبتمبر\",\n        \"أكتوبر\",\n        \"نوفمبر\",\n        \"ديسمبر\"\n    ],\n    wide: [\n        \"يناير\",\n        \"فبراير\",\n        \"مارس\",\n        \"أبريل\",\n        \"مايو\",\n        \"يونيو\",\n        \"يوليو\",\n        \"أغسطس\",\n        \"سبتمبر\",\n        \"أكتوبر\",\n        \"نوفمبر\",\n        \"ديسمبر\"\n    ]\n};\nconst dayValues = {\n    narrow: [\n        \"ح\",\n        \"ن\",\n        \"ث\",\n        \"ر\",\n        \"خ\",\n        \"ج\",\n        \"س\"\n    ],\n    short: [\n        \"أحد\",\n        \"اثنين\",\n        \"ثلاثاء\",\n        \"أربعاء\",\n        \"خميس\",\n        \"جمعة\",\n        \"سبت\"\n    ],\n    abbreviated: [\n        \"أحد\",\n        \"اثنين\",\n        \"ثلاثاء\",\n        \"أربعاء\",\n        \"خميس\",\n        \"جمعة\",\n        \"سبت\"\n    ],\n    wide: [\n        \"الأحد\",\n        \"الاثنين\",\n        \"الثلاثاء\",\n        \"الأربعاء\",\n        \"الخميس\",\n        \"الجمعة\",\n        \"السبت\"\n    ]\n};\nconst dayPeriodValues = {\n    narrow: {\n        am: \"ص\",\n        pm: \"م\",\n        morning: \"الصباح\",\n        noon: \"الظهر\",\n        afternoon: \"بعد الظهر\",\n        evening: \"المساء\",\n        night: \"الليل\",\n        midnight: \"منتصف الليل\"\n    },\n    abbreviated: {\n        am: \"ص\",\n        pm: \"م\",\n        morning: \"الصباح\",\n        noon: \"الظهر\",\n        afternoon: \"بعد الظهر\",\n        evening: \"المساء\",\n        night: \"الليل\",\n        midnight: \"منتصف الليل\"\n    },\n    wide: {\n        am: \"ص\",\n        pm: \"م\",\n        morning: \"الصباح\",\n        noon: \"الظهر\",\n        afternoon: \"بعد الظهر\",\n        evening: \"المساء\",\n        night: \"الليل\",\n        midnight: \"منتصف الليل\"\n    }\n};\nconst formattingDayPeriodValues = {\n    narrow: {\n        am: \"ص\",\n        pm: \"م\",\n        morning: \"في الصباح\",\n        noon: \"الظهر\",\n        afternoon: \"بعد الظهر\",\n        evening: \"في المساء\",\n        night: \"في الليل\",\n        midnight: \"منتصف الليل\"\n    },\n    abbreviated: {\n        am: \"ص\",\n        pm: \"م\",\n        morning: \"في الصباح\",\n        noon: \"الظهر\",\n        afternoon: \"بعد الظهر\",\n        evening: \"في المساء\",\n        night: \"في الليل\",\n        midnight: \"منتصف الليل\"\n    },\n    wide: {\n        am: \"ص\",\n        pm: \"م\",\n        morning: \"في الصباح\",\n        noon: \"الظهر\",\n        afternoon: \"بعد الظهر\",\n        evening: \"في المساء\",\n        night: \"في الليل\",\n        midnight: \"منتصف الليل\"\n    }\n};\nconst ordinalNumber = (num)=>String(num);\nconst localize = {\n    ordinalNumber: ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        defaultWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"wide\",\n        formattingValues: formattingDayPeriodValues,\n        defaultFormattingWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/ar/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/ar/_lib/match.js":
/*!*******************************************************!*\
  !*** ./node_modules/date-fns/locale/ar/_lib/match.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^(\\d+)(th|st|nd|rd)?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /[قب]/,\n    abbreviated: /[قب]\\.م\\./,\n    wide: /(قبل|بعد) الميلاد/\n};\nconst parseEraPatterns = {\n    any: [\n        /قبل/,\n        /بعد/\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]/i,\n    abbreviated: /ر[1234]/,\n    wide: /الربع (الأول|الثاني|الثالث|الرابع)/\n};\nconst parseQuarterPatterns = {\n    any: [\n        /1/i,\n        /2/i,\n        /3/i,\n        /4/i\n    ]\n};\nconst matchMonthPatterns = {\n    narrow: /^[أيفمسند]/,\n    abbreviated: /^(يناير|فبراير|مارس|أبريل|مايو|يونيو|يوليو|أغسطس|سبتمبر|أكتوبر|نوفمبر|ديسمبر)/,\n    wide: /^(يناير|فبراير|مارس|أبريل|مايو|يونيو|يوليو|أغسطس|سبتمبر|أكتوبر|نوفمبر|ديسمبر)/\n};\nconst parseMonthPatterns = {\n    narrow: [\n        /^ي/i,\n        /^ف/i,\n        /^م/i,\n        /^أ/i,\n        /^م/i,\n        /^ي/i,\n        /^ي/i,\n        /^أ/i,\n        /^س/i,\n        /^أ/i,\n        /^ن/i,\n        /^د/i\n    ],\n    any: [\n        /^يناير/i,\n        /^فبراير/i,\n        /^مارس/i,\n        /^أبريل/i,\n        /^مايو/i,\n        /^يونيو/i,\n        /^يوليو/i,\n        /^أغسطس/i,\n        /^سبتمبر/i,\n        /^أكتوبر/i,\n        /^نوفمبر/i,\n        /^ديسمبر/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^[حنثرخجس]/i,\n    short: /^(أحد|اثنين|ثلاثاء|أربعاء|خميس|جمعة|سبت)/i,\n    abbreviated: /^(أحد|اثنين|ثلاثاء|أربعاء|خميس|جمعة|سبت)/i,\n    wide: /^(الأحد|الاثنين|الثلاثاء|الأربعاء|الخميس|الجمعة|السبت)/i\n};\nconst parseDayPatterns = {\n    narrow: [\n        /^ح/i,\n        /^ن/i,\n        /^ث/i,\n        /^ر/i,\n        /^خ/i,\n        /^ج/i,\n        /^س/i\n    ],\n    wide: [\n        /^الأحد/i,\n        /^الاثنين/i,\n        /^الثلاثاء/i,\n        /^الأربعاء/i,\n        /^الخميس/i,\n        /^الجمعة/i,\n        /^السبت/i\n    ],\n    any: [\n        /^أح/i,\n        /^اث/i,\n        /^ث/i,\n        /^أر/i,\n        /^خ/i,\n        /^ج/i,\n        /^س/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    narrow: /^(ص|م|منتصف الليل|الظهر|بعد الظهر|في الصباح|في المساء|في الليل)/,\n    any: /^(ص|م|منتصف الليل|الظهر|بعد الظهر|في الصباح|في المساء|في الليل)/\n};\nconst parseDayPeriodPatterns = {\n    any: {\n        am: /^ص/,\n        pm: /^م/,\n        midnight: /منتصف الليل/,\n        noon: /الظهر/,\n        afternoon: /بعد الظهر/,\n        morning: /في الصباح/,\n        evening: /في المساء/,\n        night: /في الليل/\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value, 10)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"any\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/ar/_lib/match.js\n"));

/***/ })

}]);