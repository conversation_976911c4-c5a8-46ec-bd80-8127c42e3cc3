"use strict";exports.id=7051,exports.ids=[7051],exports.modules={1039:(a,b,c)=>{c.d(b,{k:()=>d});function d(a){return (b={})=>{let c=b.width?String(b.width):a.defaultWidth;return a.formats[c]||a.formats[a.defaultWidth]}}},28153:(a,b,c)=>{function d(a){return(b,c={})=>{let d,e=c.width,f=e&&a.matchPatterns[e]||a.matchPatterns[a.defaultMatchWidth],g=b.match(f);if(!g)return null;let h=g[0],i=e&&a.parsePatterns[e]||a.parsePatterns[a.defaultParseWidth],j=Array.isArray(i)?function(a,b){for(let c=0;c<a.length;c++)if(b(a[c]))return c}(i,a=>a.test(h)):function(a,b){for(let c in a)if(Object.prototype.hasOwnProperty.call(a,c)&&b(a[c]))return c}(i,a=>a.test(h));return d=a.valueCallback?a.valueCallback(j):j,{value:d=c.valueCallback?c.valueCallback(d):d,rest:b.slice(h.length)}}}c.d(b,{A:()=>d})},55991:(a,b,c)=>{c.d(b,{K:()=>d});function d(a){return(b,c={})=>{let d=b.match(a.matchPattern);if(!d)return null;let e=d[0],f=b.match(a.parsePattern);if(!f)return null;let g=a.valueCallback?a.valueCallback(f[0]):f[0];return{value:g=c.valueCallback?c.valueCallback(g):g,rest:b.slice(e.length)}}}},80429:(a,b,c)=>{c.d(b,{o:()=>d});function d(a){return(b,c)=>{let d;if("formatting"===(c?.context?String(c.context):"standalone")&&a.formattingValues){let b=a.defaultFormattingWidth||a.defaultWidth,e=c?.width?String(c.width):b;d=a.formattingValues[e]||a.formattingValues[b]}else{let b=a.defaultWidth,e=c?.width?String(c.width):a.defaultWidth;d=a.values[e]||a.values[b]}return d[a.argumentCallback?a.argumentCallback(b):b]}}},97051:(a,b,c)=>{c.r(b),c.d(b,{default:()=>l,ko:()=>k});let d={lessThanXSeconds:{one:"1초 미만",other:"{{count}}초 미만"},xSeconds:{one:"1초",other:"{{count}}초"},halfAMinute:"30초",lessThanXMinutes:{one:"1분 미만",other:"{{count}}분 미만"},xMinutes:{one:"1분",other:"{{count}}분"},aboutXHours:{one:"약 1시간",other:"약 {{count}}시간"},xHours:{one:"1시간",other:"{{count}}시간"},xDays:{one:"1일",other:"{{count}}일"},aboutXWeeks:{one:"약 1주",other:"약 {{count}}주"},xWeeks:{one:"1주",other:"{{count}}주"},aboutXMonths:{one:"약 1개월",other:"약 {{count}}개월"},xMonths:{one:"1개월",other:"{{count}}개월"},aboutXYears:{one:"약 1년",other:"약 {{count}}년"},xYears:{one:"1년",other:"{{count}}년"},overXYears:{one:"1년 이상",other:"{{count}}년 이상"},almostXYears:{one:"거의 1년",other:"거의 {{count}}년"}};var e=c(1039);let f={date:(0,e.k)({formats:{full:"y년 M월 d일 EEEE",long:"y년 M월 d일",medium:"y.MM.dd",short:"y.MM.dd"},defaultWidth:"full"}),time:(0,e.k)({formats:{full:"a H시 mm분 ss초 zzzz",long:"a H:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},defaultWidth:"full"}),dateTime:(0,e.k)({formats:{full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},defaultWidth:"full"})},g={lastWeek:"'지난' eeee p",yesterday:"'어제' p",today:"'오늘' p",tomorrow:"'내일' p",nextWeek:"'다음' eeee p",other:"P"};var h=c(80429);let i={ordinalNumber:(a,b)=>{let c=Number(a);switch(String(b?.unit)){case"minute":case"second":return String(c);case"date":return c+"일";default:return c+"번째"}},era:(0,h.o)({values:{narrow:["BC","AD"],abbreviated:["BC","AD"],wide:["기원전","서기"]},defaultWidth:"wide"}),quarter:(0,h.o)({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1분기","2분기","3분기","4분기"]},defaultWidth:"wide",argumentCallback:a=>a-1}),month:(0,h.o)({values:{narrow:["1","2","3","4","5","6","7","8","9","10","11","12"],abbreviated:["1월","2월","3월","4월","5월","6월","7월","8월","9월","10월","11월","12월"],wide:["1월","2월","3월","4월","5월","6월","7월","8월","9월","10월","11월","12월"]},defaultWidth:"wide"}),day:(0,h.o)({values:{narrow:["일","월","화","수","목","금","토"],short:["일","월","화","수","목","금","토"],abbreviated:["일","월","화","수","목","금","토"],wide:["일요일","월요일","화요일","수요일","목요일","금요일","토요일"]},defaultWidth:"wide"}),dayPeriod:(0,h.o)({values:{narrow:{am:"오전",pm:"오후",midnight:"자정",noon:"정오",morning:"아침",afternoon:"오후",evening:"저녁",night:"밤"},abbreviated:{am:"오전",pm:"오후",midnight:"자정",noon:"정오",morning:"아침",afternoon:"오후",evening:"저녁",night:"밤"},wide:{am:"오전",pm:"오후",midnight:"자정",noon:"정오",morning:"아침",afternoon:"오후",evening:"저녁",night:"밤"}},defaultWidth:"wide",formattingValues:{narrow:{am:"오전",pm:"오후",midnight:"자정",noon:"정오",morning:"아침",afternoon:"오후",evening:"저녁",night:"밤"},abbreviated:{am:"오전",pm:"오후",midnight:"자정",noon:"정오",morning:"아침",afternoon:"오후",evening:"저녁",night:"밤"},wide:{am:"오전",pm:"오후",midnight:"자정",noon:"정오",morning:"아침",afternoon:"오후",evening:"저녁",night:"밤"}},defaultFormattingWidth:"wide"})};var j=c(28153);let k={code:"ko",formatDistance:(a,b,c)=>{let e,f=d[a];if(e="string"==typeof f?f:1===b?f.one:f.other.replace("{{count}}",b.toString()),c?.addSuffix)if(c.comparison&&c.comparison>0)return e+" 후";else return e+" 전";return e},formatLong:f,formatRelative:(a,b,c,d)=>g[a],localize:i,match:{ordinalNumber:(0,c(55991).K)({matchPattern:/^(\d+)(일|번째)?/i,parsePattern:/\d+/i,valueCallback:a=>parseInt(a,10)}),era:(0,j.A)({matchPatterns:{narrow:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(기원전|서기)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^(bc|기원전)/i,/^(ad|서기)/i]},defaultParseWidth:"any"}),quarter:(0,j.A)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234]사?분기/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:a=>a+1}),month:(0,j.A)({matchPatterns:{narrow:/^(1[012]|[123456789])/,abbreviated:/^(1[012]|[123456789])월/i,wide:/^(1[012]|[123456789])월/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^1월?$/,/^2/,/^3/,/^4/,/^5/,/^6/,/^7/,/^8/,/^9/,/^10/,/^11/,/^12/]},defaultParseWidth:"any"}),day:(0,j.A)({matchPatterns:{narrow:/^[일월화수목금토]/,short:/^[일월화수목금토]/,abbreviated:/^[일월화수목금토]/,wide:/^[일월화수목금토]요일/},defaultMatchWidth:"wide",parsePatterns:{any:[/^일/,/^월/,/^화/,/^수/,/^목/,/^금/,/^토/]},defaultParseWidth:"any"}),dayPeriod:(0,j.A)({matchPatterns:{any:/^(am|pm|오전|오후|자정|정오|아침|저녁|밤)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^(am|오전)/i,pm:/^(pm|오후)/i,midnight:/^자정/i,noon:/^정오/i,morning:/^아침/i,afternoon:/^오후/i,evening:/^저녁/i,night:/^밤/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},l=k}};