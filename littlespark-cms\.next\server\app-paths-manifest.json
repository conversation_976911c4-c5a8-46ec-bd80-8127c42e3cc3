{"/api/sync/from-main-app/route": "app/api/sync/from-main-app/route.js", "/api/auth/verify/route": "app/api/auth/verify/route.js", "/api/sync/challenges/route": "app/api/sync/challenges/route.js", "/api/sync/users/route": "app/api/sync/users/route.js", "/api/test-data/route": "app/api/test-data/route.js", "/(payload)/api/graphql-playground/route": "app/(payload)/api/graphql-playground/route.js", "/(payload)/api/[...slug]/route": "app/(payload)/api/[...slug]/route.js", "/(payload)/api/graphql/route": "app/(payload)/api/graphql/route.js", "/my-route/route": "app/my-route/route.js"}