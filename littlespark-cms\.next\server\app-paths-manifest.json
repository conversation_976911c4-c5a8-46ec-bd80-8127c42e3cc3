{"/api/auth/verify/route": "app/api/auth/verify/route.js", "/api/sync/from-main-app/route": "app/api/sync/from-main-app/route.js", "/api/sync/challenges/route": "app/api/sync/challenges/route.js", "/_not-found/page": "app/_not-found/page.js", "/api/test-data/route": "app/api/test-data/route.js", "/api/sync/users/route": "app/api/sync/users/route.js", "/(payload)/api/graphql-playground/route": "app/(payload)/api/graphql-playground/route.js", "/(payload)/api/graphql/route": "app/(payload)/api/graphql/route.js", "/(payload)/api/[...slug]/route": "app/(payload)/api/[...slug]/route.js", "/my-route/route": "app/my-route/route.js", "/(payload)/admin/sync/page": "app/(payload)/admin/sync/page.js", "/(payload)/admin/dashboard/page": "app/(payload)/admin/dashboard/page.js", "/(payload)/admin/[[...segments]]/page": "app/(payload)/admin/[[...segments]]/page.js", "/(frontend)/page": "app/(frontend)/page.js"}