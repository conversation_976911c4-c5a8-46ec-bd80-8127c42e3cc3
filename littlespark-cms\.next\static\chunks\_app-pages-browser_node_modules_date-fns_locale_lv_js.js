"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_date-fns_locale_lv_js"],{

/***/ "(app-pages-browser)/./node_modules/date-fns/isSameWeek.js":
/*!*********************************************!*\
  !*** ./node_modules/date-fns/isSameWeek.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isSameWeek: () => (/* binding */ isSameWeek)\n/* harmony export */ });\n/* harmony import */ var _lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_lib/normalizeDates.js */ \"(app-pages-browser)/./node_modules/date-fns/_lib/normalizeDates.js\");\n/* harmony import */ var _startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./startOfWeek.js */ \"(app-pages-browser)/./node_modules/date-fns/startOfWeek.js\");\n\n\n/**\n * The {@link isSameWeek} function options.\n */ /**\n * @name isSameWeek\n * @category Week Helpers\n * @summary Are the given dates in the same week (and month and year)?\n *\n * @description\n * Are the given dates in the same week (and month and year)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same week (and month and year)\n *\n * @example\n * // Are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4))\n * //=> true\n *\n * @example\n * // If week starts with Monday,\n * // are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4), {\n *   weekStartsOn: 1\n * })\n * //=> false\n *\n * @example\n * // Are 1 January 2014 and 1 January 2015 in the same week?\n * const result = isSameWeek(new Date(2014, 0, 1), new Date(2015, 0, 1))\n * //=> false\n */ function isSameWeek(laterDate, earlierDate, options) {\n    const [laterDate_, earlierDate_] = (0,_lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__.normalizeDates)(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate);\n    return +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(laterDate_, options) === +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(earlierDate_, options);\n}\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (isSameWeek);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/isSameWeek.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/lv.js":
/*!********************************************!*\
  !*** ./node_modules/date-fns/locale/lv.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   lv: () => (/* binding */ lv)\n/* harmony export */ });\n/* harmony import */ var _lv_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lv/_lib/formatDistance.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/lv/_lib/formatDistance.js\");\n/* harmony import */ var _lv_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lv/_lib/formatLong.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/lv/_lib/formatLong.js\");\n/* harmony import */ var _lv_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lv/_lib/formatRelative.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/lv/_lib/formatRelative.js\");\n/* harmony import */ var _lv_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lv/_lib/localize.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/lv/_lib/localize.js\");\n/* harmony import */ var _lv_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lv/_lib/match.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/lv/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary Latvian locale (Latvia).\n * @language Latvian\n * @iso-639-2 lav\n * <AUTHOR> Puķītis [@prudolfs](https://github.com/prudolfs)\n */ const lv = {\n    code: \"lv\",\n    formatDistance: _lv_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _lv_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _lv_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _lv_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _lv_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 1 /* Monday */ ,\n        firstWeekContainsDate: 4\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (lv);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9kYXRlLWZucy9sb2NhbGUvbHYuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUE2RDtBQUNSO0FBQ1E7QUFDWjtBQUNOO0FBRTNDOzs7Ozs7Q0FNQyxHQUNNLE1BQU1LLEtBQUs7SUFDaEJDLE1BQU07SUFDTk4sZ0JBQWdCQSxxRUFBY0E7SUFDOUJDLFlBQVlBLDZEQUFVQTtJQUN0QkMsZ0JBQWdCQSxxRUFBY0E7SUFDOUJDLFVBQVVBLHlEQUFRQTtJQUNsQkMsT0FBT0EsbURBQUtBO0lBQ1pHLFNBQVM7UUFDUEMsY0FBYyxFQUFFLFVBQVU7UUFDMUJDLHVCQUF1QjtJQUN6QjtBQUNGLEVBQUU7QUFFRixvQ0FBb0M7QUFDcEMsaUVBQWVKLEVBQUVBLEVBQUMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmF2aTFcXGthdnlhLWdpdFxcc3BhcmstbmV3XFxsaXR0bGVzcGFyay1jbXNcXG5vZGVfbW9kdWxlc1xcZGF0ZS1mbnNcXGxvY2FsZVxcbHYuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZm9ybWF0RGlzdGFuY2UgfSBmcm9tIFwiLi9sdi9fbGliL2Zvcm1hdERpc3RhbmNlLmpzXCI7XG5pbXBvcnQgeyBmb3JtYXRMb25nIH0gZnJvbSBcIi4vbHYvX2xpYi9mb3JtYXRMb25nLmpzXCI7XG5pbXBvcnQgeyBmb3JtYXRSZWxhdGl2ZSB9IGZyb20gXCIuL2x2L19saWIvZm9ybWF0UmVsYXRpdmUuanNcIjtcbmltcG9ydCB7IGxvY2FsaXplIH0gZnJvbSBcIi4vbHYvX2xpYi9sb2NhbGl6ZS5qc1wiO1xuaW1wb3J0IHsgbWF0Y2ggfSBmcm9tIFwiLi9sdi9fbGliL21hdGNoLmpzXCI7XG5cbi8qKlxuICogQGNhdGVnb3J5IExvY2FsZXNcbiAqIEBzdW1tYXJ5IExhdHZpYW4gbG9jYWxlIChMYXR2aWEpLlxuICogQGxhbmd1YWdlIExhdHZpYW5cbiAqIEBpc28tNjM5LTIgbGF2XG4gKiBAYXV0aG9yIFLFq2RvbGZzIFB1xLfEq3RpcyBbQHBydWRvbGZzXShodHRwczovL2dpdGh1Yi5jb20vcHJ1ZG9sZnMpXG4gKi9cbmV4cG9ydCBjb25zdCBsdiA9IHtcbiAgY29kZTogXCJsdlwiLFxuICBmb3JtYXREaXN0YW5jZTogZm9ybWF0RGlzdGFuY2UsXG4gIGZvcm1hdExvbmc6IGZvcm1hdExvbmcsXG4gIGZvcm1hdFJlbGF0aXZlOiBmb3JtYXRSZWxhdGl2ZSxcbiAgbG9jYWxpemU6IGxvY2FsaXplLFxuICBtYXRjaDogbWF0Y2gsXG4gIG9wdGlvbnM6IHtcbiAgICB3ZWVrU3RhcnRzT246IDEgLyogTW9uZGF5ICovLFxuICAgIGZpcnN0V2Vla0NvbnRhaW5zRGF0ZTogNCxcbiAgfSxcbn07XG5cbi8vIEZhbGxiYWNrIGZvciBtb2R1bGFyaXplZCBpbXBvcnRzOlxuZXhwb3J0IGRlZmF1bHQgbHY7XG4iXSwibmFtZXMiOlsiZm9ybWF0RGlzdGFuY2UiLCJmb3JtYXRMb25nIiwiZm9ybWF0UmVsYXRpdmUiLCJsb2NhbGl6ZSIsIm1hdGNoIiwibHYiLCJjb2RlIiwib3B0aW9ucyIsIndlZWtTdGFydHNPbiIsImZpcnN0V2Vla0NvbnRhaW5zRGF0ZSJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/lv.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/lv/_lib/formatDistance.js":
/*!****************************************************************!*\
  !*** ./node_modules/date-fns/locale/lv/_lib/formatDistance.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\nfunction buildLocalizeTokenFn(schema) {\n    return (count, options)=>{\n        if (count === 1) {\n            if (options === null || options === void 0 ? void 0 : options.addSuffix) {\n                return schema.one[0].replace(\"{{time}}\", schema.one[2]);\n            } else {\n                return schema.one[0].replace(\"{{time}}\", schema.one[1]);\n            }\n        } else {\n            const rem = count % 10 === 1 && count % 100 !== 11;\n            if (options === null || options === void 0 ? void 0 : options.addSuffix) {\n                return schema.other[0].replace(\"{{time}}\", rem ? schema.other[3] : schema.other[4]).replace(\"{{count}}\", String(count));\n            } else {\n                return schema.other[0].replace(\"{{time}}\", rem ? schema.other[1] : schema.other[2]).replace(\"{{count}}\", String(count));\n            }\n        }\n    };\n}\nconst formatDistanceLocale = {\n    lessThanXSeconds: buildLocalizeTokenFn({\n        one: [\n            \"mazāk par {{time}}\",\n            \"sekundi\",\n            \"sekundi\"\n        ],\n        other: [\n            \"mazāk nekā {{count}} {{time}}\",\n            \"sekunde\",\n            \"sekundes\",\n            \"sekundes\",\n            \"sekundēm\"\n        ]\n    }),\n    xSeconds: buildLocalizeTokenFn({\n        one: [\n            \"1 {{time}}\",\n            \"sekunde\",\n            \"sekundes\"\n        ],\n        other: [\n            \"{{count}} {{time}}\",\n            \"sekunde\",\n            \"sekundes\",\n            \"sekundes\",\n            \"sekundēm\"\n        ]\n    }),\n    halfAMinute: (_count, options)=>{\n        if (options === null || options === void 0 ? void 0 : options.addSuffix) {\n            return \"pusminūtes\";\n        } else {\n            return \"pusminūte\";\n        }\n    },\n    lessThanXMinutes: buildLocalizeTokenFn({\n        one: [\n            \"mazāk par {{time}}\",\n            \"minūti\",\n            \"minūti\"\n        ],\n        other: [\n            \"mazāk nekā {{count}} {{time}}\",\n            \"minūte\",\n            \"minūtes\",\n            \"minūtes\",\n            \"minūtēm\"\n        ]\n    }),\n    xMinutes: buildLocalizeTokenFn({\n        one: [\n            \"1 {{time}}\",\n            \"minūte\",\n            \"minūtes\"\n        ],\n        other: [\n            \"{{count}} {{time}}\",\n            \"minūte\",\n            \"minūtes\",\n            \"minūtes\",\n            \"minūtēm\"\n        ]\n    }),\n    aboutXHours: buildLocalizeTokenFn({\n        one: [\n            \"apmēram 1 {{time}}\",\n            \"stunda\",\n            \"stundas\"\n        ],\n        other: [\n            \"apmēram {{count}} {{time}}\",\n            \"stunda\",\n            \"stundas\",\n            \"stundas\",\n            \"stundām\"\n        ]\n    }),\n    xHours: buildLocalizeTokenFn({\n        one: [\n            \"1 {{time}}\",\n            \"stunda\",\n            \"stundas\"\n        ],\n        other: [\n            \"{{count}} {{time}}\",\n            \"stunda\",\n            \"stundas\",\n            \"stundas\",\n            \"stundām\"\n        ]\n    }),\n    xDays: buildLocalizeTokenFn({\n        one: [\n            \"1 {{time}}\",\n            \"diena\",\n            \"dienas\"\n        ],\n        other: [\n            \"{{count}} {{time}}\",\n            \"diena\",\n            \"dienas\",\n            \"dienas\",\n            \"dienām\"\n        ]\n    }),\n    aboutXWeeks: buildLocalizeTokenFn({\n        one: [\n            \"apmēram 1 {{time}}\",\n            \"nedēļa\",\n            \"nedēļas\"\n        ],\n        other: [\n            \"apmēram {{count}} {{time}}\",\n            \"nedēļa\",\n            \"nedēļu\",\n            \"nedēļas\",\n            \"nedēļām\"\n        ]\n    }),\n    xWeeks: buildLocalizeTokenFn({\n        one: [\n            \"1 {{time}}\",\n            \"nedēļa\",\n            \"nedēļas\"\n        ],\n        other: [\n            \"{{count}} {{time}}\",\n            \"nedēļa\",\n            \"nedēļu\",\n            \"nedēļas\",\n            \"nedēļām\"\n        ]\n    }),\n    aboutXMonths: buildLocalizeTokenFn({\n        one: [\n            \"apmēram 1 {{time}}\",\n            \"mēnesis\",\n            \"mēneša\"\n        ],\n        other: [\n            \"apmēram {{count}} {{time}}\",\n            \"mēnesis\",\n            \"mēneši\",\n            \"mēneša\",\n            \"mēnešiem\"\n        ]\n    }),\n    xMonths: buildLocalizeTokenFn({\n        one: [\n            \"1 {{time}}\",\n            \"mēnesis\",\n            \"mēneša\"\n        ],\n        other: [\n            \"{{count}} {{time}}\",\n            \"mēnesis\",\n            \"mēneši\",\n            \"mēneša\",\n            \"mēnešiem\"\n        ]\n    }),\n    aboutXYears: buildLocalizeTokenFn({\n        one: [\n            \"apmēram 1 {{time}}\",\n            \"gads\",\n            \"gada\"\n        ],\n        other: [\n            \"apmēram {{count}} {{time}}\",\n            \"gads\",\n            \"gadi\",\n            \"gada\",\n            \"gadiem\"\n        ]\n    }),\n    xYears: buildLocalizeTokenFn({\n        one: [\n            \"1 {{time}}\",\n            \"gads\",\n            \"gada\"\n        ],\n        other: [\n            \"{{count}} {{time}}\",\n            \"gads\",\n            \"gadi\",\n            \"gada\",\n            \"gadiem\"\n        ]\n    }),\n    overXYears: buildLocalizeTokenFn({\n        one: [\n            \"ilgāk par 1 {{time}}\",\n            \"gadu\",\n            \"gadu\"\n        ],\n        other: [\n            \"vairāk nekā {{count}} {{time}}\",\n            \"gads\",\n            \"gadi\",\n            \"gada\",\n            \"gadiem\"\n        ]\n    }),\n    almostXYears: buildLocalizeTokenFn({\n        one: [\n            \"gandrīz 1 {{time}}\",\n            \"gads\",\n            \"gada\"\n        ],\n        other: [\n            \"vairāk nekā {{count}} {{time}}\",\n            \"gads\",\n            \"gadi\",\n            \"gada\",\n            \"gadiem\"\n        ]\n    })\n};\nconst formatDistance = (token, count, options)=>{\n    const result = formatDistanceLocale[token](count, options);\n    if (options === null || options === void 0 ? void 0 : options.addSuffix) {\n        if (options.comparison && options.comparison > 0) {\n            return \"pēc \" + result;\n        } else {\n            return \"pirms \" + result;\n        }\n    }\n    return result;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/lv/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/lv/_lib/formatLong.js":
/*!************************************************************!*\
  !*** ./node_modules/date-fns/locale/lv/_lib/formatLong.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\nconst dateFormats = {\n    full: \"EEEE, y. 'gada' d. MMMM\",\n    long: \"y. 'gada' d. MMMM\",\n    medium: \"dd.MM.y.\",\n    short: \"dd.MM.y.\"\n};\nconst timeFormats = {\n    full: \"HH:mm:ss zzzz\",\n    long: \"HH:mm:ss z\",\n    medium: \"HH:mm:ss\",\n    short: \"HH:mm\"\n};\nconst dateTimeFormats = {\n    full: \"{{date}} 'plkst.' {{time}}\",\n    long: \"{{date}} 'plkst.' {{time}}\",\n    medium: \"{{date}}, {{time}}\",\n    short: \"{{date}}, {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"full\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"full\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/lv/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/lv/_lib/formatRelative.js":
/*!****************************************************************!*\
  !*** ./node_modules/date-fns/locale/lv/_lib/formatRelative.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\n/* harmony import */ var _isSameWeek_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../isSameWeek.js */ \"(app-pages-browser)/./node_modules/date-fns/isSameWeek.js\");\n\nconst weekdays = [\n    \"svētdienā\",\n    \"pirmdienā\",\n    \"otrdienā\",\n    \"trešdienā\",\n    \"ceturtdienā\",\n    \"piektdienā\",\n    \"sestdienā\"\n];\nconst formatRelativeLocale = {\n    lastWeek: (date, baseDate, options)=>{\n        if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_0__.isSameWeek)(date, baseDate, options)) {\n            return \"eeee 'plkst.' p\";\n        }\n        const weekday = weekdays[date.getDay()];\n        return \"'Pagājušā \" + weekday + \" plkst.' p\";\n    },\n    yesterday: \"'Vakar plkst.' p\",\n    today: \"'Šodien plkst.' p\",\n    tomorrow: \"'Rīt plkst.' p\",\n    nextWeek: (date, baseDate, options)=>{\n        if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_0__.isSameWeek)(date, baseDate, options)) {\n            return \"eeee 'plkst.' p\";\n        }\n        const weekday = weekdays[date.getDay()];\n        return \"'Nākamajā \" + weekday + \" plkst.' p\";\n    },\n    other: \"P\"\n};\nconst formatRelative = (token, date, baseDate, options)=>{\n    const format = formatRelativeLocale[token];\n    if (typeof format === \"function\") {\n        return format(date, baseDate, options);\n    }\n    return format;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/lv/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/lv/_lib/localize.js":
/*!**********************************************************!*\
  !*** ./node_modules/date-fns/locale/lv/_lib/localize.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\nconst eraValues = {\n    narrow: [\n        \"p.m.ē\",\n        \"m.ē\"\n    ],\n    abbreviated: [\n        \"p. m. ē.\",\n        \"m. ē.\"\n    ],\n    wide: [\n        \"pirms mūsu ēras\",\n        \"mūsu ērā\"\n    ]\n};\nconst quarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"1. cet.\",\n        \"2. cet.\",\n        \"3. cet.\",\n        \"4. cet.\"\n    ],\n    wide: [\n        \"pirmais ceturksnis\",\n        \"otrais ceturksnis\",\n        \"trešais ceturksnis\",\n        \"ceturtais ceturksnis\"\n    ]\n};\nconst formattingQuarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"1. cet.\",\n        \"2. cet.\",\n        \"3. cet.\",\n        \"4. cet.\"\n    ],\n    wide: [\n        \"pirmajā ceturksnī\",\n        \"otrajā ceturksnī\",\n        \"trešajā ceturksnī\",\n        \"ceturtajā ceturksnī\"\n    ]\n};\nconst monthValues = {\n    narrow: [\n        \"J\",\n        \"F\",\n        \"M\",\n        \"A\",\n        \"M\",\n        \"J\",\n        \"J\",\n        \"A\",\n        \"S\",\n        \"O\",\n        \"N\",\n        \"D\"\n    ],\n    abbreviated: [\n        \"janv.\",\n        \"febr.\",\n        \"marts\",\n        \"apr.\",\n        \"maijs\",\n        \"jūn.\",\n        \"jūl.\",\n        \"aug.\",\n        \"sept.\",\n        \"okt.\",\n        \"nov.\",\n        \"dec.\"\n    ],\n    wide: [\n        \"janvāris\",\n        \"februāris\",\n        \"marts\",\n        \"aprīlis\",\n        \"maijs\",\n        \"jūnijs\",\n        \"jūlijs\",\n        \"augusts\",\n        \"septembris\",\n        \"oktobris\",\n        \"novembris\",\n        \"decembris\"\n    ]\n};\nconst formattingMonthValues = {\n    narrow: [\n        \"J\",\n        \"F\",\n        \"M\",\n        \"A\",\n        \"M\",\n        \"J\",\n        \"J\",\n        \"A\",\n        \"S\",\n        \"O\",\n        \"N\",\n        \"D\"\n    ],\n    abbreviated: [\n        \"janv.\",\n        \"febr.\",\n        \"martā\",\n        \"apr.\",\n        \"maijs\",\n        \"jūn.\",\n        \"jūl.\",\n        \"aug.\",\n        \"sept.\",\n        \"okt.\",\n        \"nov.\",\n        \"dec.\"\n    ],\n    wide: [\n        \"janvārī\",\n        \"februārī\",\n        \"martā\",\n        \"aprīlī\",\n        \"maijā\",\n        \"jūnijā\",\n        \"jūlijā\",\n        \"augustā\",\n        \"septembrī\",\n        \"oktobrī\",\n        \"novembrī\",\n        \"decembrī\"\n    ]\n};\nconst dayValues = {\n    narrow: [\n        \"S\",\n        \"P\",\n        \"O\",\n        \"T\",\n        \"C\",\n        \"P\",\n        \"S\"\n    ],\n    short: [\n        \"Sv\",\n        \"P\",\n        \"O\",\n        \"T\",\n        \"C\",\n        \"Pk\",\n        \"S\"\n    ],\n    abbreviated: [\n        \"svētd.\",\n        \"pirmd.\",\n        \"otrd.\",\n        \"trešd.\",\n        \"ceturtd.\",\n        \"piektd.\",\n        \"sestd.\"\n    ],\n    wide: [\n        \"svētdiena\",\n        \"pirmdiena\",\n        \"otrdiena\",\n        \"trešdiena\",\n        \"ceturtdiena\",\n        \"piektdiena\",\n        \"sestdiena\"\n    ]\n};\nconst formattingDayValues = {\n    narrow: [\n        \"S\",\n        \"P\",\n        \"O\",\n        \"T\",\n        \"C\",\n        \"P\",\n        \"S\"\n    ],\n    short: [\n        \"Sv\",\n        \"P\",\n        \"O\",\n        \"T\",\n        \"C\",\n        \"Pk\",\n        \"S\"\n    ],\n    abbreviated: [\n        \"svētd.\",\n        \"pirmd.\",\n        \"otrd.\",\n        \"trešd.\",\n        \"ceturtd.\",\n        \"piektd.\",\n        \"sestd.\"\n    ],\n    wide: [\n        \"svētdienā\",\n        \"pirmdienā\",\n        \"otrdienā\",\n        \"trešdienā\",\n        \"ceturtdienā\",\n        \"piektdienā\",\n        \"sestdienā\"\n    ]\n};\nconst dayPeriodValues = {\n    narrow: {\n        am: \"am\",\n        pm: \"pm\",\n        midnight: \"pusn.\",\n        noon: \"pusd.\",\n        morning: \"rīts\",\n        afternoon: \"diena\",\n        evening: \"vakars\",\n        night: \"nakts\"\n    },\n    abbreviated: {\n        am: \"am\",\n        pm: \"pm\",\n        midnight: \"pusn.\",\n        noon: \"pusd.\",\n        morning: \"rīts\",\n        afternoon: \"pēcpusd.\",\n        evening: \"vakars\",\n        night: \"nakts\"\n    },\n    wide: {\n        am: \"am\",\n        pm: \"pm\",\n        midnight: \"pusnakts\",\n        noon: \"pusdienlaiks\",\n        morning: \"rīts\",\n        afternoon: \"pēcpusdiena\",\n        evening: \"vakars\",\n        night: \"nakts\"\n    }\n};\nconst formattingDayPeriodValues = {\n    narrow: {\n        am: \"am\",\n        pm: \"pm\",\n        midnight: \"pusn.\",\n        noon: \"pusd.\",\n        morning: \"rītā\",\n        afternoon: \"dienā\",\n        evening: \"vakarā\",\n        night: \"naktī\"\n    },\n    abbreviated: {\n        am: \"am\",\n        pm: \"pm\",\n        midnight: \"pusn.\",\n        noon: \"pusd.\",\n        morning: \"rītā\",\n        afternoon: \"pēcpusd.\",\n        evening: \"vakarā\",\n        night: \"naktī\"\n    },\n    wide: {\n        am: \"am\",\n        pm: \"pm\",\n        midnight: \"pusnaktī\",\n        noon: \"pusdienlaikā\",\n        morning: \"rītā\",\n        afternoon: \"pēcpusdienā\",\n        evening: \"vakarā\",\n        night: \"naktī\"\n    }\n};\nconst ordinalNumber = (dirtyNumber, _options)=>{\n    const number = Number(dirtyNumber);\n    return number + \".\";\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        formattingValues: formattingQuarterValues,\n        defaultFormattingWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        defaultWidth: \"wide\",\n        formattingValues: formattingMonthValues,\n        defaultFormattingWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\",\n        formattingValues: formattingDayValues,\n        defaultFormattingWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"wide\",\n        formattingValues: formattingDayPeriodValues,\n        defaultFormattingWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9kYXRlLWZucy9sb2NhbGUvbHYvX2xpYi9sb2NhbGl6ZS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFnRTtBQUVoRSxNQUFNQyxZQUFZO0lBQ2hCQyxRQUFRO1FBQUM7UUFBUztLQUFNO0lBQ3hCQyxhQUFhO1FBQUM7UUFBWTtLQUFRO0lBQ2xDQyxNQUFNO1FBQUM7UUFBbUI7S0FBVztBQUN2QztBQUVBLE1BQU1DLGdCQUFnQjtJQUNwQkgsUUFBUTtRQUFDO1FBQUs7UUFBSztRQUFLO0tBQUk7SUFDNUJDLGFBQWE7UUFBQztRQUFXO1FBQVc7UUFBVztLQUFVO0lBQ3pEQyxNQUFNO1FBQ0o7UUFDQTtRQUNBO1FBQ0E7S0FDRDtBQUNIO0FBRUEsTUFBTUUsMEJBQTBCO0lBQzlCSixRQUFRO1FBQUM7UUFBSztRQUFLO1FBQUs7S0FBSTtJQUM1QkMsYUFBYTtRQUFDO1FBQVc7UUFBVztRQUFXO0tBQVU7SUFDekRDLE1BQU07UUFDSjtRQUNBO1FBQ0E7UUFDQTtLQUNEO0FBQ0g7QUFFQSxNQUFNRyxjQUFjO0lBQ2xCTCxRQUFRO1FBQUM7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO0tBQUk7SUFDcEVDLGFBQWE7UUFDWDtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7S0FDRDtJQUVEQyxNQUFNO1FBQ0o7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0Q7QUFDSDtBQUVBLE1BQU1JLHdCQUF3QjtJQUM1Qk4sUUFBUTtRQUFDO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztLQUFJO0lBQ3BFQyxhQUFhO1FBQ1g7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0Q7SUFFREMsTUFBTTtRQUNKO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtLQUNEO0FBQ0g7QUFFQSxNQUFNSyxZQUFZO0lBQ2hCUCxRQUFRO1FBQUM7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7S0FBSTtJQUMzQ1EsT0FBTztRQUFDO1FBQU07UUFBSztRQUFLO1FBQUs7UUFBSztRQUFNO0tBQUk7SUFDNUNQLGFBQWE7UUFDWDtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtLQUNEO0lBRURDLE1BQU07UUFDSjtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtLQUNEO0FBQ0g7QUFFQSxNQUFNTyxzQkFBc0I7SUFDMUJULFFBQVE7UUFBQztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztLQUFJO0lBQzNDUSxPQUFPO1FBQUM7UUFBTTtRQUFLO1FBQUs7UUFBSztRQUFLO1FBQU07S0FBSTtJQUM1Q1AsYUFBYTtRQUNYO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0Q7SUFFREMsTUFBTTtRQUNKO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0Q7QUFDSDtBQUVBLE1BQU1RLGtCQUFrQjtJQUN0QlYsUUFBUTtRQUNOVyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsVUFBVTtRQUNWQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxTQUFTO1FBQ1RDLE9BQU87SUFDVDtJQUNBakIsYUFBYTtRQUNYVSxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsVUFBVTtRQUNWQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxTQUFTO1FBQ1RDLE9BQU87SUFDVDtJQUNBaEIsTUFBTTtRQUNKUyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsVUFBVTtRQUNWQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxTQUFTO1FBQ1RDLE9BQU87SUFDVDtBQUNGO0FBRUEsTUFBTUMsNEJBQTRCO0lBQ2hDbkIsUUFBUTtRQUNOVyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsVUFBVTtRQUNWQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxTQUFTO1FBQ1RDLE9BQU87SUFDVDtJQUNBakIsYUFBYTtRQUNYVSxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsVUFBVTtRQUNWQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxTQUFTO1FBQ1RDLE9BQU87SUFDVDtJQUNBaEIsTUFBTTtRQUNKUyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsVUFBVTtRQUNWQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxTQUFTO1FBQ1RDLE9BQU87SUFDVDtBQUNGO0FBRUEsTUFBTUUsZ0JBQWdCLENBQUNDLGFBQWFDO0lBQ2xDLE1BQU1DLFNBQVNDLE9BQU9IO0lBQ3RCLE9BQU9FLFNBQVM7QUFDbEI7QUFFTyxNQUFNRSxXQUFXO0lBQ3RCTDtJQUVBTSxLQUFLNUIsd0VBQWVBLENBQUM7UUFDbkI2QixRQUFRNUI7UUFDUjZCLGNBQWM7SUFDaEI7SUFFQUMsU0FBUy9CLHdFQUFlQSxDQUFDO1FBQ3ZCNkIsUUFBUXhCO1FBQ1J5QixjQUFjO1FBQ2RFLGtCQUFrQjFCO1FBQ2xCMkIsd0JBQXdCO1FBQ3hCQyxrQkFBa0IsQ0FBQ0gsVUFBWUEsVUFBVTtJQUMzQztJQUVBSSxPQUFPbkMsd0VBQWVBLENBQUM7UUFDckI2QixRQUFRdEI7UUFDUnVCLGNBQWM7UUFDZEUsa0JBQWtCeEI7UUFDbEJ5Qix3QkFBd0I7SUFDMUI7SUFFQUcsS0FBS3BDLHdFQUFlQSxDQUFDO1FBQ25CNkIsUUFBUXBCO1FBQ1JxQixjQUFjO1FBQ2RFLGtCQUFrQnJCO1FBQ2xCc0Isd0JBQXdCO0lBQzFCO0lBRUFJLFdBQVdyQyx3RUFBZUEsQ0FBQztRQUN6QjZCLFFBQVFqQjtRQUNSa0IsY0FBYztRQUNkRSxrQkFBa0JYO1FBQ2xCWSx3QkFBd0I7SUFDMUI7QUFDRixFQUFFIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHJhdmkxXFxrYXZ5YS1naXRcXHNwYXJrLW5ld1xcbGl0dGxlc3BhcmstY21zXFxub2RlX21vZHVsZXNcXGRhdGUtZm5zXFxsb2NhbGVcXGx2XFxfbGliXFxsb2NhbGl6ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBidWlsZExvY2FsaXplRm4gfSBmcm9tIFwiLi4vLi4vX2xpYi9idWlsZExvY2FsaXplRm4uanNcIjtcblxuY29uc3QgZXJhVmFsdWVzID0ge1xuICBuYXJyb3c6IFtcInAubS7Ek1wiLCBcIm0uxJNcIl0sXG4gIGFiYnJldmlhdGVkOiBbXCJwLiBtLiDEky5cIiwgXCJtLiDEky5cIl0sXG4gIHdpZGU6IFtcInBpcm1zIG3Fq3N1IMSTcmFzXCIsIFwibcWrc3UgxJNyxIFcIl0sXG59O1xuXG5jb25zdCBxdWFydGVyVmFsdWVzID0ge1xuICBuYXJyb3c6IFtcIjFcIiwgXCIyXCIsIFwiM1wiLCBcIjRcIl0sXG4gIGFiYnJldmlhdGVkOiBbXCIxLiBjZXQuXCIsIFwiMi4gY2V0LlwiLCBcIjMuIGNldC5cIiwgXCI0LiBjZXQuXCJdLFxuICB3aWRlOiBbXG4gICAgXCJwaXJtYWlzIGNldHVya3NuaXNcIixcbiAgICBcIm90cmFpcyBjZXR1cmtzbmlzXCIsXG4gICAgXCJ0cmXFoWFpcyBjZXR1cmtzbmlzXCIsXG4gICAgXCJjZXR1cnRhaXMgY2V0dXJrc25pc1wiLFxuICBdLFxufTtcblxuY29uc3QgZm9ybWF0dGluZ1F1YXJ0ZXJWYWx1ZXMgPSB7XG4gIG5hcnJvdzogW1wiMVwiLCBcIjJcIiwgXCIzXCIsIFwiNFwiXSxcbiAgYWJicmV2aWF0ZWQ6IFtcIjEuIGNldC5cIiwgXCIyLiBjZXQuXCIsIFwiMy4gY2V0LlwiLCBcIjQuIGNldC5cIl0sXG4gIHdpZGU6IFtcbiAgICBcInBpcm1hasSBIGNldHVya3NuxKtcIixcbiAgICBcIm90cmFqxIEgY2V0dXJrc27Eq1wiLFxuICAgIFwidHJlxaFhasSBIGNldHVya3NuxKtcIixcbiAgICBcImNldHVydGFqxIEgY2V0dXJrc27Eq1wiLFxuICBdLFxufTtcblxuY29uc3QgbW9udGhWYWx1ZXMgPSB7XG4gIG5hcnJvdzogW1wiSlwiLCBcIkZcIiwgXCJNXCIsIFwiQVwiLCBcIk1cIiwgXCJKXCIsIFwiSlwiLCBcIkFcIiwgXCJTXCIsIFwiT1wiLCBcIk5cIiwgXCJEXCJdLFxuICBhYmJyZXZpYXRlZDogW1xuICAgIFwiamFudi5cIixcbiAgICBcImZlYnIuXCIsXG4gICAgXCJtYXJ0c1wiLFxuICAgIFwiYXByLlwiLFxuICAgIFwibWFpanNcIixcbiAgICBcImrFq24uXCIsXG4gICAgXCJqxatsLlwiLFxuICAgIFwiYXVnLlwiLFxuICAgIFwic2VwdC5cIixcbiAgICBcIm9rdC5cIixcbiAgICBcIm5vdi5cIixcbiAgICBcImRlYy5cIixcbiAgXSxcblxuICB3aWRlOiBbXG4gICAgXCJqYW52xIFyaXNcIixcbiAgICBcImZlYnJ1xIFyaXNcIixcbiAgICBcIm1hcnRzXCIsXG4gICAgXCJhcHLEq2xpc1wiLFxuICAgIFwibWFpanNcIixcbiAgICBcImrFq25panNcIixcbiAgICBcImrFq2xpanNcIixcbiAgICBcImF1Z3VzdHNcIixcbiAgICBcInNlcHRlbWJyaXNcIixcbiAgICBcIm9rdG9icmlzXCIsXG4gICAgXCJub3ZlbWJyaXNcIixcbiAgICBcImRlY2VtYnJpc1wiLFxuICBdLFxufTtcblxuY29uc3QgZm9ybWF0dGluZ01vbnRoVmFsdWVzID0ge1xuICBuYXJyb3c6IFtcIkpcIiwgXCJGXCIsIFwiTVwiLCBcIkFcIiwgXCJNXCIsIFwiSlwiLCBcIkpcIiwgXCJBXCIsIFwiU1wiLCBcIk9cIiwgXCJOXCIsIFwiRFwiXSxcbiAgYWJicmV2aWF0ZWQ6IFtcbiAgICBcImphbnYuXCIsXG4gICAgXCJmZWJyLlwiLFxuICAgIFwibWFydMSBXCIsXG4gICAgXCJhcHIuXCIsXG4gICAgXCJtYWlqc1wiLFxuICAgIFwiasWrbi5cIixcbiAgICBcImrFq2wuXCIsXG4gICAgXCJhdWcuXCIsXG4gICAgXCJzZXB0LlwiLFxuICAgIFwib2t0LlwiLFxuICAgIFwibm92LlwiLFxuICAgIFwiZGVjLlwiLFxuICBdLFxuXG4gIHdpZGU6IFtcbiAgICBcImphbnbEgXLEq1wiLFxuICAgIFwiZmVicnXEgXLEq1wiLFxuICAgIFwibWFydMSBXCIsXG4gICAgXCJhcHLEq2zEq1wiLFxuICAgIFwibWFpasSBXCIsXG4gICAgXCJqxatuaWrEgVwiLFxuICAgIFwiasWrbGlqxIFcIixcbiAgICBcImF1Z3VzdMSBXCIsXG4gICAgXCJzZXB0ZW1icsSrXCIsXG4gICAgXCJva3RvYnLEq1wiLFxuICAgIFwibm92ZW1icsSrXCIsXG4gICAgXCJkZWNlbWJyxKtcIixcbiAgXSxcbn07XG5cbmNvbnN0IGRheVZhbHVlcyA9IHtcbiAgbmFycm93OiBbXCJTXCIsIFwiUFwiLCBcIk9cIiwgXCJUXCIsIFwiQ1wiLCBcIlBcIiwgXCJTXCJdLFxuICBzaG9ydDogW1wiU3ZcIiwgXCJQXCIsIFwiT1wiLCBcIlRcIiwgXCJDXCIsIFwiUGtcIiwgXCJTXCJdLFxuICBhYmJyZXZpYXRlZDogW1xuICAgIFwic3bEk3RkLlwiLFxuICAgIFwicGlybWQuXCIsXG4gICAgXCJvdHJkLlwiLFxuICAgIFwidHJlxaFkLlwiLFxuICAgIFwiY2V0dXJ0ZC5cIixcbiAgICBcInBpZWt0ZC5cIixcbiAgICBcInNlc3RkLlwiLFxuICBdLFxuXG4gIHdpZGU6IFtcbiAgICBcInN2xJN0ZGllbmFcIixcbiAgICBcInBpcm1kaWVuYVwiLFxuICAgIFwib3RyZGllbmFcIixcbiAgICBcInRyZcWhZGllbmFcIixcbiAgICBcImNldHVydGRpZW5hXCIsXG4gICAgXCJwaWVrdGRpZW5hXCIsXG4gICAgXCJzZXN0ZGllbmFcIixcbiAgXSxcbn07XG5cbmNvbnN0IGZvcm1hdHRpbmdEYXlWYWx1ZXMgPSB7XG4gIG5hcnJvdzogW1wiU1wiLCBcIlBcIiwgXCJPXCIsIFwiVFwiLCBcIkNcIiwgXCJQXCIsIFwiU1wiXSxcbiAgc2hvcnQ6IFtcIlN2XCIsIFwiUFwiLCBcIk9cIiwgXCJUXCIsIFwiQ1wiLCBcIlBrXCIsIFwiU1wiXSxcbiAgYWJicmV2aWF0ZWQ6IFtcbiAgICBcInN2xJN0ZC5cIixcbiAgICBcInBpcm1kLlwiLFxuICAgIFwib3RyZC5cIixcbiAgICBcInRyZcWhZC5cIixcbiAgICBcImNldHVydGQuXCIsXG4gICAgXCJwaWVrdGQuXCIsXG4gICAgXCJzZXN0ZC5cIixcbiAgXSxcblxuICB3aWRlOiBbXG4gICAgXCJzdsSTdGRpZW7EgVwiLFxuICAgIFwicGlybWRpZW7EgVwiLFxuICAgIFwib3RyZGllbsSBXCIsXG4gICAgXCJ0cmXFoWRpZW7EgVwiLFxuICAgIFwiY2V0dXJ0ZGllbsSBXCIsXG4gICAgXCJwaWVrdGRpZW7EgVwiLFxuICAgIFwic2VzdGRpZW7EgVwiLFxuICBdLFxufTtcblxuY29uc3QgZGF5UGVyaW9kVmFsdWVzID0ge1xuICBuYXJyb3c6IHtcbiAgICBhbTogXCJhbVwiLFxuICAgIHBtOiBcInBtXCIsXG4gICAgbWlkbmlnaHQ6IFwicHVzbi5cIixcbiAgICBub29uOiBcInB1c2QuXCIsXG4gICAgbW9ybmluZzogXCJyxKt0c1wiLFxuICAgIGFmdGVybm9vbjogXCJkaWVuYVwiLFxuICAgIGV2ZW5pbmc6IFwidmFrYXJzXCIsXG4gICAgbmlnaHQ6IFwibmFrdHNcIixcbiAgfSxcbiAgYWJicmV2aWF0ZWQ6IHtcbiAgICBhbTogXCJhbVwiLFxuICAgIHBtOiBcInBtXCIsXG4gICAgbWlkbmlnaHQ6IFwicHVzbi5cIixcbiAgICBub29uOiBcInB1c2QuXCIsXG4gICAgbW9ybmluZzogXCJyxKt0c1wiLFxuICAgIGFmdGVybm9vbjogXCJwxJNjcHVzZC5cIixcbiAgICBldmVuaW5nOiBcInZha2Fyc1wiLFxuICAgIG5pZ2h0OiBcIm5ha3RzXCIsXG4gIH0sXG4gIHdpZGU6IHtcbiAgICBhbTogXCJhbVwiLFxuICAgIHBtOiBcInBtXCIsXG4gICAgbWlkbmlnaHQ6IFwicHVzbmFrdHNcIixcbiAgICBub29uOiBcInB1c2RpZW5sYWlrc1wiLFxuICAgIG1vcm5pbmc6IFwicsSrdHNcIixcbiAgICBhZnRlcm5vb246IFwicMSTY3B1c2RpZW5hXCIsXG4gICAgZXZlbmluZzogXCJ2YWthcnNcIixcbiAgICBuaWdodDogXCJuYWt0c1wiLFxuICB9LFxufTtcblxuY29uc3QgZm9ybWF0dGluZ0RheVBlcmlvZFZhbHVlcyA9IHtcbiAgbmFycm93OiB7XG4gICAgYW06IFwiYW1cIixcbiAgICBwbTogXCJwbVwiLFxuICAgIG1pZG5pZ2h0OiBcInB1c24uXCIsXG4gICAgbm9vbjogXCJwdXNkLlwiLFxuICAgIG1vcm5pbmc6IFwicsSrdMSBXCIsXG4gICAgYWZ0ZXJub29uOiBcImRpZW7EgVwiLFxuICAgIGV2ZW5pbmc6IFwidmFrYXLEgVwiLFxuICAgIG5pZ2h0OiBcIm5ha3TEq1wiLFxuICB9LFxuICBhYmJyZXZpYXRlZDoge1xuICAgIGFtOiBcImFtXCIsXG4gICAgcG06IFwicG1cIixcbiAgICBtaWRuaWdodDogXCJwdXNuLlwiLFxuICAgIG5vb246IFwicHVzZC5cIixcbiAgICBtb3JuaW5nOiBcInLEq3TEgVwiLFxuICAgIGFmdGVybm9vbjogXCJwxJNjcHVzZC5cIixcbiAgICBldmVuaW5nOiBcInZha2FyxIFcIixcbiAgICBuaWdodDogXCJuYWt0xKtcIixcbiAgfSxcbiAgd2lkZToge1xuICAgIGFtOiBcImFtXCIsXG4gICAgcG06IFwicG1cIixcbiAgICBtaWRuaWdodDogXCJwdXNuYWt0xKtcIixcbiAgICBub29uOiBcInB1c2RpZW5sYWlrxIFcIixcbiAgICBtb3JuaW5nOiBcInLEq3TEgVwiLFxuICAgIGFmdGVybm9vbjogXCJwxJNjcHVzZGllbsSBXCIsXG4gICAgZXZlbmluZzogXCJ2YWthcsSBXCIsXG4gICAgbmlnaHQ6IFwibmFrdMSrXCIsXG4gIH0sXG59O1xuXG5jb25zdCBvcmRpbmFsTnVtYmVyID0gKGRpcnR5TnVtYmVyLCBfb3B0aW9ucykgPT4ge1xuICBjb25zdCBudW1iZXIgPSBOdW1iZXIoZGlydHlOdW1iZXIpO1xuICByZXR1cm4gbnVtYmVyICsgXCIuXCI7XG59O1xuXG5leHBvcnQgY29uc3QgbG9jYWxpemUgPSB7XG4gIG9yZGluYWxOdW1iZXIsXG5cbiAgZXJhOiBidWlsZExvY2FsaXplRm4oe1xuICAgIHZhbHVlczogZXJhVmFsdWVzLFxuICAgIGRlZmF1bHRXaWR0aDogXCJ3aWRlXCIsXG4gIH0pLFxuXG4gIHF1YXJ0ZXI6IGJ1aWxkTG9jYWxpemVGbih7XG4gICAgdmFsdWVzOiBxdWFydGVyVmFsdWVzLFxuICAgIGRlZmF1bHRXaWR0aDogXCJ3aWRlXCIsXG4gICAgZm9ybWF0dGluZ1ZhbHVlczogZm9ybWF0dGluZ1F1YXJ0ZXJWYWx1ZXMsXG4gICAgZGVmYXVsdEZvcm1hdHRpbmdXaWR0aDogXCJ3aWRlXCIsXG4gICAgYXJndW1lbnRDYWxsYmFjazogKHF1YXJ0ZXIpID0+IHF1YXJ0ZXIgLSAxLFxuICB9KSxcblxuICBtb250aDogYnVpbGRMb2NhbGl6ZUZuKHtcbiAgICB2YWx1ZXM6IG1vbnRoVmFsdWVzLFxuICAgIGRlZmF1bHRXaWR0aDogXCJ3aWRlXCIsXG4gICAgZm9ybWF0dGluZ1ZhbHVlczogZm9ybWF0dGluZ01vbnRoVmFsdWVzLFxuICAgIGRlZmF1bHRGb3JtYXR0aW5nV2lkdGg6IFwid2lkZVwiLFxuICB9KSxcblxuICBkYXk6IGJ1aWxkTG9jYWxpemVGbih7XG4gICAgdmFsdWVzOiBkYXlWYWx1ZXMsXG4gICAgZGVmYXVsdFdpZHRoOiBcIndpZGVcIixcbiAgICBmb3JtYXR0aW5nVmFsdWVzOiBmb3JtYXR0aW5nRGF5VmFsdWVzLFxuICAgIGRlZmF1bHRGb3JtYXR0aW5nV2lkdGg6IFwid2lkZVwiLFxuICB9KSxcblxuICBkYXlQZXJpb2Q6IGJ1aWxkTG9jYWxpemVGbih7XG4gICAgdmFsdWVzOiBkYXlQZXJpb2RWYWx1ZXMsXG4gICAgZGVmYXVsdFdpZHRoOiBcIndpZGVcIixcbiAgICBmb3JtYXR0aW5nVmFsdWVzOiBmb3JtYXR0aW5nRGF5UGVyaW9kVmFsdWVzLFxuICAgIGRlZmF1bHRGb3JtYXR0aW5nV2lkdGg6IFwid2lkZVwiLFxuICB9KSxcbn07XG4iXSwibmFtZXMiOlsiYnVpbGRMb2NhbGl6ZUZuIiwiZXJhVmFsdWVzIiwibmFycm93IiwiYWJicmV2aWF0ZWQiLCJ3aWRlIiwicXVhcnRlclZhbHVlcyIsImZvcm1hdHRpbmdRdWFydGVyVmFsdWVzIiwibW9udGhWYWx1ZXMiLCJmb3JtYXR0aW5nTW9udGhWYWx1ZXMiLCJkYXlWYWx1ZXMiLCJzaG9ydCIsImZvcm1hdHRpbmdEYXlWYWx1ZXMiLCJkYXlQZXJpb2RWYWx1ZXMiLCJhbSIsInBtIiwibWlkbmlnaHQiLCJub29uIiwibW9ybmluZyIsImFmdGVybm9vbiIsImV2ZW5pbmciLCJuaWdodCIsImZvcm1hdHRpbmdEYXlQZXJpb2RWYWx1ZXMiLCJvcmRpbmFsTnVtYmVyIiwiZGlydHlOdW1iZXIiLCJfb3B0aW9ucyIsIm51bWJlciIsIk51bWJlciIsImxvY2FsaXplIiwiZXJhIiwidmFsdWVzIiwiZGVmYXVsdFdpZHRoIiwicXVhcnRlciIsImZvcm1hdHRpbmdWYWx1ZXMiLCJkZWZhdWx0Rm9ybWF0dGluZ1dpZHRoIiwiYXJndW1lbnRDYWxsYmFjayIsIm1vbnRoIiwiZGF5IiwiZGF5UGVyaW9kIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/lv/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/date-fns/locale/lv/_lib/match.js":
/*!*******************************************************!*\
  !*** ./node_modules/date-fns/locale/lv/_lib/match.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/./node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^(\\d+)\\./i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^(p\\.m\\.ē|m\\.ē)/i,\n    abbreviated: /^(p\\. m\\. ē\\.|m\\. ē\\.)/i,\n    wide: /^(pirms mūsu ēras|mūsu ērā)/i\n};\nconst parseEraPatterns = {\n    any: [\n        /^p/i,\n        /^m/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]/i,\n    abbreviated: /^[1234](\\. cet\\.)/i,\n    wide: /^(pirma(is|jā)|otra(is|jā)|treša(is|jā)|ceturta(is|jā)) ceturksn(is|ī)/i\n};\nconst parseQuarterPatterns = {\n    narrow: [\n        /^1/i,\n        /^2/i,\n        /^3/i,\n        /^4/i\n    ],\n    abbreviated: [\n        /^1/i,\n        /^2/i,\n        /^3/i,\n        /^4/i\n    ],\n    wide: [\n        /^p/i,\n        /^o/i,\n        /^t/i,\n        /^c/i\n    ]\n};\nconst matchMonthPatterns = {\n    narrow: /^[jfmasond]/i,\n    abbreviated: /^(janv\\.|febr\\.|marts|apr\\.|maijs|jūn\\.|jūl\\.|aug\\.|sept\\.|okt\\.|nov\\.|dec\\.)/i,\n    wide: /^(janvār(is|ī)|februār(is|ī)|mart[sā]|aprīl(is|ī)|maij[sā]|jūnij[sā]|jūlij[sā]|august[sā]|septembr(is|ī)|oktobr(is|ī)|novembr(is|ī)|decembr(is|ī))/i\n};\nconst parseMonthPatterns = {\n    narrow: [\n        /^j/i,\n        /^f/i,\n        /^m/i,\n        /^a/i,\n        /^m/i,\n        /^j/i,\n        /^j/i,\n        /^a/i,\n        /^s/i,\n        /^o/i,\n        /^n/i,\n        /^d/i\n    ],\n    any: [\n        /^ja/i,\n        /^f/i,\n        /^mar/i,\n        /^ap/i,\n        /^mai/i,\n        /^jūn/i,\n        /^jūl/i,\n        /^au/i,\n        /^s/i,\n        /^o/i,\n        /^n/i,\n        /^d/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^[spotc]/i,\n    short: /^(sv|pi|o|t|c|pk|s)/i,\n    abbreviated: /^(svētd\\.|pirmd\\.|otrd.\\|trešd\\.|ceturtd\\.|piektd\\.|sestd\\.)/i,\n    wide: /^(svētdien(a|ā)|pirmdien(a|ā)|otrdien(a|ā)|trešdien(a|ā)|ceturtdien(a|ā)|piektdien(a|ā)|sestdien(a|ā))/i\n};\nconst parseDayPatterns = {\n    narrow: [\n        /^s/i,\n        /^p/i,\n        /^o/i,\n        /^t/i,\n        /^c/i,\n        /^p/i,\n        /^s/i\n    ],\n    any: [\n        /^sv/i,\n        /^pi/i,\n        /^o/i,\n        /^t/i,\n        /^c/i,\n        /^p/i,\n        /^se/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    narrow: /^(am|pm|pusn\\.|pusd\\.|rīt(s|ā)|dien(a|ā)|vakar(s|ā)|nakt(s|ī))/,\n    abbreviated: /^(am|pm|pusn\\.|pusd\\.|rīt(s|ā)|pēcpusd\\.|vakar(s|ā)|nakt(s|ī))/,\n    wide: /^(am|pm|pusnakt(s|ī)|pusdienlaik(s|ā)|rīt(s|ā)|pēcpusdien(a|ā)|vakar(s|ā)|nakt(s|ī))/i\n};\nconst parseDayPeriodPatterns = {\n    any: {\n        am: /^am/i,\n        pm: /^pm/i,\n        midnight: /^pusn/i,\n        noon: /^pusd/i,\n        morning: /^r/i,\n        afternoon: /^(d|pēc)/i,\n        evening: /^v/i,\n        night: /^n/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value, 10)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"wide\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/date-fns/locale/lv/_lib/match.js\n"));

/***/ })

}]);