"use strict";exports.id=5344,exports.ids=[5344],exports.modules={57841:(t,e,n)=>{n.d(e,{w:()=>o});let a=Symbol.for("constructDateFrom");function o(t,e){return"function"==typeof t?t(e):t&&"object"==typeof t&&a in t?t[a](e):t instanceof Date?new t.constructor(e):new Date(e)}},40165:(t,e,n)=>{n.d(e,{R:()=>u});var a=n(57841);let o={};var r=n(87046);function i(t,e){let n=e?.weekStartsOn??e?.locale?.options?.weekStartsOn??o.weekStartsOn??o.locale?.options?.weekStartsOn??0,a=(0,r.a)(t,e?.in),i=a.getDay();return a.setDate(a.getDate()-((i<n?7:0)+i-n)),a.setHours(0,0,0,0),a}function u(t,e,n){let[o,r]=function(t,...e){let n=a.w.bind(null,t||e.find(t=>"object"==typeof t));return e.map(n)}(n?.in,t,e);return+i(o,n)==+i(r,n)}},78526:(t,e,n)=>{n.d(e,{k:()=>a});function a(t){return (e={})=>{let n=e.width?String(e.width):t.defaultWidth;return t.formats[n]||t.formats[t.defaultWidth]}}},94940:(t,e,n)=>{n.d(e,{o:()=>a});function a(t){return(e,n)=>{let a;if("formatting"===(n?.context?String(n.context):"standalone")&&t.formattingValues){let e=t.defaultFormattingWidth||t.defaultWidth,o=n?.width?String(n.width):e;a=t.formattingValues[o]||t.formattingValues[e]}else{let e=t.defaultWidth,o=n?.width?String(n.width):t.defaultWidth;a=t.values[o]||t.values[e]}return a[t.argumentCallback?t.argumentCallback(e):e]}}},44754:(t,e,n)=>{function a(t){return(e,n={})=>{let a;let o=n.width,r=o&&t.matchPatterns[o]||t.matchPatterns[t.defaultMatchWidth],i=e.match(r);if(!i)return null;let u=i[0],s=o&&t.parsePatterns[o]||t.parsePatterns[t.defaultParseWidth],d=Array.isArray(s)?function(t,e){for(let n=0;n<t.length;n++)if(e(t[n]))return n}(s,t=>t.test(u)):function(t,e){for(let n in t)if(Object.prototype.hasOwnProperty.call(t,n)&&e(t[n]))return n}(s,t=>t.test(u));return a=t.valueCallback?t.valueCallback(d):d,{value:a=n.valueCallback?n.valueCallback(a):a,rest:e.slice(u.length)}}}n.d(e,{A:()=>a})},71886:(t,e,n)=>{n.d(e,{K:()=>a});function a(t){return(e,n={})=>{let a=e.match(t.matchPattern);if(!a)return null;let o=a[0],r=e.match(t.parsePattern);if(!r)return null;let i=t.valueCallback?t.valueCallback(r[0]):r[0];return{value:i=n.valueCallback?n.valueCallback(i):i,rest:e.slice(o.length)}}}},35344:(t,e,n)=>{function a(t,e,n){return(1===e&&t.one?t.one:e>=2&&e<=4&&t.twoFour?t.twoFour:t.other)[n].replace("{{count}}",String(e))}function o(t){let e="";return"almost"===t&&(e="takmer"),"about"===t&&(e="približne"),e.length>0?e+" ":""}function r(t){let e="";return"lessThan"===t&&(e="menej než"),"over"===t&&(e="viac než"),e.length>0?e+" ":""}n.r(e),n.d(e,{default:()=>b,sk:()=>v});let i={xSeconds:{one:{present:"sekunda",past:"sekundou",future:"sekundu"},twoFour:{present:"{{count}} sekundy",past:"{{count}} sekundami",future:"{{count}} sekundy"},other:{present:"{{count}} sek\xfand",past:"{{count}} sekundami",future:"{{count}} sek\xfand"}},halfAMinute:{other:{present:"pol min\xfaty",past:"pol min\xfatou",future:"pol min\xfaty"}},xMinutes:{one:{present:"min\xfata",past:"min\xfatou",future:"min\xfatu"},twoFour:{present:"{{count}} min\xfaty",past:"{{count}} min\xfatami",future:"{{count}} min\xfaty"},other:{present:"{{count}} min\xfat",past:"{{count}} min\xfatami",future:"{{count}} min\xfat"}},xHours:{one:{present:"hodina",past:"hodinou",future:"hodinu"},twoFour:{present:"{{count}} hodiny",past:"{{count}} hodinami",future:"{{count}} hodiny"},other:{present:"{{count}} hod\xedn",past:"{{count}} hodinami",future:"{{count}} hod\xedn"}},xDays:{one:{present:"deň",past:"dňom",future:"deň"},twoFour:{present:"{{count}} dni",past:"{{count}} dňami",future:"{{count}} dni"},other:{present:"{{count}} dn\xed",past:"{{count}} dňami",future:"{{count}} dn\xed"}},xWeeks:{one:{present:"t\xfdždeň",past:"t\xfdždňom",future:"t\xfdždeň"},twoFour:{present:"{{count}} t\xfdždne",past:"{{count}} t\xfdždňami",future:"{{count}} t\xfdždne"},other:{present:"{{count}} t\xfdždňov",past:"{{count}} t\xfdždňami",future:"{{count}} t\xfdždňov"}},xMonths:{one:{present:"mesiac",past:"mesiacom",future:"mesiac"},twoFour:{present:"{{count}} mesiace",past:"{{count}} mesiacmi",future:"{{count}} mesiace"},other:{present:"{{count}} mesiacov",past:"{{count}} mesiacmi",future:"{{count}} mesiacov"}},xYears:{one:{present:"rok",past:"rokom",future:"rok"},twoFour:{present:"{{count}} roky",past:"{{count}} rokmi",future:"{{count}} roky"},other:{present:"{{count}} rokov",past:"{{count}} rokmi",future:"{{count}} rokov"}}};var u=n(78526);let s={date:(0,u.k)({formats:{full:"EEEE d. MMMM y",long:"d. MMMM y",medium:"d. M. y",short:"d. M. y"},defaultWidth:"full"}),time:(0,u.k)({formats:{full:"H:mm:ss zzzz",long:"H:mm:ss z",medium:"H:mm:ss",short:"H:mm"},defaultWidth:"full"}),dateTime:(0,u.k)({formats:{full:"{{date}}, {{time}}",long:"{{date}}, {{time}}",medium:"{{date}}, {{time}}",short:"{{date}} {{time}}"},defaultWidth:"full"})};var d=n(40165);let l=["nedeľu","pondelok","utorok","stredu","štvrtok","piatok","sobotu"];function p(t){let e=l[t];return 4===t?"'vo' eeee 'o' p":"'v "+e+" o' p"}let m={lastWeek:(t,e,n)=>{let a=t.getDay();return(0,d.R)(t,e,n)?p(a):function(t){let e=l[t];switch(t){case 0:case 3:case 6:return"'minul\xfa "+e+" o' p";default:return"'minul\xfd' eeee 'o' p"}}(a)},yesterday:"'včera o' p",today:"'dnes o' p",tomorrow:"'zajtra o' p",nextWeek:(t,e,n)=>{let a=t.getDay();return(0,d.R)(t,e,n)?p(a):function(t){let e=l[t];switch(t){case 0:case 4:case 6:return"'bud\xfacu "+e+" o' p";default:return"'bud\xfaci' eeee 'o' p"}}(a)},other:"P"};var c=n(94940);let f={ordinalNumber:(t,e)=>Number(t)+".",era:(0,c.o)({values:{narrow:["pred Kr.","po Kr."],abbreviated:["pred Kr.","po Kr."],wide:["pred Kristom","po Kristovi"]},defaultWidth:"wide"}),quarter:(0,c.o)({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1. štvrťrok","2. štvrťrok","3. štvrťrok","4. štvrťrok"]},defaultWidth:"wide",argumentCallback:t=>t-1}),month:(0,c.o)({values:{narrow:["j","f","m","a","m","j","j","a","s","o","n","d"],abbreviated:["jan","feb","mar","apr","m\xe1j","j\xfan","j\xfal","aug","sep","okt","nov","dec"],wide:["janu\xe1r","febru\xe1r","marec","apr\xedl","m\xe1j","j\xfan","j\xfal","august","september","okt\xf3ber","november","december"]},defaultWidth:"wide",formattingValues:{narrow:["j","f","m","a","m","j","j","a","s","o","n","d"],abbreviated:["jan","feb","mar","apr","m\xe1j","j\xfan","j\xfal","aug","sep","okt","nov","dec"],wide:["janu\xe1ra","febru\xe1ra","marca","apr\xedla","m\xe1ja","j\xfana","j\xfala","augusta","septembra","okt\xf3bra","novembra","decembra"]},defaultFormattingWidth:"wide"}),day:(0,c.o)({values:{narrow:["n","p","u","s","š","p","s"],short:["ne","po","ut","st","št","pi","so"],abbreviated:["ne","po","ut","st","št","pi","so"],wide:["nedeľa","pondelok","utorok","streda","štvrtok","piatok","sobota"]},defaultWidth:"wide"}),dayPeriod:(0,c.o)({values:{narrow:{am:"AM",pm:"PM",midnight:"poln.",noon:"pol.",morning:"r\xe1no",afternoon:"pop.",evening:"več.",night:"noc"},abbreviated:{am:"AM",pm:"PM",midnight:"poln.",noon:"pol.",morning:"r\xe1no",afternoon:"popol.",evening:"večer",night:"noc"},wide:{am:"AM",pm:"PM",midnight:"polnoc",noon:"poludnie",morning:"r\xe1no",afternoon:"popoludnie",evening:"večer",night:"noc"}},defaultWidth:"wide",formattingValues:{narrow:{am:"AM",pm:"PM",midnight:"o poln.",noon:"nap.",morning:"r\xe1no",afternoon:"pop.",evening:"več.",night:"v n."},abbreviated:{am:"AM",pm:"PM",midnight:"o poln.",noon:"napol.",morning:"r\xe1no",afternoon:"popol.",evening:"večer",night:"v noci"},wide:{am:"AM",pm:"PM",midnight:"o polnoci",noon:"napoludnie",morning:"r\xe1no",afternoon:"popoludn\xed",evening:"večer",night:"v noci"}},defaultFormattingWidth:"wide"})};var h=n(44754);let v={code:"sk",formatDistance:(t,e,n)=>{let u=["lessThan","about","over","almost"].filter(function(e){return!!t.match(RegExp("^"+e))})[0]||"",s=i[function(t){return t.charAt(0).toLowerCase()+t.slice(1)}(t.substring(u.length))];return n?.addSuffix?n.comparison&&n.comparison>0?o(u)+"o "+r(u)+a(s,e,"future"):o(u)+"pred "+r(u)+a(s,e,"past"):o(u)+r(u)+a(s,e,"present")},formatLong:s,formatRelative:(t,e,n,a)=>{let o=m[t];return"function"==typeof o?o(e,n,a):o},localize:f,match:{ordinalNumber:(0,n(71886).K)({matchPattern:/^(\d+)\.?/i,parsePattern:/\d+/i,valueCallback:t=>parseInt(t,10)}),era:(0,h.A)({matchPatterns:{narrow:/^(pred Kr\.|pred n\. l\.|po Kr\.|n\. l\.)/i,abbreviated:/^(pred Kr\.|pred n\. l\.|po Kr\.|n\. l\.)/i,wide:/^(pred Kristom|pred na[šs][íi]m letopo[čc]tom|po Kristovi|n[áa][šs]ho letopo[čc]tu)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^pr/i,/^(po|n)/i]},defaultParseWidth:"any"}),quarter:(0,h.A)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234]\. [šs]tvr[ťt]rok/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:t=>t+1}),month:(0,h.A)({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|m[áa]j|j[úu]n|j[úu]l|aug|sep|okt|nov|dec)/i,wide:/^(janu[áa]ra?|febru[áa]ra?|(marec|marca)|apr[íi]la?|m[áa]ja?|j[úu]na?|j[úu]la?|augusta?|(september|septembra)|(okt[óo]ber|okt[óo]bra)|(november|novembra)|(december|decembra))/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^m[áa]j/i,/^j[úu]n/i,/^j[úu]l/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:(0,h.A)({matchPatterns:{narrow:/^[npusšp]/i,short:/^(ne|po|ut|st|št|pi|so)/i,abbreviated:/^(ne|po|ut|st|št|pi|so)/i,wide:/^(nede[ľl]a|pondelok|utorok|streda|[šs]tvrtok|piatok|sobota])/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^n/i,/^p/i,/^u/i,/^s/i,/^š/i,/^p/i,/^s/i],any:[/^n/i,/^po/i,/^u/i,/^st/i,/^(št|stv)/i,/^pi/i,/^so/i]},defaultParseWidth:"any"}),dayPeriod:(0,h.A)({matchPatterns:{narrow:/^(am|pm|(o )?poln\.?|(nap\.?|pol\.?)|r[áa]no|pop\.?|ve[čc]\.?|(v n\.?|noc))/i,abbreviated:/^(am|pm|(o )?poln\.?|(napol\.?|pol\.?)|r[áa]no|pop\.?|ve[čc]er|(v )?noci?)/i,any:/^(am|pm|(o )?polnoci?|(na)?poludnie|r[áa]no|popoludn(ie|í|i)|ve[čc]er|(v )?noci?)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^am/i,pm:/^pm/i,midnight:/poln/i,noon:/^(nap|(na)?pol(\.|u))/i,morning:/^r[áa]no/i,afternoon:/^pop/i,evening:/^ve[čc]/i,night:/^(noc|v n\.)/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:4}},b=v},87046:(t,e,n)=>{n.d(e,{a:()=>o});var a=n(57841);function o(t,e){return(0,a.w)(e||t,t)}}};