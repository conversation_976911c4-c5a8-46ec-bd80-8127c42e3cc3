globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/my-route/route"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\node_modules\\@payloadcms\\next\\dist\\prod\\styles.css":{"id":2280,"name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\node_modules\\@payloadcms\\next\\dist\\esm\\prod\\styles.css":{"id":2280,"name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\":[],"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\my-route\\route":[]},"rscModuleMapping":{"2280":{"*":{"id":"63046","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}