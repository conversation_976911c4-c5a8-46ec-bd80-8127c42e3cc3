"use strict";exports.id=5452,exports.ids=[5452],exports.modules={99702:(e,t,n)=>{n.d(t,{w:()=>i});var r=n(14723);let i=(e,t)=>(0,r.Px)(e,t).then(e=>t.utf8Encoder(e))},45452:(e,t,n)=>{let r;n.d(t,{getDefaultRoleAssumer:()=>nz,getDefaultRoleAssumerWithWebIdentity:()=>nL});var i=n(10531),s=n(45275),o=n(7033),a=n(90105),l=n(26288),u=n(11146),d=n(20068),c=n(38713),p=n(40089),h=n(39775),f=n(81522),g=n(14723),m=n(60720),x=n(26771);let b=async(e,t,n)=>({operation:(0,x.u)(t).operation,region:await (0,x.t)(e.region)()||(()=>{throw Error("expected `region` to be configured for `aws.auth#sigv4`")})()}),y=e=>{let t=[];return"AssumeRoleWithWebIdentity"===e.operation?t.push({schemeId:"smithy.api#noAuth"}):t.push({schemeId:"aws.auth#sigv4",signingProperties:{name:"sts",region:e.region},propertiesExtractor:(e,t)=>({signingProperties:{config:e,context:t}})}),t},E=e=>Object.assign(e,{stsClientCtor:eN}),v=e=>{let t=E(e);return Object.assign((0,m.h)(t),{authSchemePreference:(0,x.t)(e.authSchemePreference??[])})},S=e=>Object.assign(e,{useDualstackEndpoint:e.useDualstackEndpoint??!1,useFipsEndpoint:e.useFipsEndpoint??!1,useGlobalEndpoint:e.useGlobalEndpoint??!1,defaultSigningName:"sts"}),w={UseGlobalEndpoint:{type:"builtInParams",name:"useGlobalEndpoint"},UseFIPS:{type:"builtInParams",name:"useFipsEndpoint"},Endpoint:{type:"builtInParams",name:"endpoint"},Region:{type:"builtInParams",name:"region"},UseDualStack:{type:"builtInParams",name:"useDualstackEndpoint"}};var P=n(43279),I=n(78398),T=n(79659),A=n(90241),N=n(67591),C=n(15816),O=n(11101),k=n(99540),D=n(52934),$=n(36316),R=n(27426),F=n(72629),M=n(91357),j=n(44216),K=n(28045),_=n(92518);let U="required",V="type",W="argv",z="booleanEquals",L="stringEquals",q="sigv4",B="us-east-1",Y="endpoint",H="https://sts.{Region}.{PartitionResult#dnsSuffix}",G="tree",Z="error",X="getAttr",J={[U]:!1,[V]:"String"},Q={[U]:!0,default:!1,[V]:"Boolean"},ee={ref:"Endpoint"},et={fn:"isSet",[W]:[{ref:"Region"}]},en={ref:"Region"},er={fn:"aws.partition",[W]:[en],assign:"PartitionResult"},ei={ref:"UseFIPS"},es={ref:"UseDualStack"},eo={url:"https://sts.amazonaws.com",properties:{authSchemes:[{name:q,signingName:"sts",signingRegion:B}]},headers:{}},ea={},el={conditions:[{fn:L,[W]:[en,"aws-global"]}],[Y]:eo,[V]:Y},eu={fn:z,[W]:[ei,!0]},ed={fn:z,[W]:[es,!0]},ec={fn:X,[W]:[{ref:"PartitionResult"},"supportsFIPS"]},ep={ref:"PartitionResult"},eh={fn:z,[W]:[!0,{fn:X,[W]:[ep,"supportsDualStack"]}]},ef=[{fn:"isSet",[W]:[ee]}],eg=[eu],em=[ed],ex={version:"1.0",parameters:{Region:J,UseDualStack:Q,UseFIPS:Q,Endpoint:J,UseGlobalEndpoint:Q},rules:[{conditions:[{fn:z,[W]:[{ref:"UseGlobalEndpoint"},!0]},{fn:"not",[W]:ef},et,er,{fn:z,[W]:[ei,!1]},{fn:z,[W]:[es,!1]}],rules:[{conditions:[{fn:L,[W]:[en,"ap-northeast-1"]}],endpoint:eo,[V]:Y},{conditions:[{fn:L,[W]:[en,"ap-south-1"]}],endpoint:eo,[V]:Y},{conditions:[{fn:L,[W]:[en,"ap-southeast-1"]}],endpoint:eo,[V]:Y},{conditions:[{fn:L,[W]:[en,"ap-southeast-2"]}],endpoint:eo,[V]:Y},el,{conditions:[{fn:L,[W]:[en,"ca-central-1"]}],endpoint:eo,[V]:Y},{conditions:[{fn:L,[W]:[en,"eu-central-1"]}],endpoint:eo,[V]:Y},{conditions:[{fn:L,[W]:[en,"eu-north-1"]}],endpoint:eo,[V]:Y},{conditions:[{fn:L,[W]:[en,"eu-west-1"]}],endpoint:eo,[V]:Y},{conditions:[{fn:L,[W]:[en,"eu-west-2"]}],endpoint:eo,[V]:Y},{conditions:[{fn:L,[W]:[en,"eu-west-3"]}],endpoint:eo,[V]:Y},{conditions:[{fn:L,[W]:[en,"sa-east-1"]}],endpoint:eo,[V]:Y},{conditions:[{fn:L,[W]:[en,B]}],endpoint:eo,[V]:Y},{conditions:[{fn:L,[W]:[en,"us-east-2"]}],endpoint:eo,[V]:Y},{conditions:[{fn:L,[W]:[en,"us-west-1"]}],endpoint:eo,[V]:Y},{conditions:[{fn:L,[W]:[en,"us-west-2"]}],endpoint:eo,[V]:Y},{endpoint:{url:H,properties:{authSchemes:[{name:q,signingName:"sts",signingRegion:"{Region}"}]},headers:ea},[V]:Y}],[V]:G},{conditions:ef,rules:[{conditions:eg,error:"Invalid Configuration: FIPS and custom endpoint are not supported",[V]:Z},{conditions:em,error:"Invalid Configuration: Dualstack and custom endpoint are not supported",[V]:Z},{endpoint:{url:ee,properties:ea,headers:ea},[V]:Y}],[V]:G},{conditions:[et],rules:[{conditions:[er],rules:[{conditions:[eu,ed],rules:[{conditions:[{fn:z,[W]:[!0,ec]},eh],rules:[{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:ea,headers:ea},[V]:Y}],[V]:G},{error:"FIPS and DualStack are enabled, but this partition does not support one or both",[V]:Z}],[V]:G},{conditions:eg,rules:[{conditions:[{fn:z,[W]:[ec,!0]}],rules:[{conditions:[{fn:L,[W]:[{fn:X,[W]:[ep,"name"]},"aws-us-gov"]}],endpoint:{url:"https://sts.{Region}.amazonaws.com",properties:ea,headers:ea},[V]:Y},{endpoint:{url:"https://sts-fips.{Region}.{PartitionResult#dnsSuffix}",properties:ea,headers:ea},[V]:Y}],[V]:G},{error:"FIPS is enabled but this partition does not support FIPS",[V]:Z}],[V]:G},{conditions:em,rules:[{conditions:[eh],rules:[{endpoint:{url:"https://sts.{Region}.{PartitionResult#dualStackDnsSuffix}",properties:ea,headers:ea},[V]:Y}],[V]:G},{error:"DualStack is enabled but this partition does not support DualStack",[V]:Z}],[V]:G},el,{endpoint:{url:H,properties:ea,headers:ea},[V]:Y}],[V]:G}],[V]:G},{error:"Invalid Configuration: Missing Region",[V]:Z}]},eb=new _.kS({size:50,params:["Endpoint","Region","UseDualStack","UseFIPS","UseGlobalEndpoint"]}),ey=(e,t={})=>eb.get(e,()=>(0,_.sO)(ex,{endpointParams:e,logger:t.logger}));_.mw.aws=K.UF;let eE=e=>({apiVersion:"2011-06-15",base64Decoder:e?.base64Decoder??M.E,base64Encoder:e?.base64Encoder??M.n,disableHostPrefix:e?.disableHostPrefix??!1,endpointProvider:e?.endpointProvider??ey,extensions:e?.extensions??[],httpAuthSchemeProvider:e?.httpAuthSchemeProvider??y,httpAuthSchemes:e?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:e=>e.getIdentityProvider("aws.auth#sigv4"),signer:new A.f2},{schemeId:"smithy.api#noAuth",identityProvider:e=>e.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new C.m}],logger:e?.logger??new g.N4,serviceId:e?.serviceId??"STS",urlParser:e?.urlParser??F.D,utf8Decoder:e?.utf8Decoder??j.ar,utf8Encoder:e?.utf8Encoder??j.Pq});var ev=n(68746);let eS=e=>{(0,g.I9)(process.version);let t=(0,ev.I)(e),n=()=>t().then(g.lT),r=eE(e);(0,I.I)(process.version);let i={profile:e?.profile,logger:r.logger};return{...r,...e,runtime:"node",defaultsMode:t,authSchemePreference:e?.authSchemePreference??(0,k.Z)(T.$,i),bodyLengthChecker:e?.bodyLengthChecker??$.n,defaultUserAgentProvider:e?.defaultUserAgentProvider??(0,N.pf)({serviceId:r.serviceId,clientVersion:P.rE}),httpAuthSchemes:e?.httpAuthSchemes??[{schemeId:"aws.auth#sigv4",identityProvider:t=>t.getIdentityProvider("aws.auth#sigv4")||(async t=>await e.credentialDefaultProvider(t?.__config||{})()),signer:new A.f2},{schemeId:"smithy.api#noAuth",identityProvider:e=>e.getIdentityProvider("smithy.api#noAuth")||(async()=>({})),signer:new C.m}],maxAttempts:e?.maxAttempts??(0,k.Z)(f.qs,e),region:e?.region??(0,k.Z)(l.GG,{...l.zH,...i}),requestHandler:D.$c.create(e?.requestHandler??n),retryMode:e?.retryMode??(0,k.Z)({...f.kN,default:async()=>(await n()).retryMode||R.L0},e),sha256:e?.sha256??O.V.bind(null,"sha256"),streamCollector:e?.streamCollector??D.kv,useDualstackEndpoint:e?.useDualstackEndpoint??(0,k.Z)(l.e$,i),useFipsEndpoint:e?.useFipsEndpoint??(0,k.Z)(l.Ko,i),userAgentAppId:e?.userAgentAppId??(0,k.Z)(N.hV,i)}};var ew=n(71168),eP=n(37250);let eI=e=>{let t=e.httpAuthSchemes,n=e.httpAuthSchemeProvider,r=e.credentials;return{setHttpAuthScheme(e){let n=t.findIndex(t=>t.schemeId===e.schemeId);-1===n?t.push(e):t.splice(n,1,e)},httpAuthSchemes:()=>t,setHttpAuthSchemeProvider(e){n=e},httpAuthSchemeProvider:()=>n,setCredentials(e){r=e},credentials:()=>r}},eT=e=>({httpAuthSchemes:e.httpAuthSchemes(),httpAuthSchemeProvider:e.httpAuthSchemeProvider(),credentials:e.credentials()}),eA=(e,t)=>{let n=Object.assign((0,ew.Rq)(e),(0,g.xA)(e),(0,eP.eS)(e),eI(e));return t.forEach(e=>e.configure(n)),Object.assign(e,(0,ew.$3)(n),(0,g.uv)(n),(0,eP.jt)(n),eT(n))};class eN extends g.Kj{config;constructor(...[e]){let t=eS(e||{});super(t),this.initConfig=t;let n=S(t),r=(0,a.Dc)(n),g=(0,f.$z)(r),m=(0,l.TD)(g),x=(0,i.OV)(m),y=eA(v((0,h.Co)(x)),e?.extensions||[]);this.config=y,this.middlewareStack.use((0,a.sM)(this.config)),this.middlewareStack.use((0,f.ey)(this.config)),this.middlewareStack.use((0,p.vK)(this.config)),this.middlewareStack.use((0,i.TC)(this.config)),this.middlewareStack.use((0,s.Y7)(this.config)),this.middlewareStack.use((0,o.n4)(this.config)),this.middlewareStack.use((0,u.w)(this.config,{httpAuthSchemeParametersProvider:b,identityProviderConfigProvider:async e=>new d.h({"aws.auth#sigv4":e.credentials})})),this.middlewareStack.use((0,c.l)(this.config))}destroy(){super.destroy()}}var eC=n(20318);class eO extends g.TJ{constructor(e){super(e),Object.setPrototypeOf(this,eO.prototype)}}let ek=e=>({...e,...e.SecretAccessKey&&{SecretAccessKey:g.$H}}),eD=e=>({...e,...e.Credentials&&{Credentials:ek(e.Credentials)}});class e$ extends eO{name="ExpiredTokenException";$fault="client";constructor(e){super({name:"ExpiredTokenException",$fault:"client",...e}),Object.setPrototypeOf(this,e$.prototype)}}class eR extends eO{name="MalformedPolicyDocumentException";$fault="client";constructor(e){super({name:"MalformedPolicyDocumentException",$fault:"client",...e}),Object.setPrototypeOf(this,eR.prototype)}}class eF extends eO{name="PackedPolicyTooLargeException";$fault="client";constructor(e){super({name:"PackedPolicyTooLargeException",$fault:"client",...e}),Object.setPrototypeOf(this,eF.prototype)}}class eM extends eO{name="RegionDisabledException";$fault="client";constructor(e){super({name:"RegionDisabledException",$fault:"client",...e}),Object.setPrototypeOf(this,eM.prototype)}}class ej extends eO{name="IDPRejectedClaimException";$fault="client";constructor(e){super({name:"IDPRejectedClaimException",$fault:"client",...e}),Object.setPrototypeOf(this,ej.prototype)}}class eK extends eO{name="InvalidIdentityTokenException";$fault="client";constructor(e){super({name:"InvalidIdentityTokenException",$fault:"client",...e}),Object.setPrototypeOf(this,eK.prototype)}}let e_=e=>({...e,...e.WebIdentityToken&&{WebIdentityToken:g.$H}}),eU=e=>({...e,...e.Credentials&&{Credentials:ek(e.Credentials)}});class eV extends eO{name="IDPCommunicationErrorException";$fault="client";constructor(e){super({name:"IDPCommunicationErrorException",$fault:"client",...e}),Object.setPrototypeOf(this,eV.prototype)}}let eW={preserveOrder:!1,attributeNamePrefix:"@_",attributesGroupName:!1,textNodeName:"#text",ignoreAttributes:!0,removeNSPrefix:!1,allowBooleanAttributes:!1,parseTagValue:!0,parseAttributeValue:!1,trimValues:!0,cdataPropName:!1,numberParseOptions:{hex:!0,leadingZeros:!0,eNotation:!0},tagValueProcessor:function(e,t){return t},attributeValueProcessor:function(e,t){return t},stopNodes:[],alwaysCreateTextNode:!1,isArray:()=>!1,commentPropName:!1,unpairedTags:[],processEntities:!0,htmlEntities:!1,ignoreDeclaration:!1,ignorePiTags:!1,transformTagName:!1,transformAttributeName:!1,updateTag:function(e,t,n){return e},captureMetaData:!1},ez=":A-Za-z_\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD",eL=RegExp("^"+("["+ez+"][")+ez+"\\-.\\d\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$");function eq(e,t){let n=[],r=t.exec(e);for(;r;){let i=[];i.startIndex=t.lastIndex-r[0].length;let s=r.length;for(let e=0;e<s;e++)i.push(r[e]);n.push(i),r=t.exec(e)}return n}let eB=function(e){return null!=eL.exec(e)};r="function"!=typeof Symbol?"@@xmlMetadata":Symbol("XML Node Metadata");class eY{constructor(e){this.tagname=e,this.child=[],this[":@"]={}}add(e,t){"__proto__"===e&&(e="#__proto__"),this.child.push({[e]:t})}addChild(e,t){"__proto__"===e.tagname&&(e.tagname="#__proto__"),e[":@"]&&Object.keys(e[":@"]).length>0?this.child.push({[e.tagname]:e.child,":@":e[":@"]}):this.child.push({[e.tagname]:e.child}),void 0!==t&&(this.child[this.child.length-1][r]={startIndex:t})}static getMetaDataSymbol(){return r}}let eH=(e,t)=>{for(;t<e.length&&/\s/.test(e[t]);)t++;return t};function eG(e,t,n){let r="",i=e[t];if('"'!==i&&"'"!==i)throw Error(`Expected quoted string, found "${i}"`);for(t++;t<e.length&&e[t]!==i;)r+=e[t],t++;if(e[t]!==i)throw Error(`Unterminated ${n} value`);return[++t,r]}function eZ(e,t,n){for(let r=0;r<t.length;r++)if(t[r]!==e[n+r+1])return!1;return!0}function eX(e){if(eB(e))return e;throw Error(`Invalid entity name ${e}`)}let eJ=/^[-+]?0x[a-fA-F0-9]+$/,eQ=/^([\-\+])?(0*)([0-9]*(\.[0-9]*)?)$/,e0={hex:!0,leadingZeros:!0,decimalPoint:".",eNotation:!0},e1=/^([-+])?(0*)(\d*(\.\d*)?[eE][-\+]?\d+)$/;class e2{constructor(e){this.options=e,this.currentNode=null,this.tagsNodeStack=[],this.docTypeEntities={},this.lastEntities={apos:{regex:/&(apos|#39|#x27);/g,val:"'"},gt:{regex:/&(gt|#62|#x3E);/g,val:">"},lt:{regex:/&(lt|#60|#x3C);/g,val:"<"},quot:{regex:/&(quot|#34|#x22);/g,val:'"'}},this.ampEntity={regex:/&(amp|#38|#x26);/g,val:"&"},this.htmlEntities={space:{regex:/&(nbsp|#160);/g,val:" "},cent:{regex:/&(cent|#162);/g,val:"\xa2"},pound:{regex:/&(pound|#163);/g,val:"\xa3"},yen:{regex:/&(yen|#165);/g,val:"\xa5"},euro:{regex:/&(euro|#8364);/g,val:"€"},copyright:{regex:/&(copy|#169);/g,val:"\xa9"},reg:{regex:/&(reg|#174);/g,val:"\xae"},inr:{regex:/&(inr|#8377);/g,val:"₹"},num_dec:{regex:/&#([0-9]{1,7});/g,val:(e,t)=>String.fromCodePoint(Number.parseInt(t,10))},num_hex:{regex:/&#x([0-9a-fA-F]{1,6});/g,val:(e,t)=>String.fromCodePoint(Number.parseInt(t,16))}},this.addExternalEntities=e3,this.parseXml=e8,this.parseTextData=e4,this.resolveNameSpace=e6,this.buildAttributesMap=e5,this.isItStopNode=tn,this.replaceEntitiesValue=te,this.readStopNodeData=ts,this.saveTextToParentTag=tt,this.addChild=e9,this.ignoreAttributesFn=function(e){return"function"==typeof e?e:Array.isArray(e)?t=>{for(let n of e)if("string"==typeof n&&t===n||n instanceof RegExp&&n.test(t))return!0}:()=>!1}(this.options.ignoreAttributes)}}function e3(e){let t=Object.keys(e);for(let n=0;n<t.length;n++){let r=t[n];this.lastEntities[r]={regex:RegExp("&"+r+";","g"),val:e[r]}}}function e4(e,t,n,r,i,s,o){if(void 0!==e&&(this.options.trimValues&&!r&&(e=e.trim()),e.length>0)){o||(e=this.replaceEntitiesValue(e));let r=this.options.tagValueProcessor(t,e,n,i,s);return null==r?e:typeof r!=typeof e||r!==e?r:this.options.trimValues?to(e,this.options.parseTagValue,this.options.numberParseOptions):e.trim()===e?to(e,this.options.parseTagValue,this.options.numberParseOptions):e}}function e6(e){if(this.options.removeNSPrefix){let t=e.split(":"),n="/"===e.charAt(0)?"/":"";if("xmlns"===t[0])return"";2===t.length&&(e=n+t[1])}return e}let e7=RegExp("([^\\s=]+)\\s*(=\\s*(['\"])([\\s\\S]*?)\\3)?","gm");function e5(e,t,n){if(!0!==this.options.ignoreAttributes&&"string"==typeof e){let n=eq(e,e7),r=n.length,i={};for(let e=0;e<r;e++){let r=this.resolveNameSpace(n[e][1]);if(this.ignoreAttributesFn(r,t))continue;let s=n[e][4],o=this.options.attributeNamePrefix+r;if(r.length){if(this.options.transformAttributeName&&(o=this.options.transformAttributeName(o)),"__proto__"===o&&(o="#__proto__"),void 0!==s){this.options.trimValues&&(s=s.trim()),s=this.replaceEntitiesValue(s);let e=this.options.attributeValueProcessor(r,s,t);null==e?i[o]=s:typeof e!=typeof s||e!==s?i[o]=e:i[o]=to(s,this.options.parseAttributeValue,this.options.numberParseOptions)}else this.options.allowBooleanAttributes&&(i[o]=!0)}}if(Object.keys(i).length){if(this.options.attributesGroupName){let e={};return e[this.options.attributesGroupName]=i,e}return i}}}let e8=function(e){e=e.replace(/\r\n?/g,"\n");let t=new eY("!xml"),n=t,r="",i="";for(let s=0;s<e.length;s++)if("<"===e[s]){if("/"===e[s+1]){let t=tr(e,">",s,"Closing Tag is not closed."),o=e.substring(s+2,t).trim();if(this.options.removeNSPrefix){let e=o.indexOf(":");-1!==e&&(o=o.substr(e+1))}this.options.transformTagName&&(o=this.options.transformTagName(o)),n&&(r=this.saveTextToParentTag(r,n,i));let a=i.substring(i.lastIndexOf(".")+1);if(o&&-1!==this.options.unpairedTags.indexOf(o))throw Error(`Unpaired tag can not be used as closing tag: </${o}>`);let l=0;a&&-1!==this.options.unpairedTags.indexOf(a)?(l=i.lastIndexOf(".",i.lastIndexOf(".")-1),this.tagsNodeStack.pop()):l=i.lastIndexOf("."),i=i.substring(0,l),n=this.tagsNodeStack.pop(),r="",s=t}else if("?"===e[s+1]){let t=ti(e,s,!1,"?>");if(!t)throw Error("Pi Tag is not closed.");if(r=this.saveTextToParentTag(r,n,i),this.options.ignoreDeclaration&&"?xml"===t.tagName||this.options.ignorePiTags);else{let e=new eY(t.tagName);e.add(this.options.textNodeName,""),t.tagName!==t.tagExp&&t.attrExpPresent&&(e[":@"]=this.buildAttributesMap(t.tagExp,i,t.tagName)),this.addChild(n,e,i,s)}s=t.closeIndex+1}else if("!--"===e.substr(s+1,3)){let t=tr(e,"-->",s+4,"Comment is not closed.");if(this.options.commentPropName){let o=e.substring(s+4,t-2);r=this.saveTextToParentTag(r,n,i),n.add(this.options.commentPropName,[{[this.options.textNodeName]:o}])}s=t}else if("!D"===e.substr(s+1,2)){let t=function(e,t){let n={};if("O"===e[t+3]&&"C"===e[t+4]&&"T"===e[t+5]&&"Y"===e[t+6]&&"P"===e[t+7]&&"E"===e[t+8]){t+=9;let r=1,i=!1,s=!1;for(;t<e.length;t++)if("<"!==e[t]||s){if(">"===e[t]){if(s?"-"===e[t-1]&&"-"===e[t-2]&&(s=!1,r--):r--,0===r)break}else"["===e[t]?i=!0:e[t]}else{if(i&&eZ(e,"!ENTITY",t)){let r,i;t+=7,[r,i,t]=function(e,t){t=eH(e,t);let n="";for(;t<e.length&&!/\s/.test(e[t])&&'"'!==e[t]&&"'"!==e[t];)n+=e[t],t++;if(eX(n),t=eH(e,t),"SYSTEM"===e.substring(t,t+6).toUpperCase())throw Error("External entities are not supported");if("%"===e[t])throw Error("Parameter entities are not supported");let r="";return[t,r]=eG(e,t,"entity"),[n,r,--t]}(e,t+1),-1===i.indexOf("&")&&(n[r]={regx:RegExp(`&${r};`,"g"),val:i})}else if(i&&eZ(e,"!ELEMENT",t)){let{index:n}=function(e,t){t=eH(e,t);let n="";for(;t<e.length&&!/\s/.test(e[t]);)n+=e[t],t++;if(!eX(n))throw Error(`Invalid element name: "${n}"`);t=eH(e,t);let r="";if("E"===e[t]&&eZ(e,"MPTY",t))t+=4;else if("A"===e[t]&&eZ(e,"NY",t))t+=2;else if("("===e[t]){for(t++;t<e.length&&")"!==e[t];)r+=e[t],t++;if(")"!==e[t])throw Error("Unterminated content model")}else throw Error(`Invalid Element Expression, found "${e[t]}"`);return{elementName:n,contentModel:r.trim(),index:t}}(e,(t+=8)+1);t=n}else if(i&&eZ(e,"!ATTLIST",t))t+=8;else if(i&&eZ(e,"!NOTATION",t)){let{index:n}=function(e,t){t=eH(e,t);let n="";for(;t<e.length&&!/\s/.test(e[t]);)n+=e[t],t++;eX(n),t=eH(e,t);let r=e.substring(t,t+6).toUpperCase();if("SYSTEM"!==r&&"PUBLIC"!==r)throw Error(`Expected SYSTEM or PUBLIC, found "${r}"`);t+=r.length,t=eH(e,t);let i=null,s=null;if("PUBLIC"===r)[t,i]=eG(e,t,"publicIdentifier"),t=eH(e,t),('"'===e[t]||"'"===e[t])&&([t,s]=eG(e,t,"systemIdentifier"));else if("SYSTEM"===r&&([t,s]=eG(e,t,"systemIdentifier"),!s))throw Error("Missing mandatory system identifier for SYSTEM notation");return{notationName:n,publicIdentifier:i,systemIdentifier:s,index:--t}}(e,(t+=9)+1);t=n}else if(eZ(e,"!--",t))s=!0;else throw Error("Invalid DOCTYPE");r++}if(0!==r)throw Error("Unclosed DOCTYPE")}else throw Error("Invalid Tag instead of DOCTYPE");return{entities:n,i:t}}(e,s);this.docTypeEntities=t.entities,s=t.i}else if("!["===e.substr(s+1,2)){let t=tr(e,"]]>",s,"CDATA is not closed.")-2,o=e.substring(s+9,t);r=this.saveTextToParentTag(r,n,i);let a=this.parseTextData(o,n.tagname,i,!0,!1,!0,!0);void 0==a&&(a=""),this.options.cdataPropName?n.add(this.options.cdataPropName,[{[this.options.textNodeName]:o}]):n.add(this.options.textNodeName,a),s=t+2}else{let o=ti(e,s,this.options.removeNSPrefix),a=o.tagName,l=o.rawTagName,u=o.tagExp,d=o.attrExpPresent,c=o.closeIndex;this.options.transformTagName&&(a=this.options.transformTagName(a)),n&&r&&"!xml"!==n.tagname&&(r=this.saveTextToParentTag(r,n,i,!1));let p=n;p&&-1!==this.options.unpairedTags.indexOf(p.tagname)&&(n=this.tagsNodeStack.pop(),i=i.substring(0,i.lastIndexOf("."))),a!==t.tagname&&(i+=i?"."+a:a);let h=s;if(this.isItStopNode(this.options.stopNodes,i,a)){let t="";if(u.length>0&&u.lastIndexOf("/")===u.length-1)"/"===a[a.length-1]?(a=a.substr(0,a.length-1),i=i.substr(0,i.length-1),u=a):u=u.substr(0,u.length-1),s=o.closeIndex;else if(-1!==this.options.unpairedTags.indexOf(a))s=o.closeIndex;else{let n=this.readStopNodeData(e,l,c+1);if(!n)throw Error(`Unexpected end of ${l}`);s=n.i,t=n.tagContent}let r=new eY(a);a!==u&&d&&(r[":@"]=this.buildAttributesMap(u,i,a)),t&&(t=this.parseTextData(t,a,i,!0,d,!0,!0)),i=i.substr(0,i.lastIndexOf(".")),r.add(this.options.textNodeName,t),this.addChild(n,r,i,h)}else{if(u.length>0&&u.lastIndexOf("/")===u.length-1){"/"===a[a.length-1]?(a=a.substr(0,a.length-1),i=i.substr(0,i.length-1),u=a):u=u.substr(0,u.length-1),this.options.transformTagName&&(a=this.options.transformTagName(a));let e=new eY(a);a!==u&&d&&(e[":@"]=this.buildAttributesMap(u,i,a)),this.addChild(n,e,i,h),i=i.substr(0,i.lastIndexOf("."))}else{let e=new eY(a);this.tagsNodeStack.push(n),a!==u&&d&&(e[":@"]=this.buildAttributesMap(u,i,a)),this.addChild(n,e,i,h),n=e}r="",s=c}}}else r+=e[s];return t.child};function e9(e,t,n,r){this.options.captureMetaData||(r=void 0);let i=this.options.updateTag(t.tagname,n,t[":@"]);!1===i||("string"==typeof i&&(t.tagname=i),e.addChild(t,r))}let te=function(e){if(this.options.processEntities){for(let t in this.docTypeEntities){let n=this.docTypeEntities[t];e=e.replace(n.regx,n.val)}for(let t in this.lastEntities){let n=this.lastEntities[t];e=e.replace(n.regex,n.val)}if(this.options.htmlEntities)for(let t in this.htmlEntities){let n=this.htmlEntities[t];e=e.replace(n.regex,n.val)}e=e.replace(this.ampEntity.regex,this.ampEntity.val)}return e};function tt(e,t,n,r){return e&&(void 0===r&&(r=0===t.child.length),void 0!==(e=this.parseTextData(e,t.tagname,n,!1,!!t[":@"]&&0!==Object.keys(t[":@"]).length,r))&&""!==e&&t.add(this.options.textNodeName,e),e=""),e}function tn(e,t,n){let r="*."+n;for(let n in e){let i=e[n];if(r===i||t===i)return!0}return!1}function tr(e,t,n,r){let i=e.indexOf(t,n);if(-1!==i)return i+t.length-1;throw Error(r)}function ti(e,t,n,r=">"){let i=function(e,t,n=">"){let r;let i="";for(let s=t;s<e.length;s++){let t=e[s];if(r)t===r&&(r="");else if('"'===t||"'"===t)r=t;else if(t===n[0]){if(!n[1]||e[s+1]===n[1])return{data:i,index:s}}else"	"===t&&(t=" ");i+=t}}(e,t+1,r);if(!i)return;let s=i.data,o=i.index,a=s.search(/\s/),l=s,u=!0;-1!==a&&(l=s.substring(0,a),s=s.substring(a+1).trimStart());let d=l;if(n){let e=l.indexOf(":");-1!==e&&(u=(l=l.substr(e+1))!==i.data.substr(e+1))}return{tagName:l,tagExp:s,closeIndex:o,attrExpPresent:u,rawTagName:d}}function ts(e,t,n){let r=n,i=1;for(;n<e.length;n++)if("<"===e[n]){if("/"===e[n+1]){let s=tr(e,">",n,`${t} is not closed`);if(e.substring(n+2,s).trim()===t&&0==--i)return{tagContent:e.substring(r,n),i:s};n=s}else if("?"===e[n+1])n=tr(e,"?>",n+1,"StopNode is not closed.");else if("!--"===e.substr(n+1,3))n=tr(e,"-->",n+3,"StopNode is not closed.");else if("!["===e.substr(n+1,2))n=tr(e,"]]>",n,"StopNode is not closed.")-2;else{let r=ti(e,n,">");r&&((r&&r.tagName)===t&&"/"!==r.tagExp[r.tagExp.length-1]&&i++,n=r.closeIndex)}}}function to(e,t,n){if(t&&"string"==typeof e){let t=e.trim();return"true"===t||"false"!==t&&function(e,t={}){if(t=Object.assign({},e0,t),!e||"string"!=typeof e)return e;let n=e.trim();if(void 0!==t.skipLike&&t.skipLike.test(n))return e;if("0"===e)return 0;if(t.hex&&eJ.test(n))return function(e,t){if(parseInt)return parseInt(e,16);if(Number.parseInt)return Number.parseInt(e,16);if(window&&window.parseInt)return window.parseInt(e,t);throw Error("parseInt, Number.parseInt, window.parseInt are not supported")}(n,16);{if(-1!==n.search(/.+[eE].+/))return function(e,t,n){if(!n.eNotation)return e;let r=t.match(e1);if(!r)return e;{let i=r[1]||"",s=-1===r[3].indexOf("e")?"E":"e",o=r[2],a=i?e[o.length+1]===s:e[o.length]===s;return o.length>1&&a?e:1===o.length&&(r[3].startsWith(`.${s}`)||r[3][0]===s)?Number(t):n.leadingZeros&&!a?Number(t=(r[1]||"")+r[3]):e}}(e,n,t);let i=eQ.exec(n);if(!i)return e;{var r;let s=i[1]||"",o=i[2],a=((r=i[3])&&-1!==r.indexOf(".")&&("."===(r=r.replace(/0+$/,""))?r="0":"."===r[0]?r="0"+r:"."===r[r.length-1]&&(r=r.substring(0,r.length-1))),r),l=s?"."===e[o.length+1]:"."===e[o.length];if(!t.leadingZeros&&(o.length>1||1===o.length&&!l))return e;{let r=Number(n),i=String(r);if(0===r)return r;if(-1!==i.search(/[eE]/))return t.eNotation?r:e;if(-1!==n.indexOf("."))return"0"===i?r:i===a?r:i===`${s}${a}`?r:e;let l=o?a:n;return o?l===i||s+l===i?r:e:l===i||l===s+i?r:e}}}}(e,n)}return void 0!==e?e:""}let ta=eY.getMetaDataSymbol(),tl={allowBooleanAttributes:!1,unpairedTags:[]};function tu(e){return" "===e||"	"===e||"\n"===e||"\r"===e}function td(e,t){let n=t;for(;t<e.length;t++)if("?"==e[t]||" "==e[t]){let r=e.substr(n,t-n);if(t>5&&"xml"===r)return tf("InvalidXml","XML declaration allowed only at the start of the document.",tg(e,t));if("?"!=e[t]||">"!=e[t+1])continue;t++;break}return t}function tc(e,t){if(e.length>t+5&&"-"===e[t+1]&&"-"===e[t+2]){for(t+=3;t<e.length;t++)if("-"===e[t]&&"-"===e[t+1]&&">"===e[t+2]){t+=2;break}}else if(e.length>t+8&&"D"===e[t+1]&&"O"===e[t+2]&&"C"===e[t+3]&&"T"===e[t+4]&&"Y"===e[t+5]&&"P"===e[t+6]&&"E"===e[t+7]){let n=1;for(t+=8;t<e.length;t++)if("<"===e[t])n++;else if(">"===e[t]&&0==--n)break}else if(e.length>t+9&&"["===e[t+1]&&"C"===e[t+2]&&"D"===e[t+3]&&"A"===e[t+4]&&"T"===e[t+5]&&"A"===e[t+6]&&"["===e[t+7]){for(t+=8;t<e.length;t++)if("]"===e[t]&&"]"===e[t+1]&&">"===e[t+2]){t+=2;break}}return t}let tp=RegExp("(\\s*)([^\\s=]+)(\\s*=)?(\\s*(['\"])(([\\s\\S])*?)\\5)?","g");function th(e,t){let n=eq(e,tp),r={};for(let e=0;e<n.length;e++){if(0===n[e][1].length)return tf("InvalidAttr","Attribute '"+n[e][2]+"' has no space in starting.",tm(n[e]));if(void 0!==n[e][3]&&void 0===n[e][4])return tf("InvalidAttr","Attribute '"+n[e][2]+"' is without value.",tm(n[e]));if(void 0===n[e][3]&&!t.allowBooleanAttributes)return tf("InvalidAttr","boolean attribute '"+n[e][2]+"' is not allowed.",tm(n[e]));let i=n[e][2];if(!eB(i))return tf("InvalidAttr","Attribute '"+i+"' is an invalid name.",tm(n[e]));if(r.hasOwnProperty(i))return tf("InvalidAttr","Attribute '"+i+"' is repeated.",tm(n[e]));r[i]=1}return!0}function tf(e,t,n){return{err:{code:e,msg:t,line:n.line||n,col:n.col}}}function tg(e,t){let n=e.substring(0,t).split(/\r?\n/);return{line:n.length,col:n[n.length-1].length+1}}function tm(e){return e.startIndex+e[1].length}class tx{constructor(e){this.externalEntities={},this.options=Object.assign({},eW,e)}parse(e,t){if("string"==typeof e);else if(e.toString)e=e.toString();else throw Error("XML data is accepted in String or Bytes[] form.");if(t){!0===t&&(t={});let n=function(e,t){t=Object.assign({},tl,t);let n=[],r=!1,i=!1;"\uFEFF"===e[0]&&(e=e.substr(1));for(let s=0;s<e.length;s++)if("<"===e[s]&&"?"===e[s+1]){if(s+=2,(s=td(e,s)).err)return s}else if("<"===e[s]){let o=s;if("!"===e[++s]){s=tc(e,s);continue}{let a=!1;"/"===e[s]&&(a=!0,s++);let l="";for(;s<e.length&&">"!==e[s]&&" "!==e[s]&&"	"!==e[s]&&"\n"!==e[s]&&"\r"!==e[s];s++)l+=e[s];if("/"===(l=l.trim())[l.length-1]&&(l=l.substring(0,l.length-1),s--),!eB(l))return tf("InvalidTag",0===l.trim().length?"Invalid space after '<'.":"Tag '"+l+"' is an invalid name.",tg(e,s));let u=function(e,t){let n="",r="",i=!1;for(;t<e.length;t++){if('"'===e[t]||"'"===e[t])""===r?r=e[t]:r!==e[t]||(r="");else if(">"===e[t]&&""===r){i=!0;break}n+=e[t]}return""===r&&{value:n,index:t,tagClosed:i}}(e,s);if(!1===u)return tf("InvalidAttr","Attributes for '"+l+"' have open quote.",tg(e,s));let d=u.value;if(s=u.index,"/"===d[d.length-1]){let n=s-d.length,i=th(d=d.substring(0,d.length-1),t);if(!0!==i)return tf(i.err.code,i.err.msg,tg(e,n+i.err.line));r=!0}else if(a){if(!u.tagClosed)return tf("InvalidTag","Closing tag '"+l+"' doesn't have proper closing.",tg(e,s));if(d.trim().length>0)return tf("InvalidTag","Closing tag '"+l+"' can't have attributes or invalid starting.",tg(e,o));{if(0===n.length)return tf("InvalidTag","Closing tag '"+l+"' has not been opened.",tg(e,o));let t=n.pop();if(l!==t.tagName){let n=tg(e,t.tagStartPos);return tf("InvalidTag","Expected closing tag '"+t.tagName+"' (opened in line "+n.line+", col "+n.col+") instead of closing tag '"+l+"'.",tg(e,o))}0==n.length&&(i=!0)}}else{let a=th(d,t);if(!0!==a)return tf(a.err.code,a.err.msg,tg(e,s-d.length+a.err.line));if(!0===i)return tf("InvalidXml","Multiple possible root nodes found.",tg(e,s));-1!==t.unpairedTags.indexOf(l)||n.push({tagName:l,tagStartPos:o}),r=!0}for(s++;s<e.length;s++)if("<"===e[s]){if("!"===e[s+1]){s=tc(e,++s);continue}if("?"===e[s+1]){if((s=td(e,++s)).err)return s}else break}else if("&"===e[s]){let t=function(e,t){if(";"===e[++t])return -1;if("#"===e[t])return function(e,t){let n=/\d/;for("x"===e[t]&&(t++,n=/[\da-fA-F]/);t<e.length;t++){if(";"===e[t])return t;if(!e[t].match(n))break}return -1}(e,++t);let n=0;for(;t<e.length;t++,n++)if(!e[t].match(/\w/)||!(n<20)){if(";"===e[t])break;return -1}return t}(e,s);if(-1==t)return tf("InvalidChar","char '&' is not expected.",tg(e,s));s=t}else if(!0===i&&!tu(e[s]))return tf("InvalidXml","Extra text at the end",tg(e,s));"<"===e[s]&&s--}}else{if(tu(e[s]))continue;return tf("InvalidChar","char '"+e[s]+"' is not expected.",tg(e,s))}return r?1==n.length?tf("InvalidTag","Unclosed tag '"+n[0].tagName+"'.",tg(e,n[0].tagStartPos)):!(n.length>0)||tf("InvalidXml","Invalid '"+JSON.stringify(n.map(e=>e.tagName),null,4).replace(/\r?\n/g,"")+"' found.",{line:1,col:1}):tf("InvalidXml","Start tag expected.",1)}(e,t);if(!0!==n)throw Error(`${n.err.msg}:${n.err.line}:${n.err.col}`)}let n=new e2(this.options);n.addExternalEntities(this.externalEntities);let r=n.parseXml(e);return this.options.preserveOrder||void 0===r?r:function e(t,n,r){let i;let s={};for(let o=0;o<t.length;o++){let a=t[o],l=function(e){let t=Object.keys(e);for(let e=0;e<t.length;e++){let n=t[e];if(":@"!==n)return n}}(a),u="";if(u=void 0===r?l:r+"."+l,l===n.textNodeName)void 0===i?i=a[l]:i+=""+a[l];else if(void 0===l)continue;else if(a[l]){let t=e(a[l],n,u),r=function(e,t){let{textNodeName:n}=t,r=Object.keys(e).length;return 0===r||1===r&&(!!e[n]||"boolean"==typeof e[n]||0===e[n])}(t,n);void 0!==a[ta]&&(t[ta]=a[ta]),a[":@"]?function(e,t,n,r){if(t){let i=Object.keys(t),s=i.length;for(let o=0;o<s;o++){let s=i[o];r.isArray(s,n+"."+s,!0,!0)?e[s]=[t[s]]:e[s]=t[s]}}}(t,a[":@"],u,n):1!==Object.keys(t).length||void 0===t[n.textNodeName]||n.alwaysCreateTextNode?0===Object.keys(t).length&&(n.alwaysCreateTextNode?t[n.textNodeName]="":t=""):t=t[n.textNodeName],void 0!==s[l]&&s.hasOwnProperty(l)?(Array.isArray(s[l])||(s[l]=[s[l]]),s[l].push(t)):n.isArray(l,u,r)?s[l]=[t]:s[l]=t}}return"string"==typeof i?i.length>0&&(s[n.textNodeName]=i):void 0!==i&&(s[n.textNodeName]=i),s}(r,this.options)}addEntity(e,t){if(-1!==t.indexOf("&"))throw Error("Entity value can't have '&'");if(-1!==e.indexOf("&")||-1!==e.indexOf(";"))throw Error("An entity must be set without '&' and ';'. Eg. use '#xD' for '&#xD;'");if("&"===t)throw Error("An entity with value '&' is not permitted");this.externalEntities[e]=t}static getMetaDataSymbol(){return eY.getMetaDataSymbol()}}var tb=n(99702);let ty=(e,t)=>(0,tb.w)(e,t).then(e=>{if(e.length){let t;let n=new tx({attributeNamePrefix:"",htmlEntities:!0,ignoreAttributes:!1,ignoreDeclaration:!0,parseTagValue:!1,trimValues:!1,tagValueProcessor:(e,t)=>""===t.trim()&&t.includes("\n")?"":void 0});n.addEntity("#xD","\r"),n.addEntity("#10","\n");try{t=n.parse(e,!0)}catch(t){throw t&&"object"==typeof t&&Object.defineProperty(t,"$responseBodyText",{value:e}),t}let r="#text",i=Object.keys(t)[0],s=t[i];return s[r]&&(s[i]=s[r],delete s[r]),(0,g.rm)(s)}return{}}),tE=async(e,t)=>{let n=await ty(e,t);return n.Error&&(n.Error.message=n.Error.message??n.Error.Message),n},tv=async(e,t)=>{let n;return n=nO({...t$(e,t),[t4]:t7,[nI]:t3}),t1(t,t2,"/",void 0,n)},tS=async(e,t)=>{let n;return n=nO({...tR(e,t),[t4]:t9,[nI]:t3}),t1(t,t2,"/",void 0,n)},tw=async(e,t)=>{if(e.statusCode>=300)return tI(e,t);let n=await ty(e.body,t),r={};return r=tz(n.AssumeRoleResult,t),{$metadata:tQ(e),...r}},tP=async(e,t)=>{if(e.statusCode>=300)return tI(e,t);let n=await ty(e.body,t),r={};return r=tL(n.AssumeRoleWithWebIdentityResult,t),{$metadata:tQ(e),...r}},tI=async(e,t)=>{let n={...e,body:await tE(e.body,t)},r=nk(e,n.body);switch(r){case"ExpiredTokenException":case"com.amazonaws.sts#ExpiredTokenException":throw await tT(n,t);case"MalformedPolicyDocument":case"com.amazonaws.sts#MalformedPolicyDocumentException":throw await tO(n,t);case"PackedPolicyTooLarge":case"com.amazonaws.sts#PackedPolicyTooLargeException":throw await tk(n,t);case"RegionDisabledException":case"com.amazonaws.sts#RegionDisabledException":throw await tD(n,t);case"IDPCommunicationError":case"com.amazonaws.sts#IDPCommunicationErrorException":throw await tA(n,t);case"IDPRejectedClaim":case"com.amazonaws.sts#IDPRejectedClaimException":throw await tN(n,t);case"InvalidIdentityToken":case"com.amazonaws.sts#InvalidIdentityTokenException":throw await tC(n,t);default:return t0({output:e,parsedBody:n.body.Error,errorCode:r})}},tT=async(e,t)=>{let n=e.body,r=tB(n.Error,t),i=new e$({$metadata:tQ(e),...r});return(0,g.Mw)(i,n)},tA=async(e,t)=>{let n=e.body,r=tY(n.Error,t),i=new eV({$metadata:tQ(e),...r});return(0,g.Mw)(i,n)},tN=async(e,t)=>{let n=e.body,r=tH(n.Error,t),i=new ej({$metadata:tQ(e),...r});return(0,g.Mw)(i,n)},tC=async(e,t)=>{let n=e.body,r=tG(n.Error,t),i=new eK({$metadata:tQ(e),...r});return(0,g.Mw)(i,n)},tO=async(e,t)=>{let n=e.body,r=tZ(n.Error,t),i=new eR({$metadata:tQ(e),...r});return(0,g.Mw)(i,n)},tk=async(e,t)=>{let n=e.body,r=tX(n.Error,t),i=new eF({$metadata:tQ(e),...r});return(0,g.Mw)(i,n)},tD=async(e,t)=>{let n=e.body,r=tJ(n.Error,t),i=new eM({$metadata:tQ(e),...r});return(0,g.Mw)(i,n)},t$=(e,t)=>{let n={};if(null!=e[ng]&&(n[ng]=e[ng]),null!=e[nm]&&(n[nm]=e[nm]),null!=e[nu]){let r=tF(e[nu],t);e[nu]?.length===0&&(n.PolicyArns=[]),Object.entries(r).forEach(([e,t])=>{n[`PolicyArns.${e}`]=t})}if(null!=e[nl]&&(n[nl]=e[nl]),null!=e[ni]&&(n[ni]=e[ni]),null!=e[nS]){let r=tV(e[nS],t);e[nS]?.length===0&&(n.Tags=[]),Object.entries(r).forEach(([e,t])=>{n[`Tags.${e}`]=t})}if(null!=e[nP]){let r=tU(e[nP],t);e[nP]?.length===0&&(n.TransitiveTagKeys=[]),Object.entries(r).forEach(([e,t])=>{n[`TransitiveTagKeys.${e}`]=t})}if(null!=e[no]&&(n[no]=e[no]),null!=e[nE]&&(n[nE]=e[nE]),null!=e[nw]&&(n[nw]=e[nw]),null!=e[ny]&&(n[ny]=e[ny]),null!=e[nc]){let r=tK(e[nc],t);e[nc]?.length===0&&(n.ProvidedContexts=[]),Object.entries(r).forEach(([e,t])=>{n[`ProvidedContexts.${e}`]=t})}return n},tR=(e,t)=>{let n={};if(null!=e[ng]&&(n[ng]=e[ng]),null!=e[nm]&&(n[nm]=e[nm]),null!=e[nA]&&(n[nA]=e[nA]),null!=e[np]&&(n[np]=e[np]),null!=e[nu]){let r=tF(e[nu],t);e[nu]?.length===0&&(n.PolicyArns=[]),Object.entries(r).forEach(([e,t])=>{n[`PolicyArns.${e}`]=t})}return null!=e[nl]&&(n[nl]=e[nl]),null!=e[ni]&&(n[ni]=e[ni]),n},tF=(e,t)=>{let n={},r=1;for(let i of e)null!==i&&(Object.entries(tM(i,t)).forEach(([e,t])=>{n[`member.${r}.${e}`]=t}),r++);return n},tM=(e,t)=>{let n={};return null!=e[nN]&&(n[nN]=e[nN]),n},tj=(e,t)=>{let n={};return null!=e[nd]&&(n[nd]=e[nd]),null!=e[nr]&&(n[nr]=e[nr]),n},tK=(e,t)=>{let n={},r=1;for(let i of e)null!==i&&(Object.entries(tj(i,t)).forEach(([e,t])=>{n[`member.${r}.${e}`]=t}),r++);return n},t_=(e,t)=>{let n={};return null!=e[na]&&(n[na]=e[na]),null!=e[nT]&&(n[nT]=e[nT]),n},tU=(e,t)=>{let n={},r=1;for(let t of e)null!==t&&(n[`member.${r}`]=t,r++);return n},tV=(e,t)=>{let n={},r=1;for(let i of e)null!==i&&(Object.entries(t_(i,t)).forEach(([e,t])=>{n[`member.${r}.${e}`]=t}),r++);return n},tW=(e,t)=>{let n={};return null!=e[t5]&&(n[t5]=(0,g.lK)(e[t5])),null!=e[ne]&&(n[ne]=(0,g.lK)(e[ne])),n},tz=(e,t)=>{let n={};return null!=e[nn]&&(n[nn]=tq(e[nn],t)),null!=e[t8]&&(n[t8]=tW(e[t8],t)),null!=e[nh]&&(n[nh]=(0,g.xW)(e[nh])),null!=e[ny]&&(n[ny]=(0,g.lK)(e[ny])),n},tL=(e,t)=>{let n={};return null!=e[nn]&&(n[nn]=tq(e[nn],t)),null!=e[nb]&&(n[nb]=(0,g.lK)(e[nb])),null!=e[t8]&&(n[t8]=tW(e[t8],t)),null!=e[nh]&&(n[nh]=(0,g.xW)(e[nh])),null!=e[nf]&&(n[nf]=(0,g.lK)(e[nf])),null!=e[nt]&&(n[nt]=(0,g.lK)(e[nt])),null!=e[ny]&&(n[ny]=(0,g.lK)(e[ny])),n},tq=(e,t)=>{let n={};return null!=e[t6]&&(n[t6]=(0,g.lK)(e[t6])),null!=e[nx]&&(n[nx]=(0,g.lK)(e[nx])),null!=e[nv]&&(n[nv]=(0,g.lK)(e[nv])),null!=e[ns]&&(n[ns]=(0,g.Y0)((0,g.t_)(e[ns]))),n},tB=(e,t)=>{let n={};return null!=e[nC]&&(n[nC]=(0,g.lK)(e[nC])),n},tY=(e,t)=>{let n={};return null!=e[nC]&&(n[nC]=(0,g.lK)(e[nC])),n},tH=(e,t)=>{let n={};return null!=e[nC]&&(n[nC]=(0,g.lK)(e[nC])),n},tG=(e,t)=>{let n={};return null!=e[nC]&&(n[nC]=(0,g.lK)(e[nC])),n},tZ=(e,t)=>{let n={};return null!=e[nC]&&(n[nC]=(0,g.lK)(e[nC])),n},tX=(e,t)=>{let n={};return null!=e[nC]&&(n[nC]=(0,g.lK)(e[nC])),n},tJ=(e,t)=>{let n={};return null!=e[nC]&&(n[nC]=(0,g.lK)(e[nC])),n},tQ=e=>({httpStatusCode:e.statusCode,requestId:e.headers["x-amzn-requestid"]??e.headers["x-amzn-request-id"]??e.headers["x-amz-request-id"],extendedRequestId:e.headers["x-amz-id-2"],cfId:e.headers["x-amz-cf-id"]}),t0=(0,g.jr)(eO),t1=async(e,t,n,r,i)=>{let{hostname:s,protocol:o="https",port:a,path:l}=await e.endpoint(),u={protocol:o,hostname:s,port:a,method:"POST",path:l.endsWith("/")?l.slice(0,-1)+n:l+n,headers:t};return void 0!==r&&(u.hostname=r),void 0!==i&&(u.body=i),new eP.Kd(u)},t2={"content-type":"application/x-www-form-urlencoded"},t3="2011-06-15",t4="Action",t6="AccessKeyId",t7="AssumeRole",t5="AssumedRoleId",t8="AssumedRoleUser",t9="AssumeRoleWithWebIdentity",ne="Arn",nt="Audience",nn="Credentials",nr="ContextAssertion",ni="DurationSeconds",ns="Expiration",no="ExternalId",na="Key",nl="Policy",nu="PolicyArns",nd="ProviderArn",nc="ProvidedContexts",np="ProviderId",nh="PackedPolicySize",nf="Provider",ng="RoleArn",nm="RoleSessionName",nx="SecretAccessKey",nb="SubjectFromWebIdentityToken",ny="SourceIdentity",nE="SerialNumber",nv="SessionToken",nS="Tags",nw="TokenCode",nP="TransitiveTagKeys",nI="Version",nT="Value",nA="WebIdentityToken",nN="arn",nC="message",nO=e=>Object.entries(e).map(([e,t])=>(0,g.$6)(e)+"="+(0,g.$6)(t)).join("&"),nk=(e,t)=>t.Error?.Code!==void 0?t.Error.Code:404==e.statusCode?"NotFound":void 0;class nD extends g.uB.classBuilder().ep(w).m(function(e,t,n,r){return[(0,eC.TM)(n,this.serialize,this.deserialize),(0,h.rD)(n,e.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRole",{}).n("STSClient","AssumeRoleCommand").f(void 0,eD).ser(tv).de(tw).build(){}class n$ extends g.uB.classBuilder().ep(w).m(function(e,t,n,r){return[(0,eC.TM)(n,this.serialize,this.deserialize),(0,h.rD)(n,e.getEndpointParameterInstructions())]}).s("AWSSecurityTokenServiceV20110615","AssumeRoleWithWebIdentity",{}).n("STSClient","AssumeRoleWithWebIdentityCommand").f(e_,eU).ser(tS).de(tP).build(){}class nR extends eN{}(0,g.J1)({AssumeRoleCommand:nD,AssumeRoleWithWebIdentityCommand:n$},nR);var nF=n(6884);let nM="us-east-1",nj=e=>{if("string"==typeof e?.Arn){let t=e.Arn.split(":");if(t.length>4&&""!==t[4])return t[4]}},nK=async(e,t,n)=>{let r="function"==typeof e?await e():e,i="function"==typeof t?await t():t;return n?.debug?.("@aws-sdk/client-sts::resolveRegion","accepting first of:",`${r} (provider)`,`${i} (parent client)`,`${nM} (STS default)`),r??i??nM},n_=(e,t)=>{let n,r;return async(i,s)=>{if(r=i,!n){let{logger:i=e?.parentClientConfig?.logger,region:s,requestHandler:o=e?.parentClientConfig?.requestHandler,credentialProviderLogger:a}=e,l=await nK(s,e?.parentClientConfig?.region,a),u=!nV(o);n=new t({profile:e?.parentClientConfig?.profile,credentialDefaultProvider:()=>async()=>r,region:l,requestHandler:u?o:void 0,logger:i})}let{Credentials:o,AssumedRoleUser:a}=await n.send(new nD(s));if(!o||!o.AccessKeyId||!o.SecretAccessKey)throw Error(`Invalid response from STS.assumeRole call with role ${s.RoleArn}`);let l=nj(a),u={accessKeyId:o.AccessKeyId,secretAccessKey:o.SecretAccessKey,sessionToken:o.SessionToken,expiration:o.Expiration,...o.CredentialScope&&{credentialScope:o.CredentialScope},...l&&{accountId:l}};return(0,nF.g)(u,"CREDENTIALS_STS_ASSUME_ROLE","i"),u}},nU=(e,t)=>{let n;return async r=>{if(!n){let{logger:r=e?.parentClientConfig?.logger,region:i,requestHandler:s=e?.parentClientConfig?.requestHandler,credentialProviderLogger:o}=e,a=await nK(i,e?.parentClientConfig?.region,o),l=!nV(s);n=new t({profile:e?.parentClientConfig?.profile,region:a,requestHandler:l?s:void 0,logger:r})}let{Credentials:i,AssumedRoleUser:s}=await n.send(new n$(r));if(!i||!i.AccessKeyId||!i.SecretAccessKey)throw Error(`Invalid response from STS.assumeRoleWithWebIdentity call with role ${r.RoleArn}`);let o=nj(s),a={accessKeyId:i.AccessKeyId,secretAccessKey:i.SecretAccessKey,sessionToken:i.SessionToken,expiration:i.Expiration,...i.CredentialScope&&{credentialScope:i.CredentialScope},...o&&{accountId:o}};return o&&(0,nF.g)(a,"RESOLVED_ACCOUNT_ID","T"),(0,nF.g)(a,"CREDENTIALS_STS_ASSUME_ROLE_WEB_ID","k"),a}},nV=e=>e?.metadata?.handlerProtocol==="h2",nW=(e,t)=>t?class extends e{constructor(e){for(let n of(super(e),t))this.middlewareStack.use(n)}}:e,nz=(e={},t)=>n_(e,nW(eN,t)),nL=(e={},t)=>nU(e,nW(eN,t))},43279:e=>{e.exports={rE:"3.855.0"}}};