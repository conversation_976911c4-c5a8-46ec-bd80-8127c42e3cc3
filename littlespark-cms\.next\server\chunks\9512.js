"use strict";exports.id=9512,exports.ids=[9512],exports.modules={30740:(a,b,c)=>{c.d(b,{R:()=>f});var d=c(29789),e=c(51877);function f(a,b,c){let[f,g]=(0,d.x)(c?.in,a,b);return+(0,e.k)(f,c)==+(0,e.k)(g,c)}},79512:(a,b,c)=>{c.r(b),c.d(b,{bg:()=>p,default:()=>q});let d={lessThanXSeconds:{one:"по-малко от секунда",other:"по-малко от {{count}} секунди"},xSeconds:{one:"1 секунда",other:"{{count}} секунди"},halfAMinute:"половин минута",lessThanXMinutes:{one:"по-малко от минута",other:"по-малко от {{count}} минути"},xMinutes:{one:"1 минута",other:"{{count}} минути"},aboutXHours:{one:"около час",other:"около {{count}} часа"},xHours:{one:"1 час",other:"{{count}} часа"},xDays:{one:"1 ден",other:"{{count}} дни"},aboutXWeeks:{one:"около седмица",other:"около {{count}} седмици"},xWeeks:{one:"1 седмица",other:"{{count}} седмици"},aboutXMonths:{one:"около месец",other:"около {{count}} месеца"},xMonths:{one:"1 месец",other:"{{count}} месеца"},aboutXYears:{one:"около година",other:"около {{count}} години"},xYears:{one:"1 година",other:"{{count}} години"},overXYears:{one:"над година",other:"над {{count}} години"},almostXYears:{one:"почти година",other:"почти {{count}} години"}};var e=c(14137);let f={date:(0,e.k)({formats:{full:"EEEE, dd MMMM yyyy",long:"dd MMMM yyyy",medium:"dd MMM yyyy",short:"dd.MM.yyyy"},defaultWidth:"full"}),time:(0,e.k)({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"H:mm"},defaultWidth:"full"}),dateTime:(0,e.k)({formats:{any:"{{date}} {{time}}"},defaultWidth:"any"})};var g=c(30740),h=c(46092);let i=["неделя","понеделник","вторник","сряда","четвъртък","петък","събота"];function j(a){let b=i[a];return 2===a?"'във "+b+" в' p":"'в "+b+" в' p"}let k={lastWeek:(a,b,c)=>{let d=(0,h.a)(a),e=d.getDay();return(0,g.R)(d,b,c)?j(e):function(a){let b=i[a];switch(a){case 0:case 3:case 6:return"'миналата "+b+" в' p";case 1:case 2:case 4:case 5:return"'миналия "+b+" в' p"}}(e)},yesterday:"'вчера в' p",today:"'днес в' p",tomorrow:"'утре в' p",nextWeek:(a,b,c)=>{let d=(0,h.a)(a),e=d.getDay();return(0,g.R)(d,b,c)?j(e):function(a){let b=i[a];switch(a){case 0:case 3:case 6:return"'следващата "+b+" в' p";case 1:case 2:case 4:case 5:return"'следващия "+b+" в' p"}}(e)},other:"P"};var l=c(54755);function m(a,b,c,d,e){return a+"-"+("quarter"===b?e:"year"===b||"week"===b||"minute"===b||"second"===b?d:c)}let n={ordinalNumber:(a,b)=>{let c=Number(a),d=b?.unit;if(0===c)return m(0,d,"ев","ева","ево");if(c%1e3==0)return m(c,d,"ен","на","но");if(c%100==0)return m(c,d,"тен","тна","тно");let e=c%100;if(e>20||e<10)switch(e%10){case 1:return m(c,d,"ви","ва","во");case 2:return m(c,d,"ри","ра","ро");case 7:case 8:return m(c,d,"ми","ма","мо")}return m(c,d,"ти","та","то")},era:(0,l.o)({values:{narrow:["пр.н.е.","н.е."],abbreviated:["преди н. е.","н. е."],wide:["преди новата ера","новата ера"]},defaultWidth:"wide"}),quarter:(0,l.o)({values:{narrow:["1","2","3","4"],abbreviated:["1-во тримес.","2-ро тримес.","3-то тримес.","4-то тримес."],wide:["1-во тримесечие","2-ро тримесечие","3-то тримесечие","4-то тримесечие"]},defaultWidth:"wide",argumentCallback:a=>a-1}),month:(0,l.o)({values:{abbreviated:["яну","фев","мар","апр","май","юни","юли","авг","сеп","окт","ное","дек"],wide:["януари","февруари","март","април","май","юни","юли","август","септември","октомври","ноември","декември"]},defaultWidth:"wide"}),day:(0,l.o)({values:{narrow:["Н","П","В","С","Ч","П","С"],short:["нд","пн","вт","ср","чт","пт","сб"],abbreviated:["нед","пон","вто","сря","чет","пет","съб"],wide:["неделя","понеделник","вторник","сряда","четвъртък","петък","събота"]},defaultWidth:"wide"}),dayPeriod:(0,l.o)({values:{wide:{am:"преди обяд",pm:"след обяд",midnight:"в полунощ",noon:"на обяд",morning:"сутринта",afternoon:"следобед",evening:"вечерта",night:"през нощта"}},defaultWidth:"wide"})};var o=c(86787);let p={code:"bg",formatDistance:(a,b,c)=>{let e,f=d[a];if(e="string"==typeof f?f:1===b?f.one:f.other.replace("{{count}}",String(b)),c?.addSuffix)if(c.comparison&&c.comparison>0)return"след "+e;else return"преди "+e;return e},formatLong:f,formatRelative:(a,b,c,d)=>{let e=k[a];return"function"==typeof e?e(b,c,d):e},localize:n,match:{ordinalNumber:(0,c(43890).K)({matchPattern:/^(\d+)(-?[врмт][аи]|-?т?(ен|на)|-?(ев|ева))?/i,parsePattern:/\d+/i,valueCallback:a=>parseInt(a,10)}),era:(0,o.A)({matchPatterns:{narrow:/^((пр)?н\.?\s?е\.?)/i,abbreviated:/^((пр)?н\.?\s?е\.?)/i,wide:/^(преди новата ера|новата ера|нова ера)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^п/i,/^н/i]},defaultParseWidth:"any"}),quarter:(0,o.A)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^[1234](-?[врт]?o?)? тримес.?/i,wide:/^[1234](-?[врт]?о?)? тримесечие/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:a=>a+1}),month:(0,o.A)({matchPatterns:{abbreviated:/^(яну|фев|мар|апр|май|юни|юли|авг|сеп|окт|ное|дек)/i,wide:/^(януари|февруари|март|април|май|юни|юли|август|септември|октомври|ноември|декември)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^я/i,/^ф/i,/^мар/i,/^ап/i,/^май/i,/^юн/i,/^юл/i,/^ав/i,/^се/i,/^окт/i,/^но/i,/^де/i]},defaultParseWidth:"any"}),day:(0,o.A)({matchPatterns:{narrow:/^[нпвсч]/i,short:/^(нд|пн|вт|ср|чт|пт|сб)/i,abbreviated:/^(нед|пон|вто|сря|чет|пет|съб)/i,wide:/^(неделя|понеделник|вторник|сряда|четвъртък|петък|събота)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^н/i,/^п/i,/^в/i,/^с/i,/^ч/i,/^п/i,/^с/i],any:[/^н[ед]/i,/^п[он]/i,/^вт/i,/^ср/i,/^ч[ет]/i,/^п[ет]/i,/^с[ъб]/i]},defaultParseWidth:"any"}),dayPeriod:(0,o.A)({matchPatterns:{any:/^(преди о|след о|в по|на о|през|веч|сут|следо)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^преди о/i,pm:/^след о/i,midnight:/^в пол/i,noon:/^на об/i,morning:/^сут/i,afternoon:/^следо/i,evening:/^веч/i,night:/^през н/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:1}},q=p}};