"use strict";exports.id=4147,exports.ids=[4147],exports.modules={14147:(a,b,c)=>{c.d(b,{ENV_CMDS_FULL_URI:()=>q,ENV_CMDS_RELATIVE_URI:()=>r,fromContainerMetadata:()=>t,fromInstanceMetadata:()=>L,getInstanceMetadataEndpoint:()=>D,httpRequest:()=>j});var d,e,f=c(71930),g=c(79551),h=c(79428),i=c(81630);function j(a){return new Promise((b,c)=>{let d=(0,i.request)({method:"GET",...a,hostname:a.hostname?.replace(/^\[(.+)\]$/,"$1")});d.on("error",a=>{c(Object.assign(new f.mZ("Unable to connect to instance metadata service"),a)),d.destroy()}),d.on("timeout",()=>{c(new f.mZ("TimeoutError from instance metadata service")),d.destroy()}),d.on("response",a=>{let{statusCode:e=400}=a;(e<200||300<=e)&&(c(Object.assign(new f.mZ("Error response received from instance metadata service"),{statusCode:e})),d.destroy());let g=[];a.on("data",a=>{g.push(a)}),a.on("end",()=>{b(h.Buffer.concat(g)),d.destroy()})}),d.end()})}let k=a=>!!a&&"object"==typeof a&&"string"==typeof a.AccessKeyId&&"string"==typeof a.SecretAccessKey&&"string"==typeof a.Token&&"string"==typeof a.Expiration,l=a=>({accessKeyId:a.AccessKeyId,secretAccessKey:a.SecretAccessKey,sessionToken:a.Token,expiration:new Date(a.Expiration),...a.AccountId&&{accountId:a.AccountId}}),m=1e3,n=0,o=({maxRetries:a=n,timeout:b=m})=>({maxRetries:a,timeout:b}),p=(a,b)=>{let c=a();for(let d=0;d<b;d++)c=c.catch(a);return c},q="AWS_CONTAINER_CREDENTIALS_FULL_URI",r="AWS_CONTAINER_CREDENTIALS_RELATIVE_URI",s="AWS_CONTAINER_AUTHORIZATION_TOKEN",t=(a={})=>{let{timeout:b,maxRetries:c}=o(a);return()=>p(async()=>{let c=await x({logger:a.logger}),d=JSON.parse(await u(b,c));if(!k(d))throw new f.C1("Invalid response received from instance metadata service.",{logger:a.logger});return l(d)},c)},u=async(a,b)=>(process.env[s]&&(b.headers={...b.headers,Authorization:process.env[s]}),(await j({...b,timeout:a})).toString()),v={localhost:!0,"127.0.0.1":!0},w={"http:":!0,"https:":!0},x=async({logger:a})=>{if(process.env[r])return{hostname:"*************",path:process.env[r]};if(process.env[q]){let b=(0,g.parse)(process.env[q]);if(!b.hostname||!(b.hostname in v))throw new f.C1(`${b.hostname} is not a valid container metadata service hostname`,{tryNextLink:!1,logger:a});if(!b.protocol||!(b.protocol in w))throw new f.C1(`${b.protocol} is not a valid container metadata service protocol`,{tryNextLink:!1,logger:a});return{...b,port:b.port?parseInt(b.port,10):void 0}}throw new f.C1(`The container metadata credential provider cannot be used unless the ${r} or ${q} environment variable is set`,{tryNextLink:!1,logger:a})};var y=c(1226);class z extends f.C1{constructor(a,b=!0){super(a,b),this.tryNextLink=b,this.name="InstanceMetadataV1FallbackError",Object.setPrototypeOf(this,z.prototype)}}var A=c(68378);!function(a){a.IPv4="http://***************",a.IPv6="http://[fd00:ec2::254]"}(d||(d={}));let B={environmentVariableSelector:a=>a.AWS_EC2_METADATA_SERVICE_ENDPOINT,configFileSelector:a=>a.ec2_metadata_service_endpoint,default:void 0};!function(a){a.IPv4="IPv4",a.IPv6="IPv6"}(e||(e={}));let C={environmentVariableSelector:a=>a.AWS_EC2_METADATA_SERVICE_ENDPOINT_MODE,configFileSelector:a=>a.ec2_metadata_service_endpoint_mode,default:e.IPv4},D=async()=>(0,A.D)(await E()||await F()),E=async()=>(0,y.Z)(B)(),F=async()=>{let a=await (0,y.Z)(C)();switch(a){case e.IPv4:return d.IPv4;case e.IPv6:return d.IPv6;default:throw Error(`Unsupported endpoint mode: ${a}. Select from ${Object.values(e)}`)}},G=(a,b)=>{let c=300+Math.floor(300*Math.random()),d=new Date(Date.now()+1e3*c);b.warn(`Attempting credential expiration extension due to a credential service availability issue. A refresh of these credentials will be attempted after ${new Date(d)}.
For more information, please visit: https://docs.aws.amazon.com/sdkref/latest/guide/feature-static-credentials.html`);let e=a.originalExpiration??a.expiration;return{...a,...e?{originalExpiration:e}:{},expiration:d}},H="/latest/meta-data/iam/security-credentials/",I="AWS_EC2_METADATA_V1_DISABLED",J="ec2_metadata_v1_disabled",K="x-aws-ec2-metadata-token",L=(a={})=>((a,b={})=>{let c,d=b?.logger||console;return async()=>{let b;try{(b=await a()).expiration&&b.expiration.getTime()<Date.now()&&(b=G(b,d))}catch(a){if(c)d.warn("Credential renew failed: ",a),b=G(c,d);else throw a}return c=b,b}})(M(a),{logger:a.logger}),M=(a={})=>{let b=!1,{logger:c,profile:d}=a,{timeout:e,maxRetries:g}=o(a),h=async(c,e)=>{if(b||e.headers?.[K]==null){let b=!1,c=!1,e=await (0,y.Z)({environmentVariableSelector:b=>{let d=b[I];if(c=!!d&&"false"!==d,void 0===d)throw new f.C1(`${I} not set in env, checking config file next.`,{logger:a.logger});return c},configFileSelector:a=>{let c=a[J];return b=!!c&&"false"!==c},default:!1},{profile:d})();if(a.ec2MetadataV1Disabled||e){let d=[];throw a.ec2MetadataV1Disabled&&d.push("credential provider initialization (runtime option ec2MetadataV1Disabled)"),b&&d.push(`config file profile (${J})`),c&&d.push(`process environment variable (${I})`),new z(`AWS EC2 Metadata v1 fallback has been blocked by AWS SDK configuration in the following: [${d.join(", ")}].`)}}let g=(await p(async()=>{let a;try{a=await O(e)}catch(a){throw 401===a.statusCode&&(b=!1),a}return a},c)).trim();return p(async()=>{let c;try{c=await P(g,e,a)}catch(a){throw 401===a.statusCode&&(b=!1),a}return c},c)};return async()=>{let a=await D();if(b)return c?.debug("AWS SDK Instance Metadata","using v1 fallback (no token fetch)"),h(g,{...a,timeout:e});{let d;try{d=(await N({...a,timeout:e})).toString()}catch(d){if(d?.statusCode===400)throw Object.assign(d,{message:"EC2 Metadata token request returned error"});return("TimeoutError"===d.message||[403,404,405].includes(d.statusCode))&&(b=!0),c?.debug("AWS SDK Instance Metadata","using v1 fallback (initial)"),h(g,{...a,timeout:e})}return h(g,{...a,headers:{[K]:d},timeout:e})}}},N=async a=>j({...a,path:"/latest/api/token",method:"PUT",headers:{"x-aws-ec2-metadata-token-ttl-seconds":"21600"}}),O=async a=>(await j({...a,path:H})).toString(),P=async(a,b,c)=>{let d=JSON.parse((await j({...b,path:H+a})).toString());if(!k(d))throw new f.C1("Invalid response received from instance metadata service.",{logger:c.logger});return l(d)}}};