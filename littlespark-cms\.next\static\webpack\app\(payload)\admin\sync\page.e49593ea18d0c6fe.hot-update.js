"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(payload)/admin/sync/page",{

/***/ "(app-pages-browser)/./src/app/(payload)/admin/sync/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/(payload)/admin/sync/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SyncPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction SyncPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"sync-page\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                padding: '20px',\n                maxWidth: '1200px',\n                margin: '0 auto'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        marginBottom: '30px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            style: {\n                                fontSize: '28px',\n                                fontWeight: 'bold',\n                                marginBottom: '10px'\n                            },\n                            children: \"\\uD83D\\uDD04 Sync Management\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: '#666',\n                                fontSize: '16px'\n                            },\n                            children: \"Synchronize content and users between CMS and main application\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'inline-block',\n                                backgroundColor: '#f0f9ff',\n                                color: '#0369a1',\n                                padding: '4px 12px',\n                                borderRadius: '6px',\n                                fontSize: '14px',\n                                fontWeight: '500',\n                                marginTop: '10px'\n                            },\n                            children: \"\\uD83D\\uDEE1️ Admin Only Feature\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'grid',\n                        gridTemplateColumns: '1fr 1fr 1fr',\n                        gap: '20px',\n                        marginBottom: '30px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                border: '1px solid #e5e7eb',\n                                borderRadius: '8px',\n                                padding: '20px',\n                                backgroundColor: '#fff'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        fontSize: '20px',\n                                        fontWeight: '600',\n                                        marginBottom: '10px'\n                                    },\n                                    children: \"\\uD83D\\uDCDD Challenge Sync\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: '#666',\n                                        marginBottom: '20px',\n                                        fontSize: '14px'\n                                    },\n                                    children: \"Sync published challenges from CMS to main application\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'grid',\n                                        gridTemplateColumns: '1fr 1fr',\n                                        gap: '15px',\n                                        marginBottom: '20px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                backgroundColor: '#eff6ff',\n                                                padding: '15px',\n                                                borderRadius: '6px',\n                                                textAlign: 'center'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '24px',\n                                                        fontWeight: 'bold',\n                                                        color: '#2563eb'\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        id: \"cms-challenges-count\",\n                                                        children: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                        lineNumber: 53,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                    lineNumber: 52,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '12px',\n                                                        color: '#1e40af',\n                                                        fontWeight: '500'\n                                                    },\n                                                    children: \"CMS Challenges\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                    lineNumber: 55,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                backgroundColor: '#f0fdf4',\n                                                padding: '15px',\n                                                borderRadius: '6px',\n                                                textAlign: 'center'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '24px',\n                                                        fontWeight: 'bold',\n                                                        color: '#16a34a'\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        id: \"synced-challenges-count\",\n                                                        children: \"0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                        lineNumber: 66,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '12px',\n                                                        color: '#15803d',\n                                                        fontWeight: '500'\n                                                    },\n                                                    children: \"Synced to Main App\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    id: \"sync-challenges-btn\",\n                                    style: {\n                                        width: '100%',\n                                        backgroundColor: '#2563eb',\n                                        color: 'white',\n                                        border: 'none',\n                                        padding: '12px 20px',\n                                        borderRadius: '6px',\n                                        fontSize: '14px',\n                                        fontWeight: '500',\n                                        cursor: 'pointer',\n                                        marginBottom: '10px'\n                                    },\n                                    onMouseOver: (e)=>e.currentTarget.style.backgroundColor = '#1d4ed8',\n                                    onMouseOut: (e)=>e.currentTarget.style.backgroundColor = '#2563eb',\n                                    children: \"\\uD83D\\uDD04 Sync Challenges to Main App\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"challenge-sync-result\",\n                                    style: {\n                                        fontSize: '12px',\n                                        color: '#666'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                border: '1px solid #e5e7eb',\n                                borderRadius: '8px',\n                                padding: '20px',\n                                backgroundColor: '#fff'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        fontSize: '20px',\n                                        fontWeight: '600',\n                                        marginBottom: '10px'\n                                    },\n                                    children: \"\\uD83D\\uDC65 User Sync\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: '#666',\n                                        marginBottom: '20px',\n                                        fontSize: '14px'\n                                    },\n                                    children: \"Sync CMS users to main application for content access\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'grid',\n                                        gridTemplateColumns: '1fr 1fr',\n                                        gap: '15px',\n                                        marginBottom: '20px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                backgroundColor: '#faf5ff',\n                                                padding: '15px',\n                                                borderRadius: '6px',\n                                                textAlign: 'center'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '24px',\n                                                        fontWeight: 'bold',\n                                                        color: '#9333ea'\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        id: \"cms-users-count\",\n                                                        children: \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                        lineNumber: 119,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '12px',\n                                                        color: '#7c3aed',\n                                                        fontWeight: '500'\n                                                    },\n                                                    children: \"CMS Users\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                    lineNumber: 121,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                backgroundColor: '#fff7ed',\n                                                padding: '15px',\n                                                borderRadius: '6px',\n                                                textAlign: 'center'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '24px',\n                                                        fontWeight: 'bold',\n                                                        color: '#ea580c'\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        id: \"synced-users-count\",\n                                                        children: \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                        lineNumber: 132,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '12px',\n                                                        color: '#c2410c',\n                                                        fontWeight: '500'\n                                                    },\n                                                    children: \"Synced to Main App\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                    lineNumber: 134,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    id: \"sync-users-btn\",\n                                    style: {\n                                        width: '100%',\n                                        backgroundColor: '#9333ea',\n                                        color: 'white',\n                                        border: 'none',\n                                        padding: '12px 20px',\n                                        borderRadius: '6px',\n                                        fontSize: '14px',\n                                        fontWeight: '500',\n                                        cursor: 'pointer',\n                                        marginBottom: '10px'\n                                    },\n                                    onMouseOver: (e)=>e.currentTarget.style.backgroundColor = '#7c3aed',\n                                    onMouseOut: (e)=>e.currentTarget.style.backgroundColor = '#9333ea',\n                                    children: \"\\uD83D\\uDD04 Sync Users to Main App\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"user-sync-result\",\n                                    style: {\n                                        fontSize: '12px',\n                                        color: '#666'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                border: '1px solid #e5e7eb',\n                                borderRadius: '8px',\n                                padding: '20px',\n                                backgroundColor: '#fff'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        fontSize: '20px',\n                                        fontWeight: '600',\n                                        marginBottom: '10px'\n                                    },\n                                    children: \"⬅️ Import User Challenges\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: '#666',\n                                        marginBottom: '20px',\n                                        fontSize: '14px'\n                                    },\n                                    children: \"Import user-created challenges from main app to CMS for review\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'grid',\n                                        gridTemplateColumns: '1fr 1fr',\n                                        gap: '15px',\n                                        marginBottom: '20px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                backgroundColor: '#f0f9ff',\n                                                padding: '15px',\n                                                borderRadius: '6px',\n                                                textAlign: 'center'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '24px',\n                                                        fontWeight: 'bold',\n                                                        color: '#0369a1'\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        id: \"available-user-challenges-count\",\n                                                        children: \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '12px',\n                                                        color: '#0c4a6e',\n                                                        fontWeight: '500'\n                                                    },\n                                                    children: \"Available to Import\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                backgroundColor: '#f0fdf4',\n                                                padding: '15px',\n                                                borderRadius: '6px',\n                                                textAlign: 'center'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '24px',\n                                                        fontWeight: 'bold',\n                                                        color: '#16a34a'\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        id: \"imported-challenges-count\",\n                                                        children: \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '12px',\n                                                        color: '#15803d',\n                                                        fontWeight: '500'\n                                                    },\n                                                    children: \"Already Imported\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    id: \"import-challenges-btn\",\n                                    style: {\n                                        width: '100%',\n                                        backgroundColor: '#0369a1',\n                                        color: 'white',\n                                        border: 'none',\n                                        padding: '12px 20px',\n                                        borderRadius: '6px',\n                                        fontSize: '14px',\n                                        fontWeight: '500',\n                                        cursor: 'pointer',\n                                        marginBottom: '10px'\n                                    },\n                                    onMouseOver: (e)=>e.currentTarget.style.backgroundColor = '#0c4a6e',\n                                    onMouseOut: (e)=>e.currentTarget.style.backgroundColor = '#0369a1',\n                                    children: \"⬅️ Import User Challenges from Main App\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"import-sync-result\",\n                                    style: {\n                                        fontSize: '12px',\n                                        color: '#666'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        textAlign: 'center',\n                        marginBottom: '30px'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            gap: '15px',\n                            justifyContent: 'center',\n                            flexWrap: 'wrap'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                id: \"refresh-stats-btn\",\n                                style: {\n                                    backgroundColor: '#f3f4f6',\n                                    color: '#374151',\n                                    border: '1px solid #d1d5db',\n                                    padding: '10px 20px',\n                                    borderRadius: '6px',\n                                    fontSize: '14px',\n                                    fontWeight: '500',\n                                    cursor: 'pointer'\n                                },\n                                onMouseOver: (e)=>e.currentTarget.style.backgroundColor = '#e5e7eb',\n                                onMouseOut: (e)=>e.currentTarget.style.backgroundColor = '#f3f4f6',\n                                children: \"\\uD83D\\uDD04 Refresh Statistics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                id: \"add-test-data-btn\",\n                                style: {\n                                    backgroundColor: '#10b981',\n                                    color: 'white',\n                                    border: 'none',\n                                    padding: '10px 20px',\n                                    borderRadius: '6px',\n                                    fontSize: '14px',\n                                    fontWeight: '500',\n                                    cursor: 'pointer'\n                                },\n                                onMouseOver: (e)=>e.currentTarget.style.backgroundColor = '#059669',\n                                onMouseOut: (e)=>e.currentTarget.style.backgroundColor = '#10b981',\n                                children: \"\\uD83E\\uDDEA Add Sample Challenges\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    id: \"error-display\",\n                    style: {\n                        display: 'none',\n                        backgroundColor: '#fef2f2',\n                        border: '1px solid #fecaca',\n                        color: '#dc2626',\n                        padding: '15px',\n                        borderRadius: '6px',\n                        marginBottom: '20px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"Error:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 9\n                        }, this),\n                        \" \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            id: \"error-message\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 33\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: '#fffbeb',\n                        border: '1px solid #fed7aa',\n                        borderRadius: '8px',\n                        padding: '20px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            style: {\n                                fontSize: '16px',\n                                fontWeight: '600',\n                                marginBottom: '10px',\n                                color: '#92400e'\n                            },\n                            children: \"ℹ️ Sync Information\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            style: {\n                                fontSize: '14px',\n                                color: '#92400e',\n                                lineHeight: '1.6'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Challenge Sync:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                            lineNumber: 298,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" Publishes CMS challenges to the main application for users to access\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"User Sync:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" Creates accounts in the main application for CMS content creators and educators\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Admin Only:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" Only CMS administrators can perform sync operations\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Safe Operation:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" Sync operations are idempotent and won't create duplicates\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                    src: \"/admin/sync/sync.js\",\n                    defer: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                    lineNumber: 306,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                    dangerouslySetInnerHTML: {\n                        __html: \"\\n            console.log('\\uD83D\\uDD04 Sync page loaded');\\n\\n            // Fetch and update stats\\n            window.fetchStats = async function() {\\n              try {\\n                console.log('Fetching stats...');\\n                const response = await fetch('/api/sync/challenges');\\n                const data = await response.json();\\n                console.log('Stats data:', data);\\n\\n                if (data.success && data.stats) {\\n                  document.getElementById('cms-challenges-count').textContent = data.stats.cmsChallenge || '0';\\n                  document.getElementById('synced-challenges-count').textContent = data.stats.mainAppCmsChallenge || '0';\\n                }\\n              } catch (error) {\\n                console.error('Error fetching stats:', error);\\n              }\\n            };\\n\\n            // Simple test data function\\n            window.addTestDataSimple = async function() {\\n              try {\\n                console.log('Adding test data...');\\n                const response = await fetch('/api/test-data', {\\n                  method: 'POST',\\n                  headers: { 'Content-Type': 'application/json' }\\n                });\\n                const data = await response.json();\\n                console.log('Test data result:', data);\\n                alert('Test data result: ' + JSON.stringify(data, null, 2));\\n\\n                // Refresh stats\\n                window.location.reload();\\n              } catch (error) {\\n                console.error('Error:', error);\\n                alert('Error: ' + error.message);\\n              }\\n            };\\n\\n            // Simple sync function\\n            window.syncChallengesSimple = async function() {\\n              try {\\n                console.log('Syncing challenges...');\\n                const response = await fetch('/api/sync/challenges', {\\n                  method: 'POST',\\n                  headers: { 'Content-Type': 'application/json' }\\n                });\\n                const data = await response.json();\\n                console.log('Sync result:', data);\\n                alert('Sync result: ' + JSON.stringify(data, null, 2));\\n\\n                // Refresh stats\\n                window.location.reload();\\n              } catch (error) {\\n                console.error('Error:', error);\\n                alert('Error: ' + error.message);\\n              }\\n            };\\n\\n            // Add click handlers when DOM is ready\\n            document.addEventListener('DOMContentLoaded', function() {\\n              const addTestBtn = document.getElementById('add-test-data-btn');\\n              const syncBtn = document.getElementById('sync-challenges-btn');\\n\\n              if (addTestBtn) {\\n                addTestBtn.onclick = window.addTestDataSimple;\\n                console.log('✅ Added test data button handler');\\n              }\\n\\n              if (syncBtn) {\\n                syncBtn.onclick = window.syncChallengesSimple;\\n                console.log('✅ Added sync button handler');\\n              }\\n            });\\n          \"\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                    dangerouslySetInnerHTML: {\n                        __html: \"\\n            // Add floating sync button to CMS admin panel\\n            (function() {\\n              function addSyncButtonToOtherPages() {\\n                // Only add on non-sync pages\\n                if (window.location.pathname.includes('/admin/sync')) return;\\n\\n                // Check if button already exists\\n                if (document.getElementById('floating-sync-btn')) return;\\n\\n                const syncButton = document.createElement('div');\\n                syncButton.id = 'floating-sync-btn';\\n                syncButton.style.cssText = `\\n                  position: fixed;\\n                  bottom: 20px;\\n                  right: 20px;\\n                  z-index: 9999;\\n                  background: linear-gradient(135deg, #0070f3, #0056b3);\\n                  color: white;\\n                  width: 60px;\\n                  height: 60px;\\n                  border-radius: 50%;\\n                  display: flex;\\n                  align-items: center;\\n                  justify-content: center;\\n                  cursor: pointer;\\n                  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\\n                  transition: all 0.3s ease;\\n                  font-size: 24px;\\n                  border: 2px solid white;\\n                `;\\n\\n                syncButton.innerHTML = '\\uD83D\\uDD04';\\n                syncButton.title = 'Open Sync Management Panel';\\n\\n                syncButton.addEventListener('mouseenter', function() {\\n                  this.style.transform = 'scale(1.1)';\\n                });\\n\\n                syncButton.addEventListener('mouseleave', function() {\\n                  this.style.transform = 'scale(1)';\\n                });\\n\\n                syncButton.addEventListener('click', function() {\\n                  window.location.href = '/admin/sync';\\n                });\\n\\n                document.body.appendChild(syncButton);\\n              }\\n\\n              // Try to add button to parent window (if in iframe)\\n              try {\\n                if (window.parent && window.parent !== window) {\\n                  window.parent.postMessage({type: 'ADD_SYNC_BUTTON'}, '*');\\n                }\\n              } catch(e) {}\\n\\n              // Add to current window\\n              setTimeout(addSyncButtonToOtherPages, 1000);\\n            })();\\n          \"\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                    lineNumber: 389,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n_c = SyncPage;\nvar _c;\n$RefreshReg$(_c, \"SyncPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(payload)/admin/sync/page.tsx\n"));

/***/ })

});