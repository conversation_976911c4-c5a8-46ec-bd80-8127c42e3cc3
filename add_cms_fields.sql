-- Add CMS integration fields to profiles table
ALTER TABLE "profiles" ADD COLUMN IF NOT EXISTS "cms_user_id" TEXT;
ALTER TABLE "profiles" ADD COLUMN IF NOT EXISTS "is_cms_user" BOOLEAN DEFAULT false;
ALTER TABLE "profiles" ADD COLUMN IF NOT EXISTS "cms_role" TEXT;
ALTER TABLE "profiles" ADD COLUMN IF NOT EXISTS "cms_specialties" TEXT[] DEFAULT ARRAY[]::TEXT[];
ALTER TABLE "profiles" ADD COLUMN IF NOT EXISTS "bio" TEXT;

-- Clean up failed migration record
DELETE FROM "_prisma_migrations" WHERE migration_name = '20241214_add_content_hash';
