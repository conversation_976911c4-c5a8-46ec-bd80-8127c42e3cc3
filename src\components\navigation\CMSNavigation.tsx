'use client';

import { useState, useEffect } from 'react';
import { getChallenges, getStoryTemplates } from '@/lib/cms-api';
import { ChevronDown, Brush, BookOpen, GraduationCap } from 'lucide-react';

export function CMSNavigation() {
  const [isOpen, setIsOpen] = useState(false);
  const [featuredChallenges, setFeaturedChallenges] = useState<Array<{id: string; title: string; slug: string}>>([]);
  const [featuredStories, setFeaturedStories] = useState<Array<{id: string; title: string; slug: string}>>([]);

  useEffect(() => {
    async function loadFeaturedContent() {
      try {
        const [challenges, stories] = await Promise.all([
          getChallenges({ featured: true }),
          getStoryTemplates()
        ]);
        
        setFeaturedChallenges(challenges.slice(0, 3));
        setFeaturedStories(stories.slice(0, 3));
      } catch (error) {
        console.error('Error loading featured content:', error);
      }
    }

    loadFeaturedContent();
  }, []);

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-1 px-4 py-2 text-gray-700 hover:text-blue-600 transition-colors"
      >
        Create
        <ChevronDown className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Dropdown Menu */}
          <div className="absolute top-full left-0 mt-2 w-96 bg-white rounded-lg shadow-xl border border-gray-200 z-20">
            <div className="p-6">
              {/* Challenges Section */}
              <div className="mb-6">
                <div className="flex items-center gap-2 mb-3">
                  <Brush className="w-5 h-5 text-pink-500" />
                  <h3 className="font-semibold text-gray-800">Creative Challenges</h3>
                </div>
                
                {featuredChallenges.length > 0 ? (
                  <div className="space-y-2">
                    {featuredChallenges.map((challenge) => (
                      <a
                        key={challenge.id}
                        href={`/challenges/${challenge.slug}`}
                        className="block p-3 rounded-lg hover:bg-gray-50 transition-colors"
                        onClick={() => setIsOpen(false)}
                      >
                        <div className="font-medium text-gray-800 text-sm">{challenge.title}</div>
                        <div className="text-xs text-gray-600 mt-1">
                          {challenge.category} • {challenge.estimatedTime} min
                        </div>
                      </a>
                    ))}
                    <a
                      href="/dashboard/creative?tab=challenges"
                      className="block text-sm text-blue-600 hover:text-blue-700 font-medium mt-2"
                      onClick={() => setIsOpen(false)}
                    >
                      View all challenges →
                    </a>
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">No featured challenges available</p>
                )}
              </div>

              {/* Stories Section */}
              <div className="mb-6">
                <div className="flex items-center gap-2 mb-3">
                  <BookOpen className="w-5 h-5 text-blue-500" />
                  <h3 className="font-semibold text-gray-800">Story Templates</h3>
                </div>
                
                {featuredStories.length > 0 ? (
                  <div className="space-y-2">
                    {featuredStories.map((story) => (
                      <a
                        key={story.id}
                        href={`/stories/${story.slug}`}
                        className="block p-3 rounded-lg hover:bg-gray-50 transition-colors"
                        onClick={() => setIsOpen(false)}
                      >
                        <div className="font-medium text-gray-800 text-sm">{story.title}</div>
                        <div className="text-xs text-gray-600 mt-1">
                          {story.genre?.[0]} • {story.ageGroup?.[0]} years
                        </div>
                      </a>
                    ))}
                    <a
                      href="/dashboard/creative?tab=stories"
                      className="block text-sm text-blue-600 hover:text-blue-700 font-medium mt-2"
                      onClick={() => setIsOpen(false)}
                    >
                      View all story templates →
                    </a>
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">No story templates available</p>
                )}
              </div>

              {/* Learning Section */}
              <div>
                <div className="flex items-center gap-2 mb-3">
                  <GraduationCap className="w-5 h-5 text-green-500" />
                  <h3 className="font-semibold text-gray-800">Learning Hub</h3>
                </div>
                
                <div className="space-y-2">
                  <a
                    href="/dashboard/creative?tab=learning"
                    className="block p-3 rounded-lg hover:bg-gray-50 transition-colors"
                    onClick={() => setIsOpen(false)}
                  >
                    <div className="font-medium text-gray-800 text-sm">Educational Resources</div>
                    <div className="text-xs text-gray-600 mt-1">
                      Tutorials, guides, and learning materials
                    </div>
                  </a>
                  
                  <a
                    href="/dashboard/creative?tab=tools"
                    className="block p-3 rounded-lg hover:bg-gray-50 transition-colors"
                    onClick={() => setIsOpen(false)}
                  >
                    <div className="font-medium text-gray-800 text-sm">Creative Tools</div>
                    <div className="text-xs text-gray-600 mt-1">
                      Digital tools for art, music, and more
                    </div>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
