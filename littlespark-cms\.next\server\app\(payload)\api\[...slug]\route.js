(()=>{var e={};e.id=7951,e.ids=[7951],e.modules={91043:e=>{"use strict";e.exports=require("@aws-sdk/client-s3")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},82015:e=>{"use strict";e.exports=require("react")},8732:e=>{"use strict";e.exports=require("react/jsx-runtime")},79428:e=>{"use strict";e.exports=require("buffer")},79646:e=>{"use strict";e.exports=require("child_process")},55511:e=>{"use strict";e.exports=require("crypto")},14985:e=>{"use strict";e.exports=require("dns")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},73496:e=>{"use strict";e.exports=require("http2")},55591:e=>{"use strict";e.exports=require("https")},8086:e=>{"use strict";e.exports=require("module")},91645:e=>{"use strict";e.exports=require("net")},21820:e=>{"use strict";e.exports=require("os")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},11997:e=>{"use strict";e.exports=require("punycode")},4984:e=>{"use strict";e.exports=require("readline")},27910:e=>{"use strict";e.exports=require("stream")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},28855:e=>{"use strict";e.exports=import("@libsql/client")},14902:e=>{"use strict";e.exports=import("@payloadcms/next/routes")},34589:e=>{"use strict";e.exports=require("node:assert")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},4573:e=>{"use strict";e.exports=require("node:buffer")},37540:e=>{"use strict";e.exports=require("node:console")},77598:e=>{"use strict";e.exports=require("node:crypto")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},40610:e=>{"use strict";e.exports=require("node:dns")},78474:e=>{"use strict";e.exports=require("node:events")},73024:e=>{"use strict";e.exports=require("node:fs")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},37067:e=>{"use strict";e.exports=require("node:http")},32467:e=>{"use strict";e.exports=require("node:http2")},98995:e=>{"use strict";e.exports=require("node:module")},77030:e=>{"use strict";e.exports=require("node:net")},48161:e=>{"use strict";e.exports=require("node:os")},76760:e=>{"use strict";e.exports=require("node:path")},643:e=>{"use strict";e.exports=require("node:perf_hooks")},1708:e=>{"use strict";e.exports=require("node:process")},41792:e=>{"use strict";e.exports=require("node:querystring")},80099:e=>{"use strict";e.exports=require("node:sqlite")},57075:e=>{"use strict";e.exports=require("node:stream")},37830:e=>{"use strict";e.exports=require("node:stream/web")},41692:e=>{"use strict";e.exports=require("node:tls")},73136:e=>{"use strict";e.exports=require("node:url")},57975:e=>{"use strict";e.exports=require("node:util")},73429:e=>{"use strict";e.exports=require("node:util/types")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},38522:e=>{"use strict";e.exports=require("node:zlib")},65022:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{patchFetch:()=>n,routeModule:()=>a,serverHooks:()=>q,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>d});var i=t(42706),u=t(28203),o=t(45994),p=t(97580),c=e([p]);p=(c.then?(await c)():c)[0];let a=new i.AppRouteRouteModule({definition:{kind:u.RouteKind.APP_ROUTE,page:"/(payload)/api/[...slug]/route",pathname:"/api/[...slug]",filename:"route",bundlePath:"app/(payload)/api/[...slug]/route"},resolvedPagePath:"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\(payload)\\api\\[...slug]\\route.ts",nextConfigOutput:"standalone",userland:p}),{workAsyncStorage:x,workUnitAsyncStorage:d,serverHooks:q}=a;function n(){return(0,o.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:d})}s()}catch(e){s(e)}})},69972:()=>{},29652:()=>{},42706:(e,r,t)=>{"use strict";e.exports=t(44870)},97580:(e,r,t)=>{"use strict";t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{DELETE:()=>n,GET:()=>p,OPTIONS:()=>d,PATCH:()=>a,POST:()=>c,PUT:()=>x});var i=t(17750);t(63046);var u=t(14902),o=e([i,u]);[i,u]=o.then?(await o)():o;let p=(0,u.REST_GET)(i.A),c=(0,u.REST_POST)(i.A),n=(0,u.REST_DELETE)(i.A),a=(0,u.REST_PATCH)(i.A),x=(0,u.REST_PUT)(i.A),d=(0,u.REST_OPTIONS)(i.A);s()}catch(e){s(e)}})},63046:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[5994,2259,7750],()=>t(65022));module.exports=s})();