generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model Profile {
  id                  String    @id @db.Uuid
  email               String    @unique
  full_name           String?
  avatar_url          String?
  created_at          DateTime  @default(now()) @db.Timestamptz(6)
  updated_at          DateTime  @default(now()) @updatedAt @db.Timestamptz(6)
  subscription_id     String?
  subscription_status String?
  plan_id             String?
  plan_name           String?
  billing_cycle       String?
  subscription_start  DateTime?
  subscription_end    DateTime?
  trial_start         DateTime?
  trial_end           DateTime?
  trial_used          Boolean   @default(false)
  stripe_customer_id  String?
  payment_method_id   String?

  // Relations
  payments            Payment[]
  user_content        UserContent[]
  challenge_completions ChallengeCompletion[]

  @@map("profiles")
}

model Payment {
  id                String   @id @default(cuid())
  profile_id        String   @db.Uuid
  stripe_payment_id String
  amount            Decimal  @db.Decimal(10, 2)
  currency          String   @default("usd")
  status            String
  plan_id           String?
  plan_name         String?
  payment_date      DateTime @default(now()) @db.Timestamptz(6)
  created_at        DateTime @default(now()) @db.Timestamptz(6)
  updated_at        DateTime @default(now()) @updatedAt @db.Timestamptz(6)
  profile           Profile  @relation(fields: [profile_id], references: [id], onDelete: Cascade)

  @@map("payments")
}

model Challenge {
  id                    String                 @id
  title                 String
  description           String
  difficulty            String                 // 'easy', 'medium', 'hard'
  type                  String                 // 'story', 'art', 'music', 'game'
  prompt                String
  is_active             Boolean                @default(true)
  created_by            String                 @default("system") // 'admin', 'ai', 'system'
  valid_until           DateTime?              // For time-limited challenges
  created_at            DateTime               @default(now()) @db.Timestamptz(6)
  updated_at            DateTime               @default(now()) @updatedAt @db.Timestamptz(6)

  // Relations
  user_content          UserContent[]
  challenge_completions ChallengeCompletion[]

  @@map("challenges")
}

model UserContent {
  id               String    @id @default(uuid()) @db.Uuid
  user_id          String    @db.Uuid
  type             String    // 'story', 'art', 'music', 'chat', 'challenges', 'mindspark_history'
  title            String?
  content_metadata Json?     // Flexible JSON field for different content types
  preview_url      String?   // For images, audio files, etc.
  challenge_id     String?   // Link to challenge if content was created for a challenge
  content_hash     String?   // Hash of content to prevent duplicates
  created_at       DateTime  @default(now()) @db.Timestamptz(6)
  updated_at       DateTime  @default(now()) @updatedAt @db.Timestamptz(6)

  // Relations
  profile          Profile   @relation(fields: [user_id], references: [id], onDelete: Cascade)
  challenge        Challenge? @relation(fields: [challenge_id], references: [id], onDelete: SetNull)
  challenge_completion ChallengeCompletion?

  @@index([user_id, type])
  @@index([challenge_id])
  @@index([user_id, content_hash]) // For duplicate prevention
  @@map("user_content")
}

model ChallengeCompletion {
  id           String      @id @default(uuid()) @db.Uuid
  user_id      String      @db.Uuid
  challenge_id String
  content_id   String      @unique @db.Uuid // The content that completed the challenge
  completed_at DateTime    @default(now()) @db.Timestamptz(6)

  // Relations
  profile      Profile     @relation(fields: [user_id], references: [id], onDelete: Cascade)
  challenge    Challenge   @relation(fields: [challenge_id], references: [id], onDelete: Cascade)
  content      UserContent @relation(fields: [content_id], references: [id], onDelete: Cascade)

  @@unique([user_id, challenge_id]) // User can only complete each challenge once
  @@index([user_id])
  @@index([challenge_id])
  @@map("challenge_completions")
}