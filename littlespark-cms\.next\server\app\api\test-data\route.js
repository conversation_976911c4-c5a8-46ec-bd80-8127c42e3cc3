(()=>{var e={};e.id=7831,e.ids=[7831],e.modules={91043:e=>{"use strict";e.exports=require("@aws-sdk/client-s3")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74552:e=>{"use strict";e.exports=require("pino")},14007:e=>{"use strict";e.exports=require("pino-pretty")},79428:e=>{"use strict";e.exports=require("buffer")},79646:e=>{"use strict";e.exports=require("child_process")},55511:e=>{"use strict";e.exports=require("crypto")},14985:e=>{"use strict";e.exports=require("dns")},94735:e=>{"use strict";e.exports=require("events")},29021:e=>{"use strict";e.exports=require("fs")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},73496:e=>{"use strict";e.exports=require("http2")},55591:e=>{"use strict";e.exports=require("https")},8086:e=>{"use strict";e.exports=require("module")},91645:e=>{"use strict";e.exports=require("net")},21820:e=>{"use strict";e.exports=require("os")},33873:e=>{"use strict";e.exports=require("path")},19771:e=>{"use strict";e.exports=require("process")},11997:e=>{"use strict";e.exports=require("punycode")},4984:e=>{"use strict";e.exports=require("readline")},27910:e=>{"use strict";e.exports=require("stream")},34631:e=>{"use strict";e.exports=require("tls")},79551:e=>{"use strict";e.exports=require("url")},28354:e=>{"use strict";e.exports=require("util")},74075:e=>{"use strict";e.exports=require("zlib")},28855:e=>{"use strict";e.exports=import("@libsql/client")},34589:e=>{"use strict";e.exports=require("node:assert")},16698:e=>{"use strict";e.exports=require("node:async_hooks")},4573:e=>{"use strict";e.exports=require("node:buffer")},37540:e=>{"use strict";e.exports=require("node:console")},77598:e=>{"use strict";e.exports=require("node:crypto")},53053:e=>{"use strict";e.exports=require("node:diagnostics_channel")},40610:e=>{"use strict";e.exports=require("node:dns")},78474:e=>{"use strict";e.exports=require("node:events")},73024:e=>{"use strict";e.exports=require("node:fs")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},37067:e=>{"use strict";e.exports=require("node:http")},32467:e=>{"use strict";e.exports=require("node:http2")},98995:e=>{"use strict";e.exports=require("node:module")},77030:e=>{"use strict";e.exports=require("node:net")},48161:e=>{"use strict";e.exports=require("node:os")},76760:e=>{"use strict";e.exports=require("node:path")},643:e=>{"use strict";e.exports=require("node:perf_hooks")},1708:e=>{"use strict";e.exports=require("node:process")},41792:e=>{"use strict";e.exports=require("node:querystring")},80099:e=>{"use strict";e.exports=require("node:sqlite")},57075:e=>{"use strict";e.exports=require("node:stream")},37830:e=>{"use strict";e.exports=require("node:stream/web")},41692:e=>{"use strict";e.exports=require("node:tls")},73136:e=>{"use strict";e.exports=require("node:url")},57975:e=>{"use strict";e.exports=require("node:util")},73429:e=>{"use strict";e.exports=require("node:util/types")},75919:e=>{"use strict";e.exports=require("node:worker_threads")},38522:e=>{"use strict";e.exports=require("node:zlib")},85758:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{patchFetch:()=>u,routeModule:()=>l,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>p});var i=r(42706),o=r(28203),a=r(45994),n=r(66468),c=e([n]);n=(c.then?(await c)():c)[0];let l=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/test-data/route",pathname:"/api/test-data",filename:"route",bundlePath:"app/api/test-data/route"},resolvedPagePath:"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\test-data\\route.ts",nextConfigOutput:"standalone",userland:n}),{workAsyncStorage:d,workUnitAsyncStorage:p,serverHooks:m}=l;function u(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:p})}s()}catch(e){s(e)}})},96487:()=>{},78335:()=>{},66468:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{GET:()=>u,POST:()=>c});var i=r(39187),o=r(66280),a=r(17750),n=e([a]);async function c(){try{console.log("\uD83E\uDDEA [CMS-TEST] Adding sample challenges for testing");let e=await (0,o.nm0)({config:a.A}),t=[{title:"Create a Digital Art Masterpiece",description:"Design a stunning digital artwork using any digital art tool",category:"art",difficulty:"medium",prompt:"Create a digital artwork that represents your vision of the future. Use vibrant colors and imaginative elements.",instructions:"Use any digital art software like Procreate, Photoshop, or free alternatives like GIMP. Focus on composition, color harmony, and storytelling.",ageGroup:["12-14"],status:"published",is_active:!0,syncStatus:"not_synced",estimatedTime:45,subscriptionTier:"free",learningObjectives:[{objective:"Learn digital art composition techniques"},{objective:"Understand color theory and harmony"}],materials:[{material:"Digital art software (Procreate, Photoshop, GIMP)"},{material:"Drawing tablet or touchscreen device"}],createdBy:1},{title:"Write a Short Story Adventure",description:"Craft an engaging short story with compelling characters",category:"story",difficulty:"easy",prompt:"Write a 500-word adventure story about a character who discovers a hidden door in their house.",instructions:"Include dialogue, descriptive language, and a clear beginning, middle, and end. Make your characters relatable and your plot engaging.",ageGroup:["6-8","9-11","12-14"],status:"published",is_active:!0,syncStatus:"not_synced",estimatedTime:30,subscriptionTier:"free",learningObjectives:[{objective:"Develop creative writing skills"},{objective:"Learn story structure and character development"}],materials:[{material:"Notebook or computer for writing"},{material:"Imagination and creativity"}],createdBy:1},{title:"Compose a Musical Melody",description:"Create an original musical composition",category:"music",difficulty:"hard",prompt:"Compose a 2-minute instrumental piece that tells a story without words.",instructions:"Use any music creation software or traditional instruments. Focus on melody, rhythm, and emotional expression.",ageGroup:["12-14"],status:"published",is_active:!0,syncStatus:"not_synced",estimatedTime:60,subscriptionTier:"premium",learningObjectives:[{objective:"Learn music composition basics"},{objective:"Understand melody and rhythm creation"}],materials:[{material:"Music creation software or instruments"},{material:"Audio recording device"}],createdBy:1},{title:"Build a Simple Game",description:"Create a fun and interactive game",category:"game",difficulty:"medium",prompt:"Build a simple puzzle or arcade-style game that others can play and enjoy.",instructions:"Use any programming language or game development tool. Focus on gameplay mechanics and user experience.",ageGroup:["12-14"],status:"published",is_active:!0,syncStatus:"not_synced",estimatedTime:90,subscriptionTier:"premium",learningObjectives:[{objective:"Learn basic game development concepts"},{objective:"Understand user interface design"}],materials:[{material:"Computer with game development software"},{material:"Basic programming knowledge"}],createdBy:1},{title:"Create a Stop-Motion Video",description:"Produce a creative stop-motion animation",category:"video",difficulty:"medium",prompt:"Create a 30-second stop-motion video that tells a simple story or demonstrates a concept.",instructions:"Use everyday objects, clay, or drawings. Take photos for each frame and compile them into a video.",ageGroup:["6-8","9-11","12-14"],status:"published",is_active:!0,syncStatus:"not_synced",estimatedTime:60,subscriptionTier:"free",learningObjectives:[{objective:"Learn animation principles"},{objective:"Understand video production basics"}],materials:[{material:"Camera or smartphone"},{material:"Objects for animation (clay, toys, drawings)"},{material:"Video editing software"}],createdBy:1}],r=0,s=0;for(let i of t)try{if((await e.find({collection:"challenges",where:{title:{equals:i.title}},limit:1})).docs.length>0){console.log(`⏭️ [CMS-TEST] Skipping existing challenge: ${i.title}`),s++;continue}console.log(`✅ [CMS-TEST] Created challenge: ${i.title}`),r++}catch(e){console.error(`❌ [CMS-TEST] Error creating challenge ${i.title}:`,e)}return console.log(`🎉 [CMS-TEST] Test data creation complete: ${r} created, ${s} skipped`),i.NextResponse.json({success:!0,message:"Sample challenges created successfully",stats:{total:t.length,created:r,skipped:s}})}catch(e){return console.error("❌ [CMS-TEST] Error creating test data:",e),i.NextResponse.json({success:!1,error:"Failed to create test data",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function u(){try{let e=await (0,o.nm0)({config:a.A}),t=await e.find({collection:"challenges",limit:100});return i.NextResponse.json({success:!0,stats:{totalChallenges:t.totalDocs,publishedChallenges:t.docs.filter(e=>"published"===e.status).length,draftChallenges:t.docs.filter(e=>"draft"===e.status).length},challenges:t.docs.map(e=>({id:e.id,title:e.title,category:e.category,difficulty:e.difficulty,status:e.status,syncStatus:e.syncStatus||"not_synced"}))})}catch(e){return console.error("❌ [CMS-TEST] Error getting test data:",e),i.NextResponse.json({success:!1,error:"Failed to get test data",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}a=(n.then?(await n)():n)[0],s()}catch(e){s(e)}})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9572,9187,6425],()=>r(85758));module.exports=s})();