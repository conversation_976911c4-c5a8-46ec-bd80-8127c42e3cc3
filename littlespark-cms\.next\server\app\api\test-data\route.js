(()=>{var a={};a.id=7831,a.ids=[7831],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},643:a=>{"use strict";a.exports=require("node:perf_hooks")},1708:a=>{"use strict";a.exports=require("node:process")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:a=>{"use strict";a.exports=require("node:buffer")},4984:a=>{"use strict";a.exports=require("readline")},5754:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{handler:()=>x,patchFetch:()=>w,routeModule:()=>y,serverHooks:()=>B,workAsyncStorage:()=>z,workUnitAsyncStorage:()=>A});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(91581),v=a([u]);u=(v.then?(await v)():v)[0];let y=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/test-data/route",pathname:"/api/test-data",filename:"route",bundlePath:"app/api/test-data/route"},distDir:".next",projectDir:"",resolvedPagePath:"C:\\Users\\<USER>\\kavya-git\\spark-new\\littlespark-cms\\src\\app\\api\\test-data\\route.ts",nextConfigOutput:"",userland:u}),{workAsyncStorage:z,workUnitAsyncStorage:A,serverHooks:B}=y;function w(){return(0,g.patchFetch)({workAsyncStorage:z,workUnitAsyncStorage:A})}async function x(a,b,c){var d;let e="/api/test-data/route";"/index"===e&&(e="/");let g=await y.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:z,routerServerContext:A,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,resolvedPathname:D}=g,E=(0,j.normalizeAppPath)(e),F=!!(z.dynamicRoutes[E]||z.routes[D]);if(F&&!x){let a=!!z.routes[D],b=z.dynamicRoutes[E];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let G=null;!F||y.isDev||x||(G=D,G="/index"===G?"/":G);let H=!0===y.isDev||!F,I=F&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:z,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>y.onRequestError(a,b,d,A)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>y.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&B&&C&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!F)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await y.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})},A),b}},l=await y.handleResponse({req:a,nextConfig:w,cacheKey:G,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:z,isRoutePPREnabled:!1,isOnDemandRevalidate:B,revalidateOnlyGenerated:C,responseGenerator:k,waitUntil:c.waitUntil});if(!F)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",B?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&F||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||b instanceof s.NoFallbackError||await y.onRequestError(a,b,{routerKind:"App Router",routePath:E,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:B})}),F)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}d()}catch(a){d(a)}})},8086:a=>{"use strict";a.exports=require("module")},9288:a=>{"use strict";a.exports=require("sharp")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:a=>{"use strict";a.exports=require("punycode")},14007:a=>{"use strict";a.exports=require("pino-pretty")},14985:a=>{"use strict";a.exports=require("dns")},16698:a=>{"use strict";a.exports=require("node:async_hooks")},19771:a=>{"use strict";a.exports=require("process")},21820:a=>{"use strict";a.exports=require("os")},27910:a=>{"use strict";a.exports=require("stream")},28354:a=>{"use strict";a.exports=require("util")},28855:a=>{"use strict";a.exports=import("@libsql/client")},29021:a=>{"use strict";a.exports=require("fs")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32467:a=>{"use strict";a.exports=require("node:http2")},33873:a=>{"use strict";a.exports=require("path")},34589:a=>{"use strict";a.exports=require("node:assert")},34631:a=>{"use strict";a.exports=require("tls")},37067:a=>{"use strict";a.exports=require("node:http")},37540:a=>{"use strict";a.exports=require("node:console")},37830:a=>{"use strict";a.exports=require("node:stream/web")},38522:a=>{"use strict";a.exports=require("node:zlib")},40610:a=>{"use strict";a.exports=require("node:dns")},41692:a=>{"use strict";a.exports=require("node:tls")},41792:a=>{"use strict";a.exports=require("node:querystring")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},48161:a=>{"use strict";a.exports=require("node:os")},51455:a=>{"use strict";a.exports=require("node:fs/promises")},53053:a=>{"use strict";a.exports=require("node:diagnostics_channel")},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},57075:a=>{"use strict";a.exports=require("node:stream")},57975:a=>{"use strict";a.exports=require("node:util")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:a=>{"use strict";a.exports=require("node:fs")},73136:a=>{"use strict";a.exports=require("node:url")},73429:a=>{"use strict";a.exports=require("node:util/types")},73496:a=>{"use strict";a.exports=require("http2")},74075:a=>{"use strict";a.exports=require("zlib")},74552:a=>{"use strict";a.exports=require("pino")},75919:a=>{"use strict";a.exports=require("node:worker_threads")},76760:a=>{"use strict";a.exports=require("node:path")},77030:a=>{"use strict";a.exports=require("node:net")},77598:a=>{"use strict";a.exports=require("node:crypto")},78335:()=>{},78474:a=>{"use strict";a.exports=require("node:events")},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},79646:a=>{"use strict";a.exports=require("child_process")},79748:a=>{"use strict";a.exports=require("fs/promises")},80099:a=>{"use strict";a.exports=require("node:sqlite")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},91043:a=>{"use strict";a.exports=require("@aws-sdk/client-s3")},91581:(a,b,c)=>{"use strict";c.a(a,async(a,d)=>{try{c.r(b),c.d(b,{GET:()=>j,POST:()=>i});var e=c(32190),f=c(65143),g=c(81329),h=a([g]);async function i(a){try{console.log("\uD83E\uDDEA [CMS-TEST] Adding sample challenges for testing");let a=await (0,f.nm0)({config:g.A}),b=[{title:"Create a Digital Art Masterpiece",description:"Design a stunning digital artwork using any digital art tool",category:"art",difficulty:"medium",prompt:"Create a digital artwork that represents your vision of the future. Use vibrant colors and imaginative elements.",instructions:"Use any digital art software like Procreate, Photoshop, or free alternatives like GIMP. Focus on composition, color harmony, and storytelling.",ageGroup:["12-14"],status:"published",is_active:!0,syncStatus:"not_synced",estimatedTime:45,subscriptionTier:"free",learningObjectives:[{objective:"Learn digital art composition techniques"},{objective:"Understand color theory and harmony"}],materials:[{material:"Digital art software (Procreate, Photoshop, GIMP)"},{material:"Drawing tablet or touchscreen device"}],createdBy:1},{title:"Write a Short Story Adventure",description:"Craft an engaging short story with compelling characters",category:"story",difficulty:"easy",prompt:"Write a 500-word adventure story about a character who discovers a hidden door in their house.",instructions:"Include dialogue, descriptive language, and a clear beginning, middle, and end. Make your characters relatable and your plot engaging.",ageGroup:["6-8","9-11","12-14"],status:"published",is_active:!0,syncStatus:"not_synced",estimatedTime:30,subscriptionTier:"free",learningObjectives:[{objective:"Develop creative writing skills"},{objective:"Learn story structure and character development"}],materials:[{material:"Notebook or computer for writing"},{material:"Imagination and creativity"}],createdBy:1},{title:"Compose a Musical Melody",description:"Create an original musical composition",category:"music",difficulty:"hard",prompt:"Compose a 2-minute instrumental piece that tells a story without words.",instructions:"Use any music creation software or traditional instruments. Focus on melody, rhythm, and emotional expression.",ageGroup:["12-14"],status:"published",is_active:!0,syncStatus:"not_synced",estimatedTime:60,subscriptionTier:"premium",learningObjectives:[{objective:"Learn music composition basics"},{objective:"Understand melody and rhythm creation"}],materials:[{material:"Music creation software or instruments"},{material:"Audio recording device"}],createdBy:1},{title:"Build a Simple Game",description:"Create a fun and interactive game",category:"game",difficulty:"medium",prompt:"Build a simple puzzle or arcade-style game that others can play and enjoy.",instructions:"Use any programming language or game development tool. Focus on gameplay mechanics and user experience.",ageGroup:["12-14"],status:"published",is_active:!0,syncStatus:"not_synced",estimatedTime:90,subscriptionTier:"premium",learningObjectives:[{objective:"Learn basic game development concepts"},{objective:"Understand user interface design"}],materials:[{material:"Computer with game development software"},{material:"Basic programming knowledge"}],createdBy:1},{title:"Create a Stop-Motion Video",description:"Produce a creative stop-motion animation",category:"video",difficulty:"medium",prompt:"Create a 30-second stop-motion video that tells a simple story or demonstrates a concept.",instructions:"Use everyday objects, clay, or drawings. Take photos for each frame and compile them into a video.",ageGroup:["6-8","9-11","12-14"],status:"published",is_active:!0,syncStatus:"not_synced",estimatedTime:60,subscriptionTier:"free",learningObjectives:[{objective:"Learn animation principles"},{objective:"Understand video production basics"}],materials:[{material:"Camera or smartphone"},{material:"Objects for animation (clay, toys, drawings)"},{material:"Video editing software"}],createdBy:1}],c=0,d=0;for(let e of b)try{if((await a.find({collection:"challenges",where:{title:{equals:e.title}},limit:1})).docs.length>0){console.log(`⏭️ [CMS-TEST] Skipping existing challenge: ${e.title}`),d++;continue}let b={...e,slug:e.title.toLowerCase().replace(/[^a-z0-9]+/g,"-").replace(/(^-|-$)/g,"")};await a.create({collection:"challenges",data:b}),console.log(`✅ [CMS-TEST] Created challenge: ${e.title}`),c++}catch(a){console.error(`❌ [CMS-TEST] Error creating challenge ${e.title}:`,a)}return console.log(`🎉 [CMS-TEST] Test data creation complete: ${c} created, ${d} skipped`),e.NextResponse.json({success:!0,message:"Sample challenges created successfully",stats:{total:b.length,created:c,skipped:d}})}catch(a){return console.error("❌ [CMS-TEST] Error creating test data:",a),e.NextResponse.json({success:!1,error:"Failed to create test data",details:a instanceof Error?a.message:"Unknown error"},{status:500})}}async function j(a){try{let a=await (0,f.nm0)({config:g.A}),b=await a.find({collection:"challenges",limit:100});return e.NextResponse.json({success:!0,stats:{totalChallenges:b.totalDocs,publishedChallenges:b.docs.filter(a=>"published"===a.status).length,draftChallenges:b.docs.filter(a=>"draft"===a.status).length},challenges:b.docs.map(a=>({id:a.id,title:a.title,category:a.category,difficulty:a.difficulty,status:a.status,syncStatus:a.syncStatus||"not_synced"}))})}catch(a){return console.error("❌ [CMS-TEST] Error getting test data:",a),e.NextResponse.json({success:!1,error:"Failed to get test data",details:a instanceof Error?a.message:"Unknown error"},{status:500})}}g=(h.then?(await h)():h)[0],d()}catch(a){d(a)}})},91645:a=>{"use strict";a.exports=require("net")},94735:a=>{"use strict";a.exports=require("events")},96487:()=>{},98995:a=>{"use strict";a.exports=require("node:module")}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[1103,9598,6055,6622],()=>b(b.s=5754));module.exports=c})();