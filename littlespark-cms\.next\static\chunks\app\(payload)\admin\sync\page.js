/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/(payload)/admin/sync/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravi1%5C%5Ckavya-git%5C%5Cspark-new%5C%5Clittlespark-cms%5C%5Csrc%5C%5Capp%5C%5C(payload)%5C%5Cadmin%5C%5Csync%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravi1%5C%5Ckavya-git%5C%5Cspark-new%5C%5Clittlespark-cms%5C%5Csrc%5C%5Capp%5C%5C(payload)%5C%5Cadmin%5C%5Csync%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(payload)/admin/sync/page.tsx */ \"(app-pages-browser)/./src/app/(payload)/admin/sync/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDcmF2aTElNUMlNUNrYXZ5YS1naXQlNUMlNUNzcGFyay1uZXclNUMlNUNsaXR0bGVzcGFyay1jbXMlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUMocGF5bG9hZCklNUMlNUNhZG1pbiU1QyU1Q3N5bmMlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLHdNQUF3SSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxccmF2aTFcXFxca2F2eWEtZ2l0XFxcXHNwYXJrLW5ld1xcXFxsaXR0bGVzcGFyay1jbXNcXFxcc3JjXFxcXGFwcFxcXFwocGF5bG9hZClcXFxcYWRtaW5cXFxcc3luY1xcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravi1%5C%5Ckavya-git%5C%5Cspark-new%5C%5Clittlespark-cms%5C%5Csrc%5C%5Capp%5C%5C(payload)%5C%5Cadmin%5C%5Csync%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xccmF2aTFcXGthdnlhLWdpdFxcc3BhcmstbmV3XFxsaXR0bGVzcGFyay1jbXNcXG5vZGVfbW9kdWxlc1xcbmV4dFxcZGlzdFxcY29tcGlsZWRcXHJlYWN0XFxqc3gtZGV2LXJ1bnRpbWUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJykge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5wcm9kdWN0aW9uLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/(payload)/admin/sync/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/(payload)/admin/sync/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SyncPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction SyncPage() {\n    _s();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        cmsChallenge: 0,\n        mainAppCmsChallenge: 0\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"SyncPage.useEffect\": ()=>{\n            setMounted(true);\n            fetchStats();\n        }\n    }[\"SyncPage.useEffect\"], []);\n    const fetchStats = async ()=>{\n        try {\n            const response = await fetch('/api/sync/challenges');\n            const data = await response.json();\n            if (data.success && data.stats) {\n                setStats({\n                    cmsChallenge: data.stats.cmsChallenge || 0,\n                    mainAppCmsChallenge: data.stats.mainAppCmsChallenge || 0\n                });\n            }\n        } catch (error) {\n            console.error('Error fetching stats:', error);\n        }\n    };\n    const addTestData = async ()=>{\n        try {\n            var _data_stats, _data_stats1;\n            const response = await fetch('/api/test-data', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            const data = await response.json();\n            alert(\"Test data result: \".concat(((_data_stats = data.stats) === null || _data_stats === void 0 ? void 0 : _data_stats.created) || 0, \" challenges created, \").concat(((_data_stats1 = data.stats) === null || _data_stats1 === void 0 ? void 0 : _data_stats1.skipped) || 0, \" skipped\"));\n            await fetchStats();\n        } catch (error) {\n            alert('Error: ' + error.message);\n        }\n    };\n    const syncChallenges = async ()=>{\n        try {\n            var _data_stats;\n            const response = await fetch('/api/sync/challenges', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            const data = await response.json();\n            alert(\"Sync result: \".concat(((_data_stats = data.stats) === null || _data_stats === void 0 ? void 0 : _data_stats.synced) || 0, \" challenges synced successfully\"));\n            await fetchStats();\n        } catch (error) {\n            alert('Error: ' + error.message);\n        }\n    };\n    if (!mounted) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                padding: '20px',\n                textAlign: 'center'\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"Loading sync panel...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                lineNumber: 63,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n            lineNumber: 62,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"sync-page\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            style: {\n                padding: '20px',\n                maxWidth: '1200px',\n                margin: '0 auto'\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        marginBottom: '30px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            style: {\n                                fontSize: '28px',\n                                fontWeight: 'bold',\n                                marginBottom: '10px'\n                            },\n                            children: \"\\uD83D\\uDD04 Sync Management\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            style: {\n                                color: '#666',\n                                fontSize: '16px'\n                            },\n                            children: \"Synchronize content and users between CMS and main application\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                display: 'inline-block',\n                                backgroundColor: '#f0f9ff',\n                                color: '#0369a1',\n                                padding: '4px 12px',\n                                borderRadius: '6px',\n                                fontSize: '14px',\n                                fontWeight: '500',\n                                marginTop: '10px'\n                            },\n                            children: \"\\uD83D\\uDEE1️ Admin Only Feature\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        display: 'grid',\n                        gridTemplateColumns: '1fr 1fr 1fr',\n                        gap: '20px',\n                        marginBottom: '30px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                border: '1px solid #e5e7eb',\n                                borderRadius: '8px',\n                                padding: '20px',\n                                backgroundColor: '#fff'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        fontSize: '20px',\n                                        fontWeight: '600',\n                                        marginBottom: '10px'\n                                    },\n                                    children: \"\\uD83D\\uDCDD Challenge Sync\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: '#666',\n                                        marginBottom: '20px',\n                                        fontSize: '14px'\n                                    },\n                                    children: \"Sync published challenges from CMS to main application\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'grid',\n                                        gridTemplateColumns: '1fr 1fr',\n                                        gap: '15px',\n                                        marginBottom: '20px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                backgroundColor: '#eff6ff',\n                                                padding: '15px',\n                                                borderRadius: '6px',\n                                                textAlign: 'center'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '24px',\n                                                        fontWeight: 'bold',\n                                                        color: '#2563eb'\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        id: \"cms-challenges-count\",\n                                                        children: stats.cmsChallenge\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '12px',\n                                                        color: '#1e40af',\n                                                        fontWeight: '500'\n                                                    },\n                                                    children: \"CMS Challenges\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                backgroundColor: '#f0fdf4',\n                                                padding: '15px',\n                                                borderRadius: '6px',\n                                                textAlign: 'center'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '24px',\n                                                        fontWeight: 'bold',\n                                                        color: '#16a34a'\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        id: \"synced-challenges-count\",\n                                                        children: stats.mainAppCmsChallenge\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                    lineNumber: 126,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '12px',\n                                                        color: '#15803d',\n                                                        fontWeight: '500'\n                                                    },\n                                                    children: \"Synced to Main App\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    id: \"sync-challenges-btn\",\n                                    onClick: syncChallenges,\n                                    style: {\n                                        width: '100%',\n                                        backgroundColor: '#2563eb',\n                                        color: 'white',\n                                        border: 'none',\n                                        padding: '12px 20px',\n                                        borderRadius: '6px',\n                                        fontSize: '14px',\n                                        fontWeight: '500',\n                                        cursor: 'pointer',\n                                        marginBottom: '10px'\n                                    },\n                                    onMouseOver: (e)=>e.currentTarget.style.backgroundColor = '#1d4ed8',\n                                    onMouseOut: (e)=>e.currentTarget.style.backgroundColor = '#2563eb',\n                                    children: \"\\uD83D\\uDD04 Sync Challenges to Main App\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"challenge-sync-result\",\n                                    style: {\n                                        fontSize: '12px',\n                                        color: '#666'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                border: '1px solid #e5e7eb',\n                                borderRadius: '8px',\n                                padding: '20px',\n                                backgroundColor: '#fff'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        fontSize: '20px',\n                                        fontWeight: '600',\n                                        marginBottom: '10px'\n                                    },\n                                    children: \"\\uD83D\\uDC65 User Sync\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: '#666',\n                                        marginBottom: '20px',\n                                        fontSize: '14px'\n                                    },\n                                    children: \"Sync CMS users to main application for content access\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'grid',\n                                        gridTemplateColumns: '1fr 1fr',\n                                        gap: '15px',\n                                        marginBottom: '20px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                backgroundColor: '#faf5ff',\n                                                padding: '15px',\n                                                borderRadius: '6px',\n                                                textAlign: 'center'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '24px',\n                                                        fontWeight: 'bold',\n                                                        color: '#9333ea'\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        id: \"cms-users-count\",\n                                                        children: \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                        lineNumber: 181,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '12px',\n                                                        color: '#7c3aed',\n                                                        fontWeight: '500'\n                                                    },\n                                                    children: \"CMS Users\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                backgroundColor: '#fff7ed',\n                                                padding: '15px',\n                                                borderRadius: '6px',\n                                                textAlign: 'center'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '24px',\n                                                        fontWeight: 'bold',\n                                                        color: '#ea580c'\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        id: \"synced-users-count\",\n                                                        children: \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '12px',\n                                                        color: '#c2410c',\n                                                        fontWeight: '500'\n                                                    },\n                                                    children: \"Synced to Main App\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 173,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    id: \"sync-users-btn\",\n                                    style: {\n                                        width: '100%',\n                                        backgroundColor: '#9333ea',\n                                        color: 'white',\n                                        border: 'none',\n                                        padding: '12px 20px',\n                                        borderRadius: '6px',\n                                        fontSize: '14px',\n                                        fontWeight: '500',\n                                        cursor: 'pointer',\n                                        marginBottom: '10px'\n                                    },\n                                    onMouseOver: (e)=>e.currentTarget.style.backgroundColor = '#7c3aed',\n                                    onMouseOut: (e)=>e.currentTarget.style.backgroundColor = '#9333ea',\n                                    children: \"\\uD83D\\uDD04 Sync Users to Main App\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"user-sync-result\",\n                                    style: {\n                                        fontSize: '12px',\n                                        color: '#666'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            style: {\n                                border: '1px solid #e5e7eb',\n                                borderRadius: '8px',\n                                padding: '20px',\n                                backgroundColor: '#fff'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    style: {\n                                        fontSize: '20px',\n                                        fontWeight: '600',\n                                        marginBottom: '10px'\n                                    },\n                                    children: \"⬅️ Import User Challenges\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    style: {\n                                        color: '#666',\n                                        marginBottom: '20px',\n                                        fontSize: '14px'\n                                    },\n                                    children: \"Import user-created challenges from main app to CMS for review\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        display: 'grid',\n                                        gridTemplateColumns: '1fr 1fr',\n                                        gap: '15px',\n                                        marginBottom: '20px'\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                backgroundColor: '#f0f9ff',\n                                                padding: '15px',\n                                                borderRadius: '6px',\n                                                textAlign: 'center'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '24px',\n                                                        fontWeight: 'bold',\n                                                        color: '#0369a1'\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        id: \"available-user-challenges-count\",\n                                                        children: \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '12px',\n                                                        color: '#0c4a6e',\n                                                        fontWeight: '500'\n                                                    },\n                                                    children: \"Available to Import\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                            lineNumber: 240,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                backgroundColor: '#f0fdf4',\n                                                padding: '15px',\n                                                borderRadius: '6px',\n                                                textAlign: 'center'\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '24px',\n                                                        fontWeight: 'bold',\n                                                        color: '#16a34a'\n                                                    },\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        id: \"imported-challenges-count\",\n                                                        children: \"-\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    style: {\n                                                        fontSize: '12px',\n                                                        color: '#15803d',\n                                                        fontWeight: '500'\n                                                    },\n                                                    children: \"Already Imported\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    id: \"import-challenges-btn\",\n                                    style: {\n                                        width: '100%',\n                                        backgroundColor: '#0369a1',\n                                        color: 'white',\n                                        border: 'none',\n                                        padding: '12px 20px',\n                                        borderRadius: '6px',\n                                        fontSize: '14px',\n                                        fontWeight: '500',\n                                        cursor: 'pointer',\n                                        marginBottom: '10px'\n                                    },\n                                    onMouseOver: (e)=>e.currentTarget.style.backgroundColor = '#0c4a6e',\n                                    onMouseOut: (e)=>e.currentTarget.style.backgroundColor = '#0369a1',\n                                    children: \"⬅️ Import User Challenges from Main App\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 268,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    id: \"import-sync-result\",\n                                    style: {\n                                        fontSize: '12px',\n                                        color: '#666'\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 288,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        textAlign: 'center',\n                        marginBottom: '30px'\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            display: 'flex',\n                            gap: '15px',\n                            justifyContent: 'center',\n                            flexWrap: 'wrap'\n                        },\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                id: \"refresh-stats-btn\",\n                                onClick: fetchStats,\n                                style: {\n                                    backgroundColor: '#f3f4f6',\n                                    color: '#374151',\n                                    border: '1px solid #d1d5db',\n                                    padding: '10px 20px',\n                                    borderRadius: '6px',\n                                    fontSize: '14px',\n                                    fontWeight: '500',\n                                    cursor: 'pointer'\n                                },\n                                onMouseOver: (e)=>e.currentTarget.style.backgroundColor = '#e5e7eb',\n                                onMouseOut: (e)=>e.currentTarget.style.backgroundColor = '#f3f4f6',\n                                children: \"\\uD83D\\uDD04 Refresh Statistics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                id: \"add-test-data-btn\",\n                                onClick: addTestData,\n                                style: {\n                                    backgroundColor: '#10b981',\n                                    color: 'white',\n                                    border: 'none',\n                                    padding: '10px 20px',\n                                    borderRadius: '6px',\n                                    fontSize: '14px',\n                                    fontWeight: '500',\n                                    cursor: 'pointer'\n                                },\n                                onMouseOver: (e)=>e.currentTarget.style.backgroundColor = '#059669',\n                                onMouseOut: (e)=>e.currentTarget.style.backgroundColor = '#10b981',\n                                children: \"\\uD83E\\uDDEA Add Sample Challenges\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                    lineNumber: 293,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    id: \"error-display\",\n                    style: {\n                        display: 'none',\n                        backgroundColor: '#fef2f2',\n                        border: '1px solid #fecaca',\n                        color: '#dc2626',\n                        padding: '15px',\n                        borderRadius: '6px',\n                        marginBottom: '20px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"Error:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 9\n                        }, this),\n                        \" \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            id: \"error-message\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 33\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                    lineNumber: 336,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        backgroundColor: '#fffbeb',\n                        border: '1px solid #fed7aa',\n                        borderRadius: '8px',\n                        padding: '20px'\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            style: {\n                                fontSize: '16px',\n                                fontWeight: '600',\n                                marginBottom: '10px',\n                                color: '#92400e'\n                            },\n                            children: \"ℹ️ Sync Information\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            style: {\n                                fontSize: '14px',\n                                color: '#92400e',\n                                lineHeight: '1.6'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Challenge Sync:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" Publishes CMS challenges to the main application for users to access\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"User Sync:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" Creates accounts in the main application for CMS content creators and educators\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Admin Only:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" Only CMS administrators can perform sync operations\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Safe Operation:\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 15\n                                        }, this),\n                                        \" Sync operations are idempotent and won't create duplicates\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 7\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                    src: \"/admin/sync/sync.js\",\n                    defer: true\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                    lineNumber: 370,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                    dangerouslySetInnerHTML: {\n                        __html: \"\\n            console.log('\\uD83D\\uDD04 Sync page loaded');\\n\\n            // Fetch and update stats\\n            window.fetchStats = async function() {\\n              try {\\n                console.log('Fetching stats...');\\n                const response = await fetch('/api/sync/challenges');\\n                const data = await response.json();\\n                console.log('Stats data:', data);\\n\\n                if (data.success && data.stats) {\\n                  document.getElementById('cms-challenges-count').textContent = data.stats.cmsChallenge || '0';\\n                  document.getElementById('synced-challenges-count').textContent = data.stats.mainAppCmsChallenge || '0';\\n                }\\n              } catch (error) {\\n                console.error('Error fetching stats:', error);\\n              }\\n            };\\n\\n            // Simple test data function\\n            window.addTestDataSimple = async function() {\\n              try {\\n                console.log('Adding test data...');\\n                const response = await fetch('/api/test-data', {\\n                  method: 'POST',\\n                  headers: { 'Content-Type': 'application/json' }\\n                });\\n                const data = await response.json();\\n                console.log('Test data result:', data);\\n                alert('Test data result: ' + JSON.stringify(data, null, 2));\\n\\n                // Refresh stats\\n                await window.fetchStats();\\n              } catch (error) {\\n                console.error('Error:', error);\\n                alert('Error: ' + error.message);\\n              }\\n            };\\n\\n            // Simple sync function\\n            window.syncChallengesSimple = async function() {\\n              try {\\n                console.log('Syncing challenges...');\\n                const response = await fetch('/api/sync/challenges', {\\n                  method: 'POST',\\n                  headers: { 'Content-Type': 'application/json' }\\n                });\\n                const data = await response.json();\\n                console.log('Sync result:', data);\\n                alert('Sync result: ' + JSON.stringify(data, null, 2));\\n\\n                // Refresh stats\\n                await window.fetchStats();\\n              } catch (error) {\\n                console.error('Error:', error);\\n                alert('Error: ' + error.message);\\n              }\\n            };\\n\\n            // Add click handlers when DOM is ready\\n            document.addEventListener('DOMContentLoaded', function() {\\n              const addTestBtn = document.getElementById('add-test-data-btn');\\n              const syncBtn = document.getElementById('sync-challenges-btn');\\n              const refreshBtn = document.getElementById('refresh-stats-btn');\\n\\n              if (addTestBtn) {\\n                addTestBtn.onclick = window.addTestDataSimple;\\n                console.log('✅ Added test data button handler');\\n              }\\n\\n              if (syncBtn) {\\n                syncBtn.onclick = window.syncChallengesSimple;\\n                console.log('✅ Added sync button handler');\\n              }\\n\\n              if (refreshBtn) {\\n                refreshBtn.onclick = window.fetchStats;\\n                console.log('✅ Added refresh stats button handler');\\n              }\\n\\n              // Load initial stats\\n              window.fetchStats();\\n            });\\n          \"\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                    lineNumber: 373,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"script\", {\n                    dangerouslySetInnerHTML: {\n                        __html: \"\\n            // Add floating sync button to CMS admin panel\\n            (function() {\\n              function addSyncButtonToOtherPages() {\\n                // Only add on non-sync pages\\n                if (window.location.pathname.includes('/admin/sync')) return;\\n\\n                // Check if button already exists\\n                if (document.getElementById('floating-sync-btn')) return;\\n\\n                const syncButton = document.createElement('div');\\n                syncButton.id = 'floating-sync-btn';\\n                syncButton.style.cssText = `\\n                  position: fixed;\\n                  bottom: 20px;\\n                  right: 20px;\\n                  z-index: 9999;\\n                  background: linear-gradient(135deg, #0070f3, #0056b3);\\n                  color: white;\\n                  width: 60px;\\n                  height: 60px;\\n                  border-radius: 50%;\\n                  display: flex;\\n                  align-items: center;\\n                  justify-content: center;\\n                  cursor: pointer;\\n                  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\\n                  transition: all 0.3s ease;\\n                  font-size: 24px;\\n                  border: 2px solid white;\\n                `;\\n\\n                syncButton.innerHTML = '\\uD83D\\uDD04';\\n                syncButton.title = 'Open Sync Management Panel';\\n\\n                syncButton.addEventListener('mouseenter', function() {\\n                  this.style.transform = 'scale(1.1)';\\n                });\\n\\n                syncButton.addEventListener('mouseleave', function() {\\n                  this.style.transform = 'scale(1)';\\n                });\\n\\n                syncButton.addEventListener('click', function() {\\n                  window.location.href = '/admin/sync';\\n                });\\n\\n                document.body.appendChild(syncButton);\\n              }\\n\\n              // Try to add button to parent window (if in iframe)\\n              try {\\n                if (window.parent && window.parent !== window) {\\n                  window.parent.postMessage({type: 'ADD_SYNC_BUTTON'}, '*');\\n                }\\n              } catch(e) {}\\n\\n              // Add to current window\\n              setTimeout(addSyncButtonToOtherPages, 1000);\\n            })();\\n          \"\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n                    lineNumber: 462,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\kavya-git\\\\spark-new\\\\littlespark-cms\\\\src\\\\app\\\\(payload)\\\\admin\\\\sync\\\\page.tsx\",\n        lineNumber: 68,\n        columnNumber: 5\n    }, this);\n}\n_s(SyncPage, \"iwKN5ET9/XcmU6MCD+jCorzLSPk=\");\n_c = SyncPage;\nvar _c;\n$RefreshReg$(_c, \"SyncPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(payload)/admin/sync/page.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cravi1%5C%5Ckavya-git%5C%5Cspark-new%5C%5Clittlespark-cms%5C%5Csrc%5C%5Capp%5C%5C(payload)%5C%5Cadmin%5C%5Csync%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);