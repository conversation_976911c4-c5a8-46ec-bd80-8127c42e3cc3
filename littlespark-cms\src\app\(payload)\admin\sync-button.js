// Add floating sync button to CMS admin panel
(function() {
  'use strict';
  
  // Wait for DOM to be ready
  function addSyncButton() {
    // Check if button already exists
    if (document.getElementById('floating-sync-btn')) {
      return;
    }
    
    // Create floating sync button
    const syncButton = document.createElement('div');
    syncButton.id = 'floating-sync-btn';
    syncButton.style.cssText = `
      position: fixed;
      bottom: 20px;
      right: 20px;
      z-index: 9999;
      background: linear-gradient(135deg, #0070f3, #0056b3);
      color: white;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      transition: all 0.3s ease;
      font-size: 24px;
      border: 2px solid white;
    `;
    
    syncButton.innerHTML = '🔄';
    syncButton.title = 'Open Sync Management Panel';
    
    // Add hover effects
    syncButton.addEventListener('mouseenter', function() {
      this.style.transform = 'scale(1.1)';
      this.style.boxShadow = '0 6px 25px rgba(0, 0, 0, 0.2)';
    });
    
    syncButton.addEventListener('mouseleave', function() {
      this.style.transform = 'scale(1)';
      this.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.15)';
    });
    
    // Add click handler
    syncButton.addEventListener('click', function() {
      window.location.href = '/admin/sync';
    });
    
    // Add to page
    document.body.appendChild(syncButton);
  }
  
  // Add button when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', addSyncButton);
  } else {
    addSyncButton();
  }
  
  // Also add button when navigating (for SPA behavior)
  let lastUrl = location.href;
  new MutationObserver(() => {
    const url = location.href;
    if (url !== lastUrl) {
      lastUrl = url;
      setTimeout(addSyncButton, 100);
    }
  }).observe(document, { subtree: true, childList: true });
  
})();
