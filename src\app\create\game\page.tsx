"use client";
import React, { useEffect, useState } from "react";
import { useSubscriptionStatus } from '@/hooks/useSubscriptionStatus';
import { useRouter, useSearchParams } from 'next/navigation';
import { Loader2 } from 'lucide-react';

export default function GamePage() {
    const { hasActiveSubscription, isLoading } = useSubscriptionStatus();
    const router = useRouter();
    const searchParams = useSearchParams();
    const [challengeData, setChallengeData] = useState(null);

    useEffect(() => {
        const challengeParam = searchParams.get('challenge');
        if (challengeParam) {
            try {
                const decoded = JSON.parse(decodeURIComponent(challengeParam));
                setChallengeData(decoded);
            } catch (error) {
                console.error('Error parsing challenge data:', error);
            }
        }
    }, [searchParams]);

    // Show loading screen while fetching subscription status
    if (isLoading) {
        return (
            <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <Loader2 className="h-12 w-12 text-[#FF6B35] animate-spin mx-auto mb-4" />
                    <p className="text-gray-600 text-lg">Loading Game Designer...</p>
                </div>
            </div>
        );
    }

    return (
        <div className="relative">
            <main className="min-h-screen bg-gradient-to-b from-white to-gray-50">
                <div className="container mx-auto px-4 py-8">
                    <div className="max-w-6xl mx-auto">
                        <div className="text-center mb-8">
                            <h1 className="text-4xl font-bold text-gray-800 mb-4">🎮 Game Designer Studio</h1>
                            <p className="text-xl text-gray-600">Create amazing games and interactive experiences</p>
                        </div>

                        {/* Challenge Information */}
                        {challengeData && (
                            <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
                                <h2 className="text-xl font-bold text-green-900 mb-2">
                                    🎮 Challenge: {challengeData.title}
                                </h2>
                                <p className="text-green-800 mb-3">{challengeData.description}</p>
                                
                                <div className="grid md:grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <h4 className="font-semibold text-green-900 mb-2">Instructions:</h4>
                                        <p className="text-green-700 whitespace-pre-line">{challengeData.instructions}</p>
                                    </div>
                                    
                                    <div>
                                        <h4 className="font-semibold text-green-900 mb-2">Learning Goals:</h4>
                                        <ul className="text-green-700 space-y-1">
                                            {challengeData.learningObjectives?.map((obj: {objective: string}, index: number) => (
                                                <li key={index}>• {obj.objective}</li>
                                            ))}
                                        </ul>
                                        
                                        {challengeData.materials && challengeData.materials.length > 0 && (
                                            <div className="mt-3">
                                                <h4 className="font-semibold text-green-900 mb-2">Materials:</h4>
                                                <ul className="text-green-700 space-y-1">
                                                    {challengeData.materials.map((mat: {material: string}, index: number) => (
                                                        <li key={index}>• {mat.material}</li>
                                                    ))}
                                                </ul>
                                            </div>
                                        )}
                                    </div>
                                </div>
                                
                                <div className="mt-4 flex items-center gap-4 text-sm text-green-600">
                                    <span>⏱️ {challengeData.estimatedTime} minutes</span>
                                    <span>📊 {challengeData.difficulty}</span>
                                </div>
                            </div>
                        )}

                        {/* Game Designer Content */}
                        <div className="bg-white rounded-lg shadow-lg p-8">
                            <div className="text-center py-16">
                                <div className="text-6xl mb-4">🚧</div>
                                <h2 className="text-2xl font-bold text-gray-800 mb-4">Game Designer Coming Soon!</h2>
                                <p className="text-gray-600 mb-6">
                                    We&apos;re building an amazing game design tool where you can create interactive games,
                                    design characters, and build exciting adventures.
                                </p>
                                <div className="space-y-2 text-left max-w-md mx-auto">
                                    <div className="flex items-center gap-2">
                                        <span className="text-green-500">✓</span>
                                        <span>Visual game design interface</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <span className="text-green-500">✓</span>
                                        <span>Character and world creation</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <span className="text-green-500">✓</span>
                                        <span>Logic and puzzle design</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <span className="text-green-500">✓</span>
                                        <span>Share and play games</span>
                                    </div>
                                </div>
                                <button
                                    onClick={() => router.push('/dashboard')}
                                    className="mt-6 bg-[#FF6B35] text-white px-6 py-3 rounded-lg font-semibold hover:bg-[#e55a2b] transition-colors"
                                >
                                    Back to Dashboard
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
            {!hasActiveSubscription && (
                <div
                    className="absolute inset-0 bg-black bg-opacity-50 cursor-pointer flex items-center justify-center"
                    onClick={() => router.push('/pricing')}
                >
                    <div className="bg-white rounded-lg p-8 max-w-md mx-4 text-center">
                        <h3 className="text-xl font-bold mb-2">Upgrade to Access Game Designer</h3>
                        <p className="text-gray-600 mb-4">
                            Create interactive games and adventures. Unlock this feature with a subscription.
                        </p>
                        <button
                            type="button"
                            onClick={() => router.push('/pricing')}
                            className="bg-[#FF6B35] text-white px-6 py-2 rounded-lg font-semibold hover:bg-[#e55a2b] transition-colors"
                        >
                            View Plans
                        </button>
                    </div>
                </div>
            )}
        </div>
    );
}
