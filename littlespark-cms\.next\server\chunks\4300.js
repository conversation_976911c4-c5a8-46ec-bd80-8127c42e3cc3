"use strict";exports.id=4300,exports.ids=[4300],exports.modules={74300:(a,b,c)=>{c.d(b,{fromIni:()=>p});var d=c(92413),e=c(71930),f=c(21905);let g=a=>(0,f.g)(a,"CREDENTIALS_PROFILE_NAMED_PROVIDER","p"),h=async(a,b,h,j={})=>{h.logger?.debug("@aws-sdk/credential-provider-ini - resolveAssumeRoleCredentials (STS)");let k=b[a],{source_profile:l,region:m}=k;if(!h.roleAssumer){let{getDefaultRoleAssumer:a}=await c.e(2686).then(c.bind(c,32686));h.roleAssumer=a({...h.clientConfig,credentialProviderLogger:h.logger,parentClientConfig:{...h?.parentClientConfig,region:m??h?.parentClientConfig?.region}},h.clientPlugins)}if(l&&l in j)throw new e.C1(`Detected a cycle attempting to resolve credentials for profile ${(0,d.Bz)(h)}. Profiles visited: `+Object.keys(j).join(", "),{logger:h.logger});h.logger?.debug(`@aws-sdk/credential-provider-ini - finding credential resolver using ${l?`source_profile=[${l}]`:`profile=[${a}]`}`);let n=l?o(l,b,h,{...j,[l]:!0},i(b[l]??{})):(await ((a,b,d)=>{let f={EcsContainer:async a=>{let{fromHttp:b}=await c.e(9189).then(c.bind(c,99189)),{fromContainerMetadata:f}=await c.e(4147).then(c.bind(c,14147));return d?.debug("@aws-sdk/credential-provider-ini - credential_source is EcsContainer"),async()=>(0,e.cy)(b(a??{}),f(a))().then(g)},Ec2InstanceMetadata:async a=>{d?.debug("@aws-sdk/credential-provider-ini - credential_source is Ec2InstanceMetadata");let{fromInstanceMetadata:b}=await c.e(4147).then(c.bind(c,14147));return async()=>b(a)().then(g)},Environment:async a=>{d?.debug("@aws-sdk/credential-provider-ini - credential_source is Environment");let{fromEnv:b}=await Promise.resolve().then(c.bind(c,4899));return async()=>b(a)().then(g)}};if(a in f)return f[a];throw new e.C1(`Unsupported credential source in profile ${b}. Got ${a}, expected EcsContainer or Ec2InstanceMetadata or Environment.`,{logger:d})})(k.credential_source,a,h.logger)(h))();if(i(k))return n.then(a=>(0,f.g)(a,"CREDENTIALS_PROFILE_SOURCE_PROFILE","o"));{let b={RoleArn:k.role_arn,RoleSessionName:k.role_session_name||`aws-sdk-js-${Date.now()}`,ExternalId:k.external_id,DurationSeconds:parseInt(k.duration_seconds||"3600",10)},{mfa_serial:c}=k;if(c){if(!h.mfaCodeProvider)throw new e.C1(`Profile ${a} requires multi-factor authentication, but no MFA code callback was provided.`,{logger:h.logger,tryNextLink:!1});b.SerialNumber=c,b.TokenCode=await h.mfaCodeProvider(c)}let d=await n;return h.roleAssumer(d,b).then(a=>(0,f.g)(a,"CREDENTIALS_PROFILE_SOURCE_PROFILE","o"))}},i=a=>!a.role_arn&&!!a.credential_source,j=async(a,b)=>c.e(5245).then(c.bind(c,95245)).then(({fromProcess:c})=>c({...a,profile:b})().then(a=>(0,f.g)(a,"CREDENTIALS_PROFILE_PROCESS","v"))),k=async(a,b,d={})=>{let{fromSSO:e}=await c.e(6717).then(c.bind(c,6717));return e({profile:a,logger:d.logger,parentClientConfig:d.parentClientConfig,clientConfig:d.clientConfig})().then(a=>b.sso_session?(0,f.g)(a,"CREDENTIALS_PROFILE_SSO","r"):(0,f.g)(a,"CREDENTIALS_PROFILE_SSO_LEGACY","t"))},l=a=>!!a&&"object"==typeof a&&"string"==typeof a.aws_access_key_id&&"string"==typeof a.aws_secret_access_key&&["undefined","string"].indexOf(typeof a.aws_session_token)>-1&&["undefined","string"].indexOf(typeof a.aws_account_id)>-1,m=async(a,b)=>{b?.logger?.debug("@aws-sdk/credential-provider-ini - resolveStaticCredentials");let c={accessKeyId:a.aws_access_key_id,secretAccessKey:a.aws_secret_access_key,sessionToken:a.aws_session_token,...a.aws_credential_scope&&{credentialScope:a.aws_credential_scope},...a.aws_account_id&&{accountId:a.aws_account_id}};return(0,f.g)(c,"CREDENTIALS_PROFILE","n")},n=async(a,b)=>c.e(5165).then(c.bind(c,85165)).then(({fromTokenFile:c})=>c({webIdentityTokenFile:a.web_identity_token_file,roleArn:a.role_arn,roleSessionName:a.role_session_name,roleAssumerWithWebIdentity:b.roleAssumerWithWebIdentity,logger:b.logger,parentClientConfig:b.parentClientConfig})().then(a=>(0,f.g)(a,"CREDENTIALS_PROFILE_STS_WEB_ID_TOKEN","q"))),o=async(a,b,c,d={},f=!1)=>{let g=b[a];if(Object.keys(d).length>0&&l(g))return m(g,c);if(f||((a,{profile:b="default",logger:c}={})=>!!a&&"object"==typeof a&&"string"==typeof a.role_arn&&["undefined","string"].indexOf(typeof a.role_session_name)>-1&&["undefined","string"].indexOf(typeof a.external_id)>-1&&["undefined","string"].indexOf(typeof a.mfa_serial)>-1&&(((a,{profile:b,logger:c})=>{let d="string"==typeof a.source_profile&&void 0===a.credential_source;return d&&c?.debug?.(`    ${b} isAssumeRoleWithSourceProfile source_profile=${a.source_profile}`),d})(a,{profile:b,logger:c})||((a,{profile:b,logger:c})=>{let d="string"==typeof a.credential_source&&void 0===a.source_profile;return d&&c?.debug?.(`    ${b} isCredentialSourceProfile credential_source=${a.credential_source}`),d})(a,{profile:b,logger:c})))(g,{profile:a,logger:c.logger}))return h(a,b,c,d);if(l(g))return m(g,c);if((a=>!!a&&"object"==typeof a&&"string"==typeof a.web_identity_token_file&&"string"==typeof a.role_arn&&["undefined","string"].indexOf(typeof a.role_session_name)>-1)(g))return n(g,c);if((a=>!!a&&"object"==typeof a&&"string"==typeof a.credential_process)(g))return j(c,a);if((a=>a&&("string"==typeof a.sso_start_url||"string"==typeof a.sso_account_id||"string"==typeof a.sso_session||"string"==typeof a.sso_region||"string"==typeof a.sso_role_name))(g))return await k(a,g,c);throw new e.C1(`Could not resolve credentials using profile: [${a}] in configuration/credentials file(s).`,{logger:c.logger})},p=(a={})=>async({callerClientConfig:b}={})=>{let c={...a,parentClientConfig:{...b,...a.parentClientConfig}};c.logger?.debug("@aws-sdk/credential-provider-ini - fromIni");let e=await (0,d.YU)(c);return o((0,d.Bz)({profile:a.profile??b?.profile}),e,c)}}};