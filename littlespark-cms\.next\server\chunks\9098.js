"use strict";exports.id=9098,exports.ids=[9098],exports.modules={57841:(e,t,a)=>{a.d(t,{w:()=>i});let n=Symbol.for("constructDateFrom");function i(e,t){return"function"==typeof e?e(t):e&&"object"==typeof e&&n in e?e[n](t):e instanceof Date?new e.constructor(t):new Date(t)}},40165:(e,t,a)=>{a.d(t,{R:()=>d});var n=a(57841);let i={};var o=a(87046);function r(e,t){let a=t?.weekStartsOn??t?.locale?.options?.weekStartsOn??i.weekStartsOn??i.locale?.options?.weekStartsOn??0,n=(0,o.a)(e,t?.in),r=n.getDay();return n.setDate(n.getDate()-((r<a?7:0)+r-a)),n.setHours(0,0,0,0),n}function d(e,t,a){let[i,o]=function(e,...t){let a=n.w.bind(null,e||t.find(e=>"object"==typeof e));return t.map(a)}(a?.in,e,t);return+r(i,a)==+r(o,a)}},78526:(e,t,a)=>{a.d(t,{k:()=>n});function n(e){return (t={})=>{let a=t.width?String(t.width):e.defaultWidth;return e.formats[a]||e.formats[e.defaultWidth]}}},94940:(e,t,a)=>{a.d(t,{o:()=>n});function n(e){return(t,a)=>{let n;if("formatting"===(a?.context?String(a.context):"standalone")&&e.formattingValues){let t=e.defaultFormattingWidth||e.defaultWidth,i=a?.width?String(a.width):t;n=e.formattingValues[i]||e.formattingValues[t]}else{let t=e.defaultWidth,i=a?.width?String(a.width):e.defaultWidth;n=e.values[i]||e.values[t]}return n[e.argumentCallback?e.argumentCallback(t):t]}}},44754:(e,t,a)=>{function n(e){return(t,a={})=>{let n;let i=a.width,o=i&&e.matchPatterns[i]||e.matchPatterns[e.defaultMatchWidth],r=t.match(o);if(!r)return null;let d=r[0],l=i&&e.parsePatterns[i]||e.parsePatterns[e.defaultParseWidth],m=Array.isArray(l)?function(e,t){for(let a=0;a<e.length;a++)if(t(e[a]))return a}(l,e=>e.test(d)):function(e,t){for(let a in e)if(Object.prototype.hasOwnProperty.call(e,a)&&t(e[a]))return a}(l,e=>e.test(d));return n=e.valueCallback?e.valueCallback(m):m,{value:n=a.valueCallback?a.valueCallback(n):n,rest:t.slice(d.length)}}}a.d(t,{A:()=>n})},71886:(e,t,a)=>{a.d(t,{K:()=>n});function n(e){return(t,a={})=>{let n=t.match(e.matchPattern);if(!n)return null;let i=n[0],o=t.match(e.parsePattern);if(!o)return null;let r=e.valueCallback?e.valueCallback(o[0]):o[0];return{value:r=a.valueCallback?a.valueCallback(r):r,rest:t.slice(i.length)}}}},69098:(e,t,a)=>{a.r(t),a.d(t,{default:()=>h,it:()=>c});let n={lessThanXSeconds:{one:"meno di un secondo",other:"meno di {{count}} secondi"},xSeconds:{one:"un secondo",other:"{{count}} secondi"},halfAMinute:"alcuni secondi",lessThanXMinutes:{one:"meno di un minuto",other:"meno di {{count}} minuti"},xMinutes:{one:"un minuto",other:"{{count}} minuti"},aboutXHours:{one:"circa un'ora",other:"circa {{count}} ore"},xHours:{one:"un'ora",other:"{{count}} ore"},xDays:{one:"un giorno",other:"{{count}} giorni"},aboutXWeeks:{one:"circa una settimana",other:"circa {{count}} settimane"},xWeeks:{one:"una settimana",other:"{{count}} settimane"},aboutXMonths:{one:"circa un mese",other:"circa {{count}} mesi"},xMonths:{one:"un mese",other:"{{count}} mesi"},aboutXYears:{one:"circa un anno",other:"circa {{count}} anni"},xYears:{one:"un anno",other:"{{count}} anni"},overXYears:{one:"pi\xf9 di un anno",other:"pi\xf9 di {{count}} anni"},almostXYears:{one:"quasi un anno",other:"quasi {{count}} anni"}};var i=a(78526);let o={date:(0,i.k)({formats:{full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"dd/MM/y"},defaultWidth:"full"}),time:(0,i.k)({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},defaultWidth:"full"}),dateTime:(0,i.k)({formats:{full:"{{date}} {{time}}",long:"{{date}} {{time}}",medium:"{{date}} {{time}}",short:"{{date}} {{time}}"},defaultWidth:"full"})};var r=a(40165);let d=["domenica","luned\xec","marted\xec","mercoled\xec","gioved\xec","venerd\xec","sabato"];function l(e){return"'"+d[e]+" alle' p"}let m={lastWeek:(e,t,a)=>{let n=e.getDay();return(0,r.R)(e,t,a)?l(n):function(e){return 0===e?"'domenica scorsa alle' p":"'"+d[e]+" scorso alle' p"}(n)},yesterday:"'ieri alle' p",today:"'oggi alle' p",tomorrow:"'domani alle' p",nextWeek:(e,t,a)=>{let n=e.getDay();return(0,r.R)(e,t,a)?l(n):function(e){return 0===e?"'domenica prossima alle' p":"'"+d[e]+" prossimo alle' p"}(n)},other:"P"};var s=a(94940);let u={ordinalNumber:(e,t)=>String(Number(e)),era:(0,s.o)({values:{narrow:["aC","dC"],abbreviated:["a.C.","d.C."],wide:["avanti Cristo","dopo Cristo"]},defaultWidth:"wide"}),quarter:(0,s.o)({values:{narrow:["1","2","3","4"],abbreviated:["T1","T2","T3","T4"],wide:["1\xba trimestre","2\xba trimestre","3\xba trimestre","4\xba trimestre"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:(0,s.o)({values:{narrow:["G","F","M","A","M","G","L","A","S","O","N","D"],abbreviated:["gen","feb","mar","apr","mag","giu","lug","ago","set","ott","nov","dic"],wide:["gennaio","febbraio","marzo","aprile","maggio","giugno","luglio","agosto","settembre","ottobre","novembre","dicembre"]},defaultWidth:"wide"}),day:(0,s.o)({values:{narrow:["D","L","M","M","G","V","S"],short:["dom","lun","mar","mer","gio","ven","sab"],abbreviated:["dom","lun","mar","mer","gio","ven","sab"],wide:["domenica","luned\xec","marted\xec","mercoled\xec","gioved\xec","venerd\xec","sabato"]},defaultWidth:"wide"}),dayPeriod:(0,s.o)({values:{narrow:{am:"m.",pm:"p.",midnight:"mezzanotte",noon:"mezzogiorno",morning:"mattina",afternoon:"pomeriggio",evening:"sera",night:"notte"},abbreviated:{am:"AM",pm:"PM",midnight:"mezzanotte",noon:"mezzogiorno",morning:"mattina",afternoon:"pomeriggio",evening:"sera",night:"notte"},wide:{am:"AM",pm:"PM",midnight:"mezzanotte",noon:"mezzogiorno",morning:"mattina",afternoon:"pomeriggio",evening:"sera",night:"notte"}},defaultWidth:"wide",formattingValues:{narrow:{am:"m.",pm:"p.",midnight:"mezzanotte",noon:"mezzogiorno",morning:"di mattina",afternoon:"del pomeriggio",evening:"di sera",night:"di notte"},abbreviated:{am:"AM",pm:"PM",midnight:"mezzanotte",noon:"mezzogiorno",morning:"di mattina",afternoon:"del pomeriggio",evening:"di sera",night:"di notte"},wide:{am:"AM",pm:"PM",midnight:"mezzanotte",noon:"mezzogiorno",morning:"di mattina",afternoon:"del pomeriggio",evening:"di sera",night:"di notte"}},defaultFormattingWidth:"wide"})};var g=a(44754);let c={code:"it",formatDistance:(e,t,a)=>{let i;let o=n[e];return(i="string"==typeof o?o:1===t?o.one:o.other.replace("{{count}}",t.toString()),a?.addSuffix)?a.comparison&&a.comparison>0?"tra "+i:i+" fa":i},formatLong:o,formatRelative:(e,t,a,n)=>{let i=m[e];return"function"==typeof i?i(t,a,n):i},localize:u,match:{ordinalNumber:(0,a(71886).K)({matchPattern:/^(\d+)(º)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:(0,g.A)({matchPatterns:{narrow:/^(aC|dC)/i,abbreviated:/^(a\.?\s?C\.?|a\.?\s?e\.?\s?v\.?|d\.?\s?C\.?|e\.?\s?v\.?)/i,wide:/^(avanti Cristo|avanti Era Volgare|dopo Cristo|Era Volgare)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^a/i,/^(d|e)/i]},defaultParseWidth:"any"}),quarter:(0,g.A)({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^t[1234]/i,wide:/^[1234](º)? trimestre/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:(0,g.A)({matchPatterns:{narrow:/^[gfmalsond]/i,abbreviated:/^(gen|feb|mar|apr|mag|giu|lug|ago|set|ott|nov|dic)/i,wide:/^(gennaio|febbraio|marzo|aprile|maggio|giugno|luglio|agosto|settembre|ottobre|novembre|dicembre)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^g/i,/^f/i,/^m/i,/^a/i,/^m/i,/^g/i,/^l/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ge/i,/^f/i,/^mar/i,/^ap/i,/^mag/i,/^gi/i,/^l/i,/^ag/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:(0,g.A)({matchPatterns:{narrow:/^[dlmgvs]/i,short:/^(do|lu|ma|me|gi|ve|sa)/i,abbreviated:/^(dom|lun|mar|mer|gio|ven|sab)/i,wide:/^(domenica|luned[i|ì]|marted[i|ì]|mercoled[i|ì]|gioved[i|ì]|venerd[i|ì]|sabato)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^g/i,/^v/i,/^s/i],any:[/^d/i,/^l/i,/^ma/i,/^me/i,/^g/i,/^v/i,/^s/i]},defaultParseWidth:"any"}),dayPeriod:(0,g.A)({matchPatterns:{narrow:/^(a|m\.|p|mezzanotte|mezzogiorno|(di|del) (mattina|pomeriggio|sera|notte))/i,any:/^([ap]\.?\s?m\.?|mezzanotte|mezzogiorno|(di|del) (mattina|pomeriggio|sera|notte))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mezza/i,noon:/^mezzo/i,morning:/mattina/i,afternoon:/pomeriggio/i,evening:/sera/i,night:/notte/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:4}},h=c},87046:(e,t,a)=>{a.d(t,{a:()=>i});var n=a(57841);function i(e,t){return(0,n.w)(t||e,e)}}};