(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2362],{79889:(n,e,t)=>{Promise.resolve().then(t.bind(t,80852))},80852:(n,e,t)=>{"use strict";t.r(e),t.d(e,{default:()=>r});var o=t(95155),s=t(12115);function r(){let[n,e]=(0,s.useState)(!1),[t,r]=(0,s.useState)({cmsChallenge:0,mainAppCmsChallenge:0});(0,s.useEffect)(()=>{e(!0),a()},[]);let a=async()=>{try{let n=await fetch("/api/sync/challenges"),e=await n.json();e.success&&e.stats&&r({cmsChallenge:e.stats.cmsChallenge||0,mainAppCmsChallenge:e.stats.mainAppCmsChallenge||0})}catch(n){console.error("Error fetching stats:",n)}},i=async()=>{try{var n,e;let t=await fetch("/api/test-data",{method:"POST",headers:{"Content-Type":"application/json"}}),o=await t.json();alert("Test data result: ".concat((null==(n=o.stats)?void 0:n.created)||0," challenges created, ").concat((null==(e=o.stats)?void 0:e.skipped)||0," skipped")),await a()}catch(n){alert("Error: "+n.message)}},d=async()=>{try{var n;let e=await fetch("/api/sync/challenges",{method:"POST",headers:{"Content-Type":"application/json"}}),t=await e.json();alert("Sync result: ".concat((null==(n=t.stats)?void 0:n.synced)||0," challenges synced successfully")),await a()}catch(n){alert("Error: "+n.message)}};return n?(0,o.jsx)("div",{className:"sync-page",children:(0,o.jsxs)("div",{style:{padding:"20px",maxWidth:"1200px",margin:"0 auto"},children:[(0,o.jsxs)("div",{style:{marginBottom:"30px"},children:[(0,o.jsx)("h1",{style:{fontSize:"28px",fontWeight:"bold",marginBottom:"10px"},children:"\uD83D\uDD04 Sync Management"}),(0,o.jsx)("p",{style:{color:"#666",fontSize:"16px"},children:"Synchronize content and users between CMS and main application"}),(0,o.jsx)("div",{style:{display:"inline-block",backgroundColor:"#f0f9ff",color:"#0369a1",padding:"4px 12px",borderRadius:"6px",fontSize:"14px",fontWeight:"500",marginTop:"10px"},children:"\uD83D\uDEE1️ Admin Only Feature"})]}),(0,o.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr 1fr",gap:"20px",marginBottom:"30px"},children:[(0,o.jsxs)("div",{style:{border:"1px solid #e5e7eb",borderRadius:"8px",padding:"20px",backgroundColor:"#fff"},children:[(0,o.jsx)("h2",{style:{fontSize:"20px",fontWeight:"600",marginBottom:"10px"},children:"\uD83D\uDCDD Challenge Sync"}),(0,o.jsx)("p",{style:{color:"#666",marginBottom:"20px",fontSize:"14px"},children:"Sync published challenges from CMS to main application"}),(0,o.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"15px",marginBottom:"20px"},children:[(0,o.jsxs)("div",{style:{backgroundColor:"#eff6ff",padding:"15px",borderRadius:"6px",textAlign:"center"},children:[(0,o.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#2563eb"},children:(0,o.jsx)("span",{id:"cms-challenges-count",children:t.cmsChallenge})}),(0,o.jsx)("div",{style:{fontSize:"12px",color:"#1e40af",fontWeight:"500"},children:"CMS Challenges"})]}),(0,o.jsxs)("div",{style:{backgroundColor:"#f0fdf4",padding:"15px",borderRadius:"6px",textAlign:"center"},children:[(0,o.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#16a34a"},children:(0,o.jsx)("span",{id:"synced-challenges-count",children:t.mainAppCmsChallenge})}),(0,o.jsx)("div",{style:{fontSize:"12px",color:"#15803d",fontWeight:"500"},children:"Synced to Main App"})]})]}),(0,o.jsx)("button",{id:"sync-challenges-btn",onClick:d,style:{width:"100%",backgroundColor:"#2563eb",color:"white",border:"none",padding:"12px 20px",borderRadius:"6px",fontSize:"14px",fontWeight:"500",cursor:"pointer",marginBottom:"10px"},onMouseOver:n=>n.currentTarget.style.backgroundColor="#1d4ed8",onMouseOut:n=>n.currentTarget.style.backgroundColor="#2563eb",children:"\uD83D\uDD04 Sync Challenges to Main App"}),(0,o.jsx)("div",{id:"challenge-sync-result",style:{fontSize:"12px",color:"#666"}})]}),(0,o.jsxs)("div",{style:{border:"1px solid #e5e7eb",borderRadius:"8px",padding:"20px",backgroundColor:"#fff"},children:[(0,o.jsx)("h2",{style:{fontSize:"20px",fontWeight:"600",marginBottom:"10px"},children:"\uD83D\uDC65 User Sync"}),(0,o.jsx)("p",{style:{color:"#666",marginBottom:"20px",fontSize:"14px"},children:"Sync CMS users to main application for content access"}),(0,o.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"15px",marginBottom:"20px"},children:[(0,o.jsxs)("div",{style:{backgroundColor:"#faf5ff",padding:"15px",borderRadius:"6px",textAlign:"center"},children:[(0,o.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#9333ea"},children:(0,o.jsx)("span",{id:"cms-users-count",children:"-"})}),(0,o.jsx)("div",{style:{fontSize:"12px",color:"#7c3aed",fontWeight:"500"},children:"CMS Users"})]}),(0,o.jsxs)("div",{style:{backgroundColor:"#fff7ed",padding:"15px",borderRadius:"6px",textAlign:"center"},children:[(0,o.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#ea580c"},children:(0,o.jsx)("span",{id:"synced-users-count",children:"-"})}),(0,o.jsx)("div",{style:{fontSize:"12px",color:"#c2410c",fontWeight:"500"},children:"Synced to Main App"})]})]}),(0,o.jsx)("button",{id:"sync-users-btn",style:{width:"100%",backgroundColor:"#9333ea",color:"white",border:"none",padding:"12px 20px",borderRadius:"6px",fontSize:"14px",fontWeight:"500",cursor:"pointer",marginBottom:"10px"},onMouseOver:n=>n.currentTarget.style.backgroundColor="#7c3aed",onMouseOut:n=>n.currentTarget.style.backgroundColor="#9333ea",children:"\uD83D\uDD04 Sync Users to Main App"}),(0,o.jsx)("div",{id:"user-sync-result",style:{fontSize:"12px",color:"#666"}})]}),(0,o.jsxs)("div",{style:{border:"1px solid #e5e7eb",borderRadius:"8px",padding:"20px",backgroundColor:"#fff"},children:[(0,o.jsx)("h2",{style:{fontSize:"20px",fontWeight:"600",marginBottom:"10px"},children:"⬅️ Import User Challenges"}),(0,o.jsx)("p",{style:{color:"#666",marginBottom:"20px",fontSize:"14px"},children:"Import user-created challenges from main app to CMS for review"}),(0,o.jsxs)("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"15px",marginBottom:"20px"},children:[(0,o.jsxs)("div",{style:{backgroundColor:"#f0f9ff",padding:"15px",borderRadius:"6px",textAlign:"center"},children:[(0,o.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#0369a1"},children:(0,o.jsx)("span",{id:"available-user-challenges-count",children:"-"})}),(0,o.jsx)("div",{style:{fontSize:"12px",color:"#0c4a6e",fontWeight:"500"},children:"Available to Import"})]}),(0,o.jsxs)("div",{style:{backgroundColor:"#f0fdf4",padding:"15px",borderRadius:"6px",textAlign:"center"},children:[(0,o.jsx)("div",{style:{fontSize:"24px",fontWeight:"bold",color:"#16a34a"},children:(0,o.jsx)("span",{id:"imported-challenges-count",children:"-"})}),(0,o.jsx)("div",{style:{fontSize:"12px",color:"#15803d",fontWeight:"500"},children:"Already Imported"})]})]}),(0,o.jsx)("button",{id:"import-challenges-btn",style:{width:"100%",backgroundColor:"#0369a1",color:"white",border:"none",padding:"12px 20px",borderRadius:"6px",fontSize:"14px",fontWeight:"500",cursor:"pointer",marginBottom:"10px"},onMouseOver:n=>n.currentTarget.style.backgroundColor="#0c4a6e",onMouseOut:n=>n.currentTarget.style.backgroundColor="#0369a1",children:"⬅️ Import User Challenges from Main App"}),(0,o.jsx)("div",{id:"import-sync-result",style:{fontSize:"12px",color:"#666"}})]})]}),(0,o.jsx)("div",{style:{textAlign:"center",marginBottom:"30px"},children:(0,o.jsxs)("div",{style:{display:"flex",gap:"15px",justifyContent:"center",flexWrap:"wrap"},children:[(0,o.jsx)("button",{id:"refresh-stats-btn",onClick:a,style:{backgroundColor:"#f3f4f6",color:"#374151",border:"1px solid #d1d5db",padding:"10px 20px",borderRadius:"6px",fontSize:"14px",fontWeight:"500",cursor:"pointer"},onMouseOver:n=>n.currentTarget.style.backgroundColor="#e5e7eb",onMouseOut:n=>n.currentTarget.style.backgroundColor="#f3f4f6",children:"\uD83D\uDD04 Refresh Statistics"}),(0,o.jsx)("button",{id:"add-test-data-btn",onClick:i,style:{backgroundColor:"#10b981",color:"white",border:"none",padding:"10px 20px",borderRadius:"6px",fontSize:"14px",fontWeight:"500",cursor:"pointer"},onMouseOver:n=>n.currentTarget.style.backgroundColor="#059669",onMouseOut:n=>n.currentTarget.style.backgroundColor="#10b981",children:"\uD83E\uDDEA Add Sample Challenges"})]})}),(0,o.jsxs)("div",{id:"error-display",style:{display:"none",backgroundColor:"#fef2f2",border:"1px solid #fecaca",color:"#dc2626",padding:"15px",borderRadius:"6px",marginBottom:"20px"},children:[(0,o.jsx)("strong",{children:"Error:"})," ",(0,o.jsx)("span",{id:"error-message"})]}),(0,o.jsxs)("div",{style:{backgroundColor:"#fffbeb",border:"1px solid #fed7aa",borderRadius:"8px",padding:"20px"},children:[(0,o.jsx)("h3",{style:{fontSize:"16px",fontWeight:"600",marginBottom:"10px",color:"#92400e"},children:"ℹ️ Sync Information"}),(0,o.jsxs)("ul",{style:{fontSize:"14px",color:"#92400e",lineHeight:"1.6"},children:[(0,o.jsxs)("li",{children:[(0,o.jsx)("strong",{children:"Challenge Sync:"})," Publishes CMS challenges to the main application for users to access"]}),(0,o.jsxs)("li",{children:[(0,o.jsx)("strong",{children:"User Sync:"})," Creates accounts in the main application for CMS content creators and educators"]}),(0,o.jsxs)("li",{children:[(0,o.jsx)("strong",{children:"Admin Only:"})," Only CMS administrators can perform sync operations"]}),(0,o.jsxs)("li",{children:[(0,o.jsx)("strong",{children:"Safe Operation:"})," Sync operations are idempotent and won't create duplicates"]})]})]}),(0,o.jsx)("script",{src:"/admin/sync/sync.js",defer:!0}),(0,o.jsx)("script",{dangerouslySetInnerHTML:{__html:"\n            console.log('\uD83D\uDD04 Sync page loaded');\n\n            // Fetch and update stats\n            window.fetchStats = async function() {\n              try {\n                console.log('Fetching stats...');\n                const response = await fetch('/api/sync/challenges');\n                const data = await response.json();\n                console.log('Stats data:', data);\n\n                if (data.success && data.stats) {\n                  document.getElementById('cms-challenges-count').textContent = data.stats.cmsChallenge || '0';\n                  document.getElementById('synced-challenges-count').textContent = data.stats.mainAppCmsChallenge || '0';\n                }\n              } catch (error) {\n                console.error('Error fetching stats:', error);\n              }\n            };\n\n            // Simple test data function\n            window.addTestDataSimple = async function() {\n              try {\n                console.log('Adding test data...');\n                const response = await fetch('/api/test-data', {\n                  method: 'POST',\n                  headers: { 'Content-Type': 'application/json' }\n                });\n                const data = await response.json();\n                console.log('Test data result:', data);\n                alert('Test data result: ' + JSON.stringify(data, null, 2));\n\n                // Refresh stats\n                await window.fetchStats();\n              } catch (error) {\n                console.error('Error:', error);\n                alert('Error: ' + error.message);\n              }\n            };\n\n            // Simple sync function\n            window.syncChallengesSimple = async function() {\n              try {\n                console.log('Syncing challenges...');\n                const response = await fetch('/api/sync/challenges', {\n                  method: 'POST',\n                  headers: { 'Content-Type': 'application/json' }\n                });\n                const data = await response.json();\n                console.log('Sync result:', data);\n                alert('Sync result: ' + JSON.stringify(data, null, 2));\n\n                // Refresh stats\n                await window.fetchStats();\n              } catch (error) {\n                console.error('Error:', error);\n                alert('Error: ' + error.message);\n              }\n            };\n\n            // Add click handlers when DOM is ready\n            document.addEventListener('DOMContentLoaded', function() {\n              const addTestBtn = document.getElementById('add-test-data-btn');\n              const syncBtn = document.getElementById('sync-challenges-btn');\n              const refreshBtn = document.getElementById('refresh-stats-btn');\n\n              if (addTestBtn) {\n                addTestBtn.onclick = window.addTestDataSimple;\n                console.log('✅ Added test data button handler');\n              }\n\n              if (syncBtn) {\n                syncBtn.onclick = window.syncChallengesSimple;\n                console.log('✅ Added sync button handler');\n              }\n\n              if (refreshBtn) {\n                refreshBtn.onclick = window.fetchStats;\n                console.log('✅ Added refresh stats button handler');\n              }\n\n              // Load initial stats\n              window.fetchStats();\n            });\n          "}}),(0,o.jsx)("script",{dangerouslySetInnerHTML:{__html:"\n            // Add floating sync button to CMS admin panel\n            (function() {\n              function addSyncButtonToOtherPages() {\n                // Only add on non-sync pages\n                if (window.location.pathname.includes('/admin/sync')) return;\n\n                // Check if button already exists\n                if (document.getElementById('floating-sync-btn')) return;\n\n                const syncButton = document.createElement('div');\n                syncButton.id = 'floating-sync-btn';\n                syncButton.style.cssText = `\n                  position: fixed;\n                  bottom: 20px;\n                  right: 20px;\n                  z-index: 9999;\n                  background: linear-gradient(135deg, #0070f3, #0056b3);\n                  color: white;\n                  width: 60px;\n                  height: 60px;\n                  border-radius: 50%;\n                  display: flex;\n                  align-items: center;\n                  justify-content: center;\n                  cursor: pointer;\n                  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\n                  transition: all 0.3s ease;\n                  font-size: 24px;\n                  border: 2px solid white;\n                `;\n\n                syncButton.innerHTML = '\uD83D\uDD04';\n                syncButton.title = 'Open Sync Management Panel';\n\n                syncButton.addEventListener('mouseenter', function() {\n                  this.style.transform = 'scale(1.1)';\n                });\n\n                syncButton.addEventListener('mouseleave', function() {\n                  this.style.transform = 'scale(1)';\n                });\n\n                syncButton.addEventListener('click', function() {\n                  window.location.href = '/admin/sync';\n                });\n\n                document.body.appendChild(syncButton);\n              }\n\n              // Try to add button to parent window (if in iframe)\n              try {\n                if (window.parent && window.parent !== window) {\n                  window.parent.postMessage({type: 'ADD_SYNC_BUTTON'}, '*');\n                }\n              } catch(e) {}\n\n              // Add to current window\n              setTimeout(addSyncButtonToOtherPages, 1000);\n            })();\n          "}})]})}):(0,o.jsx)("div",{style:{padding:"20px",textAlign:"center"},children:(0,o.jsx)("div",{children:"Loading sync panel..."})})}}},n=>{n.O(0,[8441,5964,7358],()=>n(n.s=79889)),_N_E=n.O()}]);