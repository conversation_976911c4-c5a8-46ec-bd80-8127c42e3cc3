"use strict";exports.id=4970,exports.ids=[4970],exports.modules={693:(e,t,r)=>{Object.defineProperty(t,"f",{enumerable:!0,get:function(){return n}});class n extends Response{static #e=this.displayName="ImageResponse";constructor(...e){let t=new ReadableStream({async start(t){let n=new(await Promise.resolve().then(r.bind(r,83725))).ImageResponse(...e);if(!n.body)return t.close();let a=n.body.getReader();for(;;){let{done:e,value:r}=await a.read();if(e)return t.close();t.enqueue(r)}}}),n=e[1]||{},a=new Headers({"content-type":"image/png","cache-control":"public, immutable, no-transform, max-age=31536000"});n.headers&&new Headers(n.headers).forEach((e,t)=>a.set(t,e)),super(t,{headers:a,status:n.status,statusText:n.statusText})}}},74970:(e,t,r)=>{r.d(t,{SJ:()=>et,fG:()=>K,lw:()=>Q,D9:()=>er,LO:()=>ee,Uc:()=>en});var n=r(12043);function a(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function o(e){return e&&e.sensitive?"":"i"}var i=r(9574),s=r(14400),l=r(69637),c=r(10230),p=r(66280),u=r(16009);let f=e=>{if(e){let t=Object.getPrototypeOf(e);if((t.constructor.name===u.h||t.constructor.name===c.i)&&e.data)return{errors:[{name:e.name,data:e.data,message:e.message}]};if(t.constructor.name===u.h&&"errors"in e&&e.errors)return{errors:Object.keys(e.errors).reduce((t,r)=>(t.push({field:e.errors[r].path,message:e.errors[r].message}),t),[])};if(Array.isArray(e.message))return{errors:e.message};if(e.name)return{errors:[{message:e.message}]}}return{errors:[{message:"An unknown error occurred."}]}};var d=r(71131);let h=async({collection:e,config:t,err:r,req:a})=>{if("payloadInitError"in r&&!0===r.payloadInitError)return console.error(r),Response.json({message:"There was an error initializing Payload"},{status:n.h.INTERNAL_SERVER_ERROR});let o=a&&"payload"in a&&a?.payload;if(!o)try{o=await (0,p.nm0)({config:t,cron:!0})}catch(e){return Response.json({message:"There was an error initializing Payload"},{status:n.h.INTERNAL_SERVER_ERROR})}let i=f(r),u=r.status||n.h.INTERNAL_SERVER_ERROR;(0,d.v)({err:r,payload:o}),a.payload=o;let h=(0,s.y)({headers:new Headers,req:a}),{config:m}=o;return m.debug||r.isPublic||u!==n.h.INTERNAL_SERVER_ERROR||(i=f(new c.L("Something went wrong."))),m.debug&&!0===m.debug&&(i.stack=r.stack),e&&await e.config.hooks.afterError?.reduce(async(t,n)=>{await t;let o=await n({collection:e.config,context:a.context,error:r,req:a,result:i});o&&(i=o.response||i,u=o.status||u)},Promise.resolve()),await m.hooks.afterError?.reduce(async(t,n)=>{await t;let o=await n({collection:e?.config,context:a.context,error:r,req:a,result:i});o&&(i=o.response||i,u=o.status||u)},Promise.resolve()),Response.json(i,{headers:a.responseHeaders?(0,l.l)(a.responseHeaders,h):h,status:u})},m=(e,t)=>Response.json({message:`Route not found "${t??new URL(e.url).pathname}"`},{headers:(0,s.y)({headers:new Headers,req:e}),status:n.h.NOT_FOUND}),y=async({basePath:e="",config:t,path:r,request:c})=>{let p,u,f;if("post"===c.method.toLowerCase()&&("GET"===c.headers.get("X-Payload-HTTP-Method-Override")||"GET"===c.headers.get("X-HTTP-Method-Override"))){let n=await c.text(),a=`${c.url}?${new URLSearchParams(n).toString()}`;return await y({basePath:e,config:t,path:r,request:new Request(a,{cache:c.cache,credentials:c.credentials,headers:c.headers,method:"GET",signal:c.signal})})}try{let d;if(u=await (0,i.o)({canSetHeaders:!0,config:t,request:c}),u.method?.toLowerCase()==="options")return Response.json({},{headers:(0,s.y)({headers:new Headers,req:u}),status:200});let{payload:h}=u,{config:y}=h,w=`${e}${r??new URL(u.url).pathname}`;if(!w.startsWith(y.routes.api))return m(u,w);let v=w.replace(y.routes.api,""),x=!1;v.startsWith("/globals")&&(x=!0,v=v.replace("/globals",""));let T=v.split("/");T.shift();let C=T[0];C&&(x?d=h.globals.config.find(e=>e.slug===C):h.collections[C]&&(f=h.collections[C]));let E=y.endpoints;if(f?(E=f.config.endpoints,v=v.replace(`/${f.config.slug}`,"")):d&&(v=v.replace(`/${d.slug}`,""),E=d.endpoints),""===v&&(v="/"),!1===E)return Response.json({message:`Cannot ${u.method?.toUpperCase()} ${u.url}`},{headers:(0,s.y)({headers:new Headers,req:u}),status:n.h.NOT_IMPLEMENTED});let R=E?.find(e=>{if(e.method!==u.method?.toLowerCase())return!1;let t=(function(e,t){var r,n,i,s,l=[];return r=function e(t,r,n){var i;return t instanceof RegExp?function(e,t){if(!t)return e;for(var r=/\((?:\?<(.*?)>)?(?!\?)/g,n=0,a=r.exec(e.source);a;)t.push({name:a[1]||n++,prefix:"",suffix:"",modifier:"",pattern:""}),a=r.exec(e.source);return e}(t,r):Array.isArray(t)?(i=t.map(function(t){return e(t,r,n).source}),new RegExp("(?:".concat(i.join("|"),")"),o(n))):function(e,t,r){void 0===r&&(r={});for(var n=r.strict,i=void 0!==n&&n,s=r.start,l=r.end,c=r.encode,p=void 0===c?function(e){return e}:c,u=r.delimiter,f=r.endsWith,d="[".concat(a(void 0===f?"":f),"]|$"),h="[".concat(a(void 0===u?"/#?":u),"]"),m=void 0===s||s?"^":"",y=0;y<e.length;y++){var w=e[y];if("string"==typeof w)m+=a(p(w));else{var v=a(p(w.prefix)),x=a(p(w.suffix));if(w.pattern){if(t&&t.push(w),v||x){if("+"===w.modifier||"*"===w.modifier){var T="*"===w.modifier?"?":"";m+="(?:".concat(v,"((?:").concat(w.pattern,")(?:").concat(x).concat(v,"(?:").concat(w.pattern,"))*)").concat(x,")").concat(T)}else m+="(?:".concat(v,"(").concat(w.pattern,")").concat(x,")").concat(w.modifier)}else{if("+"===w.modifier||"*"===w.modifier)throw TypeError('Can not repeat "'.concat(w.name,'" without a prefix and suffix'));m+="(".concat(w.pattern,")").concat(w.modifier)}}else m+="(?:".concat(v).concat(x,")").concat(w.modifier)}}if(void 0===l||l)i||(m+="".concat(h,"?")),m+=r.endsWith?"(?=".concat(d,")"):"$";else{var C=e[e.length-1],E="string"==typeof C?h.indexOf(C[C.length-1])>-1:void 0===C;i||(m+="(?:".concat(h,"(?=").concat(d,"))?")),E||(m+="(?=".concat(h,"|").concat(d,")"))}return new RegExp(m,o(r))}(function(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var a="",o=r+1;o<e.length;){var i=e.charCodeAt(o);if(i>=48&&i<=57||i>=65&&i<=90||i>=97&&i<=122||95===i){a+=e[o++];continue}break}if(!a)throw TypeError("Missing parameter name at ".concat(r));t.push({type:"NAME",index:r,value:a}),r=o;continue}if("("===n){var s=1,l="",o=r+1;if("?"===e[o])throw TypeError('Pattern cannot start with "?" at '.concat(o));for(;o<e.length;){if("\\"===e[o]){l+=e[o++]+e[o++];continue}if(")"===e[o]){if(0==--s){o++;break}}else if("("===e[o]&&(s++,"?"!==e[o+1]))throw TypeError("Capturing groups are not allowed at ".concat(o));l+=e[o++]}if(s)throw TypeError("Unbalanced pattern at ".concat(r));if(!l)throw TypeError("Missing pattern at ".concat(r));t.push({type:"PATTERN",index:r,value:l}),r=o;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,o=void 0===n?"./":n,i=t.delimiter,s=void 0===i?"/#?":i,l=[],c=0,p=0,u="",f=function(e){if(p<r.length&&r[p].type===e)return r[p++].value},d=function(e){var t=f(e);if(void 0!==t)return t;var n=r[p],a=n.type,o=n.index;throw TypeError("Unexpected ".concat(a," at ").concat(o,", expected ").concat(e))},h=function(){for(var e,t="";e=f("CHAR")||f("ESCAPED_CHAR");)t+=e;return t},m=function(e){for(var t=0;t<s.length;t++){var r=s[t];if(e.indexOf(r)>-1)return!0}return!1},y=function(e){var t=l[l.length-1],r=e||(t&&"string"==typeof t?t:"");if(t&&!r)throw TypeError('Must have text between two parameters, missing text after "'.concat(t.name,'"'));return!r||m(r)?"[^".concat(a(s),"]+?"):"(?:(?!".concat(a(r),")[^").concat(a(s),"])+?")};p<r.length;){var w=f("CHAR"),v=f("NAME"),x=f("PATTERN");if(v||x){var T=w||"";-1===o.indexOf(T)&&(u+=T,T=""),u&&(l.push(u),u=""),l.push({name:v||c++,prefix:T,suffix:"",pattern:x||y(T),modifier:f("MODIFIER")||""});continue}var C=w||f("ESCAPED_CHAR");if(C){u+=C;continue}if(u&&(l.push(u),u=""),f("OPEN")){var T=h(),E=f("NAME")||"",R=f("PATTERN")||"",b=h();d("CLOSE"),l.push({name:E||(R?c++:""),pattern:E&&!R?y(T):R,prefix:T,suffix:b,modifier:f("MODIFIER")||""});continue}d("END")}return l}(t,n),r,n)}(e,l,t),n=t,void 0===n&&(n={}),i=n.decode,s=void 0===i?function(e){return e}:i,function(e){var t=r.exec(e);if(!t)return!1;for(var n=t[0],a=t.index,o=Object.create(null),i=1;i<t.length;i++)!function(e){if(void 0!==t[e]){var r=l[e-1];"*"===r.modifier||"+"===r.modifier?o[r.name]=t[e].split(r.prefix+r.suffix).map(function(e){return s(e,r)}):o[r.name]=s(t[e],r)}}(i);return{path:n,index:a,params:o}}})(e.path,{decode:decodeURIComponent})(v);return!!t&&(u.routeParams=t.params,f?u.routeParams.collection=f.config.slug:d&&(u.routeParams.global=d.slug),!0)});if(R&&(p=R.handler),!p)return m(u,w);let b=await p(u);return new Response(b.body,{headers:(0,s.y)({headers:(0,l.l)(u.responseHeaders??new Headers,b.headers),req:u}),status:b.status,statusText:b.statusText})}catch(e){return h({collection:f,config:t,err:e,req:u})}};var w=r(62740);r(76301);var v=r(60676),x=r(961);Object.prototype.hasOwnProperty;let T=Array.isArray,C=Array.prototype.push,E=Date.prototype.toISOString,R=x.Ay;v.lF,x._J[R];var b=({elements:e,translationString:t})=>g("span",{children:t.split(/(<[^>]+>.*?<\/[^>]+>)/g).map((t,r)=>{if(e&&t.startsWith("<")&&t.endsWith(">")){let n=t[1],a=e[n];if(a){let e=RegExp(`<${n}>(.*?)</${n}>`,"g");return g(a,{children:g(b,{translationString:t.replace(e,(e,t)=>t)})},r)}}return t})}),O=({fill:e})=>{let t=e||"var(--theme-elevation-1000)";return(0,w.jsxs)("svg",{className:"graphic-icon",height:"100%",viewBox:"0 0 25 25",width:"100%",xmlns:"http://www.w3.org/2000/svg",children:[(0,w.jsx)("path",{d:"M11.8673 21.2336L4.40922 16.9845C4.31871 16.9309 4.25837 16.8355 4.25837 16.7282V10.1609C4.25837 10.0477 4.38508 9.97616 4.48162 10.0298L13.1404 14.9642C13.2611 15.0358 13.412 14.9464 13.412 14.8093V11.6091C13.412 11.4839 13.3456 11.3647 13.2309 11.2992L2.81624 5.36353C2.72573 5.30989 2.60505 5.30989 2.51454 5.36353L1.15085 6.14422C1.06034 6.19786 1 6.29321 1 6.40048V18.5995C1 18.7068 1.06034 18.8021 1.15085 18.8558L11.8491 24.9583C11.9397 25.0119 12.0603 25.0119 12.1509 24.9583L21.1355 19.8331C21.2562 19.7616 21.2562 19.5948 21.1355 19.5232L18.3357 17.9261C18.2211 17.8605 18.0883 17.8605 17.9737 17.9261L12.175 21.2336C12.0845 21.2872 11.9638 21.2872 11.8733 21.2336H11.8673Z",fill:t}),(0,w.jsx)("path",{d:"M22.8491 6.13827L12.1508 0.0417218C12.0603 -0.0119135 11.9397 -0.0119135 11.8491 0.0417218L6.19528 3.2658C6.0746 3.33731 6.0746 3.50418 6.19528 3.57569L8.97092 5.16091C9.08557 5.22647 9.21832 5.22647 9.33296 5.16091L11.8672 3.71872C11.9578 3.66508 12.0784 3.66508 12.1689 3.71872L19.627 7.96782C19.7175 8.02146 19.7778 8.11681 19.7778 8.22408V14.8212C19.7778 14.9464 19.8442 15.0656 19.9589 15.1311L22.7345 16.7104C22.8552 16.7819 23.006 16.6925 23.006 16.5554V6.40048C23.006 6.29321 22.9457 6.19786 22.8552 6.14423L22.8491 6.13827Z",fill:t})]})},D={},j={};function M(e,t){try{let r=(D[e]||=new Intl.DateTimeFormat("en-GB",{timeZone:e,hour:"numeric",timeZoneName:"longOffset"}).format)(t).split("GMT")[1]||"";return r in j?j[r]:U(r,r.split(":"))}catch{if(e in j)return j[e];let t=e?.match(P);return t?U(e,t.slice(1)):NaN}}var P=/([+-]\d\d):?(\d\d)?/;function U(e,t){let r=+t[0],n=+(t[1]||0);return j[e]=r>0?60*r+n:60*r-n}var L=class e extends Date{constructor(...e){super(),e.length>1&&"string"==typeof e[e.length-1]&&(this.timeZone=e.pop()),this.internal=new Date,isNaN(M(this.timeZone,this))?this.setTime(NaN):e.length?"number"==typeof e[0]&&(1===e.length||2===e.length&&"number"!=typeof e[1])?this.setTime(e[0]):"string"==typeof e[0]?this.setTime(+new Date(e[0])):e[0]instanceof Date?this.setTime(+e[0]):(this.setTime(+new Date(...e)),H(this,NaN),A(this)):this.setTime(Date.now())}static tz(t,...r){return r.length?new e(...r,t):new e(Date.now(),t)}withTimeZone(t){return new e(+this,t)}getTimezoneOffset(){return-M(this.timeZone,this)}setTime(e){return Date.prototype.setTime.apply(this,arguments),A(this),+this}[Symbol.for("constructDateFrom")](t){return new e(+new Date(t),this.timeZone)}},N=/^(get|set)(?!UTC)/;function A(e){e.internal.setTime(+e),e.internal.setUTCMinutes(e.internal.getUTCMinutes()-e.getTimezoneOffset())}function H(e){let t=M(e.timeZone,e),r=new Date(+e);r.setUTCHours(r.getUTCHours()-1);let n=-new Date(+e).getTimezoneOffset(),a=n- -new Date(+r).getTimezoneOffset(),o=Date.prototype.getHours.apply(e)!==e.internal.getUTCHours();a&&o&&e.internal.setUTCMinutes(e.internal.getUTCMinutes()+a);let i=n-t;i&&Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+i);let s=M(e.timeZone,e),l=-new Date(+e).getTimezoneOffset()-s-i;if(s!==t&&l){Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+l);let t=s-M(e.timeZone,e);t&&(e.internal.setUTCMinutes(e.internal.getUTCMinutes()+t),Date.prototype.setUTCMinutes.call(e,Date.prototype.getUTCMinutes.call(e)+t))}}Object.getOwnPropertyNames(Date.prototype).forEach(e=>{if(!N.test(e))return;let t=e.replace(N,"$1UTC");L.prototype[t]&&(e.startsWith("get")?L.prototype[e]=function(){return this.internal[t]()}:(L.prototype[e]=function(){return Date.prototype[t].apply(this.internal,arguments),Date.prototype.setFullYear.call(this,this.internal.getUTCFullYear(),this.internal.getUTCMonth(),this.internal.getUTCDate()),Date.prototype.setHours.call(this,this.internal.getUTCHours(),this.internal.getUTCMinutes(),this.internal.getUTCSeconds(),this.internal.getUTCMilliseconds()),H(this),+this},L.prototype[t]=function(){return Date.prototype[t].apply(this,arguments),A(this),+this}))}),function(e){e.collection="collections",e.global="globals"}({});var S=r(79748),I=r(693),k=r(33873),$=r(79551),_=r(98041),F=r(94580),z=r(30432);let Z=e=>{let{importMap:t,PayloadComponent:r,schemaPath:n,silent:a}=e,{exportName:o,path:i}=(0,z.R)(r),s=i+"#"+o,l=t[s];return l||a||console.error("getFromImportMap: PayloadComponent not found in importMap",{key:s,PayloadComponent:r,schemaPath:n},"You may need to run the `payload generate:importmap` command to generate the importMap ahead of runtime."),l};function G(e){return Object.fromEntries(Object.entries(e).filter(([,e])=>void 0!==e))}let W=({clientProps:e={},Component:t,Fallback:r,importMap:n,key:a,serverProps:o})=>{if(Array.isArray(t))return t.map((t,r)=>W({clientProps:e,Component:t,importMap:n,key:r,serverProps:o}));if("function"==typeof t){let r=(0,_.r_)(t),n=G({...e,...r?o:{}});return(0,w.jsx)(t,{...n},a)}if("string"==typeof t||(0,F.Q)(t)){let r=Z({importMap:n,PayloadComponent:t,schemaPath:""});if(r){let n=(0,_.r_)(r),i=G({...e,...n?o:{},...n&&"object"==typeof t&&t?.serverProps?t.serverProps:{},..."object"==typeof t&&t?.clientProps?t.clientProps:{}});return(0,w.jsx)(r,{...i},a)}}return r?W({clientProps:e,Component:r,importMap:n,key:a,serverProps:o}):null},V=({description:e,Fallback:t,fontFamily:r="Arial, sans-serif",Icon:n,importMap:a,leader:o,title:i})=>{let s=W({clientProps:{fill:"white"},Component:n,Fallback:t,importMap:a});return(0,w.jsxs)("div",{style:{backgroundColor:"#000",color:"#fff",display:"flex",flexDirection:"column",fontFamily:r,height:"100%",justifyContent:"space-between",padding:"100px",width:"100%"},children:[(0,w.jsxs)("div",{style:{display:"flex",flexDirection:"column",flexGrow:1,fontSize:50,height:"100%"},children:[o&&(0,w.jsx)("div",{style:{fontSize:30,marginBottom:10},children:o}),(0,w.jsx)("p",{style:{display:"-webkit-box",fontSize:90,lineHeight:1,marginBottom:0,marginTop:0,textOverflow:"ellipsis",WebkitBoxOrient:"vertical",WebkitLineClamp:2},children:i}),e&&(0,w.jsx)("p",{style:{display:"-webkit-box",flexGrow:1,fontSize:30,lineHeight:1,marginBottom:0,marginTop:40,textOverflow:"ellipsis",WebkitBoxOrient:"vertical",WebkitLineClamp:2},children:e})]}),(0,w.jsx)("div",{style:{alignItems:"flex-end",display:"flex",flexShrink:0,height:"38px",justifyContent:"center",width:"38px"},children:s})]})},q=(0,$.fileURLToPath)("file:///C:/Users/<USER>/kavya-git/spark-new/littlespark-cms/node_modules/@payloadcms/next/dist/routes/rest/og/index.js"),B=k.dirname(q),Y=async e=>{let t=e.payload.config;if("off"===t.admin.meta.defaultOGImageType)return Response.json({error:"Open Graph images are disabled"},{status:400});try{let r;let{searchParams:n}=new URL(e.url),a=n.has("title")?n.get("title")?.slice(0,100):"",o=n.has("leader")?n.get("leader")?.slice(0,100).replace("-"," "):"",i=n.has("description")?n.get("description"):"";try{r=S.readFile(k.join(B,"roboto-regular.woff"))}catch(t){e.payload.logger.error(`Error reading font file or not readable: ${t.message}`)}return new I.f((0,w.jsx)(V,{description:i,Fallback:O,fontFamily:"Roboto, sans-serif",Icon:t.admin?.components?.graphics?.Icon,importMap:e.payload.importMap,leader:o,title:a}),{...r?{fonts:[{name:"Roboto",data:await r,style:"normal",weight:400}]}:{},height:630,width:1200})}catch(t){return e.payload.logger.error(`Error generating Open Graph image: ${t.message}`),Response.json({error:`Internal Server Error: ${t.message}`},{status:500})}},J=!1,X=e=>async(t,r)=>{let n=await e;!1!==J||n.endpoints.some(e=>"/og"===e.path&&"get"===e.method)||n.endpoints.push({handler:Y,method:"get",path:"/og"}),J=!0;let a=await r.params;return await y({config:e,path:a?`${n.routes.api}/${a.slug.join("/")}`:void 0,request:t})},Q=X,K=X,ee=X,et=X,er=X,en=X}};